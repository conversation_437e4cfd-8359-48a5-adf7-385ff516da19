//***********************************
// Copyright (c) 2022.   TabaPay, Inc.   All Rights Reserved.
//***********************************

//***********************************
// Checks if Apple Pay JS API is available in browser
// Shows button if true, else prompts user to open in Safari 
//***********************************
document.addEventListener("DOMContentLoaded", function () { 
  document.querySelectorAll('input[name="paymentMethod"]').forEach(radio => { 
    radio.addEventListener("change", function () { 
      this.id === "creditCard" ? showCreditCardForm() : this.id === "applePay" && handleApplePayment() 
    }) 
  });

  const applePayRadio = document.getElementById('applePay');
  if (applePayRadio) {
    applePayRadio.addEventListener('change', function() {
      if(this.checked) {
        document.getElementById('applePayButtonContainer').style.display = 'flex';
      } else {
        document.getElementById('applePayButtonContainer').style.display = 'none';
      }
    });
  }

  // Check if user came from QR code on iOS device
  const urlParams = new URLSearchParams(window.location.search);
  if (urlParams.get('applePay') === 'true' && isIOS() && window.ApplePaySession) {
    // Auto-select Apple Pay and trigger the payment
    if (applePayRadio) {
      applePayRadio.checked = true;
      applePayRadio.dispatchEvent(new Event('change'));
      // Small delay to ensure UI is updated, then show a prompt
      setTimeout(() => {
        if (confirm('You scanned the QR code to pay with Apple Pay. Would you like to proceed with the payment?')) {
          onApplePayButtonClicked();
        }
      }, 500);
    }
  }
}); 

function showCreditCardForm() { 
  document.getElementById('payment-selection').style.display = 'none'; 
  document.getElementById('credit-card-form').style.display = 'block'; 
} 

function showPaymentSelection() { 
  document.getElementById('payment-selection').style.display = 'block'; 
  document.getElementById('credit-card-form').style.display = 'none'; 
  document.querySelectorAll('input[name="paymentMethod"]').forEach(radio => { 
    radio.checked = false;
  });
} 

function handleApplePayment() {
  console.log("handleApplePayment");
  document.getElementById('applePayButtonContainer').style.display = 'flex';
}

//***********************************
// Checks if Apple Pay JS API is available in browser
// Shows button if true, else prompts user to open in Safari 
//***********************************

// Function to detect if user is on Safari
function isSafari() {
	const userAgent = navigator.userAgent.toLowerCase();
	return userAgent.includes('safari') && !userAgent.includes('chrome') && !userAgent.includes('android');
}

// Function to detect if user is on iOS device
function isIOS() {
	return /iPad|iPhone|iPod/.test(navigator.userAgent);
}

if(window.ApplePaySession){
  	//===================================
	// Merchant Identifier should be what is set on Apple Developer website
	//===================================
	let merchantIdentifier = 'merchant.ippay';
	let promise = ApplePaySession.canMakePaymentsWithActiveCard(merchantIdentifier);
	promise.then(function (canMakePayments){
		if(canMakePayments){
			console.log('Apple Pay is supported');
		}
	})
} else {
	console.log('Apple Pay JS API not available');
}

const validateMerchant = async (validationURL) => {
  //===================================
  // Function that contacts server, requests session from AP server,
	// then returns an opaque merchant session object
  //===================================
	const data = {validationUrl: validationURL};
	console.log('validateMerchant......',validationURL);
  //-----------------------------------
	// POST to backend
  //-----------------------------------
	try {
		const response = await fetch('/api/v2/payment/alternatepay/session/create', {
			method: 'POST',
			mode: 'cors',
			headers: {
				'Content-Type': 'application/json'
			},
			body: JSON.stringify(data)
		});
		
		if (!response.ok) {
			throw new Error(`HTTP error! status: ${response.status}`);
		}
		
		//~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		// Return
		//~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		return response.json();
	} catch (error) {
		console.error('Merchant validation failed:', error);
		throw error;
	}
	//~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
};

//***********************************
// Function to show QR code for Apple Pay on non-Safari browsers
//***********************************
const showApplePayQRCode = () => {
	// Create payment URL that will work on iOS devices
	const currentUrl = window.location.href;
	const paymentUrl = currentUrl.includes('?') 
		? `${currentUrl}&applePay=true`
		: `${currentUrl}?applePay=true`;
	
	// Create QR code modal
	const qrModal = document.createElement('div');
	qrModal.id = 'applePayQRModal';
	qrModal.style.cssText = `
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background-color: rgba(0, 0, 0, 0.8);
		display: flex;
		justify-content: center;
		align-items: center;
		z-index: 10000;
	`;
	
	const qrContent = document.createElement('div');
	qrContent.style.cssText = `
		background: white;
		padding: 30px;
		border-radius: 10px;
		text-align: center;
		max-width: 400px;
		width: 90%;
	`;
	
	qrContent.innerHTML = `
		<h2 style="margin-top: 0; color: #333;">Pay with Apple Pay</h2>
		<p style="color: #666; margin-bottom: 20px;">Scan this QR code with your iPhone to complete the payment using Apple Pay</p>
		<div id="qrcode" style="margin: 20px 0;">
			<div style="
				width: 200px; 
				height: 200px; 
				border: 1px solid #ddd; 
				display: flex; 
				align-items: center; 
				justify-content: center; 
				background: #f9f9f9;
				color: #666;
			">
				Loading QR Code...
			</div>
		</div>
		<p style="color: #999; font-size: 14px; margin-bottom: 20px;">Make sure Apple Pay is set up on your iPhone</p>
		<button onclick="closeApplePayQR()" style="
			background: #007AFF;
			color: white;
			border: none;
			padding: 12px 24px;
			border-radius: 6px;
			cursor: pointer;
			font-size: 16px;
		">Close</button>
	`;
	
	// Close modal when clicking outside
	qrModal.addEventListener('click', function(e) {
		if (e.target === qrModal) {
			closeApplePayQR();
		}
	});
	
	qrModal.appendChild(qrContent);
	document.body.appendChild(qrModal);
	
	// Add escape key listener
	document.addEventListener('keydown', handleEscapeKey);
	
	// Generate QR code using a simple QR code API
	const qrCodeDiv = document.getElementById('qrcode');
	const qrCodeUrl = `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(paymentUrl)}`;
	
	const qrImage = document.createElement('img');
	qrImage.src = qrCodeUrl;
	qrImage.style.cssText = 'width: 200px; height: 200px; border: 1px solid #ddd;';
	qrImage.alt = 'QR Code for Apple Pay';
	
	// Replace loading message when image loads
	qrImage.onload = function() {
		qrCodeDiv.innerHTML = '';
		qrCodeDiv.appendChild(qrImage);
	};
	
	// Add error handling for QR code loading
	qrImage.onerror = function() {
		qrCodeDiv.innerHTML = `
			<div style="
				width: 200px; 
				height: 200px; 
				border: 1px solid #ddd; 
				display: flex; 
				align-items: center; 
				justify-content: center; 
				background: #f5f5f5;
				color: #666;
				text-align: center;
				padding: 20px;
				box-sizing: border-box;
			">
				<div>
					<p style="margin: 0 0 10px 0; font-size: 14px;">QR Code not available</p>
					<a href="${paymentUrl}" target="_blank" style="
						color: #007AFF;
						text-decoration: none;
						font-size: 12px;
					">Open payment link</a>
				</div>
			</div>
		`;
	};
};

//***********************************
// Function to close QR code modal
//***********************************
const closeApplePayQR = () => {
	const modal = document.getElementById('applePayQRModal');
	if (modal) {
		modal.remove();
		// Remove escape key listener
		document.removeEventListener('keydown', handleEscapeKey);
	}
};

// Handle escape key to close modal
const handleEscapeKey = (event) => {
	if (event.key === 'Escape') {
		closeApplePayQR();
	}
};

//***********************************
// Event listener for when Apple Pay button is clicked
//***********************************
const onApplePayButtonClicked = () => {
	// Check if Apple Pay is available
	if(!window.ApplePaySession){
		// If not Safari and not iOS, show QR code for mobile payment
		if(!isSafari() && !isIOS()) {
			showApplePayQRCode();
			return;
		} else {
		alert("Apple Pay is not supported in this browser. Please open in Safari.");
		return;
		}
	}
	console.log("Apple Pay button clicked");
  //===================================
	// Customizations 
	//===================================
	const request = {
		"countryCode": "US",
		"currencyCode": "USD",
		"merchantCapabilities": [
			"supports3DS"
		],
		"supportedNetworks": [
			"visa",
			"masterCard",
			"amex",
			"discover",
			"jcb"
		],
    //-----------------------------------
		// Customizing touch bar
		//-----------------------------------
		"total": {
			"label": "Demo (Card is not charged)",
			"type": "final",
			"amount": "1.99"
		}
	};

	//===================================
	// Create ApplePaySDK instance
	//===================================
	// Check for the highest supported version
	const version = ApplePaySession.supportsVersion(3) ? 3 : 
					ApplePaySession.supportsVersion(2) ? 2 : 1;
	const session = new ApplePaySession(version, request);
	
  //===================================
	// As soon as the system displays the payment sheet, the Apple Pay JS 
	// API calls your session object's onvalidatemerchant event handler 
	// to verify that the request is coming from a valid merchant.
	//===================================
	session.onvalidatemerchant = async event => {
		//-----------------------------------
		// Call your own server to request a new merchant session
		//-----------------------------------
		const merchantSession = await validateMerchant(event.validationURL);
    //-----------------------------------
		// Pass opaque merchant object to ApplePaySDK to
		// complete merchant validation
		//-----------------------------------
		session.completeMerchantValidation(merchantSession);
	};
  
	//===================================
	// Event handler to call when user selects a shipping method
	//===================================
	session.onshippingmethodselected = event => {
		//-----------------------------------
		// Define ApplePayShippingMethodUpdate based on the selected shipping method
		// No updates or errors are needed, pass an empty object.
		//-----------------------------------
		const update = {};
		session.completeShippingMethodSelection(update);
	};
  
	//===================================
	// Event handler to call when user selects a shipping contact
	// in the payment sheet
	//===================================
	session.onshippingcontactselected = event => {
		//-----------------------------------
		// Define ApplePayShippingContactUpdate based on the selected shipping
		// contact
		//-----------------------------------
		const update = {};
		session.completeShippingContactSelection(update);
	};

  //===================================
	// An event handler the system calls when the user has authorized 
	// the Apple Pay payment with Touch ID, Face ID, or a passcode.
	//===================================
	session.onpaymentauthorized = async (event) => {
		const paymentToken = event.payment.token;

		try {
			// Structure the payment request according to TSYS requirements
			const paymentRequest = {
				SecureSale: {
					deviceID: "20190612333301",
					transactionKey: "RIKRM7CXV65NMTKKA7VY65SIIWHJ97W8",
					cardDataSource: "INTERNET",
					encryptedData: {
						paymentData: {
							version: paymentToken.paymentData.version,
							data: paymentToken.paymentData.data,
							signature: paymentToken.paymentData.signature,
							header: {
								transactionId: paymentToken.paymentData.header.transactionId,
								ephemeralPublicKey: paymentToken.paymentData.header.ephemeralPublicKey,
								publicKeyHash: paymentToken.paymentData.header.publicKeyHash
							}
						}
					},
					developerID: "acsdf"
				}
			};

			// Call backend to process payment with TSYS API
			const response = await fetch('/api/v2/payment/alternatepay/transaction/paydirect', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify(paymentRequest) 
			});

			if (!response.ok) {
				throw new Error(`Payment processing failed: ${response.status}`);
			}

			const result = await response.json();
			console.log('TSYS API response:', result);

			// Complete the Apple Pay session
			session.completePayment({
				status: ApplePaySession.STATUS_SUCCESS
			});

			// Redirect to confirmation page with payment details
			redirectToConfirmationPage(result);

		} catch (error) {
			console.error('Payment processing failed:', error);
			
			// Complete the Apple Pay session with failure
			session.completePayment({
				status: ApplePaySession.STATUS_FAILURE,
				errors: [{
					code: 'payment_failed',
					message: error.message
				}]
			});

			// Redirect to error page
			redirectToErrorPage(error);
		}
	};

  //===================================
	// An event handler called by the system when the user 
	// enters or updates a coupon code.
	//===================================
	session.oncouponcodechanged = event => {
    //-----------------------------------
		// Define ApplePayCouponCodeUpdate
		//-----------------------------------
		// For now, we'll use the original total since we don't have coupon logic implemented
		const update = {
			newTotal: request.total,
			newLineItems: [],
			newShippingMethods: [],
			errors: []
		};

		session.completeCouponCodeChange(update);
	};

	session.oncancel = event => {
		//-----------------------------------
		// Define behavior when payment cancelled by WebKit
		//-----------------------------------
		console.log("oncancel");
	};
  
	//===================================
	// Start up SDK
	//===================================
	session.begin();

};

//===================================
// Redirect to confirmation page with payment details
//===================================
const redirectToConfirmationPage = (paymentResult) => {
	// Store payment result in sessionStorage for the confirmation page
	sessionStorage.setItem('paymentResult', JSON.stringify(paymentResult));
	
	// Redirect to confirmation page
	window.location.href = '/payment/confirmation.html';
};

//===================================
// Redirect to error page with error details
//===================================
const redirectToErrorPage = (error) => {
	// Store error details in sessionStorage for the error page
	sessionStorage.setItem('paymentError', JSON.stringify({
		message: error.message,
		timestamp: new Date().toISOString()
	}));
	
	// Redirect to error page
	window.location.href = '/payment/error.html';
};