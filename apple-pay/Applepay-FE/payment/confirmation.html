<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        * {
            box-sizing: border-box
        }

        body {
            background-color: #f5f5f5;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Arial, sans-serif;
            margin: 0;
            padding: 20px;
        }

        .confirmation-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 40px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .success-icon {
            font-size: 64px;
            color: #28a745;
            margin-bottom: 20px;
        }

        .confirmation-title {
            font-size: 28px;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }

        .confirmation-subtitle {
            font-size: 16px;
            color: #666;
            margin-bottom: 30px;
        }

        .payment-details {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
        }

        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .detail-row:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }

        .detail-label {
            font-weight: 500;
            color: #495057;
        }

        .detail-value {
            color: #333;
            font-weight: 600;
        }

        .tsys-status {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 6px;
            padding: 12px;
            margin: 20px 0;
            color: #155724;
        }

        .action-buttons {
            margin-top: 30px;
        }

        .btn {
            display: inline-block;
            padding: 12px 24px;
            margin: 0 10px;
            border: none;
            border-radius: 6px;
            text-decoration: none;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .btn-primary {
            background-color: #007bff;
            color: white;
        }

        .btn-primary:hover {
            background-color: #0056b3;
        }

        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background-color: #545b62;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007bff;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>

<body>
    <div class="confirmation-container">
        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>Processing payment...</p>
        </div>

        <div id="confirmation-content" style="display: none;">
            <div class="success-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            
            <h1 class="confirmation-title">Payment Successful!</h1>
            <p class="confirmation-subtitle">Your payment has been processed successfully through TSYS Multipass</p>
            
            <div class="payment-details">
                <div class="detail-row">
                    <span class="detail-label">Transaction ID:</span>
                    <span class="detail-value" id="transaction-id">-</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Amount:</span>
                    <span class="detail-value" id="amount">-</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Currency:</span>
                    <span class="detail-value" id="currency">-</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Status:</span>
                    <span class="detail-value" id="status">-</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Timestamp:</span>
                    <span class="detail-value" id="timestamp">-</span>
                </div>
            </div>

            <div class="tsys-status" id="tsys-status">
                <strong>TSYS Multipass Status:</strong> <span id="tsys-status-text">Processing...</span>
            </div>

            <div class="action-buttons">
                <a href="/payment/payment.html" class="btn btn-primary">Make Another Payment</a>
                <a href="/" class="btn btn-secondary">Back to Home</a>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Show loading initially
            document.getElementById('loading').style.display = 'block';
            
            // Get payment result from sessionStorage
            const paymentResult = sessionStorage.getItem('paymentResult');
            
            if (paymentResult) {
                try {
                    const result = JSON.parse(paymentResult);
                    displayConfirmation(result);
                } catch (error) {
                    console.error('Error parsing payment result:', error);
                    redirectToErrorPage('Invalid payment result data');
                }
            } else {
                redirectToErrorPage('No payment result found');
            }
        });

        function displayConfirmation(result) {
            // Hide loading and show content
            document.getElementById('loading').style.display = 'none';
            document.getElementById('confirmation-content').style.display = 'block';

            // Populate payment details
            document.getElementById('transaction-id').textContent = result.tsysResponse?.transactionId || 'N/A';
            document.getElementById('amount').textContent = result.tsysResponse?.amount || '$1.99';
            document.getElementById('currency').textContent = result.tsysResponse?.currency || 'USD';
            document.getElementById('status').textContent = result.status || 'Success';
            document.getElementById('timestamp').textContent = new Date().toLocaleString();

            // Update TSYS status
            const tsysStatusText = document.getElementById('tsys-status-text');
            if (result.tsysResponse) {
                tsysStatusText.textContent = 'Successfully processed';
                document.getElementById('tsys-status').style.background = '#d4edda';
                document.getElementById('tsys-status').style.color = '#155724';
                document.getElementById('tsys-status').style.borderColor = '#c3e6cb';
            } else {
                tsysStatusText.textContent = 'No TSYS response data';
                document.getElementById('tsys-status').style.background = '#f8d7da';
                document.getElementById('tsys-status').style.color = '#721c24';
                document.getElementById('tsys-status').style.borderColor = '#f5c6cb';
            }

            // Clear sessionStorage
            sessionStorage.removeItem('paymentResult');
        }

        function redirectToErrorPage(message) {
            sessionStorage.setItem('paymentError', JSON.stringify({
                message: message,
                timestamp: new Date().toISOString()
            }));
            window.location.href = '/payment/error.html';
        }
    </script>
</body>

</html> 