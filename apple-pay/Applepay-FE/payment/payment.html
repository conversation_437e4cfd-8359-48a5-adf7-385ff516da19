<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <script src="https://applepay.cdn-apple.com/jsapi/v1/apple-pay-sdk.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <script type="text/javascript" src="../index.js"></script>
    <style>
        * {
            box-sizing: border-box
        }

        body {
            background-color: #f5f5f5;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Arial, sans-serif
        }

        .outer-container {
            padding: 20px;
            width: 100%
        }

        .payment-method-selection {
            text-align: left;
            margin-bottom: 30px;
            background-color: #fff;
            border: none;
            padding: 10px
        }

        .payment-method-option {
            display: flex;
            align-items: center;
            padding: 10px 15px;
            margin: 5px 0;
            background-color: #fff;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            cursor: pointer
        }

        .payment-method-label {
            width: 100%;
            margin-left: 10px;
            cursor: pointer;
            color: #333;
            display: flex;
            justify-content: space-between;
            align-items: center
        }

        .payment-logos {
            margin-left: auto;
            display: flex;
            gap: 8px;
            align-items: center
        }

        .payment-logos i {
            font-size: 24px;
            color: #000
        }

        .fa-cc-visa {
            color: #1A1F71
        }

        .fa-cc-mastercard {
            color: #EB001B
        }

        .fa-cc-amex {
            color: #006FCF
        }

        .fa-apple-pay {
            color: #000
        }

        h1 {
            font-size: 24px;
            font-weight: 400
        }

        .header {
            padding: 20px;
            color: #333;
            display: flex;
            justify-content: space-between;
            align-items: center
        }

        .amount-text {
            font-size: 16px;
            text-align: right;
            padding: 10px 0
        }

        .credit-card-form {
            display: none;
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            margin: 20px
        }

        .form-group {
            margin-bottom: 15px
        }

        .form-group label {
            display: block;
            margin-bottom: 5px
        }

        .form-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px
        }

        .btn {
            padding: 10px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            color: #fff
        }

        .submit-btn {
            background-color: #007bff
        }

        .cancel-btn {
            background-color: #dc3545
        }

        .button-group {
            display: flex;
            gap: 10px;
            margin-top: 20px
        }

        apple-pay-button {
            margin-left: 15px;
            height: 40px;
            width: 120px;
        }

        .apple-pay-button-container {
            border-top: none;
            margin-top: -5px;
            padding-top: 0;
            background-color: #f8f8f8;
        }

        apple-pay-button {
            --apple-pay-button-width: 160px;
            --apple-pay-button-height: 40px;
            --apple-pay-button-border-radius: 5px;
            --apple-pay-button-padding: 5px 0px;
            display: inline-block;
            width: 200px;
            height: 40px;
            border-radius: 5px;
        }
    </style>
</head>

<body>
    <div class="header">StratusIQ Mobile<div class="amount-text">In Order To Verify The Payment Option Your Card May be
            Authorized for $10.00</div>
    </div>
    <div class="body">
        <div class="container">
            <div class="outer-container">
                <div id="payment-selection" class="payment-method-selection">
                    <div class="payment-method-option"><input type="radio" id="creditCard" name="paymentMethod"
                            class="payment-method-radio"><label for="creditCard" class="payment-method-label">Credit
                            Card<span class="payment-logos"><i class="fab fa-cc-visa"></i><i
                                    class="fab fa-cc-mastercard"></i><i class="fab fa-cc-amex"></i></span></label></div>
                    <div class="payment-method-option">
                        <input type="radio" id="applePay" name="paymentMethod" class="payment-method-radio">
                        <label for="applePay" class="payment-method-label">Apple Pay<span class="payment-logos"><i
                                    class="fab fa-apple-pay"></i></span></label>
                    </div>
                    <div class="payment-method-option apple-pay-button-container"
                        style="justify-content: center; display: none;" id="applePayButtonContainer">
                        <apple-pay-button onclick="onApplePayButtonClicked()" buttonstyle="black" type="plain"
                            locale="en-US"></apple-pay-button>
                    </div>
                </div>
                <div id="credit-card-form" class="credit-card-form">
                    <div class="form-group"><label for="cardNumber">Card Number</label><input type="text"
                            id="cardNumber" placeholder="XXXX XXXX XXXX XXXX"></div>
                    <div class="form-group"><label for="cardName">Name on Card</label><input type="text" id="cardName"
                            placeholder="Name"></div>
                    <div style="display:flex;gap:15px">
                        <div class="form-group" style="flex:1"><label for="expiry">Expiry Date</label><input type="text"
                                id="expiry" placeholder="MM/YY"></div>
                        <div class="form-group" style="flex:1"><label for="cvv">CVV</label><input type="text" id="cvv"
                                placeholder="***"></div>
                    </div>
                    <div class="button-group"><button type="submit" class="btn submit-btn"
                            style="flex:1">Submit</button><button type="button" class="btn cancel-btn" style="flex:1"
                            onclick="showPaymentSelection()">Cancel</button></div>
                </div>
            </div>
        </div>
    </div>
</body>

</html>