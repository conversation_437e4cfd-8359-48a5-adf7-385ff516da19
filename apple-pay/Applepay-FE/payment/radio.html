<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <script src="https://js.sensepass.com/scripts/<EMAIL>"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        * {
            box-sizing: border-box
        }

        body {
            background-color: #f5f5f5;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Arial, sans-serif
        }

        .outer-container {
            padding: 20px;
            width: 100%
        }

        .payment-method-selection {
            text-align: left;
            margin-bottom: 30px;
            background-color: #fff;
            border: none;
            padding: 10px
        }

        .payment-method-option {
            display: flex;
            align-items: center;
            padding: 10px 15px;
            margin: 5px 0;
            background-color: #fff;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            cursor: pointer
        }

        .payment-method-label {
            width: 100%;
            margin-left: 10px;
            cursor: pointer;
            color: #333;
            display: flex;
            justify-content: space-between;
            align-items: center
        }

        .payment-logos {
            margin-left: auto;
            display: flex;
            gap: 8px;
            align-items: center
        }

        .payment-logos i {
            font-size: 24px;
            color: #000
        }

        .fa-cc-visa {
            color: #1A1F71
        }

        .fa-cc-mastercard {
            color: #EB001B
        }

        .fa-cc-amex {
            color: #006FCF
        }

        .fa-apple-pay {
            color: #000
        }

        h1 {
            font-size: 24px;
            font-weight: 400
        }

        .header {
            padding: 20px;
            color: #333;
            display: flex;
            justify-content: space-between;
            align-items: center
        }

        .amount-text {
            font-size: 16px;
            text-align: right;
            padding: 10px 0
        }
    </style>
</head>

<body>
    <div class="header">StratusIQ Mobile<div class="amount-text">In Order To Verify The Payment Option Your Card May be
            Authorized for $10.00</div>
    </div>
    <div class="body">
        <div class="container">
            <div class="outer-container">
                <div class="payment-method-selection" style="background-color:white">
                    <div class="payment-method-option"><input type="radio" id="creditCard" name="paymentMethod"
                            class="payment-method-radio"><label for="creditCard" class="payment-method-label">Credit
                            Card<span class="payment-logos"><i class="fab fa-cc-visa"></i><i
                                    class="fab fa-cc-mastercard"></i><i class="fab fa-cc-amex"></i></span></label></div>
                    <div class="payment-method-option"><input type="radio" id="applePay" name="paymentMethod"
                            class="payment-method-radio"><label for="applePay" class="payment-method-label">Apple
                            Pay<span class="payment-logos"><i class="fab fa-apple-pay"></i></span></label></div>
                </div>
            </div>
            <div id="sensepass-front-end" class="mb-3"></div>
        </div>
    </div>
    <script>document.addEventListener("DOMContentLoaded", function () { document.querySelectorAll('input[name="paymentMethod"]').forEach(radio => { radio.addEventListener("change", function () { this.id === "creditCard" ? window.location.href = "creditCard.html" : this.id === "applePay" && handlePayment() }) }) });</script>
    <script>const spClient = SensePassFrontEndSDK.config({ mode: "sandbox", debug: !1, visualizer: !1, clientId: "9gtlcevwt4im5d40wowxu413oe92xdo1393wgq0v5v8dl3ud", methodType: "manual_capture", theme: { material: "https://pay.sandbox.sensepass.com/publicAssets/themes/custom-sample-material.theme.css", sensepassEcommerce: "https://pay.sandbox.sensepass.com/publicAssets/themes/custom-sample-ecommerce.theme.css", paymentMethodGrid: "horizontal", config: {} } }), sensepass = spClient.init(), spFrontEndContainerEl = document.getElementById("sensepass-front-end"), spFrontEndIframeEl = spFrontEndContainerEl.firstElementChild; function handlePayment() { sensepass.pay(e => { }, { amount: 1e3, currency: "USD", billingAddress: { firstName: "John", lastName: "Doe", email: "<EMAIL>", street: "123 Main St", city: "Anytown", state: "CA", country: "US" } }) } sensepass.frameDimensions(e => { e && (spFrontEndIframeEl.style.height = `${e.height}px`, spFrontEndIframeEl.style.width = `${e.width}px`) }), sensepass.paymentSelected(e => { console.log("Payment method selected:", e) }), sensepass.paying(e => { console.log("Payment processing:", e) }), sensepass.paymentMethodValidationStatus(e => { console.log("Validation status:", e) }), sensepass.error(e => { console.log("Error:", e) });</script>
</body>

</html>