<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <script src="https://js.sensepass.com/scripts/<EMAIL>"
        type="application/javascript"></script>
    <style>
        * {
            box-sizing: border-box
        }

        body {
            background-color: #f5f5f5;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Arial, sans-serif
        }

        .outer-container {
            padding: 20px;
            width: 100%
        }

        .payment-method-selection {
            text-align: left;
            margin-bottom: 30px;
            background-color: #fff;
            border: none;
            padding: 10px
        }

        .payment-method-option {
            display: flex;
            align-items: center;
            padding: 10px 15px;
            margin: 5px 0;
            background-color: #fff;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            cursor: pointer
        }

        .payment-method-label {
            width: 100%;
            margin-left: 10px;
            cursor: pointer;
            color: #333
        }

        h1 {
            font-size: 24px;
            font-weight: 400
        }

        .header {
            padding: 20px;
            color: #333;
            display: flex;
            justify-content: space-between;
            align-items: center
        }

        .amount-text {
            font-size: 16px;
            text-align: right;
            padding: 10px 0
        }

        .form-control {
            width: 100%;
            padding: 12px;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            font-size: 16px;
            color: #333;
            background-color: #fff;
            margin-top: 5px
        }

        .form-label {
            font-size: 14px;
            color: #666;
            margin-bottom: 5px;
            display: block
        }

        .submit-btn {
            background-color: #2196f3;
            color: #fff;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            cursor: pointer;
            padding: 15px 40px
        }

        .submit-btn:hover {
            background-color: #0056b3
        }

        select.form-control {
            appearance: none;
            -webkit-appearance: none;
            -moz-appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 1rem center;
            background-size: 1em
        }

        .form-group {
            max-width: 1100px;
            display: flex;
            flex-wrap: wrap;
            justify-content: left
        }

        .form-group>div {
            flex: 0 0 50%;
            max-width: 50%;
            padding: 10px
        }

        .row {
            display: flex;
            justify-content: space-between;
            max-width: 1100px;
            margin-bottom: 10px
        }

        .col-6 {
            width: 48%
        }
    </style>
</head>
<div class="header">StratusIQ Mobile<div class="amount-text">In Order To Verify The Payment Option Your Card May be
        Authorized for $10.00</div>
</div>
<div class="body">
    <div class="container">
        <div class="outer-container">
            <div id="creditCardSection">
                <div class="content-container">
                    <div class="inner-container mx-auto">
                        <div>
                            <h4 style="font-weight:700">Card Details</h4>
                        </div>
                        <div class="form-group">
                            <div><label for="card-number" class="form-label">Card number*</label><input
                                    name="cardNumber" type="text" id="cardNumber" placeholder="XXXX XXXX XXXX XXXX"
                                    class="form-control" regex="\\d{15,16}" required
                                    data-msg-regex="Please provide a valid card number"
                                    style="background-image:url('https://hpp-test.ippay.com/media/IidJJJXi/debit_card.gif');background-position:97% 50%;background-repeat:no-repeat;background-size:37px">
                            </div>
                            <div style="max-width:150px"><label for="cvv" class="form-label">CVV*</label><input
                                    name="cvv" type="password" maxlength="4" id="cvv" autocomplete="off"
                                    class="form-control input-field" regex="^[0-9]{3,4}$"
                                    data-msg-regex="Please provide a valid cvv" required placeholder="XXX"
                                    style="background-image:url('https://hpp-test.ippay.com/media/IidJJJXi/debit_card_cvv.gif');background-position:99% 50%;background-repeat:no-repeat;background-size:55px">
                            </div>
                            <div><label for="cvv" class="form-label">Expiration Year*</label><select name="CardExpYear"
                                    id="CardExpYear" class="form-control" cardexp="true"
                                    data-msg-cardexp="Expiration date is invalid" required>
                                    <option value="" disabled selected>--- Select Year ---</option>%CC_EXP_YEAR%
                                </select></div>
                            <div><label for="expiry-date" class="form-label">Expiration Month*</label><select
                                    name="CardExpMonth" id="CardExpMonth" class="form-control" cardexp="true"
                                    data-msg-cardexp="Expiration date is invalid" required>
                                    <option value="" disabled selected>--- Select Month ---</option>
                                    <option value="01">January</option>
                                    <option value="02">February</option>
                                    <option value="03">March</option>
                                    <option value="04">April</option>
                                    <option value="05">May</option>
                                    <option value="06">June</option>
                                    <option value="07">July</option>
                                    <option value="08">August</option>
                                    <option value="09">September</option>
                                    <option value="10">October</option>
                                    <option value="11">November</option>
                                    <option value="12">December</option>
                                </select></div>
                            <div><label for="cardholder-name" class="form-label">Name*</label><input
                                    name="CardHolderName" type="text" maxlength="50" id="CardHolderName"
                                    placeholder="XXXXXX XXXXXXXXX" class="form-control" minlength="5" required></div>
                            <div><label for="email" class="form-label">Customer Email*</label><input name="email"
                                    type="email" id="email" class="form-control" placeholder="Enter email address"
                                    required></div>
                        </div>
                        <div>
                            <h4 style="font-weight:700">Billing Information</h4>
                        </div>
                        <div class="form-group">
                            <div><label for="address" class="form-label">Billing Address*</label><input name="address"
                                    type="text" id="address" class="form-control" placeholder="Enter billing address"
                                    required></div>
                            <div><label for="city" class="form-label">City*</label><input name="city" type="text"
                                    id="city" class="form-control" placeholder="Enter billing city" required></div>
                            <div><label for="state" class="form-label">State*</label><select name="state" id="state"
                                    class="form-control" required>
                                    <option value="" disabled selected>Select State</option>
                                </select></div>
                            <div><label for="zip" class="form-label">ZIP Code*</label><input name="zip" type="text"
                                    id="zip" class="form-control" placeholder="Enter billing zip" required></div>
                            <div style="display: flex; gap: 10px;">
                                <button type="submit" class="btn w-100 submit-btn">Submit</button>
                                <button type="button" class="btn w-100 submit-btn" style="background-color: #dc3545;"
                                    onclick="window.location.href='radio.html'">Cancel</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

</html>