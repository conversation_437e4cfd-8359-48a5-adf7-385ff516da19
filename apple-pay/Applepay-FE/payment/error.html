<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        * {
            box-sizing: border-box
        }

        body {
            background-color: #f5f5f5;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Arial, sans-serif;
            margin: 0;
            padding: 20px;
        }

        .error-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 40px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .error-icon {
            font-size: 64px;
            color: #dc3545;
            margin-bottom: 20px;
        }

        .error-title {
            font-size: 28px;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }

        .error-subtitle {
            font-size: 16px;
            color: #666;
            margin-bottom: 30px;
        }

        .error-details {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
        }

        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .detail-row:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }

        .detail-label {
            font-weight: 500;
            color: #495057;
        }

        .detail-value {
            color: #333;
            font-weight: 600;
        }

        .error-message {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 6px;
            padding: 12px;
            margin: 20px 0;
            color: #721c24;
        }

        .action-buttons {
            margin-top: 30px;
        }

        .btn {
            display: inline-block;
            padding: 12px 24px;
            margin: 0 10px;
            border: none;
            border-radius: 6px;
            text-decoration: none;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .btn-primary {
            background-color: #007bff;
            color: white;
        }

        .btn-primary:hover {
            background-color: #0056b3;
        }

        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background-color: #545b62;
        }

        .btn-danger {
            background-color: #dc3545;
            color: white;
        }

        .btn-danger:hover {
            background-color: #c82333;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #dc3545;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .troubleshooting {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
            text-align: left;
        }

        .troubleshooting h3 {
            color: #856404;
            margin-top: 0;
            margin-bottom: 10px;
        }

        .troubleshooting ul {
            margin: 0;
            padding-left: 20px;
            color: #856404;
        }

        .troubleshooting li {
            margin-bottom: 5px;
        }
    </style>
</head>

<body>
    <div class="error-container">
        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>Loading error details...</p>
        </div>

        <div id="error-content" style="display: none;">
            <div class="error-icon">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            
            <h1 class="error-title">Payment Failed</h1>
            <p class="error-subtitle">We encountered an issue processing your payment</p>
            
            <div class="error-message" id="error-message">
                <strong>Error:</strong> <span id="error-text">Unknown error occurred</span>
            </div>

            <div class="error-details">
                <div class="detail-row">
                    <span class="detail-label">Error Type:</span>
                    <span class="detail-value" id="error-type">-</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Timestamp:</span>
                    <span class="detail-value" id="timestamp">-</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Session ID:</span>
                    <span class="detail-value" id="session-id">-</span>
                </div>
            </div>

            <div class="troubleshooting">
                <h3>What you can try:</h3>
                <ul>
                    <li>Check your internet connection</li>
                    <li>Ensure your Apple Pay is properly set up</li>
                    <li>Try using a different payment method</li>
                    <li>Contact support if the problem persists</li>
                </ul>
            </div>

            <div class="action-buttons">
                <a href="/payment/payment.html" class="btn btn-primary">Try Again</a>
                <a href="/" class="btn btn-secondary">Back to Home</a>
                <a href="mailto:<EMAIL>" class="btn btn-danger">Contact Support</a>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Show loading initially
            document.getElementById('loading').style.display = 'block';
            
            // Get error details from sessionStorage
            const paymentError = sessionStorage.getItem('paymentError');
            
            if (paymentError) {
                try {
                    const error = JSON.parse(paymentError);
                    displayError(error);
                } catch (error) {
                    console.error('Error parsing error details:', error);
                    displayDefaultError('Invalid error data format');
                }
            } else {
                displayDefaultError('No error details found');
            }
        });

        function displayError(error) {
            // Hide loading and show content
            document.getElementById('loading').style.display = 'none';
            document.getElementById('error-content').style.display = 'block';

            // Populate error details
            document.getElementById('error-text').textContent = error.message || 'Unknown error occurred';
            document.getElementById('error-type').textContent = getErrorType(error.message);
            document.getElementById('timestamp').textContent = error.timestamp ? new Date(error.timestamp).toLocaleString() : new Date().toLocaleString();
            document.getElementById('session-id').textContent = generateSessionId();

            // Clear sessionStorage
            sessionStorage.removeItem('paymentError');
        }

        function displayDefaultError(message) {
            // Hide loading and show content
            document.getElementById('loading').style.display = 'none';
            document.getElementById('error-content').style.display = 'block';

            // Set default error details
            document.getElementById('error-text').textContent = message;
            document.getElementById('error-type').textContent = 'General Error';
            document.getElementById('timestamp').textContent = new Date().toLocaleString();
            document.getElementById('session-id').textContent = generateSessionId();
        }

        function getErrorType(message) {
            if (!message) return 'Unknown';
            
            const lowerMessage = message.toLowerCase();
            
            if (lowerMessage.includes('network') || lowerMessage.includes('fetch')) {
                return 'Network Error';
            } else if (lowerMessage.includes('apple pay') || lowerMessage.includes('payment')) {
                return 'Payment Error';
            } else if (lowerMessage.includes('tsys') || lowerMessage.includes('multipass')) {
                return 'TSYS API Error';
            } else if (lowerMessage.includes('validation') || lowerMessage.includes('merchant')) {
                return 'Validation Error';
            } else {
                return 'General Error';
            }
        }

        function generateSessionId() {
            return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        }
    </script>
</body>

</html> 