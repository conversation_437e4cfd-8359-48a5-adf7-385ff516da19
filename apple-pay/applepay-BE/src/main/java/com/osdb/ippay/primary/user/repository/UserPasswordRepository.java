package com.osdb.ippay.primary.user.repository;

import com.osdb.ippay.primary.user.repository.entity.UserPassword;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

public interface UserPasswordRepository extends JpaRepository<UserPassword, Long> {

    List<UserPassword> findByUserId(Long userId);

    UserPassword findFirstByUserIdOrderByCreatedDateAsc(Long userId);

    void deleteByIdAndUserId(Long id, Long userId);

}
