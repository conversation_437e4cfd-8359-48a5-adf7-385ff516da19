package com.osdb.ippay.sqlserver.reports.repository;

import com.osdb.ippay.sqlserver.reports.repository.entity.TotalCardDataGtwReports;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.query.Procedure;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface TotalCardDataGtwReportsRepository extends
        JpaRepository<TotalCardDataGtwReports, Long>,
        JpaSpecificationExecutor<TotalCardDataGtwReports> {

	@Procedure(name = "TotalCardDataGtwReports.getTotalCCSummaryByModelEntiy")
	List<TotalCardDataGtwReports> getTotalCCSummaryByModelEntiy(
			@Param("ipTransactionId") Long ipTransactionId,
			@Param("authStartDateTime") String authStartDateTime,
			@Param("authEndDateTime") String authEndDateTime);

}
