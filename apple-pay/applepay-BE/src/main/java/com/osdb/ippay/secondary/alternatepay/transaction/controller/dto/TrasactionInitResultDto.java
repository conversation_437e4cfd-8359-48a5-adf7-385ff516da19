package com.osdb.ippay.secondary.alternatepay.transaction.controller.dto;

import lombok.*;
import lombok.experimental.FieldDefaults;

import static lombok.AccessLevel.PRIVATE;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.osdb.ippay.secondary.alternatepay.payment.controller.dto.PaymentConfirmationDto;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class TrasactionInitResultDto {

	@JsonProperty("TransactionNumber")
    String TransactionNumber;

	@JsonProperty("date")
    String date;
 
    @JsonProperty("amount")
    String amount;

    @JsonProperty("baseAmount")
    String baseAmount;

    @JsonProperty("currency")
    String currency;
    
    @JsonProperty("status")
    int status;
    
    @JsonProperty("statusName")
    String statusName;
    
    @JsonProperty("timeoutDate")
    String timeoutDate;
    
    //@JsonProperty("maxInstallments")
    //int maxInstallments;
    
    @JsonProperty("reason")
    String reason;
    
    @JsonProperty("methodType")
    int methodType;

    @JsonProperty("paymentType")
    String paymentType;
    
    @JsonProperty("confirmation")
    PaymentConfirmationDto confirmation;
    
    @JsonProperty("transactionPage")
    TransactionPageUrl transactionPage;
}
