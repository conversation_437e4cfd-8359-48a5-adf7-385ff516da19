package com.osdb.ippay.secondary.transaction.service.impl;

import com.ippay.global.clients.ipp.IppayClient;
import com.ippay.global.clients.ipp.IppayHttpClientConfiguration;
import com.ippay.global.clients.ipp.IppayRequest;
import com.ippay.global.clients.ipp.IppayResponse;
import com.osdb.ippay.common.domain.FileDomain;
import com.osdb.ippay.common.exception.business.NotFoundException;
import com.osdb.ippay.common.util.number.DoubleUtil;
import com.osdb.ippay.common.util.string.StringUtil;
import com.osdb.ippay.primary.user.repository.entity.User;
import com.osdb.ippay.secondary.merchant.repository.entity.Merchant;
import com.osdb.ippay.secondary.transaction.repository.ACHTransactionRepository;
import com.osdb.ippay.secondary.transaction.repository.entity.ACHSettlement;
import com.osdb.ippay.secondary.transaction.repository.entity.ACHTransaction;
import com.osdb.ippay.secondary.transaction.repository.entity.ACHTransactionType;
import com.osdb.ippay.secondary.transaction.service.ACHTransactionService;
import com.osdb.ippay.secondary.transaction.service.domain.RefundTransactionRequest;
import com.osdb.ippay.secondary.transaction.service.domain.VoidTransactionRequest;
import com.osdb.ippay.secondary.transaction.service.filter.AuthDateFilter;
import com.osdb.ippay.secondary.transaction.service.filter.SettlementFilter;
import com.osdb.ippay.secondary.transaction.service.filter.TransactionFilter;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.persistence.EntityManager;
import javax.persistence.Query;
import javax.persistence.criteria.Predicate;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.PrintWriter;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.osdb.ippay.common.exception.ErrorMessage.ACH_TRANSACTION_NOT_FOUND;
import static com.osdb.ippay.common.exception.ErrorMessage.TRANSACTION_ID_NOT_FOUND;
import static com.osdb.ippay.secondary.transaction.service.filter.TransactionType.SETTLEMENT;
import static com.osdb.ippay.secondary.transaction.service.filter.TransactionType.TRANSACTION;
import static java.lang.Boolean.FALSE;
import static java.lang.Boolean.TRUE;
import static lombok.AccessLevel.PRIVATE;
import static org.apache.commons.collections.CollectionUtils.isEmpty;
import static org.apache.commons.collections.CollectionUtils.isNotEmpty;
import static org.apache.commons.lang3.StringUtils.EMPTY;

@Service
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class ACHTransactionServiceImpl implements ACHTransactionService {

    ACHTransactionRepository achTransactionRepository;
    EntityManager entityManager;

    @Autowired
    public ACHTransactionServiceImpl(ACHTransactionRepository achTransactionRepository,
                                     @Qualifier("secondaryEmFactory") EntityManager entityManager) {

        this.achTransactionRepository = achTransactionRepository;
        this.entityManager = entityManager;
    }

    @NonFinal
    @Value(value = "${ippay.trans.service.url}")
    String transURL;

    @NonFinal
    @Value(value = "${ippay.trans.service.connection.timeout}")
    Integer transConnTimeout;

    @NonFinal
    @Value(value = "${ippay.trans.service.response.timeout}")
    Integer transRespTimeout;

    static final String DATE_PATTERN = "MM/dd/yyyy HH:mm:ss";

    @Override
    public List<ACHSettlement> findSettlements(SettlementFilter filter) {
        return achTransactionRepository.getSettlements(
                filter.getMerchantId(),
                filter.getFromDateStr(),
                filter.getToDateStr()
        );
    }

    @Override
    public List<ACHTransaction> find(User authUser, TransactionFilter filter) {
        List<ACHTransaction> transactions = achTransactionRepository.findAll(filterBy(authUser, filter));
        return aFilterBy(transactions, filter);
    }

    @Override
    public ACHTransaction find(Long id) {
        return achTransactionRepository
                .findById(id)
                .orElseThrow(() -> new NotFoundException(ACH_TRANSACTION_NOT_FOUND));
    }

    private Specification<ACHTransaction> filterBy(User authUser, TransactionFilter filter) {
        return (r, rq, cb) -> {
            Predicate fromDatePredicate = filter.getFromDate() != null ?
                    cb.greaterThanOrEqualTo(
                            SETTLEMENT.equals(filter.getType()) ?
                                    r.get("settlementDate") :
                                    r.get("authDate"),
                            filter.getFromDateStr()
                    ) : cb.conjunction();

            Predicate toDatePredicate = filter.getToDate() != null ?
                    cb.lessThanOrEqualTo(
                            SETTLEMENT.equals(filter.getType()) ?
                                    r.get("settlementDate") :
                                    r.get("authDate"),
                            filter.getToDateStr()
                    ) : cb.conjunction();

            Predicate merchantIdPredication = StringUtils.isNotBlank(filter.getMerchantId()) ?
                    cb.equal(
                            r.get("merchantId"),
                            filter.getMerchantId()
                    ) : cb.conjunction();

            Predicate merchantIdsPredicate = isNotEmpty(filter.getMerchantIds()) && authUser.isNotAdmin() ?
                    cb.in(r.get("merchantId")).value(filter.getMerchantIds()) :
                    cb.conjunction();

            if(isEmpty(filter.getMerchantIds()) && authUser.isNotAdmin()) {
                merchantIdsPredicate = cb.disjunction();
            }

            return cb.and(
                    fromDatePredicate,
                    toDatePredicate,
                    merchantIdPredication,
                    merchantIdsPredicate
            );
        };
    }

    private List<ACHTransaction> aFilterBy(List<ACHTransaction> transactions, TransactionFilter filter) {

        if(StringUtils.isNotBlank(filter.getTransactionId())) {
            transactions = transactions.stream()
                    .filter(t -> Objects.nonNull(t.getTransactionId()) && t.getTransactionId().contains(filter.getTransactionId()))
                    .collect(Collectors.toList());
        }

        if(StringUtils.isNotBlank(filter.getTerminalId())) {
            transactions = transactions.stream()
                    .filter(t -> Objects.nonNull(t.getTerminalId()) && t.getTerminalId().contains(filter.getTerminalId()))
                    .collect(Collectors.toList());
        }

        if(Objects.nonNull(filter.getShowOnlyApproved()) && TRUE.equals(filter.getShowOnlyApproved())) {
            transactions = transactions.stream()
                    .filter(t -> "CHECK ACCEPTED".equals(t.getResponseText()))
                    .collect(Collectors.toList());
        }

        if(StringUtils.isNotBlank(filter.getCardNumber())) {
            transactions = transactions.stream()
                    .filter(t -> Objects.nonNull(t.getAccountNumber()) && t.getAccountNumber().contains(filter.getCardNumber()))
                    .collect(Collectors.toList());
        }

        if(StringUtils.isNotBlank(filter.getCardHolder())) {
            transactions = transactions.stream()
                    .filter(t -> Objects.nonNull(t.getCardHolder()) && t.getCardHolder().contains(filter.getCardHolder()))
                    .collect(Collectors.toList());
        }

        if(StringUtils.isNotBlank(filter.getFunding())) {
            transactions = transactions.stream()
                    .filter(t -> Objects.nonNull(t.getReturnStatusFlag()) && t.getReturnStatusFlag().equalsIgnoreCase(filter.getFunding()))
                    .collect(Collectors.toList());
        }

        if(StringUtils.isNotBlank(filter.getStatus())) {
            transactions = transactions.stream()
                    .filter(t -> Objects.nonNull(t.getSettlementStatus()) && t.getSettlementStatus().equalsIgnoreCase(filter.getStatus()))
                    .collect(Collectors.toList());
        }

        if(StringUtils.isNotBlank(filter.getAuthCode())) {
            transactions = transactions.stream()
                    .filter(t -> Objects.nonNull(t.getAuthCode()) && t.getAuthCode().contains(filter.getAuthCode()))
                    .collect(Collectors.toList());
        }

        if(StringUtils.isNotBlank(filter.getUd1())) {
            transactions = transactions.stream()
                    .filter(t -> Objects.nonNull(t.getUd1()) && t.getUd1().contains(filter.getUd1()))
                    .collect(Collectors.toList());
        }

        if(StringUtils.isNotBlank(filter.getUd2())) {
            transactions = transactions.stream()
                    .filter(t -> Objects.nonNull(t.getUd2()) && t.getUd2().contains(filter.getUd2()))
                    .collect(Collectors.toList());
        }

        if(StringUtils.isNotBlank(filter.getUd3())) {
            transactions = transactions.stream()
                    .filter(t -> Objects.nonNull(t.getUd3()) && t.getUd3().contains(filter.getUd3()))
                    .collect(Collectors.toList());
        }

        if(StringUtils.isNotBlank(filter.getOrderNumber())) {
            transactions = transactions.stream()
                    .filter(t -> Objects.nonNull(t.getOrderNumber()) && t.getOrderNumber().contains(filter.getOrderNumber()))
                    .collect(Collectors.toList());
        }

        if(StringUtils.isNotBlank(filter.getTransactionType())) {
            transactions = transactions.stream()
                    .filter(t -> Objects.nonNull(t.getRequestType()) && t.getRequestType().equals(filter.getTransactionType()))
                    .collect(Collectors.toList());
        }

        if(Objects.nonNull(filter.getAmount())) {
            transactions = transactions.stream()
                    .filter(t -> Objects.nonNull(t.getAmount()) && t.getAmount().equals(filter.getAmount()))
                    .collect(Collectors.toList());
        }

        if(FALSE.equals(filter.getIncludeTokenize())) {
            transactions = transactions.stream()
                    .filter(t ->
                            ObjectUtils.notEqual("TOKENIZE", t.getRequestType())
                    ).collect(Collectors.toList());
        }

        if(StringUtils.isNotBlank(filter.getApprovalStatus())) {
            if("CHECK ACCEPTED".equals(filter.getApprovalStatus())) {
                transactions = transactions.stream()
                        .filter(t ->
                                Objects.nonNull(t.getResponseText()) &&
                                "CHECK ACCEPTED".equalsIgnoreCase(t.getResponseText())
                        ).collect(Collectors.toList());
            } else {
                transactions = transactions.stream()
                        .filter(t ->
                                Objects.nonNull(t.getResponseText()) &&
                                !"CHECK ACCEPTED".equalsIgnoreCase(t.getResponseText())
                        ).collect(Collectors.toList());
            }
        }

        return transactions;
    }

    @Override
    public FileDomain exportTransactions(User user, TransactionFilter filter) throws IOException {
        Specification<ACHTransaction> specification = filterBy(user, filter);
        List<ACHTransaction> transactions = achTransactionRepository.findAll(specification);

        ByteArrayInputStream byteArrayInputStream = user.isAdmin() ?
                adminExport(transactions, totalAchAmount(filter)) :
                export(transactions, totalAchAmount(filter, filter.getMerchantIds()));

        return FileDomain.builder()
                .file(new ByteArrayResource(byteArrayInputStream.readAllBytes()))
                .fileName(String.format("exported_ach_transactions_%s.csv", LocalDate.now()))
                .build();
    }

    private ByteArrayInputStream adminExport(List<ACHTransaction> achTransactions, Double totalAmount) throws IOException {
        String[] headers = {
                "IP Trans ID",
                "Merchant Name",
                "Merchant ID",
                "Trans Type",
                "Terminal ID",
                "Transaction ID",
                "Request Type",
                "Amount",
                "Response Code",
                "Response Text",
                "Status",
                "Auth Date",
                "Settlement Date",
                "Token",
                "Routing",
                "Account Number",
                "Check Number",
                "Account Holder",
                "Address",
                "City",
                "State",
                "Zip Code",
                "Country",
                "Phone",
                "Email",
                "UD1",
                "UD2",
                "UD3",
                "Order Number",
                "IP Address",
                "AuthResponseCode",
                "Terminal",
                "Transaction Identifier",
                "SPS MID Map",
                "Processor"
        };

        final CSVFormat format = CSVFormat.Builder.create()
                .setHeader(headers)
                .setIgnoreEmptyLines(true)
                .build();

        try (
                ByteArrayOutputStream out = new ByteArrayOutputStream();
                CSVPrinter csvPrinter = new CSVPrinter(new PrintWriter(out), format)
        ) {

            DateTimeFormatter dateFormat = DateTimeFormatter.ofPattern(DATE_PATTERN);

            for (ACHTransaction achTransaction : achTransactions) {
                String authDate = achTransaction.getAuthDate() != null ?
                        achTransaction.getAuthDateInstant().atZone(ZoneId.of("US/Central")).format(dateFormat) : "";

                String settleDate = achTransaction.getSettlementDate() != null ?
                    achTransaction.getSettlementDateInstant().atZone(ZoneId.of("US/Central")).format(dateFormat) : "";

                Merchant merchant = achTransaction.getMerchant();

                List<String> data = Arrays.asList(
                        achTransaction.getIpTransID() != null ? String.valueOf(achTransaction.getIpTransID()) : "",
                        Objects.nonNull(merchant) ? merchant.getMerchantName() : "",
                        achTransaction.getMerchantId(),
                        "ACH",
                        achTransaction.getTerminalId(),
                        achTransaction.getTransactionId(),
                        getRequestType(achTransaction.getRequestType()),
                        toPriceFormat(achTransaction.getAmount()),
                        achTransaction.getResponseCode(),
                        achTransaction.getResponseText(),
                        achTransaction.getStatus(),
                        authDate,
                        settleDate,
                        achTransaction.getToken(),
                        achTransaction.getRouting(),
                        achTransaction.getAccountNumber(),
                        achTransaction.getCheckNumber(),
                        achTransaction.getCardHolder(),
                        achTransaction.getAddress(),
                        achTransaction.getCity(),
                        achTransaction.getState(),
                        achTransaction.getZipCode(),
                        achTransaction.getCountry(),
                        achTransaction.getPhone(),
                        achTransaction.getEmail(),
                        achTransaction.getUd1(),
                        achTransaction.getUd2(),
                        achTransaction.getUd3(),
                        achTransaction.getOrderNumber(),
                        "",
                        "",
                        "",
                        "",
                        achTransaction.getInvoiceNumber(),
                        achTransaction.getAchProcessor()
                );

                csvPrinter.printRecord(data);
            }

            csvPrinter.printRecord(
                    EMPTY, EMPTY, EMPTY, EMPTY, EMPTY, EMPTY, EMPTY,
                    totalAmount != null ? totalAmount : 0.0
            );

            csvPrinter.flush();
            return new ByteArrayInputStream(out.toByteArray());
        }
    }

    private ByteArrayInputStream export(List<ACHTransaction> achTransactions, Double totalAmount) throws IOException {
        String[] headers = {
                "Merchant Name",
                "Merchant ID",
                "Trans Type",
                "Terminal ID",
                "Transaction ID",
                "Request Type",
                "Amount",
                "Response Code",
                "Response Text",
                "Status",
                "Auth Date",
                "Settlement Date",
                "Token",
                "Routing",
                "Account Number",
                "Check Number",
                "Account Holder",
                "Address",
                "City",
                "State",
                "Zip Code",
                "Country",
                "Phone",
                "Email",
                "UD1",
                "UD2",
                "UD3",
                "Order Number",
                "IP Address"
        };

        final CSVFormat format = CSVFormat.Builder.create()
                .setHeader(headers)
                .setIgnoreEmptyLines(true)
                .build();

        try (
                ByteArrayOutputStream out = new ByteArrayOutputStream();
                CSVPrinter csvPrinter = new CSVPrinter(new PrintWriter(out), format)
        ) {

            DateTimeFormatter dateFormat = DateTimeFormatter.ofPattern(DATE_PATTERN);

            for (ACHTransaction achTransaction : achTransactions) {
                String authDate = achTransaction.getAuthDate() != null ?
                        achTransaction.getAuthDateInstant().atZone(ZoneId.of("US/Central")).format(dateFormat) : "";

                String settleDate = achTransaction.getSettlementDate() != null ?
                        achTransaction.getSettlementDateInstant().atZone(ZoneId.of("US/Central")).format(dateFormat) : "";

                Merchant merchant = achTransaction.getMerchant();

                List<String> data = Arrays.asList(
                        Objects.nonNull(merchant) ? merchant.getMerchantName() : "",
                        achTransaction.getMerchantId(),
                        "ACH",
                        StringUtil.hashString(achTransaction.getTerminalId()),
                        achTransaction.getTransactionId(),
                        getRequestType(achTransaction.getRequestType()),
                        toPriceFormat(achTransaction.getAmount()),
                        achTransaction.getResponseCode(),
                        achTransaction.getResponseText(),
                        achTransaction.getStatus(),
                        authDate,
                        settleDate,
                        achTransaction.getToken(),
                        achTransaction.getRouting(),
                        achTransaction.getAccountNumber(),
                        achTransaction.getCheckNumber(),
                        achTransaction.getCardHolder(),
                        achTransaction.getAddress(),
                        achTransaction.getCity(),
                        achTransaction.getState(),
                        achTransaction.getZipCode(),
                        achTransaction.getCountry(),
                        achTransaction.getPhone(),
                        achTransaction.getEmail(),
                        achTransaction.getUd1(),
                        achTransaction.getUd2(),
                        achTransaction.getUd3(),
                        achTransaction.getOrderNumber()
                );

                csvPrinter.printRecord(data);
            }

            csvPrinter.printRecord(
                    EMPTY, EMPTY, EMPTY, EMPTY, EMPTY, EMPTY,
                    totalAmount != null ? totalAmount : 0.0
            );

            csvPrinter.flush();
            return new ByteArrayInputStream(out.toByteArray());
        }
    }

    @Override
    public FileDomain exportSettlements(User user, TransactionFilter filter) throws IOException {
        Specification<ACHTransaction> specification = filterBy(user, filter);
        List<ACHTransaction> transactions = achTransactionRepository.findAll(specification);

        ByteArrayInputStream byteArrayInputStream = user.isAdmin() ?
                adminSettlementsExport(transactions) :
                settlementsExport(transactions);

        return FileDomain.builder()
                .file(new ByteArrayResource(byteArrayInputStream.readAllBytes()))
                .fileName(String.format("exported_cc_settlements_%s.csv", LocalDate.now()))
                .build();
    }

    private ByteArrayInputStream settlementsExport(List<ACHTransaction> achTransactions) throws IOException {
        String[] HEADERS = {
                "Merchant Name",
                "Merchant ID",
                "Trans Type",
                "Terminal ID",
                "Transaction ID",
                "Request Type",
                "Amount",
                "Response Code",
                "Response Text",
                "Auth Date",
                "Settlement Date",
                "Funding",
                "Token",
                "Routing",
                "Account Number",
                "Check Number",
                "Account Holder",
                "Status",
                "Type",
                "Address",
                "City",
                "State",
                "Zip Code",
                "Country",
                "Phone",
                "Email",
                "UD1",
                "UD2",
                "UD3",
                "Order Number",
                "IP Address"
        };

        final CSVFormat format = CSVFormat.Builder.create()
                .setHeader(HEADERS)
                .setIgnoreEmptyLines(true)
                .build();

        try (
                ByteArrayOutputStream out = new ByteArrayOutputStream();
                CSVPrinter csvPrinter = new CSVPrinter(new PrintWriter(out), format)
        ) {

            DateTimeFormatter dateFormat = DateTimeFormatter.ofPattern(DATE_PATTERN);

            for (ACHTransaction achTransaction : achTransactions) {
                String authDate = achTransaction.getAuthDate() != null ?
                        achTransaction.getAuthDateInstant().atZone(ZoneId.of("US/Central")).format(dateFormat) : "";

                String settleDate = achTransaction.getSettlementDate() != null ?
                        achTransaction.getSettlementDateInstant().atZone(ZoneId.of("US/Central")).format(dateFormat) : "";

                List<String> data = Arrays.asList(
                        achTransaction.getMerchantName(),
                        achTransaction.getMerchantId(),
                        "ACH",
                        StringUtil.hashString(achTransaction.getTerminalId()),
                        achTransaction.getTransactionId(),
                        getRequestType(achTransaction.getRequestType()),
                        toPriceFormat(achTransaction.getAmount()),
                        achTransaction.getResponseCode(),
                        achTransaction.getResponseText(),
                        authDate,
                        settleDate,
                        achTransaction.getReturnStatusFlag(),
                        achTransaction.getToken(),
                        achTransaction.getRouting(),
                        achTransaction.getAccountNumber(),
                        achTransaction.getCheckNumber(),
                        achTransaction.getCardHolder(),
                        achTransaction.getSettlementStatus(),
                        achTransaction.getType(),
                        achTransaction.getAddress(),
                        achTransaction.getCity(),
                        achTransaction.getState(),
                        achTransaction.getZipCode(),
                        achTransaction.getCountry(),
                        achTransaction.getPhone(),
                        achTransaction.getEmail(),
                        achTransaction.getUd1(),
                        achTransaction.getUd2(),
                        achTransaction.getUd3(),
                        achTransaction.getOrderNumber()
                );

                csvPrinter.printRecord(data);
            }

            csvPrinter.flush();
            return new ByteArrayInputStream(out.toByteArray());
        }
    }

    private ByteArrayInputStream adminSettlementsExport(List<ACHTransaction> achTransactions) throws IOException {
        String[] HEADERS = {
                "Merchant Name",
                "Merchant ID",
                "Trans Type",
                "Terminal ID",
                "Transaction ID",
                "Request Type",
                "Amount",
                "Response Code",
                "Response Text",
                "Auth Date",
                "Settlement Date",
                "Funding",
                "Token",
                "Routing",
                "Account Number",
                "Check Number",
                "Account Holder",
                "Status",
                "Type",
                "Address",
                "City",
                "State",
                "Zip Code",
                "Country",
                "Phone",
                "Email",
                "UD1",
                "UD2",
                "UD3",
                "Order Number",
                "IP Address",
                "AuthResponseCode",
                "Terminal",
                "Transaction Identifier",
                "SPS MID Map",
                "Processor"
        };

        final CSVFormat format = CSVFormat.Builder.create()
                .setHeader(HEADERS)
                .setIgnoreEmptyLines(true)
                .build();

        try (
                ByteArrayOutputStream out = new ByteArrayOutputStream();
                CSVPrinter csvPrinter = new CSVPrinter(new PrintWriter(out), format)
        ) {

            DateTimeFormatter dateFormat = DateTimeFormatter.ofPattern(DATE_PATTERN);

            for (ACHTransaction achTransaction : achTransactions) {
                String authDate = achTransaction.getAuthDate() != null ?
                        achTransaction.getAuthDateInstant().atZone(ZoneId.of("US/Central")).format(dateFormat) : "";

                String settleDate = achTransaction.getSettlementDate() != null ?
                        achTransaction.getSettlementDateInstant().atZone(ZoneId.of("US/Central")).format(dateFormat) : "";

                List<String> data = Arrays.asList(
                        achTransaction.getMerchantName(),
                        achTransaction.getMerchantId(),
                        "ACH",
                        achTransaction.getTerminalId(),
                        achTransaction.getTransactionId(),
                        getRequestType(achTransaction.getRequestType()),
                        toPriceFormat(achTransaction.getAmount()),
                        achTransaction.getResponseCode(),
                        achTransaction.getResponseText(),
                        authDate,
                        settleDate,
                        achTransaction.getStatus(),
                        achTransaction.getToken(),
                        achTransaction.getRouting(),
                        achTransaction.getAccountNumber(),
                        achTransaction.getCheckNumber(),
                        achTransaction.getCardHolder(),
                        achTransaction.getSettlementStatus(),
                        achTransaction.getType(),
                        achTransaction.getAddress(),
                        achTransaction.getCity(),
                        achTransaction.getState(),
                        achTransaction.getZipCode(),
                        achTransaction.getCountry(),
                        achTransaction.getPhone(),
                        achTransaction.getEmail(),
                        achTransaction.getUd1(),
                        achTransaction.getUd2(),
                        achTransaction.getUd3(),
                        achTransaction.getOrderNumber(),
                        "",
                        "",
                        "",
                        "",
                        achTransaction.getInvoiceNumber(),
                        achTransaction.getAchProcessor()
                );

                csvPrinter.printRecord(data);
            }

            csvPrinter.flush();
            return new ByteArrayInputStream(out.toByteArray());
        }
    }

    private String toPriceFormat(Double value) {
        return value != null ? DoubleUtil.toPriceFormat(value, 2, 1) : null;
    }

    private String getRequestType(String requestType) {
        try {
            return ACHTransactionType
                    .valueOf(requestType.toUpperCase())
                    .getValue();

        } catch (Exception ex) {
            return requestType;
        }
    }

    @Override
    public List<String> findExistingACHAuthDates(AuthDateFilter filter) {
        List<String> transactions = TRANSACTION.equals(filter.getType()) ?
                achTransactionRepository.findAuthDatesForTransactions(filter.getMerchantId()) :
                achTransactionRepository.findAuthDatesForSettlements(filter.getMerchantId());

        return transactions.stream()
                .sorted(Comparator.reverseOrder())
                .map(authDate -> authDate != null ? authDate.split(" ")[0] : null)
                .distinct()
                .limit(15)
                .collect(Collectors.toList());
    }

    @Override
    public Double totalAchAmount(TransactionFilter filter, List<String> merchantIds) {
        if(CollectionUtils.isEmpty(merchantIds)) {
            return 0.0;
        }

        StringBuilder sqlQuery = new StringBuilder(
                "SELECT " +
                        "SUM(IF(RequestType = 'CHECK' AND ResponseText = 'CHECK ACCEPTED', TotalAmount, 0)) " +
                        "- SUM(IF(RequestType = 'VOIDACH' AND ResponseText = 'CHECK ACCEPTED' AND ReturnStatusFlag != 'B', TotalAmount, 0)) " +
                        "- sum(IF(RequestType = 'REVERSAL' AND ResponseText = 'CHECK ACCEPTED' AND ReturnStatusFlag != 'B', TotalAmount, 0)) " +
                        "FROM ACHTransactions " +
                        "WHERE " +
                        "MerchantID = :merchantId "
        );

        buildWhereClause(sqlQuery, filter, merchantIds);

        Query query = entityManager.createNativeQuery(sqlQuery.toString());
        query.setParameter("merchantId", filter.getMerchantId());
        query.setParameter("fromDate", filter.getFromDateStr());
        query.setParameter("toDate", filter.getToDateStr());
        query.setParameter("merchantIds", filter.getMerchantIds());

        Object totalCCAmount = query.getSingleResult();
        return totalCCAmount != null ? ((Number) totalCCAmount).doubleValue() : 0L;
    }

    @Override
    public Double totalAchAmount(TransactionFilter filter) {
        StringBuilder sqlQuery = new StringBuilder(
                "SELECT " +
                        "SUM(IF(RequestType = 'CHECK' AND ResponseText = 'CHECK ACCEPTED' AND (ReturnStatusFlag IS NULL OR ReturnStatusFlag != 'B'), TotalAmount, 0)) " +
                        "- SUM(IF(RequestType = 'VOIDACH' AND ResponseText = 'CHECK ACCEPTED' AND (ReturnStatusFlag IS NULL OR ReturnStatusFlag != 'B'), TotalAmount, 0)) " +
                        "- sum(IF(RequestType = 'REVERSAL' AND ResponseText = 'CHECK ACCEPTED' AND (ReturnStatusFlag IS NULL OR ReturnStatusFlag != 'B'), TotalAmount, 0)) " +
                        "FROM ACHTransactions " +
                        "WHERE " +
                        "MerchantID = :merchantId "
        );

        buildWhereClause(sqlQuery, filter);

        Query query = entityManager.createNativeQuery(sqlQuery.toString());
        query.setParameter("merchantId", filter.getMerchantId());
        query.setParameter("fromDate", filter.getFromDateStr());
        query.setParameter("toDate", filter.getToDateStr());

        Object totalCCAmount = query.getSingleResult();
        return totalCCAmount != null ? ((Number) totalCCAmount).doubleValue() : 0.0;
    }

    private void buildWhereClause(StringBuilder baseQuery, TransactionFilter filter, List<String> merchantIds) {
        buildWhereClause(baseQuery, filter);

        if(CollectionUtils.isNotEmpty(merchantIds)) {
            baseQuery.append("AND MerchantID IN (:merchantIds) ");
        }
    }

    private void buildWhereClause(StringBuilder baseQuery, TransactionFilter filter) {
        if(TRANSACTION.equals(filter.getType())) {
            baseQuery.append("AND AuthTransDate >= :fromDate ");
            baseQuery.append("AND AuthTransDate <= :toDate ");
        } else {
            baseQuery.append("AND SettleDate >= :fromDate ");
            baseQuery.append("AND SettleDate <= :toDate ");
            baseQuery.append("AND SettleDate IS NOT NULL ");
        }

        if(TRUE.equals(filter.getShowOnlyApproved())) {
            baseQuery.append("AND ResponseText = 'CHECK ACCEPTED' ");
        }
    }

    @Override
    public IppayResponse voidTransaction(Long id, VoidTransactionRequest voidTransactionRequest) {
        final IppayHttpClientConfiguration configuration = new IppayHttpClientConfiguration
                .Builder(transURL, transConnTimeout, transRespTimeout)
                .build();

        IppayClient.init(configuration);

        IppayClient client  = IppayClient.getInstance();
        IppayRequest request = new IppayRequest();

        ACHTransaction achTransaction = achTransactionRepository
                .findById(id)
                .orElseThrow(() -> new NotFoundException(TRANSACTION_ID_NOT_FOUND));

        request.setTerminalID(achTransaction.getTerminalId())
                .setUdField1(voidTransactionRequest.getUd1())
                .setUdField2(voidTransactionRequest.getUd2())
                .setUdField3(voidTransactionRequest.getUd3())
                .setTransactionType(IppayRequest.TransactionType.VOIDACH)
                .setTransactionID(achTransaction.getTransactionId());

        return client.query(request);
    }

    @Override
    public IppayResponse refundTransaction(Long id, RefundTransactionRequest refundTransactionRequest) {
        final IppayHttpClientConfiguration configuration = new IppayHttpClientConfiguration
                .Builder(transURL, transConnTimeout, transRespTimeout)
                .build();

        IppayClient.init(configuration);

        IppayClient client  = IppayClient.getInstance();
        IppayRequest request = new IppayRequest();

        ACHTransaction achTransaction = achTransactionRepository
                .findById(id)
                .orElseThrow(() -> new NotFoundException(TRANSACTION_ID_NOT_FOUND));

        request.setTerminalID(achTransaction.getTerminalId())
                .setUdField1(refundTransactionRequest.getUd1())
                .setUdField2(refundTransactionRequest.getUd2())
                .setUdField3(refundTransactionRequest.getUd3())
                .setTransactionType(IppayRequest.TransactionType.REVERSAL)
                .setTotalAmount(refundTransactionRequest.getAmount())
                .setTransactionID(achTransaction.getTransactionId());

        return client.query(request);
    }
}
