package com.osdb.ippay.secondary.retokenization.presignedurl.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

import static lombok.AccessLevel.PRIVATE;

@Getter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class PresignedURLOutDto {

    @JsonProperty(required = true)
    String link;

    @JsonProperty(required = true)
    String httpMethod;

}
