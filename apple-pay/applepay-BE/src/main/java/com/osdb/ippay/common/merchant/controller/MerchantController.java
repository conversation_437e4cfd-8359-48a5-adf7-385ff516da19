package com.osdb.ippay.common.merchant.controller;

import com.osdb.ippay.common.domain.FileDomain;
import com.osdb.ippay.common.logging.LogExecutionTime;
import com.osdb.ippay.common.merchant.facade.MerchantFacade;
import com.osdb.ippay.common.merchant.facade.dto.MerchantDto;
import com.osdb.ippay.common.security.service.AuthenticationService;
import com.osdb.ippay.common.util.annotation.ApiPageable;
import com.osdb.ippay.common.util.annotation.ApiSortable;
import com.osdb.ippay.common.util.response.PageResponse;
import com.osdb.ippay.primary.user.repository.entity.User;
import com.osdb.ippay.secondary.merchant.service.filter.MerchantFilter;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.List;

import static lombok.AccessLevel.PRIVATE;

@Tag(name = "merchant")
@RestController
@RequestMapping(value = "/api/v1/private/merchants")
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class MerchantController {
/*
    AuthenticationService authenticationService;
    MerchantFacade merchantFacade;

    @LogExecutionTime
    @ApiSortable
    @GetMapping
    public ResponseEntity<List<MerchantDto>> get(@Parameter(hidden = true) Sort sort,
                                                 @ParameterObject MerchantFilter filter,
                                                 @Parameter(hidden = true) @AuthenticationPrincipal String email) {

        List<MerchantDto> response = merchantFacade.find(email, filter, sort);
        return ResponseEntity.ok(response);
    }

    @LogExecutionTime
    @ApiPageable
    @GetMapping("/pageable")
    public ResponseEntity<PageResponse<MerchantDto>> get(@Parameter(hidden = true) Pageable pageable,
                                                         @ParameterObject MerchantFilter filter,
                                                         @Parameter(hidden = true) @AuthenticationPrincipal String email) {

        Page<MerchantDto> response = merchantFacade.find(email, filter, pageable);
        return ResponseEntity.ok(new PageResponse<>(response.getContent(), response.getTotalElements()));
    }

    @LogExecutionTime
    @GetMapping("/{id}")
    public ResponseEntity<MerchantDto> get(@PathVariable String id,
                                           @Parameter(hidden = true) @AuthenticationPrincipal String email) {

        MerchantDto response = merchantFacade.find(email, id);
        return ResponseEntity.ok(response);
    }

    @LogExecutionTime
    @GetMapping("/export")
    public ResponseEntity<ByteArrayResource> export(@ParameterObject MerchantFilter filter,
                                                    @Parameter(hidden = true) @AuthenticationPrincipal String email)
            throws IOException {

        User authUser = authenticationService.getCurrentUser(email);
        authenticationService.throwIfNoAccessToExport(authUser);

        FileDomain response = merchantFacade.export(authUser, filter);

        return ResponseEntity.ok()
                .contentType(new MediaType("application", "force-download"))
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=".concat(response.getFileName()))
                .body(response.getFile());
    }*/
}
