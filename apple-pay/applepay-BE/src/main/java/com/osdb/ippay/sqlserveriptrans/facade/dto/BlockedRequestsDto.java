package com.osdb.ippay.sqlserveriptrans.facade.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import javax.persistence.*;

import static lombok.AccessLevel.PRIVATE;


@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class BlockedRequestsDto {

    public Long id;

	public String terminalId;

    public String ipAddress;

    public String cardName;

    public String transactionType;

    public String totalAmount;
    
    public String transactionID;

    public java.util.Date date;
    
    public String xml;
    
    }

