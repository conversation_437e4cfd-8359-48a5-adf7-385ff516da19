package com.osdb.ippay.secondary.transaction.facade.mapper;

import com.osdb.ippay.common.util.number.DoubleUtil;
import com.osdb.ippay.common.util.string.StringUtil;
import com.osdb.ippay.primary.user.repository.entity.User;
import com.osdb.ippay.secondary.merchant.repository.entity.Merchant;
import com.osdb.ippay.secondary.transaction.facade.dto.ACHTransactionDto;
import com.osdb.ippay.secondary.transaction.facade.dto.RefundVoidBtn;
import com.osdb.ippay.secondary.transaction.repository.entity.ACHTransaction;
import com.osdb.ippay.secondary.transaction.service.filter.TransactionType;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.osdb.ippay.common.util.date.DateUtil.isTheSameDayZoned;
import static com.osdb.ippay.secondary.transaction.facade.dto.RefundVoidBtn.*;
import static com.osdb.ippay.secondary.transaction.service.filter.TransactionType.SETTLEMENT;
import static com.osdb.ippay.secondary.transaction.service.filter.TransactionType.TRANSACTION;

@Component
public class ACHTransactionMapper {

    public List<ACHTransactionDto> toDto(TransactionType type, User user, List<ACHTransaction> achTransactions) {
        return achTransactions.stream()
                .map(achTransaction -> toDto(type, user, achTransaction))
                .collect(Collectors.toList());
    }

    public ACHTransactionDto toDto(User user, ACHTransaction achTransaction) {
        return toDto(TRANSACTION, user, achTransaction);
    }

    public ACHTransactionDto toDto(TransactionType type, User user, ACHTransaction achTransaction) {
        Merchant merchant = achTransaction.getMerchant();

        return ACHTransactionDto.builder()
                .id(achTransaction.getId())
                .terminalId(
                        user.isAdmin() ?
                                achTransaction.getTerminalId() :
                                StringUtil.hashString(achTransaction.getTerminalId())
                )
                .merchantId(achTransaction.getMerchantId())
                .merchantName(Objects.nonNull(merchant) ? merchant.getMerchantName() : null)
                .transactionId(achTransaction.getTransactionId())
                .transactionType(achTransaction.getTransactionType())
                .accountNumber(achTransaction.getToken())
                .cardHolder(achTransaction.getCardHolder())
                .address(achTransaction.getAddress())
                .city(achTransaction.getCity())
                .state(achTransaction.getState())
                .zipCode(achTransaction.getZipCode())
                .country(achTransaction.getCountry())
                .phone(achTransaction.getPhone())
                .email(achTransaction.getEmail())
                .amount(toPriceFormat(achTransaction.getAmount()))
                .amountCents(achTransaction.getAmount() != null ? (long) (achTransaction.getAmount() * 100) : null)
                .ud1(achTransaction.getUd1())
                .ud2(achTransaction.getUd2())
                .ud3(achTransaction.getUd3())
                .authCode(achTransaction.getAuthCode())
                .authDate(achTransaction.getAuthDateInstant())
                .settlementDate(achTransaction.getSettlementDateInstant())
                .achProcessor(achTransaction.getAchProcessor())
                .requestType(achTransaction.getRequestType())
                .currencyCode(achTransaction.getCurrencyCode())
                .orderNumber(achTransaction.getOrderNumber())
                .checkNumber(achTransaction.getCheckNumber())
                .invoiceNumber(achTransaction.getInvoiceNumber())
                .routing(achTransaction.getRouting())
                .responseCode(achTransaction.getResponseCode())
                .responseText(achTransaction.getResponseText())
                .ipAddress(null) // TODO need to be reworked
                .token(achTransaction.getToken())
                .refundVoidBtn(getVoidRefundBtn(achTransaction))
                .fundingDisposition(achTransaction.getReturnStatusFlag())
                .status(
                        SETTLEMENT.equals(type) ?
                                achTransaction.getSettlementStatus() :
                                achTransaction.getAuthStatus()
                )
                .type(achTransaction.getType())
                .build();
    }

    private String toPriceFormat(Double value) {
        return value != null ? DoubleUtil.toPriceFormat(value, 2, 1) : null;
    }

    private RefundVoidBtn getVoidRefundBtn(ACHTransaction achTransaction) {
        if(StringUtils.isBlank(achTransaction.getToken())) return NONE_BTN;

        if(StringUtils.isBlank(achTransaction.getSettlementDate())) {
            ZoneId zoneId = ZoneId.of("US/Central");

            LocalDateTime authDateTime = LocalDateTime.ofInstant(achTransaction.getAuthDateInstant(), zoneId);
            LocalDateTime currDateTime = LocalDateTime.now(zoneId);

            int daysDiff = currDateTime.getDayOfMonth() - authDateTime.getDayOfMonth();

            // VOID BTN
            boolean isCurrDateEqAuthDate = isTheSameDayZoned(authDateTime, currDateTime);
            boolean isCurrDateBefore7pm = currDateTime.getHour() < 19;
            boolean isAuthDateAfter7pm = authDateTime.getHour() >= 19;
            boolean isAuthDateBefore7pmNextDay = daysDiff <= 1 && currDateTime.getHour() < 19;
            if((isCurrDateEqAuthDate && isCurrDateBefore7pm) || (isAuthDateAfter7pm && isAuthDateBefore7pmNextDay)) {
                return VOID_BTN;
            }

            return NONE_BTN;
        }

        String responseText = Optional.ofNullable(achTransaction.getResponseText()).orElse("");
        return responseText.equals("CHECK ACCEPTED") ? REFUND_BTN : NONE_BTN;
    }
}
