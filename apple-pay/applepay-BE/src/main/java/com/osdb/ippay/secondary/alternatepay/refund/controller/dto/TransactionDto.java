package com.osdb.ippay.secondary.alternatepay.refund.controller.dto;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;
import lombok.experimental.FieldDefaults;

import static lombok.AccessLevel.PRIVATE;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class TransactionDto {

    @JsonProperty("TransactionNumber")
    String TransactionNumber;
  
    String date;
    
    int amount;
    
    int status;
    
    String currency;
    
    String businessId;
    
    String confirmationNumber;
    
    int type;

}
