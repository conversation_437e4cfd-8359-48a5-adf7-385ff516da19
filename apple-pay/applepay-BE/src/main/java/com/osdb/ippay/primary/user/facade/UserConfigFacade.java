package com.osdb.ippay.primary.user.facade;

import com.osdb.ippay.primary.user.facade.dto.UserConfigDto;
import com.osdb.ippay.common.user.facade.dto.UserDto;

import java.util.List;

public interface UserConfigFacade {

    UserDto getCurrentUser(String email);

    List<UserConfigDto> findOrSave(Long userId);

    UserConfigDto create(Long userId, UserConfigDto configDto);

    UserConfigDto update(Long id, Long userId, UserConfigDto configDto);

    void delete(Long id, Long userId);

}
