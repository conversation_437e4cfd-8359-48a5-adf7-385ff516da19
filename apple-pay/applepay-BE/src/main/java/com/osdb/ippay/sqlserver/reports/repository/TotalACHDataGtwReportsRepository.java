package com.osdb.ippay.sqlserver.reports.repository;

import com.osdb.ippay.sqlserver.reports.repository.entity.TotalACHDataGtwReports;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.query.Procedure;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

@Repository
public interface TotalACHDataGtwReportsRepository extends
        JpaRepository<TotalACHDataGtwReports, Long>,
        JpaSpecificationExecutor<TotalACHDataGtwReports> {

	//@Query(value = "CALL Get_Monthly_Total_ACH_Summary_Report(:ipTransactionId, :authStartDateTime, :authEndDateTime);", nativeQuery = true)
	@Procedure(name = "TotalACHDataGtwReports.getTotalACHSummaryByModelEntiy")
	List<TotalACHDataGtwReports> getTotalACHSummaryByModelEntiy(
			@Param("ipTransactionId") Long ipTransactionId,
			@Param("authStartDateTime") String authStartDateTime,
			@Param("authEndDateTime") String authEndDateTime);

}
