package com.osdb.ippay.secondary.reports.facade.mapper;

import com.osdb.ippay.secondary.alerts.facade.dto.SettledTransactionDto;
import com.osdb.ippay.secondary.alerts.repository.entity.SettledTransaction;
import com.osdb.ippay.secondary.reports.facade.dto.CreditCountReportDto;
import com.osdb.ippay.secondary.reports.repository.entity.CreditCountReport;

import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;

@Component
public class CreditCountReportMapper {

    public Page<CreditCountReportDto> toDto(Page<CreditCountReport> creditCountReport) {
        return creditCountReport.map(this::toDto);
    }

    public CreditCountReportDto toDto(CreditCountReport creditCountReportObj) {
       

        return CreditCountReportDto.builder()
        	//	.ipTransId(settledTransaction.getIpTransId())
        		.merchantId(creditCountReportObj.getMerchantId())
                .jetpay_mid(creditCountReportObj.getJetpay_mid())
                .refundAmount(creditCountReportObj.getRefundAmount())
                .refundCount(creditCountReportObj.getRefundCount())
                .build();
    }
}
