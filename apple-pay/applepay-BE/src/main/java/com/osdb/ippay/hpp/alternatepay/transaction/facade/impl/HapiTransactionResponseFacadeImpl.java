package com.osdb.ippay.hpp.alternatepay.transaction.facade.impl;

import com.osdb.ippay.secondary.alternatepay.transaction.controller.Repository.Entity.*;
import com.osdb.ippay.hpp.alternatepay.transaction.repository.entity.HapiTransactionResponse;
import com.osdb.ippay.secondary.alternatepay.payment.controller.dto.PaymentResultDto;
import com.osdb.ippay.hpp.alternatepay.transaction.repository.HapiTransactionResponseRepository;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionSaveDto;
import com.osdb.ippay.hpp.alternatepay.transaction.dto.HapiTransactionDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionGetResultDto;
import com.osdb.ippay.hpp.alternatepay.transaction.facade.HapiTransactionResponseFacade;
import com.osdb.ippay.hpp.alternatepay.transaction.mapper.HapiTransactionResponseMapper;

import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.stereotype.Component;

import static lombok.AccessLevel.PRIVATE;

@Component
@RequiredArgsConstructor
@FieldDefaults(makeFinal = true, level = PRIVATE)
public class HapiTransactionResponseFacadeImpl implements HapiTransactionResponseFacade {
   
    HapiTransactionResponseMapper hapiTransactionResponseMapper;
    HapiTransactionResponseRepository hapiTransactionResponseRepository;


	@Override
	public HapiTransactionDto create(TransactionSaveDto transactionSaveDto) {

		HapiTransactionResponse obj = hapiTransactionResponseMapper.toEntity(transactionSaveDto);
		HapiTransactionResponse objGlobal = hapiTransactionResponseRepository.save(obj);
				
        return hapiTransactionResponseMapper.toDto(objGlobal);	
        
	}


	@Override
	public HapiTransactionDto findByTrnid(String trnid) {

		HapiTransactionResponse trnData = hapiTransactionResponseRepository.findBytransactionID(trnid);
		return hapiTransactionResponseMapper.toDto(trnData);

	}


	@Override
	public HapiTransactionDto toPaymentEntity(TransactionSaveDto transactionSaveDto,
											  PaymentResultDto paymentResultDto, 
											  TransactionGetResultDto paymentGetResultDto,
											  String mid,
											  String refId,
											  String customerId,
										      String terminalId) {

		HapiTransactionResponse obj = hapiTransactionResponseMapper.toPaymentEntity(transactionSaveDto, paymentResultDto, paymentGetResultDto, mid, refId, customerId, terminalId);
		HapiTransactionResponse objGlobal = hapiTransactionResponseRepository.save(obj);
				
        return hapiTransactionResponseMapper.toDto(objGlobal);	
	}
}
