package com.osdb.ippay.secondary.statement.controller;

import com.osdb.ippay.common.logging.LogExecutionTime;
import com.osdb.ippay.common.util.annotation.ApiPageable;
import com.osdb.ippay.secondary.statement.facade.MerchantStatementFacade;
import com.osdb.ippay.secondary.statement.facade.dto.*;
import com.osdb.ippay.secondary.statement.service.filter.MSFilter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;
import java.util.concurrent.Future;

import static lombok.AccessLevel.PRIVATE;

@Tag(name = "merchant-statement")
@RestController
@RequestMapping(value = "/api/v1/private/merchants/{merchantId}")
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class MerchantStatementController {
/*
    MerchantStatementFacade facade;

    @LogExecutionTime
    @ApiPageable
    @GetMapping("/activity-summary")
    public ResponseEntity<List<MSActivitySummaryDto>> getActivitySummary(
            @PathVariable String merchantId,
            @Valid @ParameterObject MSFilter filter) throws Exception {

        List<MSActivitySummaryDto> response = facade.findActivitySummary(merchantId, filter).get();
        return ResponseEntity.ok(response);
    }

    @LogExecutionTime
    @ApiPageable
    @GetMapping("/deposit-details")
    public ResponseEntity<List<MSDepositDetailDto>> getDepositDetails(
            @PathVariable String merchantId,
            @Valid @ParameterObject MSFilter filter) throws Exception {

        List<MSDepositDetailDto> response = facade.findDepositDetails(merchantId, filter).get();
        return ResponseEntity.ok(response);
    }

    @LogExecutionTime
    @ApiPageable
    @GetMapping("/other-card-deposits")
    public ResponseEntity<List<MSOtherCardDepositDto>> getOtherCardDeposits(
            @PathVariable String merchantId,
            @Valid @ParameterObject MSFilter filter) throws Exception {

        List<MSOtherCardDepositDto> response = facade.findOtherCardDeposits(merchantId, filter).get();
        return ResponseEntity.ok(response);
    }

    @LogExecutionTime
    @ApiPageable
    @GetMapping("/processing-details")
    public ResponseEntity<List<MSProcessingDetailDto>> getProcessingDetails(
            @PathVariable String merchantId,
            @Valid @ParameterObject MSFilter filter) throws Exception {

        List<MSProcessingDetailDto> response = facade.findProcessingDetails(merchantId, filter).get();
        return ResponseEntity.ok(response);
    }

    @LogExecutionTime
    @ApiPageable
    @GetMapping("/auth-details")
    public ResponseEntity<List<MSAuthDetailDto>> getAuthDetails(
            @PathVariable String merchantId,
            @Valid @ParameterObject MSFilter filter) throws Exception {

        List<MSAuthDetailDto> response = facade.findAuthDetails(merchantId, filter).get();
        return ResponseEntity.ok(response);
    }

    @LogExecutionTime
    @ApiPageable
    @GetMapping("/other-details")
    public ResponseEntity<List<MSOtherDetailDto>> getOtherDetails(
            @PathVariable String merchantId,
            @Valid @ParameterObject MSFilter filter) throws Exception {

        List<MSOtherDetailDto> response = facade.findOtherDetails(merchantId, filter).get();
        return ResponseEntity.ok(response);
    }

    @LogExecutionTime
    @ApiPageable
    @GetMapping("/total-details")
    public ResponseEntity<List<MSTotalDetailDto>> getTotalDetails(
            @PathVariable String merchantId,
            @Valid @ParameterObject MSFilter filter) throws Exception {

        List<MSTotalDetailDto> response = facade.findTotalDetail(merchantId, filter).get();
        return ResponseEntity.ok(response);
    }

    @LogExecutionTime
    @ApiPageable
    @GetMapping("/cc-merchant-statements")
    public ResponseEntity<CCMerchantStatementDto> getCCMerchantStatements(
            @PathVariable String merchantId,
            @Valid @ParameterObject MSFilter filter) throws Exception {

        Future<List<MSActivitySummaryDto>> activitySummary = facade.findActivitySummary(merchantId, filter);
        Future<List<MSDepositDetailDto>> depositDetails = facade.findDepositDetails(merchantId, filter);
        Future<List<MSOtherCardDepositDto>> otherCardDeposits = facade.findOtherCardDeposits(merchantId, filter);
        Future<List<MSProcessingDetailDto>> processingDetails = facade.findProcessingDetails(merchantId, filter);
        Future<List<MSAuthDetailDto>> authDetails = facade.findAuthDetails(merchantId, filter);
        Future<List<MSOtherDetailDto>> otherDetails = facade.findOtherDetails(merchantId, filter);
        Future<List<MSTotalDetailDto>> totalDetails = facade.findTotalDetail(merchantId, filter);

        return ResponseEntity.ok(
                CCMerchantStatementDto.builder()
                        .activitySummary(activitySummary.get())
                        .depositDetails(depositDetails.get())
                        .otherCardDeposits(otherCardDeposits.get())
                        .processingDetails(processingDetails.get())
                        .authDetails(authDetails.get())
                        .otherDetails(otherDetails.get())
                        .totalDetails(totalDetails.get())
                        .build()
        );
    }*/
}
