package com.osdb.ippay.secondary.partner.controller;

import com.osdb.ippay.common.logging.LogExecutionTime;
import com.osdb.ippay.common.security.service.AuthenticationService;
import com.osdb.ippay.common.util.annotation.ApiPageable;
import com.osdb.ippay.common.util.annotation.ApiSortable;
import com.osdb.ippay.common.util.response.PageResponse;
import com.osdb.ippay.secondary.partner.facade.PartnerFacade;
import com.osdb.ippay.secondary.partner.facade.dto.PartnerDto;
import com.osdb.ippay.secondary.partner.service.filter.PartnerFilter;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static lombok.AccessLevel.PRIVATE;

@Tag(name = "partner")
@RestController
@RequestMapping(value = "/api/v1/private/partners")
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class PartnerController {
/*
    AuthenticationService authenticationService;
    PartnerFacade partnerFacade;

    @LogExecutionTime
    @ApiSortable
    @GetMapping
    public ResponseEntity<List<PartnerDto>> get(@Parameter(hidden = true) Sort sort,
                                                @ParameterObject PartnerFilter filter,
                                                @Parameter(hidden = true) @AuthenticationPrincipal String email) {

        authenticationService.throwIfNotInternal(email);

        List<PartnerDto> response = partnerFacade.find(filter, sort);
        return ResponseEntity.ok(response);
    }

    @LogExecutionTime
    @ApiPageable
    @GetMapping("/pageable")
    public ResponseEntity<PageResponse<PartnerDto>> get(@Parameter(hidden = true) Pageable pageable,
                                                        @ParameterObject PartnerFilter filter,
                                                        @Parameter(hidden = true) @AuthenticationPrincipal String email) {

        authenticationService.throwIfNotInternal(email);

        Page<PartnerDto> response = partnerFacade.find(filter, pageable);
        return ResponseEntity.ok(new PageResponse<>(response.getContent(), response.getTotalElements()));
    }

    @LogExecutionTime
    @GetMapping("/{id}")
    public ResponseEntity<PartnerDto> get(@PathVariable Long id,
                                          @Parameter(hidden = true) @AuthenticationPrincipal String email) {

        authenticationService.throwIfNotInternal(email);

        PartnerDto response = partnerFacade.find(id);
        return ResponseEntity.ok(response);
    }*/
}
