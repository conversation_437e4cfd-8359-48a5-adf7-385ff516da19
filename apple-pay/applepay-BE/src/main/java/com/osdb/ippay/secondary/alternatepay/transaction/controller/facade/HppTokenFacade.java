package com.osdb.ippay.secondary.alternatepay.transaction.controller.facade;

import com.osdb.ippay.secondary.alternatepay.payment.controller.dto.PaymentResultDto;
import com.osdb.ippay.hpp.alternatepay.transaction.dto.HapiTransactionDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.HppTokenDto;


public interface HppTokenFacade {
       
	HppTokenDto create(HapiTransactionDto hapiTransactionResultDto, 
							  PaymentResultDto paymentResultDto);

}