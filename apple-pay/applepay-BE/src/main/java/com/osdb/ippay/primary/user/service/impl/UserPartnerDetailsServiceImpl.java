package com.osdb.ippay.primary.user.service.impl;

import com.osdb.ippay.primary.user.repository.DefaultConfigRepository;
import com.osdb.ippay.primary.user.repository.UserPartnerDetailsRepository;
import com.osdb.ippay.primary.user.repository.entity.DefaultConfig;
import com.osdb.ippay.primary.user.repository.entity.UserPartnerDetails;
import com.osdb.ippay.primary.user.service.DefaultConfigService;
import com.osdb.ippay.primary.user.service.UserPartnerDetailsService;

import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.stereotype.Service;

import java.util.List;

import static lombok.AccessLevel.PRIVATE;

@Service
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class UserPartnerDetailsServiceImpl implements UserPartnerDetailsService {

	UserPartnerDetailsRepository userPartnerDetailsRepository;

	@Override
	public UserPartnerDetails getPartnerDetails(String merchantId) {
		return userPartnerDetailsRepository.getPartnerDetails(merchantId);
	}
}
