package com.osdb.ippay.secondary.reports.facade.mapper;

import com.osdb.ippay.secondary.reports.facade.dto.MonthlyCreditCardReportDto;
import com.osdb.ippay.secondary.reports.repository.entity.MonthlyCreditCardReport;

import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;

@Component
public class MonthlyCreditCardReportMapper {

    public Page<MonthlyCreditCardReportDto> toDto(Page<MonthlyCreditCardReport> monthlyCreditCardReport) {
        return monthlyCreditCardReport.map(this::toDto);
    }

    public MonthlyCreditCardReportDto toDto(MonthlyCreditCardReport monthlyCreditCardReportObj) {
       

        return MonthlyCreditCardReportDto.builder()
        	//	.ipTransId(settledTransaction.getIpTransId())
        		.merchantId(monthlyCreditCardReportObj.getMerchantId())
                .merchantName(monthlyCreditCardReportObj.getMerchantName())
                .amexCreditAmount(monthlyCreditCardReportObj.getAmexCreditAmount())
                .amexSalesAmount(monthlyCreditCardReportObj.getAmexSalesAmount())
                .visaSalesAmount(monthlyCreditCardReportObj.getVisaSalesAmount())
                .visaCreditAmount(monthlyCreditCardReportObj.getVisaCreditAmount())
                .mcSalesAmount(monthlyCreditCardReportObj.getMcSalesAmount())
                .mcCreditAmount(monthlyCreditCardReportObj.getMcCreditAmount())
                .discoverSalesAmount(monthlyCreditCardReportObj.getDiscoverSalesAmount())
                .discoverCreditAmount(monthlyCreditCardReportObj.getDiscoverCreditAmount())
                .totalSales(monthlyCreditCardReportObj.getTotalSales())
                .netSales(monthlyCreditCardReportObj.getNetSales())
                .visaSalesCount(monthlyCreditCardReportObj.getVisaSalesCount())
                .visaCreditCount(monthlyCreditCardReportObj.getVisaCreditCount())
                .amexSalesCount(monthlyCreditCardReportObj.getAmexSalesCount())
                .amexCreditCount(monthlyCreditCardReportObj.getAmexCreditCount())
                .mcSalesCount(monthlyCreditCardReportObj.getMcSalesCount())
                .mcCreditCount(monthlyCreditCardReportObj.getMcCreditCount())
                .discoverSalesCount(monthlyCreditCardReportObj.getDiscoverSalesCount())
                .discoverCreditCount(monthlyCreditCardReportObj.getDiscoverCreditCount())
                .build();
    }
}
