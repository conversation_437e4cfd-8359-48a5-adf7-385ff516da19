package com.osdb.ippay.common.configuration;

import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.env.Environment;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.jdbc.datasource.DriverManagerDataSource;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.persistence.EntityManagerFactory;
import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Objects;

import static lombok.AccessLevel.PRIVATE;

@Configuration
@EnableTransactionManagement
@EnableJpaRepositories(
        entityManagerFactoryRef = "primaryEmFactory",
        transactionManagerRef = "primaryTm",
        basePackages = { "com.osdb.ippay.primary" }
)
@RequiredArgsConstructor
@FieldDefaults(makeFinal = true, level = PRIVATE)
public class PrimaryDataSourceConfig {

    Environment env;

    @Bean(name = "primaryDataSource")
    @Primary
    public DataSource primaryDataSource() {
        DriverManagerDataSource dataSource = new DriverManagerDataSource();

        String driverClassName = env.getProperty("spring.primary-datasource.driver-class-name");

        dataSource.setDriverClassName(Objects.requireNonNull(driverClassName));
        dataSource.setUrl(env.getProperty("spring.primary-datasource.url"));
        dataSource.setUsername(env.getProperty("spring.primary-datasource.username"));
        dataSource.setPassword(env.getProperty("spring.primary-datasource.password"));

        return dataSource;
    }

    @Primary
    @Bean(name = "primaryEmFactory")
    public LocalContainerEntityManagerFactoryBean primaryEmFactory(
            EntityManagerFactoryBuilder builder,
            @Qualifier("primaryDataSource") DataSource dataSource) {

        HashMap<String, Object> properties = new HashMap<>();
        properties.put("hibernate.dialect", "org.hibernate.dialect.MariaDBDialect");

        return builder
                .dataSource(dataSource)
                .packages("com.osdb.ippay.primary")
                .persistenceUnit("primary")
                .properties(properties)
                .build();
    }

    @Primary
    @Bean(name = "primaryTm")
    public PlatformTransactionManager primaryTM(
            @Qualifier("primaryEmFactory") EntityManagerFactory emFactory) {

        return new JpaTransactionManager(emFactory);
    }
}
