package com.osdb.ippay.secondary.merchant.repository;

import com.osdb.ippay.secondary.merchant.repository.entity.Merchant;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.lang.Nullable;

import java.util.List;
import java.util.Optional;

public interface MerchantRepository extends
        JpaRepository<Merchant, Long>,
        JpaSpecificationExecutor<Merchant> {

    @EntityGraph(attributePaths = { "partner", "merchantTerminals", "merchantTerminals.terminal", "bank" })
    @Override
    Page<Merchant> findAll(@Nullable Specification<Merchant> specification, Pageable pageable);

    @EntityGraph(attributePaths = { "partner", "merchantTerminals", "merchantTerminals.terminal", "bank" })
    @Override
    List<Merchant> findAll(@Nullable Specification<Merchant> specification, Sort sort);

    @EntityGraph(attributePaths = { "partner", "merchantTerminals", "merchantTerminals.terminal", "bank" })
    @Override
    List<Merchant> findAll(@Nullable Specification<Merchant> specification);

    @EntityGraph(attributePaths = { "partner", "merchantTerminals", "merchantTerminals.terminal", "bank" })
    List<Merchant> findAllByPartnerId(Long partnerId);

    @EntityGraph(attributePaths = { "partner", "merchantTerminals", "bank" })
    List<Merchant> findAllByMerchantIdIn(List<String> merchantIds);

    @EntityGraph(attributePaths = { "partner", "merchantTerminals", "merchantTerminals.terminal", "bank" })
    Optional<Merchant> findByMerchantId(String merchantId);

    @EntityGraph(attributePaths = { "partner", "merchantTerminals", "merchantTerminals.terminal", "bank" })
    Optional<Merchant> findByAchSetlMID(String merchantId);

    @EntityGraph(attributePaths = { "partner", "merchantTerminals", "merchantTerminals.terminal", "bank" })
    Optional<Merchant> findByCcSetlMID(String merchantId);

    Integer countByPartnerId(Long partnerId);

}
