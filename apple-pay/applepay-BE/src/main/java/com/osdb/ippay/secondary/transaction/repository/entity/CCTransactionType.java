package com.osdb.ippay.secondary.transaction.repository.entity;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum CCTransactionType {

    SALE("SALE"), CREDIT("CREDIT"), VOID("VOID"), CAPT("CAPT"), AUTHONLY("AUTHONLY");

    String value;

    static final Map<String, CCTransactionType> lookup = new HashMap<>();

    static {
        Arrays.stream(CCTransactionType.values())
                .forEach(value -> lookup.put(value.getValue(), value));
    }

    public static CCTransactionType getByValue(String value) {
        return lookup.get(value);
    }
}
