package com.osdb.ippay.email.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import com.osdb.ippay.email.service.ScheduledService;
import com.osdb.ippay.primary.user.repository.entity.UserPartnerDetails;
import com.osdb.ippay.primary.user.service.UserPartnerDetailsService;
import com.osdb.ippay.secondary.alerts.repository.entity.SettledTransaction;
import com.osdb.ippay.secondary.alerts.service.SettledTransactionAlertService;
import com.osdb.ippay.secondary.alerts.service.impl.SettledTransactionAlertServiceImpl;

import lombok.experimental.NonFinal;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.AbstractResource;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.mail.javamail.MimeMessageHelper;

import com.osdb.ippay.email.bean.Message;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.FileReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.PrintWriter;
import java.sql.Date;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.Calendar;
import java.util.HashMap; // Import HashMap
import java.util.List;
import java.util.Map; // Import Map
import java.util.Scanner;
import java.util.regex.Pattern;

@Slf4j
@Component
@EnableScheduling
public class ScheduledService {
    int a = 0;

    @Autowired
    private EmailService emailService; // Inject EmailService
    @Autowired
  	private SettledTransactionAlertService settledTransactionAlertService;
    private LocalDate date = LocalDate.now().minusDays(1);
    boolean isMailSentForToday = false;
    
    @NonFinal
    @Value("${security.jwt.reset-token.expire-length}")
    Long resetPasswordExpiration;
    String scheduledTime = "";
    public static java.util.Date currDate = new java.util.Date();
    ArrayList<String> arrMerchants = new ArrayList<String>();
    
    @Autowired
    UserPartnerDetailsService userPartnerDetailsService;
    
    //@Scheduled(fixedRate = 60000) 
    public void myTask() throws FileNotFoundException, IOException {
        log.info("Scheduled task 'myTask' is running!" + Integer.toString(this.a));
        this.a++;
        Map<String, Object> params = new HashMap<>();
       
        
        LocalDate endDate = LocalDate.now();
        LocalDate startDate = endDate.withDayOfMonth(1);
        
      //  if (this.a % 5 == 0) {
                       
            var fileEntries = new ArrayList<String>();
            String[] HEADERS = {
            		 "MID",
                     "Settlement date",
                     "% (70, 90 or 100) limit hit",
                     "$ Amount Hit Or Item Count",
                     "Overlimit Amount",
                     "Actual Amount"
            };
         		
            final CSVFormat format = CSVFormat.Builder.create()
                    .setHeader(HEADERS)
                    .setIgnoreEmptyLines(true)
                    .build();
            
           
                         List<SettledTransaction> settledTransactionData  = settledTransactionAlertService.generateReport("2010-01-01", "2020-01-01");
                         // List<SettledTransaction> settledTransactionData  = settledTransactionAlertService.generateReport(firstDate.toString(), localDate.toString());
                          	int iNoEmail = 0;
                          	arrMerchants.clear();
                          	 for (SettledTransaction settledTransactionObj : settledTransactionData) {
                          		 
                          		 scheduledTime = settledTransactionObj.getTriggerAlertTime();
                          		 
                                   List<String> data = Arrays.asList(
                                   		settledTransactionObj.getMerchantId(),
                                   		settledTransactionObj.getSettlementDate(),
                                   		settledTransactionObj.getPercentageHit(),
                                   		settledTransactionObj.getAmountHitOrExceeded(),
                                   		settledTransactionObj.getOverlimitAmount(),
                                   		settledTransactionObj.getActualAmount()
                                   );

                                   if(LocalTime.now().isAfter(LocalTime.parse( scheduledTime )))
                                   {
                               			
                                      	 java.util.Date triggerDate = getDateWithoutTimeUsingCalendar();
                                      	 if(!triggerDate.toString().trim().equals(SettledTransactionAlertServiceImpl.currDate.toString()))
                                      	 {
                                      		 
                                      		//log.info("triggerDate - " + triggerDate.toString());
                                      		//log.info("currDate - " + SettledTransactionAlertServiceImpl.currDate.toString().trim());
                                      		
                                   		if(!arrMerchants.contains(settledTransactionObj.getMerchantId()))
                                   		{
                                   			ByteArrayOutputStream out = new ByteArrayOutputStream();
                                            CSVPrinter csvPrinter = new CSVPrinter(new PrintWriter(out), format);
                                            csvPrinter.printRecord(data);
                                            csvPrinter.flush();
                                            csvPrinter.close();
                                            UserPartnerDetails userPartnerDetailsObj = userPartnerDetailsService.getPartnerDetails(settledTransactionObj.getMerchantId());
                                            if(userPartnerDetailsObj != null)
                                            {
                                            	 String partnerEmail = userPartnerDetailsObj.getEmail();
                                                 log.info("Partner Email - " + partnerEmail.toString());
                                            }
                                           
                                            String toEmails = "<EMAIL>";
                                            String reportType = "Monthly Settlement - Dollar amount Exceeded Alert";
                                           /* if(settledTransactionObj.getAlert_to_risk())
                                            {
                                           	 toEmails = "<EMAIL>";
                                            }
                                            if(settledTransactionObj.getAlert_to_support())
                                            {
                                           	 toEmails = "<EMAIL>";
                                            }
                                            */
                                           
                                            //log.info("mail sent");
                                           
                                            Message message = Message.builder()
                                                    .subject(reportType)
                                                    .template("templates/merchant_alert.vm")                      
                                                    .params(params)
                                                    .to(toEmails)
                                                    .build();
                                            
                                            ByteArrayResource attachment = new ByteArrayResource(new ByteArrayInputStream(out.toByteArray()).readAllBytes()); 
                                            String strEncodedAttachment = Base64.getEncoder().encodeToString(out.toByteArray());
                                            
                                          //  emailService.sendEmail(message, strEncodedAttachment);
                                            arrMerchants.add(settledTransactionObj.getMerchantId());
                                            out.flush();
                                            out.close();
                                            iNoEmail++;
                                            
                                   	                  	                          
                                   		}
                                  		 
                                                 
                                   
                               }
                   	 }
                   	 
                 }
                          	 
                          	 //Count Report
                          	settledTransactionData  = settledTransactionAlertService.generateCountReport("2010-01-01", "2020-01-01");
                            // List<SettledTransaction> settledTransactionData  = settledTransactionAlertService.generateReport(firstDate.toString(), localDate.toString());
                             	iNoEmail = 0;
                             	arrMerchants.clear();
                             	 for (SettledTransaction settledTransactionObj : settledTransactionData) {
                             		 
                             		 scheduledTime = settledTransactionObj.getTriggerAlertTime();
                             		 
                                      List<String> data = Arrays.asList(
                                      		settledTransactionObj.getMerchantId(),
                                      		settledTransactionObj.getSettlementDate(),
                                      		settledTransactionObj.getPercentageHit(),
                                      		settledTransactionObj.getAmountHitOrExceeded(),
                                      		settledTransactionObj.getOverlimitAmount(),
                                      		settledTransactionObj.getActualAmount()
                                      );

                                      if(LocalTime.now().isAfter(LocalTime.parse( scheduledTime )))
                                      {
                                  			
                                         	 java.util.Date triggerDate = getDateWithoutTimeUsingCalendar();
                                         	 if(!triggerDate.toString().trim().equals(SettledTransactionAlertServiceImpl.currDate.toString()))
                                         	 {
                                         		 
                                         		//log.info("triggerDate - " + triggerDate.toString());
                                         		//log.info("currDate - " + SettledTransactionAlertServiceImpl.currDate.toString().trim());
                                         		
                                      		if(!arrMerchants.contains(settledTransactionObj.getMerchantId()))
                                      		{
                                      			ByteArrayOutputStream out = new ByteArrayOutputStream();
                                               CSVPrinter csvPrinter = new CSVPrinter(new PrintWriter(out), format);
                                               csvPrinter.printRecord(data);
                                               csvPrinter.flush();
                                               csvPrinter.close();
                                               
                                               UserPartnerDetails userPartnerDetailsObj = userPartnerDetailsService.getPartnerDetails(settledTransactionObj.getMerchantId());
                                               if(userPartnerDetailsObj != null)
                                               {
                                               	 String partnerEmail = userPartnerDetailsObj.getEmail();
                                                    log.info("Partner Email - " + partnerEmail.toString());
                                               }
                                               
                                               String toEmails = "<EMAIL>";
                                               String reportType = "Monthly Settlement - Item count Exceeded Alert";
                                              /* if(settledTransactionObj.getAlert_to_risk())
                                               {
                                              	 toEmails = "<EMAIL>";
                                               }
                                               if(settledTransactionObj.getAlert_to_support())
                                               {
                                              	 toEmails = "<EMAIL>";
                                               }
                                               */
                                              
                                               //log.info("mail sent");
                                              
                                               Message message = Message.builder()
                                                       .subject(reportType)
                                                       .template("templates/merchant_alert.vm")                      
                                                       .params(params)
                                                       .to(toEmails)
                                                       .build();
                                               
                                               ByteArrayResource attachment = new ByteArrayResource(new ByteArrayInputStream(out.toByteArray()).readAllBytes()); 
                                               String strEncodedAttachment = Base64.getEncoder().encodeToString(out.toByteArray());
                                               
                                             //  emailService.sendEmail(message, strEncodedAttachment);
                                               arrMerchants.add(settledTransactionObj.getMerchantId());
                                               out.flush();
                                               out.close();
                                               iNoEmail++;
                                               
                                      	                  	                          
                                      		}
                                     		 
                                                    
                                      
                                  }
                      	 }
                      	 
                    }
                          	 
                          	SettledTransactionAlertServiceImpl.currDate = getDateWithoutTimeUsingCalendar();
                
       
    }
    
    public static java.util.Date getDateWithoutTimeUsingCalendar() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        return calendar.getTime();
    }
    
   
}
