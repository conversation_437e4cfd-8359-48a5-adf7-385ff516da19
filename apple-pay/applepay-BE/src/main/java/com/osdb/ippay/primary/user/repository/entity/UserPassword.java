package com.osdb.ippay.primary.user.repository.entity;

import com.osdb.ippay.common.entity.BaseEntity;
import lombok.*;
import lombok.experimental.FieldDefaults;
import lombok.experimental.SuperBuilder;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import java.time.Instant;

import static lombok.AccessLevel.PRIVATE;

@Entity
@Table(name = "user_password")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = PRIVATE)
@SuperBuilder(toBuilder = true)
public class UserPassword extends BaseEntity {

    @Column(name = "user_id")
    Long userId;

    @Column(name = "password")
    String password;

    @Column(name = "created_date")
    Instant createdDate;

}
