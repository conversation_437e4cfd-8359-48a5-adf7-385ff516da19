package com.osdb.ippay.sqlserver.reports.service.impl;

import com.osdb.ippay.sqlserver.reports.repository.TotalACHDataGtwReportsRepository;
import com.osdb.ippay.sqlserver.reports.repository.TotalACHGtwDataRepository;
import com.osdb.ippay.sqlserver.reports.repository.TotalCardDataGtwReportsRepository;
import com.osdb.ippay.sqlserver.reports.repository.TotalCardGtwDataRepository;
import com.osdb.ippay.sqlserver.reports.repository.entity.TotalACHDataGtwReports;
import com.osdb.ippay.sqlserver.reports.repository.entity.TotalACHGtwData;
import com.osdb.ippay.sqlserver.reports.repository.entity.TotalCardDataGtwReports;
import com.osdb.ippay.sqlserver.reports.repository.entity.TotalCardGtwData;
import com.osdb.ippay.sqlserver.reports.service.TotalCardDataGtwReportsService;
import com.osdb.ippay.sqlserver.reports.service.filter.TotalACHDataGtwReportsFilter;
import com.osdb.ippay.sqlserver.reports.service.filter.TotalACHGtwDataFilter;
import com.osdb.ippay.sqlserver.reports.service.filter.TotalCardDataGtwReportsFilter;
import com.osdb.ippay.sqlserver.reports.service.filter.TotalCardGtwDataFilter;

import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import javax.persistence.EntityManager;
import javax.persistence.criteria.Predicate;

import static lombok.AccessLevel.PRIVATE;

@Service
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class TotalCardDataGtwReportsServiceImpl implements TotalCardDataGtwReportsService {

	TotalCardDataGtwReportsRepository totalCardDataGtwReportsRepository;
	TotalCardGtwDataRepository totalCardGtwDataRepository;
	EntityManager entityManager;
	  
	  @Autowired
	    public TotalCardDataGtwReportsServiceImpl(TotalCardDataGtwReportsRepository totCardDataGtwReportsRepository,
	    		TotalCardGtwDataRepository totCardGtwDataRepository, @Qualifier("sqlserverEmFactory") EntityManager entityManager) {

	       
			this.totalCardDataGtwReportsRepository = totCardDataGtwReportsRepository;
			this.totalCardGtwDataRepository = totCardGtwDataRepository;
	        this.entityManager = entityManager;
	    }
	@Override
	public List<TotalCardDataGtwReports> find(Long ipTransactionId, String authStartDateTime, String authEndDateTime,
			TotalCardDataGtwReportsFilter filter) {

		List<TotalCardDataGtwReports>  totalCardDataGtwSummary= totalCardDataGtwReportsRepository.getTotalCCSummaryByModelEntiy(ipTransactionId, authStartDateTime, authEndDateTime);
		
		return totalCardDataGtwSummary;
	}

	@Override
	public Page<TotalCardDataGtwReports> find(Long ipTransactionId, String authStartDateTime, String authEndDateTime,
			TotalCardDataGtwReportsFilter filter, Pageable pageable) {
		
		 Specification<TotalCardDataGtwReports> specification = filterBy(filter);

	        PageRequest pageRequest = PageRequest.of(
	                pageable.getPageNumber(),
	                pageable.getPageSize(),
	                getSort(pageable)
	        );

	        return totalCardDataGtwReportsRepository.findAll(specification, pageRequest);
	}
	
	private Specification<TotalCardDataGtwReports> filterBy(TotalCardDataGtwReportsFilter filter) {
        return (r, rq, cb) -> {

        	Predicate trnIdPredicate = (filter.getIpTransactionID() != null) ?
          		  cb.greaterThanOrEqualTo(
                            r.get("ipTransactionId"),
                            filter.getIpTransactionID()
                    ) : cb.conjunction();
            
            Predicate fromDatePredicate = StringUtils.isNotBlank(filter.getAuthStartDateTime()) ?
                    cb.greaterThanOrEqualTo(
                            r.get("authDateTime"),
                            filter.getAuthStartDateTime()
                    ) : cb.conjunction();

            Predicate toDatePredicate = StringUtils.isNotBlank(filter.getAuthEndDateTime()) ?
                    cb.lessThanOrEqualTo(
                            r.get("authDateTime"),
                            filter.getAuthEndDateTime()
                    ) : cb.conjunction();

          

            return cb.and(
            		trnIdPredicate,
                    fromDatePredicate,
                    toDatePredicate
            );
        };
    }

	private Specification<TotalCardGtwData> filterBy(TotalCardGtwDataFilter filter) {
        return (r, rq, cb) -> {

            Predicate trnIdPredicate = (filter.getTransactionID() != null) ?
            		  cb.greaterThanOrEqualTo(
                              r.get("transactionId"),
                              filter.getTransactionID()
                      ) : cb.conjunction();
            
            Predicate ipTrnIdPredicate = (filter.getIpTransactionID() != null) ?
          		  cb.greaterThanOrEqualTo(
                            r.get("ipTransactionId"),
                            filter.getIpTransactionID()
                    ) : cb.conjunction();
            
            Predicate fromDatePredicate = StringUtils.isNotBlank(filter.getAuthStartDateTime()) ?
                    cb.greaterThanOrEqualTo(
                            r.get("authDateTime"),
                            filter.getAuthStartDateTime()
                    ) : cb.conjunction();

            Predicate toDatePredicate = StringUtils.isNotBlank(filter.getAuthEndDateTime()) ?
                    cb.lessThanOrEqualTo(
                            r.get("authDateTime"),
                            filter.getAuthEndDateTime()
                    ) : cb.conjunction();

          

            return cb.and(
            		ipTrnIdPredicate,
            		trnIdPredicate,
                    fromDatePredicate,
                    toDatePredicate
            );
        };
    }

	
    private Sort getSort(Pageable pageable) {
        Sort.Order order = pageable.getSort().get()
                .findFirst()
                .orElse(Sort.Order.desc("authDateTime"));

        Sort.Direction direction = order.getDirection();
        String property = order.getProperty();

        return switch (property) {
    
            default -> Sort.by(order);
        };
    }

	@Override
	public List generateReport(Long ipTransactionId, String authStartDateTime, String authEndDateTime) {

		List results =  entityManager.createNamedStoredProcedureQuery("TotalCardDataGtwReports.getTotalCCSummaryByModelEntiy").setParameter("ipTransactionId",ipTransactionId)
				.setParameter("authStartDateTime",authStartDateTime)
				.setParameter("authEndDateTime",authEndDateTime).getResultList();
		
		if (results.isEmpty()) {
	        return new ArrayList<>();
	    }

	    if (results.get(0) instanceof String) {
	        return ((List<String>) results)
	          .stream()
	          .map(s -> new String[] { s })
	          .collect(Collectors.toList());
	    } else {
	        return (List<String[]>) results;
	}
}
	@Override
	public Page<TotalCardGtwData> findData(Long ipTransactionId, String authStartDateTime, String authEndDateTime,
			TotalCardGtwDataFilter filter, Pageable pageable) {

		 Specification<TotalCardGtwData> specification = filterBy(filter);

	        PageRequest pageRequest = PageRequest.of(
	                pageable.getPageNumber(),
	                pageable.getPageSize(),
	                getSort(pageable)
	        );

	        return totalCardGtwDataRepository.findAll(specification, pageRequest);
	}
}
