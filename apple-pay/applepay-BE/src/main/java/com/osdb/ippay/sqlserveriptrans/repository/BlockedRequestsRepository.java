package com.osdb.ippay.sqlserveriptrans.repository;

import com.osdb.ippay.sqlserveriptrans.repository.entity.BlockedRequests;
import com.osdb.ippay.sqlserveriptrans.service.filter.BlockedRequestsFilter;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface BlockedRequestsRepository extends
        JpaRepository<BlockedRequests, Long>,
        JpaSpecificationExecutor<BlockedRequests> {
    
    Page<BlockedRequests> findTop100By(String terminalID, String ipAddress, String tgransactionType, String authStartDateTime, String authEndDateTime,
    		BlockedRequestsFilter filter, Pageable pageable);

	
}
