package com.osdb.ippay.secondary.retokenization.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

import static lombok.AccessLevel.PRIVATE;

@Getter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class RetokenizationDto {

    String state;

    String user;

    @JsonProperty("job_id")
    String jobId;

    @JsonProperty("job_type")
    String jobType;

    @JsonProperty("job_info")
    String jobInfo;

    @JsonProperty("job_description")
    String jobDescription;

    @JsonProperty("job_max_runtime")
    int jobMaxRuntime;

    @JsonProperty("job_result")
    String jobResult;

    @JsonProperty("job_started_at")
    String jobStartedAt;

    @JsonProperty("job_completed_at")
    String jobCompletedAt;

    @JsonProperty("job_error_msg")
    String jobErrorMsg;

}
