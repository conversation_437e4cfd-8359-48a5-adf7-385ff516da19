package  com.osdb.ippay.sqlserver.reports.repository.entity;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import javax.persistence.*;

import com.osdb.ippay.common.entity.BaseEntity;
import com.osdb.ippay.primary.user.repository.entity.UserMerchant;
import com.osdb.ippay.sqlserver.reports.repository.entity.TotalACHDataGtwReports;

import static javax.persistence.GenerationType.IDENTITY;
import static lombok.AccessLevel.PRIVATE;

import java.util.List;


@Entity(name = "MinimumDiscountData")
@Table(name = "MinimumDiscountData")



@NamedNativeQueries({
	@NamedNativeQuery(resultClass = MinimumDiscountData.class, name = "MinimumDiscountData.insertMinimumDiscountData", query = "INSERT INTO MinimumDiscountData (bankId, groupId, associationId, merchantId, merchantDBAName, minDiscount, minDiscountGLCode, minDiscountUserDataCode, startDate, stopDate, billingMethod) VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8, ?9, ?10, ?11)" 
)})



@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)

public class MinimumDiscountData{

	 @Id
	 @Column(name = "Id")
	 @GeneratedValue(strategy = IDENTITY)
	 Long Id;
	 
	 @Column(name = "bankId")
	 String bankId;

	 @Column(name = "groupId")
	 String groupId;

	 @Column(name = "associationId")
	 String associationId;
	 
    @Column(name = "merchantId")
    String merchantId;

    @Column(name = "merchantDBAName")
    String merchantDBAName;

    @Column(name = "minDiscount")
    String minDiscount;

    @Column(name = "minDiscountGLCode")
    String minDiscountGLCode;
    
    @Column(name = "minDiscountUserDataCode")
    String minDiscountUserDataCode;

    @Column(name = "startDate")
    String startDate;

    @Column(name = "stopDate")
    String stopDate;
    
    @Column(name = "billingMethod")
    String billingMethod;

    
    }

