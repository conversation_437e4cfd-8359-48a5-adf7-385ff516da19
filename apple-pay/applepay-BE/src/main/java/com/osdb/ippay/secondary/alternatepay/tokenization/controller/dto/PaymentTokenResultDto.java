package com.osdb.ippay.secondary.alternatepay.tokenization.controller.dto;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;
import lombok.experimental.FieldDefaults;

import static lombok.AccessLevel.PRIVATE;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class PaymentTokenResultDto {

    
    String cardNumber;

    String cardExpiration;

    String securityCode;
    
    String uid;

    String token;




}
