package com.osdb.ippay.secondary.reports.repository;

import com.osdb.ippay.secondary.reports.repository.entity.CreditCountReport;


import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CreditCountReportRepository extends
        JpaRepository<CreditCountReport, Long>,
        JpaSpecificationExecutor<CreditCountReport> {

    List<CreditCountReport> getCreditCountReport(String fromDate, String toDate);
    
	
}
