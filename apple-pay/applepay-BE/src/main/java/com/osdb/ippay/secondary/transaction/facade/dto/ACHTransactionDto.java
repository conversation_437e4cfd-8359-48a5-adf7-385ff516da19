package com.osdb.ippay.secondary.transaction.facade.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.time.Instant;

import static lombok.AccessLevel.PRIVATE;

@Getter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class ACHTransactionDto {

    Long id;

    String terminalId;

    String merchantId;

    String merchantName;

    String transactionId;

    String transactionType;

    String accountNumber;

    String cardHolder;

    String address;

    String city;

    String state;

    String zipCode;

    String country;

    String phone;

    String email;

    String status;

    String type;

    String amount;

    Long amountCents;

    String ud1;

    String ud2;

    String ud3;

    String authCode;

    Instant authDate;

    Instant settlementDate;

    String achProcessor;

    String requestType;

    String currencyCode;

    String orderNumber;

    String checkNumber;

    String invoiceNumber;

    String routing;

    String responseCode;

    String responseText;

    String ipAddress;

    String token;

    RefundVoidBtn refundVoidBtn;

    String fundingDisposition;

}
