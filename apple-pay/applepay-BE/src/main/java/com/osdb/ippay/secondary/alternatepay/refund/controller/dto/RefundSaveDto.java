package com.osdb.ippay.secondary.alternatepay.refund.controller.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import javax.persistence.*;

import static lombok.AccessLevel.PRIVATE;


@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class RefundSaveDto {

    @Id
    @GeneratedValue
    Long RefundId;

    String refundConfirmation;

    String dateApproval;

    String result;

    int amount;

    String providerId;

    String approvedBy;

    String refundBy;

    String TransactionNumber;

    String date;

    int transactionAmount;

    int status;

    String currency;

    String businessId;

    String confirmationNumber;

    int type;
}
