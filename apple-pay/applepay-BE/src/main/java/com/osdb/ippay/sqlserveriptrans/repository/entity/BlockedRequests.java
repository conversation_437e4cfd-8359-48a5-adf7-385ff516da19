package com.osdb.ippay.sqlserveriptrans.repository.entity;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import javax.persistence.*;

import com.osdb.ippay.secondary.alerts.repository.entity.SettledTransaction;

import static lombok.AccessLevel.PRIVATE;

@Entity(name = "BlockedRequests")
@Table(name = "BlockedRequests")

/*
@NamedNativeQueries({
	@NamedNativeQuery(resultClass = BlockedRequests.class, name = "BlockedRequests.getBlockedTransactions", query = "SELECT TOP (200) ID as id, TerminalID as terminalID, TransactionType as transactionType, IPAddress as ipAddress, CardName as cardName, TotalAmount as totalAmount, TransactionID as transactionID, Date as date, Xml as xml FROM BlockedRequests WHERE Date >= ?1 AND Date <= ?2 AND TerminalID = ?3 AND IPAddress = ?4 and TransactionType = ?5"
)})
*/

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
public class BlockedRequests {

	@Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "ID")
    public Long id;

	@Column(name = "TerminalID", nullable = false)
	public String terminalId;

	 
    @Column(name = "IPAddress", nullable = false)
    public String ipAddress;

    @Column(name = "CardName")
    public String cardName;

    @Column(name = "TransactionType", nullable = false)
    public String transactionType;

    @Column(name = "TotalAmount")
    public String totalAmount;
    
    @Column(name = "TransactionID")
    public String transactionID;

    @Column(name = "Date", nullable = false)
    public java.util.Date date;
    
    @Column(name = "Xml", nullable = false)
    public String xml;
    
    }

