package com.osdb.ippay.primary.user.repository;

import com.osdb.ippay.primary.user.repository.entity.UserConfig;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.Optional;

public interface UserConfigRepository extends JpaRepository<UserConfig, Long> {

    List<UserConfig> findAllByUserId(Long userId);

    Optional<UserConfig> findByIdAndUserId(Long id, Long userId);

    void deleteByIdAndUserId(Long id, Long userId);

}
