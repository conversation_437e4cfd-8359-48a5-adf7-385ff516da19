package com.osdb.ippay.common.user.facade.mapper;

import com.osdb.ippay.common.user.facade.dto.EditUserDto;
import com.osdb.ippay.common.user.facade.dto.UserDto;
import com.osdb.ippay.primary.user.repository.entity.User;
import com.osdb.ippay.primary.user.repository.entity.UserMerchant;
import com.osdb.ippay.primary.user.repository.entity.UserStatus;
import com.osdb.ippay.secondary.merchant.service.MerchantService;
import com.osdb.ippay.secondary.partner.facade.mapper.PartnerMapper;
import com.osdb.ippay.secondary.partner.repository.entity.Partner;
import com.osdb.ippay.secondary.partner.service.PartnerService;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static lombok.AccessLevel.PRIVATE;

@Component
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class UserMapper {

    // services
    PartnerService partnerService;
    MerchantService merchantService;

    // mappers
    UserMerchantMapper userMerchantMapper;
    PartnerMapper partnerMapper;

    public Page<UserDto> toDto(Page<User> users) {
        List<Long> partnerIds = users.map(User::getPartnerId).stream().collect(Collectors.toList());
        Page<UserDto> userDtos = users.map(this::toDto);

        if(CollectionUtils.isEmpty(partnerIds)) {
            return userDtos;
        }

        List<Partner> partners = partnerService.find(partnerIds);

        userDtos.forEach(user -> {
            if(user.getPartnerId() != null) {
                partners.stream()
                        .filter(pOpt -> user.getPartnerId().equals(pOpt.getId()))
                        .findFirst()
                        .ifPresent(p -> {
                            user.setPartner(partnerMapper.toDto(p));
                            user.setMerchantsAssigned(merchantService.countBy(user.getPartnerId()));
                        });
            }
        });

        return userDtos;
    }

    public UserDto toDto(User user) {
        List<UserMerchant> merchants = Optional
                .ofNullable(user.getMerchants())
                .orElse(Collections.emptyList());

        return UserDto.builder()
                .id(user.getId())
                .firstName(user.getFirstName())
                .lastName(user.getLastName())
                .email(user.getEmail())
                .merchantsAssigned(merchants.size())
                .merchants(userMerchantMapper.toDto(merchants))
                .partnerId(user.getPartnerId())
                .receiveChargebacksNotices(user.getReceiveChargebacksNotices())
                .receiveDailySettlement(user.getReceiveDailySettlement())
                .receiveEcheckRejectNotices(user.getReceiveEcheckRejectNotices())
                .receiveMonthlySettlement(user.getReceiveMonthlySettlement())
                .role(user.getRole())
                .status(user.getStatus())
                .lastLogin(user.getLastLogin())
                .lastSessionIpAddress(user.getLastSessionIpAddress())
                .totalFailedLogins(Optional.ofNullable(user.getTotalFailedLogins()).orElse(0))
                .build();
    }

    public User toEntity(UserDto userDto) {
        return User.builder()
                .firstName(userDto.getFirstName())
                .lastName(userDto.getLastName())
                .email(userDto.getEmail())
                .role(userDto.getRole())
                .status(UserStatus.INVITED)
                .receiveChargebacksNotices(userDto.getReceiveChargebacksNotices())
                .receiveDailySettlement(userDto.getReceiveDailySettlement())
                .receiveEcheckRejectNotices(userDto.getReceiveEcheckRejectNotices())
                .receiveMonthlySettlement(userDto.getReceiveMonthlySettlement())
                .totalFailedLogins(0)
                .passwordLastChanged(Instant.now())
                .partnerId(userDto.getPartnerId())
                .build();
    }

    public User putEntity(EditUserDto userDto, User existedUser) {
        return User.builder()
                .id(existedUser.getId())
                .firstName(userDto.getFirstName())
                .lastName(userDto.getLastName())
                .email(userDto.getEmail())
                .password(existedUser.getPassword())
                .role(userDto.getRole())
                .status(existedUser.getStatus())
                .merchants(existedUser.getMerchants())
                .receiveChargebacksNotices(userDto.getReceiveChargebacksNotices())
                .receiveDailySettlement(userDto.getReceiveDailySettlement())
                .receiveEcheckRejectNotices(userDto.getReceiveEcheckRejectNotices())
                .receiveMonthlySettlement(userDto.getReceiveMonthlySettlement())
                .lastLogin(existedUser.getLastLogin())
                .lastSessionIpAddress(existedUser.getLastSessionIpAddress())
                .totalFailedLogins(existedUser.getTotalFailedLogins())
                .passwordLastChanged(existedUser.getPasswordLastChanged())
                .partnerId(userDto.getPartnerId())
                .resetPasswordHash(existedUser.getResetPasswordHash())
                .sessionsHash(existedUser.getSessionsHash())
                .passwordLastChanged(existedUser.getPasswordLastChanged())
                .build();
    }

    public User patchEntity(EditUserDto userDto, User existedUser) {
        return User.builder()
                .id(existedUser.getId())
                .firstName(Optional.ofNullable(userDto.getFirstName()).orElse(existedUser.getFirstName()))
                .lastName(Optional.ofNullable(userDto.getLastName()).orElse(existedUser.getLastName()))
                .email(userDto.getEmail())
                .password(existedUser.getPassword())
                .role(Optional.ofNullable(userDto.getRole()).orElse(existedUser.getRole()))
                .status(existedUser.getStatus())
                .merchants(existedUser.getMerchants())
                .receiveChargebacksNotices(Optional.ofNullable(userDto.getReceiveChargebacksNotices()).orElse(existedUser.getReceiveChargebacksNotices()))
                .receiveDailySettlement(Optional.ofNullable(userDto.getReceiveDailySettlement()).orElse(existedUser.getReceiveDailySettlement()))
                .receiveEcheckRejectNotices(Optional.ofNullable(userDto.getReceiveEcheckRejectNotices()).orElse(existedUser.getReceiveEcheckRejectNotices()))
                .receiveMonthlySettlement(Optional.ofNullable(userDto.getReceiveMonthlySettlement()).orElse(existedUser.getReceiveMonthlySettlement()))
                .lastLogin(existedUser.getLastLogin())
                .lastSessionIpAddress(existedUser.getLastSessionIpAddress())
                .totalFailedLogins(existedUser.getTotalFailedLogins())
                .passwordLastChanged(existedUser.getPasswordLastChanged())
                .partnerId(userDto.getPartnerId())
                .resetPasswordHash(existedUser.getResetPasswordHash())
                .sessionsHash(existedUser.getSessionsHash())
                .passwordLastChanged(existedUser.getPasswordLastChanged())
                .build();
    }
}
