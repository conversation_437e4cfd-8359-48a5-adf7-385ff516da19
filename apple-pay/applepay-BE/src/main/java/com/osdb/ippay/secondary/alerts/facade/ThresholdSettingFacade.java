package com.osdb.ippay.secondary.alerts.facade;

import com.osdb.ippay.secondary.alerts.facade.dto.ThresholdSettingDto;
import com.osdb.ippay.secondary.alerts.repository.entity.AlertType;


public interface ThresholdSettingFacade {

    ThresholdSettingDto find(AlertType alertType);
    
    ThresholdSettingDto findByMid(AlertType alertType, String mid);
    
    ThresholdSettingDto create(AlertType alertType, ThresholdSettingDto thresholdSettingDto);

    ThresholdSettingDto update(AlertType alertType, ThresholdSettingDto thresholdSettingDto, String mid);
}