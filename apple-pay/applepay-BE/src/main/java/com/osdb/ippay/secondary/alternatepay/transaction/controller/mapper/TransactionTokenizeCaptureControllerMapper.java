package com.osdb.ippay.secondary.alternatepay.transaction.controller.mapper;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.*;

import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import static lombok.AccessLevel.PRIVATE;

@Component
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class TransactionTokenizeCaptureControllerMapper {

    @NonFinal
    @Value("${tokenization.api-key}")
    String apiKey;
    
    @NonFinal
    @Value("${tokenization.branch-code}")
    String branchCode;

    public TransactionTokenizeCaptureDto toDto(TransactionTokenizeCaptureDto transactionInitDto) {
        return TransactionTokenizeCaptureDto.builder()
                .branchId(branchCode)
                .apiKey(apiKey)
                .amount(transactionInitDto.getAmount())
                .currency(transactionInitDto.getCurrency())
                .callbackURL(transactionInitDto.getCallbackURL())
                .isCent(transactionInitDto.isCent())              
                .timeOut(transactionInitDto.getTimeOut())
                .methodType(transactionInitDto.getMethodType())
                .build();
    }

  
    public TrasactionInitResultDto toOutDto(TrasactionInitResultDto trnInitResultDto) {
        return TrasactionInitResultDto.builder()
                .amount(trnInitResultDto.getAmount())
                .currency(trnInitResultDto.getCurrency())
                .date(trnInitResultDto.getDate())
              //  .maxInstallments(trnInitResultDto.getMaxInstallments())
                .methodType(trnInitResultDto.getMethodType())
                .reason(trnInitResultDto.getReason())
                .status(trnInitResultDto.getStatus())
                .statusName(trnInitResultDto.getStatusName())
                .timeoutDate(trnInitResultDto.getTimeoutDate())
                .TransactionNumber(trnInitResultDto.getTransactionNumber())
                .transactionPage(trnInitResultDto.getTransactionPage())
                .paymentType("Apple Pay")
                
                .build();
    }


}
