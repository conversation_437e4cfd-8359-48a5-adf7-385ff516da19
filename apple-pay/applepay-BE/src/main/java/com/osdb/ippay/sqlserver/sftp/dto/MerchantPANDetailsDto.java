package com.osdb.ippay.sqlserver.sftp.dto;

import lombok.*;
import lombok.experimental.FieldDefaults;

import static lombok.AccessLevel.PRIVATE;


@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class MerchantPANDetailsDto {

   String Merchant_ID;

   String Merchant_Name;
	
   String Terminal_ID;
	 
   String Transaction_ID;

   String Request_Type;

   String TotalAmount;

   String FeeAmount;

   String Tax_Amount;

   String Auth_Code;

   String AuthDateTime;

   String CardNum;

   String Token;

   String CVV2;

   String CardHolderName;

   String Address;

   String City;

   String State;

   String Zip;

   String Country;

   String Phone;

   String Email;

   String TokenSourceValue;

   String TokenTypeCode;

   String ExpMonth;

   String ExpYear;

}
