package com.osdb.ippay.common.util.date;

import com.osdb.ippay.common.exception.business.ParseException;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;

public class DateUtil {

    private DateUtil() {}

    public static Instant parseToInstant(String dateToParse) {
        try {
            return dateToParse != null ?
                    LocalDateTime.parse(
                            dateToParse.replaceAll("\\..*$", ""),
                            DateTimeFormatter.ofPattern( "uuuu-MM-dd HH:mm:ss" ))
                            .atZone(ZoneId.of("US/Central"))
                            .toInstant() :
                    null;

        } catch (Exception ex) {
            throw new ParseException(ex.getMessage());
        }
    }

    public static boolean isTheSameDayZoned(LocalDateTime date1, LocalDateTime date2) {
        boolean isTheSameYear = date1.getYear() == date2.getYear();
        boolean isTheSameMonth = date1.getMonth().equals(date2.getMonth());
        boolean isTheSameDay = date1.getDayOfMonth() == date2.getDayOfMonth();

        return isTheSameDay && isTheSameMonth && isTheSameYear;
    }

    public static boolean isTheSameDayInstant(Instant date1, Instant date2) {
        ZonedDateTime zonedDateTime1 = date1.atZone(ZoneId.of("US/Central"));
        ZonedDateTime zonedDateTime2 = date2.atZone(ZoneId.of("US/Central"));

        boolean isTheSameYear = zonedDateTime1.getYear() == zonedDateTime2.getYear();
        boolean isTheSameMonth = zonedDateTime1.getMonth().equals(zonedDateTime2.getMonth());
        boolean isTheSameDay = zonedDateTime1.getDayOfMonth() == zonedDateTime2.getDayOfMonth();

        return isTheSameDay && isTheSameMonth && isTheSameYear;
    }
}
