package com.osdb.ippay.common.configuration;

import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.jdbc.datasource.DriverManagerDataSource;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.persistence.EntityManagerFactory;
import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Objects;

import static lombok.AccessLevel.PRIVATE;

@Configuration
@EnableTransactionManagement
@EnableJpaRepositories(
        entityManagerFactoryRef = "secondaryEmFactory",
        transactionManagerRef = "secondaryTm",
        basePackages = { "com.osdb.ippay.secondary" }
)
@RequiredArgsConstructor
@FieldDefaults(makeFinal = true, level = PRIVATE)
public class SecondaryDataSourceConfig {

    Environment env;

    @Bean(name = "secondaryDataSource")
    public DataSource secondaryDataSource() {
        DriverManagerDataSource dataSource = new DriverManagerDataSource();

        String driverClassName = env.getProperty("spring.secondary-datasource.driver-class-name");

        dataSource.setDriverClassName(Objects.requireNonNull(driverClassName));
        dataSource.setUrl(env.getProperty("spring.secondary-datasource.url"));
        dataSource.setUsername(env.getProperty("spring.secondary-datasource.username"));
        dataSource.setPassword(env.getProperty("spring.secondary-datasource.password"));

        return dataSource;
    }

    @Bean(name = "secondaryEmFactory")
    public LocalContainerEntityManagerFactoryBean secondaryEmFactory(
            EntityManagerFactoryBuilder builder,
            @Qualifier("secondaryDataSource") DataSource dataSource) {

        HashMap<String, Object> properties = new HashMap<>();
        properties.put("hibernate.dialect", "org.hibernate.dialect.MySQLDialect");

        return builder
                .dataSource(dataSource)
                .packages("com.osdb.ippay.secondary")
                .persistenceUnit("secondary")
                .properties(properties)
                .build();
    }

    @Bean(name = "secondaryTm")
    public PlatformTransactionManager secondaryTM(
            @Qualifier("secondaryEmFactory") EntityManagerFactory emFactory) {

        return new JpaTransactionManager(emFactory);
    }
}
