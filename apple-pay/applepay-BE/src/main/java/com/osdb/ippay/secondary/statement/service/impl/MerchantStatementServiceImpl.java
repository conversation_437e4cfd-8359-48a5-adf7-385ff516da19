package com.osdb.ippay.secondary.statement.service.impl;

import com.osdb.ippay.secondary.statement.repository.*;
import com.osdb.ippay.secondary.statement.repository.entity.*;
import com.osdb.ippay.secondary.statement.service.MerchantStatementService;
import com.osdb.ippay.secondary.statement.service.filter.MSFilter;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.Predicate;
import java.util.List;

import static lombok.AccessLevel.PRIVATE;

@Service
@RequiredArgsConstructor
@FieldDefaults(makeFinal = true, level = PRIVATE)
public class MerchantStatementServiceImpl implements MerchantStatementService {

    MSActivitySummaryRepository activitySummaryRepository;
    MSDepositDetailRepository depositDetailRepository;
    MSOtherCardDepositRepository cardDepositRepository;
    MSProcessingDetailRepository processingDetailRepository;
    MSAuthDetailRepository authDetailRepository;
    MSOtherDetailRepository otherDetailRepository;
    MSTotalDetailRepository totalDetailRepository;

    @Override
    public List<MSActivitySummary> findActivitySummary(String merchantId, MSFilter msFilter) {
        return activitySummaryRepository.findAll(
                (r, rq, cb) -> {
                    Predicate merchantIdPredicate = cb.equal(r.get("merchantId"), merchantId);
                    return cb.and(merchantIdPredicate);
                }
        );
    }

    @Override
    public List<MSDepositDetail> findDepositDetails(String merchantId, MSFilter msFilter) {
        return depositDetailRepository.findAll(
                (r, rq, cb) -> {
                    Predicate merchantIdPredicate = cb.equal(r.get("merchantId"), merchantId);
                    return cb.and(merchantIdPredicate);
                }
        );
    }

    @Override
    public List<MSOtherCardDeposit> findOtherCardDeposits(String merchantId, MSFilter msFilter) {
        return cardDepositRepository.findAll(
                (r, rq, cb) -> {
                    Predicate merchantIdPredicate = cb.equal(r.get("merchantId"), merchantId);
                    return cb.and(merchantIdPredicate);
                }
        );
    }

    @Override
    public List<MSProcessingDetail> findProcessingDetail(String merchantId, MSFilter msFilter) {
        return processingDetailRepository.findAll(
                (r, rq, cb) -> {
                    Predicate merchantIdPredicate = cb.equal(r.get("merchantId"), merchantId);
                    return cb.and(merchantIdPredicate);
                }
        );
    }

    @Override
    public List<MSAuthDetail> findAuthDetails(String merchantId, MSFilter msFilter) {
        return authDetailRepository.findAll(
                (r, rq, cb) -> {
                    Predicate merchantIdPredicate = cb.equal(r.get("merchantId"), merchantId);
                    return cb.and(merchantIdPredicate);
                }
        );
    }

    @Override
    public List<MSOtherDetail> findOtherDetails(String merchantId, MSFilter msFilter) {
        return otherDetailRepository.findAll(
                (r, rq, cb) -> {
                    Predicate merchantIdPredicate = cb.equal(r.get("merchantId"), merchantId);
                    return cb.and(merchantIdPredicate);
                }
        );
    }

    @Override
    public List<MSTotalDetail> findTotalDetail(String merchantId, MSFilter msFilter) {
        return totalDetailRepository.findAll(
                (r, rq, cb) -> {
                    Predicate merchantIdPredicate = cb.equal(r.get("merchantId"), merchantId);
                    return cb.and(merchantIdPredicate);
                }
        );
    }
}
