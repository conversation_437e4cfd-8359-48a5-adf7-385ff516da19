package com.osdb.ippay.common.merchant.facade.dto;

import com.osdb.ippay.secondary.partner.facade.dto.PartnerDto;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.List;

import static lombok.AccessLevel.PRIVATE;

@Getter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class MerchantDto {

    String merchantId;

    String ccMerchantId;

    String achMerchantId;

    String merchantName;

    String jetpayMID;

    String achMID;

    String merchantDesc;

    Boolean isActive;

    PartnerDto partner;

    BankDto bank;

    List<String> tIds;

    @Setter
    Long usersNumber;

}
