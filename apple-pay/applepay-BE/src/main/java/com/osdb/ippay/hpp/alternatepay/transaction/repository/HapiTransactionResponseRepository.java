package com.osdb.ippay.hpp.alternatepay.transaction.repository;

import com.osdb.ippay.hpp.alternatepay.transaction.repository.entity.HapiTransactionResponse;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

@Repository
public interface HapiTransactionResponseRepository extends
        JpaRepository<HapiTransactionResponse, Long>,
        JpaSpecificationExecutor<HapiTransactionResponse> {
    
	HapiTransactionResponse findBytransactionID(String transactionId);

}
