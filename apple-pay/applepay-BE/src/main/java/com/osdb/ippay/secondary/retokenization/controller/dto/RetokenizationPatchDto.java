package com.osdb.ippay.secondary.retokenization.controller.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

import static lombok.AccessLevel.PRIVATE;

@Getter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class RetokenizationPatchDto {

    String user;

    String jobType;

    JobInfoPatchDto jobInfo;

    String jobDescription;

}
