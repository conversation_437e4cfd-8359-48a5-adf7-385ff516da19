package com.osdb.ippay.common.user.facade.impl;

import com.osdb.ippay.common.exception.business.AlreadyExistsException;
import com.osdb.ippay.common.exception.business.BusinessException;
import com.osdb.ippay.common.security.provider.JwtProvider;
import com.osdb.ippay.common.user.facade.CommonUserFacade;
import com.osdb.ippay.common.user.facade.dto.EditUserDto;
import com.osdb.ippay.common.user.facade.dto.UserDto;
import com.osdb.ippay.common.user.facade.mapper.UserMapper;
import com.osdb.ippay.email.bean.Message;
import com.osdb.ippay.email.service.EmailService;
import com.osdb.ippay.primary.user.repository.entity.User;
import com.osdb.ippay.primary.user.repository.entity.UserStatus;
import com.osdb.ippay.primary.user.service.UserMerchantService;
import com.osdb.ippay.primary.user.service.UserService;
import com.osdb.ippay.primary.user.service.filter.UserFilter;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.Map;

import static com.osdb.ippay.common.exception.ErrorMessage.EMAIL_ALREADY_EXISTS;
import static com.osdb.ippay.common.exception.ErrorMessage.RESEND_INVITATION;
import static com.osdb.ippay.primary.user.repository.entity.UserStatus.INVITED;
import static lombok.AccessLevel.PRIVATE;

@Component
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class CommonUserFacadeImpl implements CommonUserFacade {

    // services
    UserService userService;
    EmailService emailService;
    UserMerchantService userMerchantService;
    JwtProvider jwtProvider;

    // mappers
    UserMapper userMapper;

    @NonFinal
    @Value(value = "${mail.specific.template.invitation}")
    String userInvitationTemplate;

    @NonFinal
    @Value(value = "${mail.specific.template.invitation.signup-url}")
    String invitationSignUpURL;

    @NonFinal
    @Value("${security.jwt.reset-token.expire-length}")
    Long resetPasswordExpiration;

    @Override
    public Page<UserDto> find(UserFilter userFilter, Pageable pageable) {
        Page<User> users = userService.find(userFilter, pageable);
        return userMapper.toDto(users);
    }

    @Override
    public UserDto find(Long id) {
        User user = userService.find(id);
        return userMapper.toDto(user);
    }

    @Override
    public UserDto create(UserDto userDto) {
        try {
            User toSave = userMapper.toEntity(userDto);
            User saved = userService.save(toSave);

            String token = jwtProvider.createResetToken(saved);
            sendInvitationEmail(saved, token);

            return userMapper.toDto(saved);

        } catch (DataIntegrityViolationException ex) {
            throw new AlreadyExistsException(EMAIL_ALREADY_EXISTS);
        }
    }

    @Override
    public UserDto put(Long id, EditUserDto userDto) {
        User existedUser = userService.find(id);

        User toUpdate = userMapper.putEntity(userDto, existedUser);

        if(ObjectUtils.notEqual(userDto.getEmail(), existedUser.getEmail())) {
            toUpdate.setPassword(null);
            toUpdate.setPasswordLastChanged(null);
            toUpdate.setStatus(INVITED);

            String token = jwtProvider.createResetToken(toUpdate);
            sendInvitationEmail(toUpdate, token);
        }

        User updated = userService.save(toUpdate);
        return userMapper.toDto(updated);
    }

    @Transactional
    @Override
    public UserDto patch(Long id, EditUserDto userDto) {
        User existedUser = userService.find(id);

        userMerchantService.deleteBy(id);

        User toUpdate = userMapper.patchEntity(userDto, existedUser);
        User updated = userService.save(toUpdate);

        return userMapper.toDto(updated);
    }

    @Override
    public void resendInvitation(Long id) {
        User user = userService.find(id);

        throwIfNotInvited(user);

        String token = jwtProvider.createResetToken(user);
        sendInvitationEmail(user, token);
    }

    private void sendInvitationEmail(User user, String token) {
        Map<String, Object> params = new HashMap<>();
        params.put("invitationSignUpURL", invitationSignUpURL);
        params.put("token", token);
        params.put("username", user.getEmail());
        params.put("fpExpired", resetPasswordExpiration / 60 / 1000 / 60);

        Message message = Message.builder()
                .subject("You have been invited to use IPReporting")
                .to(user.getEmail())
                .template(userInvitationTemplate)
                .params(params)
                .build();

        emailService.sendEmail(message);
    }

    private void throwIfNotInvited(User user) {
        UserStatus userStatus = user.getStatus();

        if(ObjectUtils.notEqual(userStatus, INVITED)) {
            throw new BusinessException(RESEND_INVITATION);
        }
    }
}
