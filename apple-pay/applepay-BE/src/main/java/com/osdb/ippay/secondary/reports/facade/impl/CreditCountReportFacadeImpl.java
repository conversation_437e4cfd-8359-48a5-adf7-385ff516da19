package com.osdb.ippay.secondary.reports.facade.impl;

import com.osdb.ippay.common.domain.FileDomain;
import com.osdb.ippay.primary.user.repository.entity.User;
import com.osdb.ippay.secondary.alerts.facade.SettledTransactionFacade;
import com.osdb.ippay.secondary.alerts.facade.dto.SettledTransactionDto;
//import com.osdb.ippay.secondary.alerts.facade.mapper.SettledTransactionMapper;
import com.osdb.ippay.secondary.alerts.repository.entity.SettledTransaction;
import com.osdb.ippay.secondary.alerts.service.SettledTransactionAlertService;
import com.osdb.ippay.secondary.alerts.service.filter.SettledTransactionFilter;
import com.osdb.ippay.secondary.chargeback.facade.dto.ChargebackDto;
import com.osdb.ippay.secondary.chargeback.repository.entity.Chargeback;
import com.osdb.ippay.secondary.chargeback.service.ChargebackFilter;
import com.osdb.ippay.secondary.reports.facade.CreditCountReportFacade;
import com.osdb.ippay.secondary.reports.facade.dto.CreditCountReportDto;
import com.osdb.ippay.secondary.reports.facade.mapper.CreditCountReportMapper;
import com.osdb.ippay.secondary.reports.repository.entity.CreditCountReport;
import com.osdb.ippay.secondary.reports.service.CreditCountReportService;
import com.osdb.ippay.secondary.reports.service.filter.CreditCountReportFilter;
import com.osdb.ippay.sqlserver.reports.facade.TotalACHDataGtwReportsFacade;
import com.osdb.ippay.sqlserver.reports.facade.dto.TotalACHDataGtwReportsDto;
import com.osdb.ippay.sqlserver.reports.facade.dto.TotalACHGtwDataDto;
import com.osdb.ippay.sqlserver.reports.facade.mapper.TotalACHDataGtwReportsMapper;
import com.osdb.ippay.sqlserver.reports.facade.mapper.TotalACHGtwDataMapper;
import com.osdb.ippay.sqlserver.reports.repository.entity.TotalACHDataGtwReports;
import com.osdb.ippay.sqlserver.reports.repository.entity.TotalACHGtwData;
import com.osdb.ippay.sqlserver.reports.service.TotalACHDataGtwReportsService;
import com.osdb.ippay.sqlserver.reports.service.filter.TotalACHDataGtwReportsFilter;
import com.osdb.ippay.sqlserver.reports.service.filter.TotalACHGtwDataFilter;

import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.ListIterator;
import java.util.Map;

import static lombok.AccessLevel.PRIVATE;

@Component
@RequiredArgsConstructor
@FieldDefaults(makeFinal = true, level = PRIVATE)
public class CreditCountReportFacadeImpl implements CreditCountReportFacade {

    // services
	CreditCountReportService creditCountReportService;
	CreditCountReportMapper creditCountReportMapper;
	
	@Override
	public FileDomain export(String startDateTime, String endDateTime, CreditCountReportFilter filter)
			throws IOException {

		String[] HEADERS = {
                "Row Labels",
                "Sum Of # of Refunds",
                "Sum Of $ of Refunds"
        };

        final CSVFormat format = CSVFormat.Builder.create()
                .setHeader(HEADERS)
                .setIgnoreEmptyLines(true)
                .build();
        
        List<CreditCountReport> creditCountReportData  = creditCountReportService.generateReport(startDateTime, endDateTime);
        for (int i = 0; i < creditCountReportData.size(); i++) {

            // Print all elements of List
            //System.out.println(settledTransactionData.get(i).toString());
        }
        try (
                ByteArrayOutputStream out = new ByteArrayOutputStream();
                CSVPrinter csvPrinter = new CSVPrinter(new PrintWriter(out), format)
        ) {
        	//List<String> result = new ArrayList<>(totalACHDataGtwReports.size());
        	//ListIterator<String> it = totalACHDataGtwReports.listIterator(totalACHDataGtwReports.size());
        	
       //  for(Object totalACHDataGtwReportsObj : totalACHDataGtwReports) {
        	// List<String> data = Arrays.asList(
        	 
         //}

            for (CreditCountReport creditCountReportObj : creditCountReportData) {
                List<String> data = Arrays.asList(
                		creditCountReportObj.getMerchantId(),
                		creditCountReportObj.getRefundCount(),
                		creditCountReportObj.getRefundAmount()
                		
                );

                csvPrinter.printRecord(data);
            }
            //csvPrinter.printRecord(data);
            csvPrinter.flush();

            return FileDomain.builder()
                    .file(new ByteArrayResource(new ByteArrayInputStream(out.toByteArray()).readAllBytes()))
                    .fileName(
                            String.format(
                                    "WAB_CreditCounts-%s.csv",	             
                                    filter.getStartDateTime()
                            )
                    )
                    .build();
        }
        
	
	}


	@Override
	public Page<CreditCountReportDto> findData(String startDateTime, String endDateTime, CreditCountReportFilter filter, Pageable pageable) {
		Page<CreditCountReport> creditCountReportData = creditCountReportService.find(startDateTime,  endDateTime, pageable);
	       return creditCountReportMapper.toDto(creditCountReportData);
	}
	
	
}
