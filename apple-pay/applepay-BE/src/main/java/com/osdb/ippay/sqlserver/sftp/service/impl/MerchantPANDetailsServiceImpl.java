package com.osdb.ippay.sqlserver.sftp.service.impl;

import com.osdb.ippay.secondary.reports.repository.MonthlyCreditCardReportRepository;
import com.osdb.ippay.secondary.reports.repository.entity.MonthlyCreditCardReport;
import com.osdb.ippay.secondary.reports.service.MonthlyCreditCardReportService;
import com.osdb.ippay.sqlserver.sftp.repository.MerchantPANDetailsRepository;
import com.osdb.ippay.sqlserver.sftp.service.MerchantPANDetailsService;

import lombok.experimental.FieldDefaults;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import javax.persistence.EntityManager;


import static lombok.AccessLevel.PRIVATE;

@Service
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class MerchantPANDetailsServiceImpl implements MerchantPANDetailsService {

	MerchantPANDetailsRepository merchantPANDetailsRepository;	
	EntityManager entityManager;
	

	    @Autowired
	    public MerchantPANDetailsServiceImpl(MerchantPANDetailsRepository merchantPANDetailsRepositoryObj,
	    		@Qualifier("sqlserverEmFactory") EntityManager entityManager) {

	        this.merchantPANDetailsRepository = merchantPANDetailsRepositoryObj;
	        this.entityManager = entityManager;
	    }

	    @Override
		public List FetchPANDetails(String mid) 
	    {

		List  merchantPANDetails = merchantPANDetailsRepository.getPANDetails(mid);
		
		if (merchantPANDetails.isEmpty()) {
			
				return new ArrayList<>();
	    }
		

	    if (merchantPANDetails.get(0) instanceof String) {
	        return ((List<String>) merchantPANDetails)
	          .stream()
	          .map(s -> new String[] { s })
	          .collect(Collectors.toList());
	    } else {
	        return (List<String[]>) merchantPANDetails;

	    }
	}
}
