package com.osdb.ippay.common.exception;

import com.fasterxml.jackson.databind.exc.InvalidFormatException;
import com.osdb.ippay.common.exception.business.BusinessException;
import com.osdb.ippay.common.exception.handling.Error;
import com.osdb.ippay.common.exception.handling.ErrorResponse;
import com.osdb.ippay.common.exception.handling.FormValidationError;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@ControllerAdvice
@FieldDefaults(level = AccessLevel.PRIVATE)
public class GlobalExceptionHandler {

    @ExceptionHandler(BusinessException.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ErrorResponse handleBusinessException(BusinessException exception) {
       // log.error(exception.getMessage());

        return new ErrorResponse(
                System.currentTimeMillis(),
                HttpStatus.BAD_REQUEST.value(),
                List.of(Error.builder().message(exception.getMessage()).build())
        );
    }

    @ExceptionHandler(InvalidFormatException.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ErrorResponse handleInvalidFormatException(InvalidFormatException exception) {
      //  log.error(exception.getMessage());

        return new ErrorResponse(
                System.currentTimeMillis(),
                HttpStatus.BAD_REQUEST.value(),
                List.of(Error.builder().message(exception.getMessage()).build())
        );
    }

   // @ExceptionHandler(BindException.class)
   // @ResponseBody
   // @ResponseStatus(HttpStatus.BAD_REQUEST)
   // public ErrorResponse handle(BindException exception) {
        /*log.error(exception.getMessage());

        return new ErrorResponse(
                System.currentTimeMillis(),
                HttpStatus.BAD_REQUEST.value(),
                exception.getBindingResult().getFieldErrors()
                        .stream()
                        .map(x -> FormValidationError.builder()
                                .field(x.getField())
                                .message(x.getDefaultMessage())
                                .build()).collect(Collectors.toList())
        );*/
   // }
}
