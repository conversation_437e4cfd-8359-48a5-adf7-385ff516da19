package com.osdb.ippay.secondary.alerts.repository;

import com.osdb.ippay.secondary.alerts.repository.entity.ThresholdSetting;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.query.Procedure;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

import com.osdb.ippay.secondary.alerts.repository.entity.AlertType;

@Repository
public interface ThresholdSettingRepository extends
        JpaRepository<ThresholdSetting, Long>,
        JpaSpecificationExecutor<ThresholdSetting> {

    Optional<ThresholdSetting> findByAlertType(AlertType alertType);
    
    ThresholdSetting findByAlertTypeAndMid(AlertType alertType, String mId);

}
