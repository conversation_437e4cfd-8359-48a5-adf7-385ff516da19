package com.osdb.ippay.sqlserveripcore.ipwhitelist.repository.entity;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import javax.persistence.*;
import static javax.persistence.GenerationType.IDENTITY;
import static lombok.AccessLevel.PRIVATE;

@Entity
@Table(name = "TerminalIpsGlobal")

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
public class TerminalIpsGlobal {

	@Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "ID")
    public Long id;

	//@Column(name = "TerminalID", nullable = false)
	//public String terminalId;

	 
    @Column(name = "IPAddress", nullable = false)
    public String ipAddress;

    @Column(name = "Active")
    public Boolean active;

    @Column(name = "Username", nullable = false)
    public String username;

    @Column(name = "DateCreated")
    @GeneratedValue(strategy = GenerationType.AUTO)
    public java.sql.Timestamp dateCreated;

    @Column(name = "DateModified")
    @GeneratedValue(strategy = GenerationType.AUTO)
    public java.sql.Timestamp dateModified;
    }

