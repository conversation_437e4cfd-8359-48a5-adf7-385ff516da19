package com.osdb.ippay.primary.user.facade.impl;

import com.osdb.ippay.common.domain.FileDomain;
import com.osdb.ippay.common.user.facade.dto.UserDto;
import com.osdb.ippay.common.user.facade.mapper.UserMapper;
import com.osdb.ippay.primary.user.facade.UserFacade;
import com.osdb.ippay.primary.user.facade.dto.UserStatusDto;
import com.osdb.ippay.primary.user.repository.entity.User;
import com.osdb.ippay.primary.user.repository.entity.UserStatus;
import com.osdb.ippay.primary.user.service.UserExportService;
import com.osdb.ippay.primary.user.service.UserMerchantService;
import com.osdb.ippay.primary.user.service.UserService;
import com.osdb.ippay.primary.user.service.filter.UserFilter;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.util.List;

import static com.osdb.ippay.primary.user.repository.entity.UserStatus.*;
import static lombok.AccessLevel.PRIVATE;

@Component
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class UserFacadeImpl implements UserFacade {

    // services
    UserService userService;
    UserExportService userExportService;
    UserMerchantService userMerchantService;

    // mappers
    UserMapper userMapper;

    @Override
    public UserDto find(String email) {
        User user = userService.findByEmail(email);
        return userMapper.toDto(user);
    }

    @Override
    public UserDto updateStatus(Long id, UserStatusDto statusDto) {
        User existedUser = userService.find(id);

        boolean isDisabledStatus = ACTIVE.equals(statusDto.getStatus());

        UserStatus userStatus = StringUtils.isBlank(existedUser.getPassword()) && isDisabledStatus ?
                INVITED :
                statusDto.getStatus();

        updateLastSessionFailedLogins(existedUser, userStatus);
        existedUser.setStatus(userStatus);
        User updated = userService.save(existedUser);

        return userMapper.toDto(updated);
    }

    @Transactional
    @Override
    public void assignMerchants(Long id, List<String> merchantIds) {
        userMerchantService.deleteBy(id);
        userMerchantService.saveAll(id, merchantIds);
    }

    @Override
    public FileDomain export(User user, UserFilter filter) throws IOException {
        return userExportService.export(user, filter);
    }

    @Override
    public Boolean exists(String email) {
        return userService.exists(email);
    }

    private void updateLastSessionFailedLogins(User user, UserStatus newStatus) {
        boolean isCurrStatusLocked = UserStatus.LOCKED.equals(user.getStatus());
        boolean isNewStatusNotLocked = !UserStatus.LOCKED.equals(newStatus);

        if(isCurrStatusLocked && isNewStatusNotLocked) {
            user.setLastSessionFailedLogins(0);
        }
    }
}
