package com.osdb.ippay.secondary.alternatepay.payment.controller.dto;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;
import lombok.experimental.FieldDefaults;

import static lombok.AccessLevel.PRIVATE;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class TSYSPaymentRequestDto {

    @JsonProperty("SecureSale")
    PaymentSecureAuthDto SecureSale;
}
