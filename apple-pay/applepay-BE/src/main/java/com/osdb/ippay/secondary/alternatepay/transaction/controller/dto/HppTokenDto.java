package com.osdb.ippay.secondary.alternatepay.transaction.controller.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import javax.persistence.*;
import static lombok.AccessLevel.PRIVATE;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class HppTokenDto {

    @Id
    @GeneratedValue
    Long Id;

    Long hppTransId;
    
    String token;
    
    String appleToken;

  
}
