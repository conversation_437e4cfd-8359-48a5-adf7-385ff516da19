package com.osdb.ippay.common.security.config;

import com.osdb.ippay.common.security.filter.CookieAuthFilter;
import com.osdb.ippay.common.security.handler.CustomAccessDeniedHandler;
import com.osdb.ippay.common.security.handler.RestAuthenticationEntryPoint;
import com.osdb.ippay.common.security.provider.JwtProvider;
import com.osdb.ippay.primary.user.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.provisioning.InMemoryUserDetailsManager;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.access.AccessDeniedHandler;
import org.springframework.security.web.authentication.www.BasicAuthenticationFilter;
import org.springframework.security.web.csrf.CookieCsrfTokenRepository;
import org.springframework.security.web.csrf.CsrfFilter;

import static lombok.AccessLevel.PRIVATE;

@Configuration
@EnableWebSecurity
@EnableGlobalMethodSecurity(prePostEnabled = true)
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class SecurityConfiguration extends WebSecurityConfigurerAdapter {

    private static final String PRIVATE_APIS = "/api/v1/private/**";

    RestAuthenticationEntryPoint authenticationEntryPoint;

    JwtProvider jwtProvider;
    UserService userService;
    Environment environment;

    @NonFinal
    @Value(value = "${allowed.domain}")
    String domain;

    @Override
    protected void configure(HttpSecurity http) throws Exception {
      //  CookieCsrfTokenRepository csrfTokenRepository= CookieCsrfTokenRepository.withHttpOnlyFalse();
      //  csrfTokenRepository.setCookieDomain(domain);

     /*   http
                .exceptionHandling().authenticationEntryPoint(authenticationEntryPoint)
                .and()
                .addFilterBefore(
                        new CookieAuthFilter(jwtProvider, userService, environment),
                        BasicAuthenticationFilter.class
                )
                .csrf()
                .csrfTokenRepository(csrfTokenRepository)
                .and()
                .sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS)
                .and()
                .logout().deleteCookies(CookieAuthFilter.AUTH_COOKIE)
                .and()
                .authorizeRequests()
                .antMatchers(PRIVATE_APIS).permitAll()
                .and()
                .addFilterBefore(new CookieFilter(), CsrfFilter.class)
                .exceptionHandling().accessDeniedHandler(accessDeniedHandler());
        */
               
        
    	CookieCsrfTokenRepository csrfTokenRepository = CookieCsrfTokenRepository.withHttpOnlyFalse();
        csrfTokenRepository.setCookieDomain(domain);
 
        http
                .exceptionHandling().authenticationEntryPoint(authenticationEntryPoint)
                .and()
                .addFilterBefore(
                        new CookieAuthFilter(jwtProvider, userService, environment),
                        BasicAuthenticationFilter.class)
                .csrf(AbstractHttpConfigurer::disable)
                .sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS)
                .and()
                .logout().deleteCookies(CookieAuthFilter.AUTH_COOKIE)
                .and()
                .authorizeRequests()
                .antMatchers(PRIVATE_APIS).permitAll()
                .and()
                .addFilterBefore(new CookieFilter(), CsrfFilter.class)
                .exceptionHandling().accessDeniedHandler(accessDeniedHandler());
    }
    
        
    

   /* @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        http.authorizeRequests(authorizeRequests -> authorizeRequests.anyRequest()
          .permitAll())
          .csrf(AbstractHttpConfigurer::disable);
        return http.build();
    }
    
    @Bean
    public InMemoryUserDetailsManager userDetailsService() {
        UserDetails user = User.withUsername("<EMAIL>")
          .password("123456")
          .roles("ADMIN_ROLE")
          .build();
        return new InMemoryUserDetailsManager(user);
    }
    */
    
    @Bean
    public AccessDeniedHandler accessDeniedHandler(){
        return new CustomAccessDeniedHandler();
    }

    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder(12);
    }
}
