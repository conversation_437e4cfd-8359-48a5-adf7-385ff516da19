/*package com.osdb.ippay.sqlserver.reports.facade.impl;

import com.osdb.ippay.common.domain.FileDomain;
import com.osdb.ippay.sqlserver.reports.facade.FANFReportsFacade;
import com.osdb.ippay.sqlserver.reports.facade.TotalACHDataGtwReportsFacade;
import com.osdb.ippay.sqlserver.reports.facade.mapper.FANFReportsMapper;
import com.osdb.ippay.sqlserver.reports.facade.mapper.TotalACHDataGtwReportsMapper;
import com.osdb.ippay.sqlserver.reports.repository.entity.FANFReports;
import com.osdb.ippay.sqlserver.reports.repository.entity.TotalACHDataGtwReports;
import com.osdb.ippay.sqlserver.reports.service.FANFReportsService;
import com.osdb.ippay.sqlserver.reports.service.TotalACHDataGtwReportsService;
import com.osdb.ippay.sqlserver.reports.service.filter.FANFReportsFilter;
import com.osdb.ippay.sqlserver.reports.service.filter.TotalACHDataGtwReportsFilter;
import com.osdb.ippay.sqlserver.reports.service.filter.TotalCardDataGtwReportsFilter;

import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.stereotype.Component;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.Arrays;
import java.util.List;

import static lombok.AccessLevel.PRIVATE;

@Component
@RequiredArgsConstructor
@FieldDefaults(makeFinal = true, level = PRIVATE)
public class FANFReportsFacadeImpl implements FANFReportsFacade {

    // services
	FANFReportsService fanfReportsService;

    // mappers
	FANFReportsMapper fanfReportsMapper;

    // Method to export FANF Data into CSV format	
	@Override
	public FileDomain export(Integer merchantID, String startDateTime, String endDateTime,
			FANFReportsFilter filter) throws IOException {
		 String[] HEADERS = {
	                "MerchantID",
	                "TerminalID",
	                "MerchantName",
	                "ApprovalStatus",
	                "CardType",
	                "CardNumber",
	                "CardHolder",
	                "Address",
	                "City",
	                "State",
	                "ZipCode",
	                "Country",
	                "Phone",
	                "Email",
	                "TransactionId",
	                "TransactionType",
	                "AuthCode",
	                "AuthAmount",
	                "AuthDate",
	                "AVS",
	                "CVV",
	                "UD1",
	                "UD2",
	                "UD3",
	                "RequestType",
	                "CurrencyCode",
	                "OrderNumber",
	                "SettlementDate",
	                "ResponseCode",
	                "ResponseText",
	                "Token",
	                "CCProcessor",
	                "POSTerminalId",
	                "POSAditionalData",
	                "VNumber",
	                "RetRefNumber",
	                "IPTransId",
	                "SafeCardNumber",
	                "Expiry",
	                "BankTransId",
	                "ExternalTransId"
	        };

	        final CSVFormat format = CSVFormat.Builder.create()
	                .setHeader(HEADERS)
	                .setIgnoreEmptyLines(true)
	                .build();
	        
	        List<FANFReports> fanfReports  = fanfReportsService.find(merchantID, startDateTime, endDateTime, filter);

	        try (
	                ByteArrayOutputStream out = new ByteArrayOutputStream();
	                CSVPrinter csvPrinter = new CSVPrinter(new PrintWriter(out), format)
	        ) {

	            for (FANFReports fanfReportsObj : fanfReports) {
	                List<String> data = Arrays.asList(
	                		fanfReportsObj.getMerchant().toString(),
	                		fanfReportsObj.getTerminalId(),
	                		fanfReportsObj.getMerchantName(),
	                		fanfReportsObj.getApprovalStatus(),
	                		fanfReportsObj.getCardType(),
	                		fanfReportsObj.getCardNumber(),
	                		fanfReportsObj.getCardHolder(),
	                		fanfReportsObj.getAddress(),
	                		fanfReportsObj.getCity(),
	                		fanfReportsObj.getState(),
	                		fanfReportsObj.getZipCode(),
	                		fanfReportsObj.getCountry(),
	                		fanfReportsObj.getPhone(),
	                		fanfReportsObj.getEmail(),
	                		fanfReportsObj.getTransactionId(),
	                		fanfReportsObj.getTransactionType(),
	                		fanfReportsObj.getAuthCode().toString(),
	                		fanfReportsObj.getAuthAmount().toString(),
	                		fanfReportsObj.getAuthDate(),
	                		fanfReportsObj.getAvs(),
	                		fanfReportsObj.getCvv(),
	                		fanfReportsObj.getUd1(),
	                		fanfReportsObj.getUd2(),
	                		fanfReportsObj.getUd3(),
	                		fanfReportsObj.getRequestType(),
	                		fanfReportsObj.getCurrencyCode(),
	                		fanfReportsObj.getOrderNumber(),
	                		fanfReportsObj.getSettlementDate(),
	                		fanfReportsObj.getResponseCode(),
	                		fanfReportsObj.getResponseText(),
	                		fanfReportsObj.getToken(),
	                		fanfReportsObj.getCcProcessor(),
	                		fanfReportsObj.getPosTerminalId(),
	                		fanfReportsObj.getPosAdditionalData(),
	                		fanfReportsObj.getVNumber(),
	                		fanfReportsObj.getRetRefNumber().toString(),
	                		fanfReportsObj.getIpTransId().toString(),
	                		fanfReportsObj.getSafeCardNumber().toString(),
	                		fanfReportsObj.getExpiry().toString(),
	                		fanfReportsObj.getBankTransID().toString(),
	                		fanfReportsObj.getExternalTransID().toString()
        
	                		
	                );

	                csvPrinter.printRecord(data);
	            }

	            csvPrinter.flush();

	            return FileDomain.builder()
	                    .file(new ByteArrayResource(new ByteArrayInputStream(out.toByteArray()).readAllBytes()))
	                    .fileName(
	                            String.format(
	                                    "Totals-ACH-%s.csv",	             
	                                    filter.getFromDate()
	                            )
	                    )
	                    .build();
	        }
	        
	}

}*/