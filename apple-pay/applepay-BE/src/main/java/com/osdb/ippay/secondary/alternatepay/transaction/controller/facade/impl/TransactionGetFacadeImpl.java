package com.osdb.ippay.secondary.alternatepay.transaction.controller.facade.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionGetDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionGetResultDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionInitDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TrasactionInitResultDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.facade.*;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.mapper.TransactionGetControllerMapper;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.mapper.TransactionInitControllerMapper;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.Base64;
import java.util.Objects;

import static lombok.AccessLevel.PRIVATE;

@Component
@RequiredArgsConstructor
@FieldDefaults(makeFinal = true, level = PRIVATE)
public class TransactionGetFacadeImpl implements TransactionGetFacade {

    public static final String INVALID_JOB_STATE = "Job can not be deleted, job state not 'Queued'";

    TransactionGetControllerMapper mapper;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    @NonFinal
    @Value("${transaction-fetch.url.get}")
    String transactionUrlGet;


    @NonFinal
    @Value("${transaction-fetch.url.post}")
    String transactionUrlPost;
    
    @NonFinal
    boolean blnIsDecoded = true;
    
	@Override
	public TransactionGetResultDto create(String apiKey, String trnId) {
		RestTemplate restTemplate = new RestTemplate();

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

       
        if(!blnIsDecoded)
        {
        	byte[] decodedBytes = Base64.getDecoder().decode(transactionUrlGet);
            transactionUrlGet = new String(decodedBytes);
        }
        
        TransactionGetResultDto trnGetResultDto = restTemplate.exchange(
        		transactionUrlGet + trnId + "?apiKey="+apiKey,
                HttpMethod.GET,
                null,
                new ParameterizedTypeReference<TransactionGetResultDto>() {
                },
                ""
        ).getBody();

        return Objects.nonNull(trnGetResultDto) ? mapper.toOutDto(trnGetResultDto) : null;
	}
}
