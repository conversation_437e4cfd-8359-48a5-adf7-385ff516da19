package com.osdb.ippay.secondary.alternatepay.tokenization.controller;

import com.osdb.ippay.common.logging.LogExecutionTime;
import com.osdb.ippay.common.util.annotation.ApiPageable;
import com.osdb.ippay.common.util.response.PageResponse;
import com.osdb.ippay.secondary.alternatepay.tokenization.controller.dto.PaymentTokenDto;
import com.osdb.ippay.secondary.alternatepay.tokenization.controller.dto.PaymentTokenResultDto;
import com.osdb.ippay.secondary.alternatepay.tokenization.controller.facade.PaymentTokenFacade;
import com.osdb.ippay.secondary.alternatepay.tokenization.controller.mapper.PaymentTokenControllerMapper;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionTokenizeCaptureDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TrasactionInitResultDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.facade.TransactionTokenizeCaptureFacade;

import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import static lombok.AccessLevel.PRIVATE;

@Tag(name = "Alternatepay/Tokenization")
@RestController
@RequestMapping(value = "/api/v2/payment/alternatepay")
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class PaymentTokenController {

    PaymentTokenFacade tokenizationFacade;
    TransactionTokenizeCaptureFacade transactionTokenizeCaptureFacade;
    PaymentTokenControllerMapper tokenizationMapper;
    
    @LogExecutionTime
    @PostMapping("/generateToken")
    public ResponseEntity<TrasactionInitResultDto> Tokenize(@Valid @RequestBody TransactionTokenizeCaptureDto transactionInitDto) {
    	TrasactionInitResultDto response = transactionTokenizeCaptureFacade.create(transactionInitDto);
        return ResponseEntity.ok(response);
    }

  

}
