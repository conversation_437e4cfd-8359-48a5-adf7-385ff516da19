package com.osdb.ippay.secondary.alerts.service.impl;

import com.osdb.ippay.common.exception.business.NotFoundException;
import com.osdb.ippay.secondary.alerts.repository.ThresholdSettingRepository;
import com.osdb.ippay.secondary.alerts.repository.entity.ThresholdSetting;
import com.osdb.ippay.secondary.alerts.service.ThresholdSettingService;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.stereotype.Service;
import com.osdb.ippay.secondary.alerts.repository.entity.AlertType;

import static lombok.AccessLevel.PRIVATE;

@Service
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class ThresholdSettingServiceImpl implements ThresholdSettingService {
    static final String THRESHOLD_SETTING_NOT_FOUND = "Threshold setting not found";
    ThresholdSettingRepository thresholdSettingRepository;

    @Override
    public ThresholdSetting find(AlertType alertType) {
        return thresholdSettingRepository
                .findByAlertType(alertType)
                .orElseThrow(() -> new NotFoundException(THRESHOLD_SETTING_NOT_FOUND));
    }

    @Override
    public ThresholdSetting update(AlertType alertType, String strMerchantId, ThresholdSetting setting) {
        return thresholdSettingRepository.save(setting);
    }

	@Override
	public ThresholdSetting findByMId(AlertType alertType, String strMerchantId) {
		 return thresholdSettingRepository
	                .findByAlertTypeAndMid(alertType, strMerchantId);
	                
	}

}
