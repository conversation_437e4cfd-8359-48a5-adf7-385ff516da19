package com.osdb.ippay.secondary.retokenization.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

import static lombok.AccessLevel.PRIVATE;

@Getter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class RetokenizationUpdateDto {

    String user;

    @JsonProperty(value = "job_type")
    String jobType;

    @JsonProperty(value = "job_info")
    String jobInfo;

    @JsonProperty(value = "job_description")
    String jobDescription;

}
