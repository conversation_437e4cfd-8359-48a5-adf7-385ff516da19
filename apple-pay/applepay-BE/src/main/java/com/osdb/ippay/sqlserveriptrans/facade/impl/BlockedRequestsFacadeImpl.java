package com.osdb.ippay.sqlserveriptrans.facade.impl;

import com.osdb.ippay.common.domain.FileDomain;
import com.osdb.ippay.primary.user.repository.entity.User;
import com.osdb.ippay.secondary.chargeback.facade.dto.ChargebackDto;
import com.osdb.ippay.secondary.chargeback.repository.entity.Chargeback;
import com.osdb.ippay.secondary.chargeback.service.ChargebackFilter;
import com.osdb.ippay.sqlserver.reports.facade.TotalACHDataGtwReportsFacade;
import com.osdb.ippay.sqlserver.reports.facade.dto.TotalACHDataGtwReportsDto;
import com.osdb.ippay.sqlserver.reports.facade.dto.TotalACHGtwDataDto;
import com.osdb.ippay.sqlserver.reports.facade.mapper.TotalACHDataGtwReportsMapper;
import com.osdb.ippay.sqlserver.reports.facade.mapper.TotalACHGtwDataMapper;
import com.osdb.ippay.sqlserver.reports.repository.entity.TotalACHDataGtwReports;
import com.osdb.ippay.sqlserver.reports.repository.entity.TotalACHGtwData;
import com.osdb.ippay.sqlserver.reports.service.TotalACHDataGtwReportsService;
import com.osdb.ippay.sqlserver.reports.service.filter.TotalACHDataGtwReportsFilter;
import com.osdb.ippay.sqlserver.reports.service.filter.TotalACHGtwDataFilter;
import com.osdb.ippay.sqlserveriptrans.facade.BlockedRequestsFacade;
import com.osdb.ippay.sqlserveriptrans.facade.dto.BlockedRequestsDto;
import com.osdb.ippay.sqlserveriptrans.facade.mapper.BlockedRequestsMapper;
import com.osdb.ippay.sqlserveriptrans.repository.entity.BlockedRequests;
import com.osdb.ippay.sqlserveriptrans.service.BlockedRequestsService;
import com.osdb.ippay.sqlserveriptrans.service.filter.BlockedRequestsFilter;

import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.ListIterator;

import static lombok.AccessLevel.PRIVATE;

@Component
@RequiredArgsConstructor
@FieldDefaults(makeFinal = true, level = PRIVATE)
public class BlockedRequestsFacadeImpl implements BlockedRequestsFacade {

    // services
	BlockedRequestsService blockedRequestsFacadeService;

    // mappers
	BlockedRequestsMapper blockedRequestsMapper;


	@Override
	public Page<BlockedRequestsDto> findData(String terminalID, String ipAddress, String transactionType,
			java.util.Date startDateTime, java.util.Date endDateTime, BlockedRequestsFilter filter, Pageable pageable) {

		Page<BlockedRequests> blockedRequests = blockedRequestsFacadeService.findData(terminalID, ipAddress, transactionType, startDateTime, endDateTime, filter, pageable);
        return blockedRequestsMapper.toDto(blockedRequests);
	}

}
