package com.osdb.ippay.secondary.statement.repository.entity;

import com.osdb.ippay.common.entity.BaseEntity;
import lombok.*;
import lombok.experimental.FieldDefaults;
import lombok.experimental.SuperBuilder;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import static lombok.AccessLevel.PRIVATE;

@Entity
@Table(name = "MERCHANT_STATEMENT_DETAIL")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = PRIVATE)
@SuperBuilder(toBuilder = true)
public class MSTotalDetail extends BaseEntity {

    @Column(name = "MERCHANT_ID")
    String merchantId;

    @Column(name = "PROCESSING_DETAIL_TOTAL")
    String processingDetailTotal;

    @Column(name = "AUTHORIZATION_DETAIL_TOTAL")
    String authorizationDetailTotal;

    @Column(name = "OTHER_DETAIL_TOTAL")
    String otherDetailTotal;

    @Column(name = "DISCOUNT_DUE_TOTAL")
    String discountDueTotal;

    @Column(name = "STATEMENT_MONTH")
    String statementMonth;

}
