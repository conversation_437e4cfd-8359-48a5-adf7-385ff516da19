package com.osdb.ippay.sqlserver.sftp.service;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.FileReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.io.PrintWriter;

import com.opencsv.CSVReader;
import com.opencsv.bean.ColumnPositionMappingStrategy;
import com.opencsv.bean.CsvToBean;
import com.opencsv.bean.CsvToBeanBuilder;
import com.osdb.ippay.common.domain.FileDomain;
import com.osdb.ippay.sqlserver.reports.repository.MinimumDiscountDataRepository;
import com.osdb.ippay.sqlserver.reports.repository.entity.TotalACHDataGtwReports;
import com.osdb.ippay.sqlserver.reports.service.MinimumDiscountDataService;
import com.osdb.ippay.sqlserver.reports.service.filter.MonthlyResidualReportsFilter;
import com.osdb.ippay.sqlserver.reports.service.filter.TotalACHDataGtwReportsFilter;
import com.osdb.ippay.sqlserver.reports.service.filter.TotalACHGtwDataFilter;
import com.osdb.ippay.sqlserver.reports.service.impl.MinimumDiscountDataServiceImpl;
import com.osdb.ippay.sqlserver.sftp.CSVData;
import com.osdb.ippay.sqlserver.sftp.PPMCSVDataType1;
import com.osdb.ippay.sqlserver.sftp.PPMCSVDataType2;
import com.osdb.ippay.sqlserver.sftp.PPMCSVDataType3;
import com.osdb.ippay.sqlserver.sftp.PPMCSVDataType4;
import com.osdb.ippay.sqlserver.sftp.PPMCSVDataType5;

import java.io.Reader;
import java.nio.charset.Charset;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.Dictionary;
import java.util.HashMap;
import java.util.Hashtable;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Scanner;
import java.util.stream.Stream;

import javax.persistence.EntityManager;
import javax.persistence.criteria.Predicate;

import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;


@Service
public class FileTransferServiceImpl implements FileTransferService {

	private Logger logger = LoggerFactory.getLogger(FileTransferServiceImpl.class);
	private static final String SAMPLE_CSV_FILE_PATH_MRR = "/testdata.csv";
	private static final String SAMPLE_CSV_FILE_PATH_PPM_TYPE_1 = "/testdata.csv";
	private static final String SAMPLE_CSV_FILE_PATH_PPM_TYPE_2 = "/testdata.csv";
	private static final String SAMPLE_CSV_FILE_PATH_PPM_TYPE_3 = "/testdata.csv";
	private static final String SAMPLE_CSV_FILE_PATH_PPM_TYPE_4 = "/testdata.csv";
	private static final String SAMPLE_CSV_FILE_PATH_PPM_TYPE_5 = "/testdata.csv";
	
	MinimumDiscountDataRepository minimumDiscountDataRepository;
	
	MinimumDiscountDataServiceImpl minimumDiscountDataServiceImpl;
	
	@Value("${sftp.host}")
	private String host;
	
	@Value("${sftp.port}")
	private Integer port;
	
	@Value("${sftp.username}")
	private String username;
	
	@Value("${sftp.password}")
	private String password;
	
	@Value("${sftp.sessionTimeout}")
	private Integer sessionTimeout;
	
	@Value("${sftp.channelTimeout}")
	private Integer channelTimeout;

	private List<Map> recordsList = new ArrayList<Map>();	
	Map<String, String> recordsMap = new HashMap<>();
	
    private List<Map> recordsListSummary = new ArrayList<Map>();	
	Map<String, String> recordsMapSummary = new HashMap<>();
	
	private List<Map> recordsListFeeItemDetail = new ArrayList<Map>();	
	Map<String, String> recordsMapFeeItemDetail = new HashMap<>();
	
	public static boolean validateJavaDate(String strDate)
	   {
		/* Check if date is 'null' */
		if (strDate.trim().equals(""))
		{
		    return true;
		}
		/* Date is not 'null' */
		else
		{
		    /*
		     * Set preferred date format,
		     * For example MM-dd-yyyy, MM.dd.yyyy,dd.MM.yyyy etc.*/
		    SimpleDateFormat sdfrmt = new SimpleDateFormat("MM/dd/yyyy");
		    sdfrmt.setLenient(false);
		    /* Create Date object
		     * parse the string into date 
	             */
		    try
		    {
		        Date javaDate = sdfrmt.parse(strDate); 
		       // System.out.println(strDate+" is valid date format");
		    }
		    /* Date format is invalid */
		    catch (ParseException e)
		    {
		       // System.out.println(strDate+" is Invalid Date format");
		        return false;
		    }
		    /* Return true if date format is valid */
		    return true;
		}
	   }
	
	@SuppressWarnings("unchecked")
	@Override
	public FileDomain exportMonthlyResidualReport(String localFilePath, MonthlyResidualReportsFilter filter)
			throws IOException {


		try (
				 InputStream stream = this.getClass().getResourceAsStream(SAMPLE_CSV_FILE_PATH_MRR);
				 
				 CSVReader csvReader = new CSVReader(new BufferedReader(new InputStreamReader(stream)));
		        ) {
            @SuppressWarnings("rawtypes")
			ColumnPositionMappingStrategy strategy = new ColumnPositionMappingStrategy();
            strategy.setType(PPMCSVDataType1.class);
            String[] memberFieldsToBindTo = {
            		"Bank",
	                "RP Agent ID",
	                "Group",
	                "Association",
	                "Merchant ID",
	                "Record Type",
	                "Processing Date",
	                "DBA Name",
	                "DBA City Address",
	                "DBA State/Country",
	                "DBA Zip",
	                "Visa SIC",
	                "Mastercard MCC",
	                "Reserved",
	                "Reserved",
	                "Merchant status",
	                "Date closed",
	                "Minimum Bill/Discount Difference",
	                "Minimum Discount Amount",
	                "Expense Rate Group"
	                };
            strategy.setColumnMapping(memberFieldsToBindTo);
   

            String[] HEADERS = {
            		"Bank",
	                "RP Agent ID",
	                "Group",
	                "Association",
	                "Merchant ID",
	                "Record Type",
	                "Processing Date",
	                "DBA Name",
	                "DBA City Address",
	                "DBA State/Country",
	                "DBA Zip",
	                "Visa SIC",
	                "Mastercard MCC",
	                "Reserved",
	                "Reserved",
	                "Merchant status",
	                "Date closed",
	                "Minimum Bill/Discount Difference",
	                "Minimum Discount Amount",
	                "Expense Rate Group"
	        };

	        final CSVFormat format = CSVFormat.Builder.create()
	        		
	                .setHeader(HEADERS)
	                .setIgnoreEmptyLines(true)
	                .build();	
	        
	        List<List<String>> records = new ArrayList<List<String>>();
	        	     
            try (
	                ByteArrayOutputStream out = new ByteArrayOutputStream();
	                CSVPrinter csvPrinter = new CSVPrinter(new PrintWriter(out), format)
	        ) {
          
            	String[] values = null;
            	
            	while ((values = csvReader.readNext()) != null) {
                	boolean blnIsValidDate = true;
                	blnIsValidDate = validateJavaDate(values[0].substring(45, 55));
                	
                	if(blnIsValidDate)
                	{
                		List<String> data = (Arrays.asList((values[0].length()>=4?values[0].substring(0, 4):""),
	                			(values[0].length()>=10?values[0].substring(5, 11):""),
	                			(values[0].length()>=17?values[0].substring(12, 18):""),
	                			(values[0].length()>=24?values[0].substring(19, 25):""),
	                			(values[0].length()>=41?" " + values[0].substring(26, 42):""),
	                			(values[0].length()>=43?values[0].substring(43, 44):""),
	                			((values[0].length()>=54 && blnIsValidDate)?values[0].substring(45, 55):""),
	                			((values[0].length()>=87 && blnIsValidDate)?values[0].substring(56, 88):(values[0].length()>=77 && !blnIsValidDate)?values[0].substring(46, 78):""),
	                			((values[0].length()>=112 && blnIsValidDate)?values[0].substring(89, 113):(values[0].length()>=92 && !blnIsValidDate)?values[0].substring(69, 93):""),
	                			(values[0].length()>=115?values[0].substring(114, 116):""),
	                			(values[0].length()>=125?values[0].substring(117, 126):""),
	                			(values[0].length()>=130?values[0].substring(127, 131):""),
	                			(values[0].length()>=135?values[0].substring(132, 136):""),
	                			(values[0].length()>=140?values[0].substring(137, 141):""),
	                			(values[0].length()>=145?values[0].substring(142, 146):""),
	                			(values[0].length()>=147?values[0].substring(147, 148):""),
	                			(values[0].length()>=158?values[0].substring(149, 159):""),
	                			(values[0].length()>=171?values[0].substring(160, 172):""),
	                			(values[0].length()>=188?values[0].substring(173, 189):""),
	                			(values[0].length()>=221?values[0].substring(190, 222):"")));
	                    //if(blnIsValidDate)
	                	//System.out.println(values[0].length());
                		//if(filter.getMerchantId().toString().equals("2") || (!filter.getMerchantId().toString().equals("2") && values[0].substring(26, 42).contains(filter.getMerchantId())))
	                    	csvPrinter.printRecord(data);
                	
                	}
                	
                	
                }
            csvPrinter.flush();

            return FileDomain.builder()
                    .file(new ByteArrayResource(new ByteArrayInputStream(out.toByteArray()).readAllBytes()))
                    .fileName(
                            String.format(
                                    "TIPPAYPRD.MONTHLY_RESIDUAL.csv",	             
                                    filter.getMerchantId()
                            )
                    )
                    .build();
        }
	}
	
}

	@SuppressWarnings("unchecked")
	@Override
	public FileDomain exportPPMReport(String localFilePath, MonthlyResidualReportsFilter filter)
			throws IOException {


		 try (
				 InputStream stream = this.getClass().getResourceAsStream(SAMPLE_CSV_FILE_PATH_PPM_TYPE_1);
				 
				 CSVReader csvReader = new CSVReader(new BufferedReader(new InputStreamReader(stream)));
		        ) {
		            @SuppressWarnings("rawtypes")
					ColumnPositionMappingStrategy strategy = new ColumnPositionMappingStrategy();
		            strategy.setType(PPMCSVDataType1.class);
		            String[] memberFieldsToBindTo = {
		            		"Bank",
			                "RP Agent ID",
			                "Group",
			                "Association",
			                "Merchant ID",
			                "Record Type",
			                "Processing Date",
			                "DBA Name",
			                "DBA City Address",
			                "DBA State/Country",
			                "DBA Zip",
			                "Visa SIC",
			                "Mastercard MCC",
			                "Reserved",
			                "Reserved",
			                "Merchant status",
			                "Date closed",
			                "Minimum Bill/Discount Difference",
			                "Minimum Discount Amount",
			                "Expense Rate Group"
			                };
		            strategy.setColumnMapping(memberFieldsToBindTo);
           

		            String[] HEADERS = {
		            		"Bank",
			                "RP Agent ID",
			                "Group",
			                "Association",
			                "Merchant ID",
			                "Record Type",
			                "Processing Date",
			                "DBA Name",
			                "DBA City Address",
			                "DBA State/Country",
			                "DBA Zip",
			                "Visa SIC",
			                "Mastercard MCC",
			                "Reserved",
			                "Reserved",
			                "Merchant status",
			                "Date closed",
			                "Minimum Bill/Discount Difference",
			                "Minimum Discount Amount",
			                "Expense Rate Group"
			        };

			        final CSVFormat format = CSVFormat.Builder.create()
			        		
			                .setHeader(HEADERS)
			                .setIgnoreEmptyLines(true)
			                .build();	
			        
			        List<List<String>> records = new ArrayList<List<String>>();
			        	     
		            try (
			                ByteArrayOutputStream out = new ByteArrayOutputStream();
			                CSVPrinter csvPrinter = new CSVPrinter(new PrintWriter(out), format)
			        ) {
		          
		            	String[] values = null;
		            	
		            	while ((values = csvReader.readNext()) != null) {
		                	boolean blnIsValidDate = true;
		                	blnIsValidDate = validateJavaDate(values[0].substring(45, 55));
		                	
		                	if(blnIsValidDate)
		                	{
		                		List<String> data = (Arrays.asList((values[0].length()>=4?values[0].substring(0, 4):""),
			                			(values[0].length()>=10?values[0].substring(5, 11):""),
			                			(values[0].length()>=17?values[0].substring(12, 18):""),
			                			(values[0].length()>=24?values[0].substring(19, 25):""),
			                			(values[0].length()>=41?values[0].substring(26, 42):"").toString(),
			                			(values[0].length()>=43?values[0].substring(43, 44):""),
			                			((values[0].length()>=54 && blnIsValidDate)?values[0].substring(45, 55):""),
			                			((values[0].length()>=87 && blnIsValidDate)?values[0].substring(56, 88):(values[0].length()>=77 && !blnIsValidDate)?values[0].substring(46, 78):""),
			                			((values[0].length()>=112 && blnIsValidDate)?values[0].substring(89, 113):(values[0].length()>=92 && !blnIsValidDate)?values[0].substring(69, 93):""),
			                			(values[0].length()>=115?values[0].substring(114, 116):""),
			                			(values[0].length()>=125?values[0].substring(117, 126):""),
			                			(values[0].length()>=130?values[0].substring(127, 131):""),
			                			(values[0].length()>=135?values[0].substring(132, 136):""),
			                			(values[0].length()>=140?values[0].substring(137, 141):""),
			                			(values[0].length()>=145?values[0].substring(142, 146):""),
			                			(values[0].length()>=147?values[0].substring(147, 148):""),
			                			(values[0].length()>=158?values[0].substring(149, 159):""),
			                			(values[0].length()>=171?values[0].substring(160, 172):""),
			                			(values[0].length()>=188?values[0].substring(173, 189):""),
			                			(values[0].length()>=221?values[0].substring(190, 222):"")));
			                    //if(blnIsValidDate)
			                	//System.out.println(values[0].length());
		                		//if(filter.getMerchantId().toString().equals("2") || (!filter.getMerchantId().toString().equals("2") && values[0].substring(26, 42).contains(filter.getMerchantId())))
			                    	csvPrinter.printRecord(data);
		                		
		                	}
		                	
		                }
		            csvPrinter.flush();

		            return FileDomain.builder()
		                    .file(new ByteArrayResource(new ByteArrayInputStream(out.toByteArray()).readAllBytes()))
		                    .fileName(
		                            String.format(
		                                    "TIPPAYPRD.PPM.csv",	             
		                                    filter.getMerchantId()
		                            )
		                    )
		                    .build();
		        }
		 }
	}

	@SuppressWarnings("unchecked")
	@Override
	public FileDomain exportPPMReportType2(String localFilePath, MonthlyResidualReportsFilter filter)
			throws IOException {

		 try (
				 InputStream stream = this.getClass().getResourceAsStream(SAMPLE_CSV_FILE_PATH_PPM_TYPE_2);
				 
				 CSVReader csvReader = new CSVReader(new BufferedReader(new InputStreamReader(stream)));
		        ) 
		 {
	            @SuppressWarnings("rawtypes")
				ColumnPositionMappingStrategy strategy = new ColumnPositionMappingStrategy();
	            strategy.setType(PPMCSVDataType1.class);
	            String[] memberFieldsToBindTo = {
	            		"RP Agent ID",
		                "Group",
		                "Association",
		                "Merchant ID",
		                "Record Type",
		                "Fee Category Description",
		                "Fee Category",
		                "Fee Item Key 1",
		                "Fee Item Key 2",
		                "Fee Item Key 3",
		                "Fee Item Key 4",
		                "Fee Item Key 5",
		                "Include in Volume Totals Indicator",
		                "Sales Count",
		                "Sales Amount",
		                "Credit Count",
		                "Credit Amount",
		                "Interchange Fee",
		                "Interchange Percent Rate",
		                "Interchange Per Item Rate",
		                "Multiple Fee Items Flag",
		                "Multiple Pricing Flag",
		                "Fee Item Name",
		                "Fee Item Hierarchy Level",
		                "Count N/G/B Indicator",
		                "Amount N/G/B Indicator",
		                "Include In Monthly Minimum Indicator",
		                "Pass Through Indicator",
		                "Sales Count Applied To Income",
		                "Sales Amount Applied To Income",
		                "Credits Count Applied To Income",
		                "Credits Amount Applied To Income",
		                "Pricing Fee Item Percent",
		                "Pricing Fee Item Per Item",
		                "Income Amount",
		                "Interchange Pass Through Income",
		                "Pricing Fee Item Sales Tax Amount",
		                "Reserved",
		                "Statement Description",
		                "Reserved",
		                "GL Item Code (Income)",
		                "Multiple Expense Volume Flag",
		                "Sales Count Applied To Expense",
		                "Sales Amount Applied To Expense",
		                "Credits Count Applied To Expense",
		                "Credits Amount Applied To Expense",
		                "Expense Amount",
		                "User Date Code (Income)",
		                "Fees Paid",
		                "Expense Rate Group Name",
		                "Income Rate Group Name"
		                };
	            strategy.setColumnMapping(memberFieldsToBindTo);
    

	            String[] HEADERS = {
	            		"RP Agent ID",
		                "Group",
		                "Association",
		                "Merchant ID",
		                "Record Type",
		                "Fee Category Description",
		                "Fee Category",
		                "Fee Item Key 1",
		                "Fee Item Key 2",
		                "Fee Item Key 3",
		                "Fee Item Key 4",
		                "Fee Item Key 5",
		                "Include in Volume Totals Indicator",
		                "Sales Count",
		                "Sales Amount",
		                "Credit Count",
		                "Credit Amount",
		                "Interchange Fee",
		                "Interchange Percent Rate",
		                "Interchange Per Item Rate",
		                "Multiple Fee Items Flag",
		                "Multiple Pricing Flag",
		                "Fee Item Name",
		                "Fee Item Hierarchy Level",
		                "Count N/G/B Indicator",
		                "Amount N/G/B Indicator",
		                "Include In Monthly Minimum Indicator",
		                "Pass Through Indicator",
		                "Sales Count Applied To Income",
		                "Sales Amount Applied To Income",
		                "Credits Count Applied To Income",
		                "Credits Amount Applied To Income",
		                "Pricing Fee Item Percent",
		                "Pricing Fee Item Per Item",
		                "Income Amount",
		                "Interchange Pass Through Income",
		                "Pricing Fee Item Sales Tax Amount",
		                "Reserved",
		                "Statement Description",
		                "Reserved",
		                "GL Item Code (Income)",
		                "Multiple Expense Volume Flag",
		                "Sales Count Applied To Expense",
		                "Sales Amount Applied To Expense",
		                "Credits Count Applied To Expense",
		                "Credits Amount Applied To Expense",
		                "Expense Amount",
		                "User Date Code (Income)",
		                "Fees Paid",
		                "Expense Rate Group Name",
		                "Income Rate Group Name"
		        };

		        final CSVFormat format = CSVFormat.Builder.create()
		        		
		                .setHeader(HEADERS)
		                .setIgnoreEmptyLines(true)
		                .build();	
		        
		        List<List<String>> records = new ArrayList<List<String>>();
		        	     
	            try (
		                ByteArrayOutputStream out = new ByteArrayOutputStream();
		                CSVPrinter csvPrinter = new CSVPrinter(new PrintWriter(out), format)
		        ) {
	          
	            	String[] values = null;
	            	boolean blnIsNestedHeaderCreated = false;
	            	
	            	
	            	while ((values = csvReader.readNext()) != null) {
	                	int iStart = 0; int iEnd = 0; boolean blnIsValidDate = true;
	                	blnIsValidDate = validateJavaDate(values[0].substring(45, 55));
	                	
	                	if(!blnIsValidDate)
	                	{
	                		String strRecordType = values[0].substring(43, 44);
	                		
	                		if(strRecordType.contains("2"))
	                		{
	                			//System.out.println("Merchant Id - "+ filter.getMerchantId() + "- value");
	                			
			                    	List<String> data = (Arrays.asList((values[0].length()>=4?values[0].substring(0, 4):""),
					                		(values[0].length()>=10?values[0].substring(5, 11):""),
					                		(values[0].length()>=17?values[0].substring(12, 18):""),
					                		(values[0].length()>=24?values[0].substring(19, 25):""),
					                		(values[0].length()>=41?" " + values[0].substring(26, 42):""),
					                		(values[0].length()>=43?values[0].substring(43, 44):""),
					                		((values[0].length()>=64)?values[0].substring(45, 65):""),
					                		((values[0].length()>=68)?" " + values[0].substring(66, 69):""),
					                		((values[0].length()>=76)?" " + values[0].substring(70, 77):""),
					                		(values[0].length()>=84?values[0].substring(78, 85):""),
					                		(values[0].length()>=92?values[0].substring(86, 93):""),
					                		(values[0].length()>=100?values[0].substring(94, 101):""),
					                		(values[0].length()>=108?values[0].substring(102, 109):""),
					                		(values[0].length()>=110?values[0].substring(110, 111):""),
					                		(values[0].length()>=122?values[0].substring(112, 123):""),
					                		(values[0].length()>=139?values[0].substring(124, 140):""),
					                		(values[0].length()>=151?values[0].substring(141, 152):""),
					                		(values[0].length()>=168?values[0].substring(153, 169):""),
					                		(values[0].length()>=185?values[0].substring(170, 186):""),
					                		(values[0].length()>=195?values[0].substring(187, 196):""),
					                		(values[0].length()>=209?values[0].substring(197, 210):""),
					                		(values[0].length()>=211?values[0].substring(211, 212):""),
					                		(values[0].length()>=213?values[0].substring(213, 214):""),
					                		(values[0].length()>=215?values[0].substring(215, 245):""),
					                		(values[0].length()>=246?values[0].substring(246, 247):""),
					                		(values[0].length()>=248?values[0].substring(248, 249):""),
					                		(values[0].length()>=250?values[0].substring(250, 251):""),
					                		(values[0].length()>=252?values[0].substring(252, 253):""),
					                		(values[0].length()>=254?values[0].substring(254, 255):""),
					                		(values[0].length()>=266?values[0].substring(256, 267):""),
					                		(values[0].length()>=283?values[0].substring(268, 284):""),
					                		(values[0].length()>=295?values[0].substring(285, 296):""),
					                		(values[0].length()>=312?values[0].substring(297, 313):""),
					                		(values[0].length()>=322?values[0].substring(314, 323):""),
					                		(values[0].length()>=322?values[0].substring(314, 323):""),
					                		(values[0].length()>=336?values[0].substring(324, 337):""),
					                		(values[0].length()>=353?values[0].substring(338, 354):""),
					                		(values[0].length()>=370?values[0].substring(355, 371):""),
					                		(values[0].length()>=387?values[0].substring(372, 388):""),
					                		(values[0].length()>=397?values[0].substring(389, 398):""),
					                		(values[0].length()>=458?values[0].substring(399, 459):""),
					                		(values[0].length()>=593?values[0].substring(460, 594):""),
					                		(values[0].length()>=619?values[0].substring(595, 620):""),
					                		(values[0].length()>=621?values[0].substring(621, 622):""),
					                		(values[0].length()>=633?values[0].substring(623, 634):""),
					                		(values[0].length()>=650?values[0].substring(635, 651):""),
					                		(values[0].length()>=662?values[0].substring(652, 663):""),
					                		(values[0].length()>=679?values[0].substring(664, 680):""),
					                		(values[0].length()>=696?values[0].substring(681, 697):""),
					                		(values[0].length()>=722?values[0].substring(698, 723):""),
					                		(values[0].length()>=739?values[0].substring(724, 740):""),
					                		(values[0].length()>=770?values[0].substring(741, 771):""),
					                		(values[0].length()>=801?values[0].substring(772, 802):"")
					                		));
					                    //if(blnIsValidDate)
					                	//System.out.println(values[0].length());
			                    	//if(filter.getMerchantId().toString().equals("2") || (!filter.getMerchantId().toString().equals("2") && values[0].substring(26, 42).contains(filter.getMerchantId())))
					                    	csvPrinter.printRecord(data);
	                		}
	                	}
	                	
	                }
	            csvPrinter.flush();

	            return FileDomain.builder()
	                    .file(new ByteArrayResource(new ByteArrayInputStream(out.toByteArray()).readAllBytes()))
	                    .fileName(
	                            String.format(
	                                    "TIPPAYPRD.PPM.csv",	             
	                                    filter.getMerchantId()
	                            )
	                    )
	                    .build();
	        }
		 }
	}

	@Override
	public FileDomain exportPPMReportType3(String localFilePath, MonthlyResidualReportsFilter filter)
			throws IOException {
		 try (
				 InputStream stream = this.getClass().getResourceAsStream(SAMPLE_CSV_FILE_PATH_PPM_TYPE_3);
				 
				 CSVReader csvReader = new CSVReader(new BufferedReader(new InputStreamReader(stream)));
		        ) {
	            @SuppressWarnings("rawtypes")
				ColumnPositionMappingStrategy strategy = new ColumnPositionMappingStrategy();
	            strategy.setType(PPMCSVDataType1.class);
	            String[] memberFieldsToBindTo = {
	            		"RP Agent ID",
		                "Group",
		                "Association",
		                "Merchant ID",
		                "Record Type",
		                "Fee Category Description",
		                "Fee Category",
		                "Fee Item Name",
		                "Fee Item Hierarchy Level",
		                "Start Date",
		                "Stop Date",
		                "Frequency Flag - January",
		                "Frequency Flag - February",
		                "Frequency Flag - March",
		                "Frequency Flag - April",
		                "Frequency Flag - May",
		                "Frequency Flag - June",
		                "Frequency Flag - July",
		                "Frequency Flag - August",
		                "Frequency Flag - September",
		                "Frequency Flag - October",
		                "Frequency Flag - November",
		                "Frequency Flag - December",
		                "Include In Monthly Minimum Indicator",
		                "Reserved",
		                "Daily Fee Indicator",
		                "Billing Method",
		                "Income Rate Group Name"
		                };
	            strategy.setColumnMapping(memberFieldsToBindTo);
    

	            String[] HEADERS = {
	            		 	"RP Agent ID",
			                "Group",
			                "Association",
			                "Merchant ID",
			                "Record Type",
			                "Fee Category Description",
			                "Fee Category",
			                "Fee Item Name",
			                "Fee Item Hierarchy Level",
			                "Start Date",
			                "Stop Date",
			                "Frequency Flag - January",
			                "Frequency Flag - February",
			                "Frequency Flag - March",
			                "Frequency Flag - April",
			                "Frequency Flag - May",
			                "Frequency Flag - June",
			                "Frequency Flag - July",
			                "Frequency Flag - August",
			                "Frequency Flag - September",
			                "Frequency Flag - October",
			                "Frequency Flag - November",
			                "Frequency Flag - December",
			                "Include In Monthly Minimum Indicator",
			                "Reserved",
			                "Daily Fee Indicator",
			                "Billing Method",
			                "Income Rate Group Name"
		        };

		        final CSVFormat format = CSVFormat.Builder.create()
		        		
		                .setHeader(HEADERS)
		                .setIgnoreEmptyLines(true)
		                .build();	
		        
		        List<List<String>> records = new ArrayList<List<String>>();
		        	     
	            try (
		                ByteArrayOutputStream out = new ByteArrayOutputStream();
		                CSVPrinter csvPrinter = new CSVPrinter(new PrintWriter(out), format)
		        ) {
	          
	            	String[] values = null;
	            		            	
	            	while ((values = csvReader.readNext()) != null) {
	                	
	            		boolean blnIsValidDate = true;
	                	blnIsValidDate = validateJavaDate(values[0].substring(45, 55));
	                	String strRecordType = values[0].substring(43, 44);
	                	
	                	if(!blnIsValidDate && strRecordType.toString().equals("3"))
	                	{
	                		List<String> data = (Arrays.asList((values[0].length()>=4?values[0].substring(0, 4):""),
			                		(values[0].length()>=10?values[0].substring(5, 11):""),
			                		(values[0].length()>=17?values[0].substring(12, 18):""),
			                		(values[0].length()>=24?values[0].substring(19, 25):""),
			                		(values[0].length()>=41?" " + values[0].substring(26, 42):""),
			                		(values[0].length()>=43?values[0].substring(43, 44):""),
			                		((values[0].length()>=64)?values[0].substring(45, 65):""),
			                		((values[0].length()>=68)?" " + values[0].substring(66, 69):""),
			                		((values[0].length()>=99)?" " + values[0].substring(70, 100):""),
			                		(values[0].length()>=101?values[0].substring(101, 102):""),
			                		(values[0].length()>=112?values[0].substring(103, 113):""),
			                		(values[0].length()>=123?values[0].substring(114, 124):""),
			                		(values[0].length()>=125?values[0].substring(125, 126):""),
			                		(values[0].length()>=127?values[0].substring(127, 128):""),
			                		(values[0].length()>=129?values[0].substring(129, 130):""),
			                		(values[0].length()>=131?values[0].substring(131, 132):""),
			                		(values[0].length()>=133?values[0].substring(133, 134):""),
			                		(values[0].length()>=135?values[0].substring(135, 136):""),
			                		(values[0].length()>=137?values[0].substring(137, 138):""),
			                		(values[0].length()>=139?values[0].substring(139, 140):""),
			                		(values[0].length()>=141?values[0].substring(141, 142):""),
			                		(values[0].length()>=143?values[0].substring(143, 144):""),
			                		(values[0].length()>=145?values[0].substring(145, 146):""),
			                		(values[0].length()>=147?values[0].substring(147, 148):""),
			                		(values[0].length()>=149?values[0].substring(149, 150):""),
			                		(values[0].length()>=151?values[0].substring(151, 152):""),
			                		(values[0].length()>=153?values[0].substring(153, 154):""),
			                		(values[0].length()>=155?values[0].substring(155, 156):""),
			                		(values[0].length()>=186?values[0].substring(157, 187):"")));
			                    //if(blnIsValidDate)
			                	//System.out.println(values[0].length());
	                    	//if(filter.getMerchantId().toString().equals("2") || (!filter.getMerchantId().toString().equals("2") && values[0].substring(26, 42).contains(filter.getMerchantId())))
			                    	csvPrinter.printRecord(data);
	                	}
	                	
	                	
	                }
	            csvPrinter.flush();

		            return FileDomain.builder()
		                    .file(new ByteArrayResource(new ByteArrayInputStream(out.toByteArray()).readAllBytes()))
		                    .fileName(
		                            String.format(
		                                    "TIPPAYPRD.PPM.3.csv",	             
		                                    filter.getMerchantId()
		                            )
		                    )
		                    .build();
		        }
		 }
	}

	@SuppressWarnings("unchecked")
	@Override
	public FileDomain exportPPMReportType4(String localFilePath, MonthlyResidualReportsFilter filter)
			throws IOException {

		 try (
				 InputStream stream = this.getClass().getResourceAsStream(SAMPLE_CSV_FILE_PATH_PPM_TYPE_4);
				 
				 CSVReader csvReader = new CSVReader(new BufferedReader(new InputStreamReader(stream)));
		        ) {
	            @SuppressWarnings("rawtypes")
				ColumnPositionMappingStrategy strategy = new ColumnPositionMappingStrategy();
	            strategy.setType(PPMCSVDataType1.class);
	            String[] memberFieldsToBindTo = {
	            		"RP Agent ID",
		                "Group",
		                "Association",
		                "Merchant ID",
		                "Record Type",
		                "Fee Category Description",
		                "Fee Category",
		                "Fee Item Key 1",
		                "Fee Item Key 2",
		                "Fee Item Key 3",
		                "Fee Item Key 4",
		                "Fee Item Key 5",
		                "ERG ID",
		                "Fee Item Name",
		                "Sales Count",
		                "Sales Amount",
		                "Credit Count",
		                "Credit Amount",
		                "Interchange Fee",
		                "Count N/G/B Indicator",
		                "Amount N/G/B Indicator",
		                "Multiple Expense Volume Flag",
		                "Sales Count Applied To Expense",
		                "Sales Amount Applied To Expense",
		                "Credits Count Applied To Expense",
		                "Credits Amount Applied To  Expense",
		                "Expense Amount",
		                "Pass Through Indicator",
		                "Expense Fee Item Percent",
		                "Expense Fee Item Per Item",
		                "Cost Plus % of Fee",
		                "GL Item Code (Expense)",
		                "User Data Code (Expense)"
		                };
	            strategy.setColumnMapping(memberFieldsToBindTo);
    

	            String[] HEADERS = {
	            		"RP Agent ID",
		                "Group",
		                "Association",
		                "Merchant ID",
		                "Record Type",
		                "Fee Category Description",
		                "Fee Category",
		                "Fee Item Key 1",
		                "Fee Item Key 2",
		                "Fee Item Key 3",
		                "Fee Item Key 4",
		                "Fee Item Key 5",
		                "ERG ID",
		                "Fee Item Name",
		                "Sales Count",
		                "Sales Amount",
		                "Credit Count",
		                "Credit Amount",
		                "Interchange Fee",
		                "Count N/G/B Indicator",
		                "Amount N/G/B Indicator",
		                "Multiple Expense Volume Flag",
		                "Sales Count Applied To Expense",
		                "Sales Amount Applied To Expense",
		                "Credits Count Applied To Expense",
		                "Credits Amount Applied To  Expense",
		                "Expense Amount",
		                "Pass Through Indicator",
		                "Expense Fee Item Percent",
		                "Expense Fee Item Per Item",
		                "Cost Plus % of Fee",
		                "GL Item Code (Expense)",
		                "User Data Code (Expense)"
		        };

		        final CSVFormat format = CSVFormat.Builder.create()
		        		
		                .setHeader(HEADERS)
		                .setIgnoreEmptyLines(true)
		                .build();	
		        
		        List<List<String>> records = new ArrayList<List<String>>();
		        	     
	            try (
		                ByteArrayOutputStream out = new ByteArrayOutputStream();
		                CSVPrinter csvPrinter = new CSVPrinter(new PrintWriter(out), format)
		        ) {
	          
	            	String[] values = null;
	            	
	            	while ((values = csvReader.readNext()) != null) {
	                	boolean blnIsValidDate = true;
	                	blnIsValidDate = validateJavaDate(values[0].substring(45, 55));
	                	
	                	String strRecordType = values[0].substring(43, 44);
	                	if(!blnIsValidDate && strRecordType.contains("6"))
	                	{
	                		
	                		List<String> data = (Arrays.asList((values[0].length()>=4?values[0].substring(0, 4):""),
			                		(values[0].length()>=10?values[0].substring(5, 11):""),
			                		(values[0].length()>=17?values[0].substring(12, 18):""),
			                		(values[0].length()>=24?values[0].substring(19, 25):""),
			                		(values[0].length()>=41?" " + values[0].substring(26, 42):""),
			                		(values[0].length()>=43?values[0].substring(43, 44):""),
			                		((values[0].length()>=64)?values[0].substring(45, 65):""),
			                		((values[0].length()>=68)?" " + values[0].substring(66, 69):""),
			                		((values[0].length()>=76)?" " + values[0].substring(70, 77):""),
			                		(values[0].length()>=84?values[0].substring(78, 85):""),
			                		(values[0].length()>=92?values[0].substring(86, 93):""),
			                		(values[0].length()>=100?values[0].substring(94, 101):""),
			                		(values[0].length()>=108?values[0].substring(102, 109):""),
			                		(values[0].length()>=139?values[0].substring(110, 140):""),
			                		(values[0].length()>=170?values[0].substring(141, 171):""),
			                		(values[0].length()>=182?values[0].substring(172, 183):""),
			                		(values[0].length()>=199?values[0].substring(184, 200):""),
			                		(values[0].length()>=211?values[0].substring(201, 212):""),
			                		(values[0].length()>=228?values[0].substring(213, 229):""),
			                		(values[0].length()>=245?values[0].substring(230, 246):""),
			                		(values[0].length()>=247?values[0].substring(247, 248):""),
			                		(values[0].length()>=249?values[0].substring(249, 250):""),
			                		(values[0].length()>=251?values[0].substring(251, 252):""),
			                		(values[0].length()>=263?values[0].substring(253, 264):""),
			                		(values[0].length()>=280?values[0].substring(265, 281):""),
			                		(values[0].length()>=293?values[0].substring(282, 293):""),
			                		(values[0].length()>=309?values[0].substring(294, 310):""),
			                		(values[0].length()>=326?values[0].substring(311, 327):""),
			                		(values[0].length()>=328?values[0].substring(328, 329):""),
			                		(values[0].length()>=338?values[0].substring(330, 339):""),
			                		(values[0].length()>=352?values[0].substring(340, 353):""),
			                		(values[0].length()>=362?values[0].substring(354, 363):""),
			                		(values[0].length()>=388?values[0].substring(364, 389):""),
			                		(values[0].length()>=414?values[0].substring(390, 415):"")));
			                    //if(blnIsValidDate)
			                	//System.out.println(values[0].length());
		                    	//if(filter.getMerchantId().toString().equals("2") || (!filter.getMerchantId().toString().equals("2") && values[0].substring(26, 42).contains(filter.getMerchantId())))
			                    	csvPrinter.printRecord(data);
	                	}
	                	
	                	
	                }
	            csvPrinter.flush();

		            return FileDomain.builder()
		                    .file(new ByteArrayResource(new ByteArrayInputStream(out.toByteArray()).readAllBytes()))
		                    .fileName(
		                            String.format(
		                                    "TIPPAYPRD.PPM.4.csv",	             
		                                    filter.getMerchantId()
		                            )
		                    )
		                    .build();
		        }
		 }
	}
	
	@SuppressWarnings("unchecked")
	@Override
	public FileDomain exportPPMReportType5(String localFilePath, MonthlyResidualReportsFilter filter)
			throws IOException {

		 try (
				 InputStream stream = this.getClass().getResourceAsStream(SAMPLE_CSV_FILE_PATH_PPM_TYPE_5);
				 
				 CSVReader csvReader = new CSVReader(new BufferedReader(new InputStreamReader(stream)));
		        ) {
		            @SuppressWarnings("rawtypes")
					ColumnPositionMappingStrategy strategy = new ColumnPositionMappingStrategy();
		            strategy.setType(PPMCSVDataType5.class);
		            String[] memberFieldsToBindTo = {
		            		"Bank",
			                "RP Agent ID",
			                "Group",
			                "Association",
			                "Merchant ID",
			                "Record Type",
			                "Fee Category Description",
			                "Fee Category",
			                "Fee Item Key 1",
			                "Fee Item Key 2",
			                "Fee Item Key 3",
			                "Fee Item Key 4",
			                "Fee Item Key 5",
			                "Net Count Applied To RP Agent Expense",
			                "Net Amount Applied To RP Agent Expense",
			                "RP Agent Expense Fee Percent (Buy Rate Percent)",
			                "RP Agent Expense Fee Per Item (Buy Rate Per Item)",
			                "Apply 100% Passthrough",
			                "Share on Net Income Flag",
			                "Rev Share Percent",
			                "Rev Share Per Item",
			                "Merchant Income",
			                "RP Agent Fees Paid (Expense)",
			                "RP Agent Eligible Share Amount",
			                "Partner Share Percent",
			                "Agent Share"
			                };
		            strategy.setColumnMapping(memberFieldsToBindTo);
           

		            String[] HEADERS = {
		            		"Bank",
			                "RP Agent ID",
			                "Group",
			                "Association",
			                "Merchant ID",
			                "Record Type",
			                "Fee Category Description",
			                "Fee Category",
			                "Fee Item Key 1",
			                "Fee Item Key 2",
			                "Fee Item Key 3",
			                "Fee Item Key 4",
			                "Fee Item Key 5",
			                "Net Count Applied To RP Agent Expense",
			                "Net Amount Applied To RP Agent Expense",
			                "RP Agent Expense Fee Percent (Buy Rate Percent)",
			                "RP Agent Expense Fee Per Item (Buy Rate Per Item)",
			                "Apply 100% Passthrough",
			                "Share on Net Income Flag",
			                "Rev Share Percent",
			                "Rev Share Per Item",
			                "Merchant Income",
			                "RP Agent Fees Paid (Expense)",
			                "RP Agent Eligible Share Amount",
			                "Partner Share Percent",
			                "Agent Share"
			        };

			        final CSVFormat format = CSVFormat.Builder.create()
			                .setHeader(HEADERS)
			                .setIgnoreEmptyLines(true)
			                .build();	
			        
			        List<List<String>> records = new ArrayList<List<String>>();
			        	     
		            try (
			                ByteArrayOutputStream out = new ByteArrayOutputStream();
			                CSVPrinter csvPrinter = new CSVPrinter(new PrintWriter(out), format)
			        ) {
		          
		            	String[] values = null;
		                while ((values = csvReader.readNext()) != null) {
		                    records.add(Arrays.asList(values));
		                    if(values[0].startsWith(filter.getMerchantId()))
		                    	csvPrinter.printRecord(Arrays.asList(values));
		                }
		            csvPrinter.flush();

		            return FileDomain.builder()
		                    .file(new ByteArrayResource(new ByteArrayInputStream(out.toByteArray()).readAllBytes()))
		                    .fileName(
		                            String.format(
		                                    "TIPPAYPRD.PPM.5.csv",	             
		                                    filter.getMerchantId()
		                            )
		                    )
		                    .build();
		        }
		 }
	}
	
	@Override
	public FileDomain exportMonthlyResidualReportType2(String localFilePath, MonthlyResidualReportsFilter filter)
			throws IOException {
		 try (
				 InputStream stream = this.getClass().getResourceAsStream(SAMPLE_CSV_FILE_PATH_PPM_TYPE_2);
				 
				 CSVReader csvReader = new CSVReader(new BufferedReader(new InputStreamReader(stream)));
		        ) 
		 {
	            @SuppressWarnings("rawtypes")
				ColumnPositionMappingStrategy strategy = new ColumnPositionMappingStrategy();
	            strategy.setType(PPMCSVDataType1.class);
	            String[] memberFieldsToBindTo = {
	            		"RP Agent ID",
		                "Group",
		                "Association",
		                "Merchant ID",
		                "Record Type",
		                "Fee Category Description",
		                "Fee Category",
		                "Fee Item Key 1",
		                "Fee Item Key 2",
		                "Fee Item Key 3",
		                "Fee Item Key 4",
		                "Fee Item Key 5",
		                "Include in Volume Totals Indicator",
		                "Sales Count",
		                "Sales Amount",
		                "Credit Count",
		                "Credit Amount",
		                "Interchange Fee",
		                "Interchange Percent Rate",
		                "Interchange Per Item Rate",
		                "Multiple Fee Items Flag",
		                "Multiple Pricing Flag",
		                "Fee Item Name",
		                "Fee Item Hierarchy Level",
		                "Count N/G/B Indicator",
		                "Amount N/G/B Indicator",
		                "Include In Monthly Minimum Indicator",
		                "Pass Through Indicator",
		                "Sales Count Applied To Income",
		                "Sales Amount Applied To Income",
		                "Credits Count Applied To Income",
		                "Credits Amount Applied To Income",
		                "Pricing Fee Item Percent",
		                "Pricing Fee Item Per Item",
		                "Income Amount",
		                "Interchange Pass Through Income",
		                "Pricing Fee Item Sales Tax Amount",
		                "Reserved",
		                "Statement Description",
		                "Reserved",
		                "GL Item Code (Income)",
		                "Multiple Expense Volume Flag",
		                "Sales Count Applied To Expense",
		                "Sales Amount Applied To Expense",
		                "Credits Count Applied To Expense",
		                "Credits Amount Applied To Expense",
		                "Expense Amount",
		                "User Date Code (Income)",
		                "Fees Paid",
		                "Expense Rate Group Name",
		                "Income Rate Group Name"
		                };
	            strategy.setColumnMapping(memberFieldsToBindTo);
    

	            String[] HEADERS = {
	            		"RP Agent ID",
		                "Group",
		                "Association",
		                "Merchant ID",
		                "Record Type",
		                "Fee Category Description",
		                "Fee Category",
		                "Fee Item Key 1",
		                "Fee Item Key 2",
		                "Fee Item Key 3",
		                "Fee Item Key 4",
		                "Fee Item Key 5",
		                "Include in Volume Totals Indicator",
		                "Sales Count",
		                "Sales Amount",
		                "Credit Count",
		                "Credit Amount",
		                "Interchange Fee",
		                "Interchange Percent Rate",
		                "Interchange Per Item Rate",
		                "Multiple Fee Items Flag",
		                "Multiple Pricing Flag",
		                "Fee Item Name",
		                "Fee Item Hierarchy Level",
		                "Count N/G/B Indicator",
		                "Amount N/G/B Indicator",
		                "Include In Monthly Minimum Indicator",
		                "Pass Through Indicator",
		                "Sales Count Applied To Income",
		                "Sales Amount Applied To Income",
		                "Credits Count Applied To Income",
		                "Credits Amount Applied To Income",
		                "Pricing Fee Item Percent",
		                "Pricing Fee Item Per Item",
		                "Income Amount",
		                "Interchange Pass Through Income",
		                "Pricing Fee Item Sales Tax Amount",
		                "Reserved",
		                "Statement Description",
		                "Reserved",
		                "GL Item Code (Income)",
		                "Multiple Expense Volume Flag",
		                "Sales Count Applied To Expense",
		                "Sales Amount Applied To Expense",
		                "Credits Count Applied To Expense",
		                "Credits Amount Applied To Expense",
		                "Expense Amount",
		                "User Date Code (Income)",
		                "Fees Paid",
		                "Expense Rate Group Name",
		                "Income Rate Group Name"
		        };

		        final CSVFormat format = CSVFormat.Builder.create()
		        		
		                .setHeader(HEADERS)
		                .setIgnoreEmptyLines(true)
		                .build();	
		        
		        List<List<String>> records = new ArrayList<List<String>>();
		        	     
	            try (
		                ByteArrayOutputStream out = new ByteArrayOutputStream();
		                CSVPrinter csvPrinter = new CSVPrinter(new PrintWriter(out), format)
		        ) {
	          
	            	String[] values = null;
	            	boolean blnIsNestedHeaderCreated = false;
	            	
	            	
	            	while ((values = csvReader.readNext()) != null) {
	                	int iStart = 0; int iEnd = 0; boolean blnIsValidDate = true;
	                	blnIsValidDate = validateJavaDate(values[0].substring(45, 55));
	                	
	                	if(!blnIsValidDate)
	                	{
	                		String strRecordType = values[0].substring(43, 44);
	                		
	                		if(strRecordType.contains("2"))
	                		{
	                			//System.out.println("Merchant Id - "+ filter.getMerchantId() + "- value");
	                			
			                    	List<String> data = (Arrays.asList((values[0].length()>=4?values[0].substring(0, 4):""),
					                		(values[0].length()>=10?values[0].substring(5, 11):""),
					                		(values[0].length()>=17?values[0].substring(12, 18):""),
					                		(values[0].length()>=24?values[0].substring(19, 25):""),
					                		(values[0].length()>=41?" " + values[0].substring(26, 42):""),
					                		(values[0].length()>=43?values[0].substring(43, 44):""),
					                		((values[0].length()>=64)?values[0].substring(45, 65):""),
					                		((values[0].length()>=68)?" " + values[0].substring(66, 69):""),
					                		((values[0].length()>=76)?" " + values[0].substring(70, 77):""),
					                		(values[0].length()>=84?values[0].substring(78, 85):""),
					                		(values[0].length()>=92?values[0].substring(86, 93):""),
					                		(values[0].length()>=100?values[0].substring(94, 101):""),
					                		(values[0].length()>=108?values[0].substring(102, 109):""),
					                		(values[0].length()>=110?values[0].substring(110, 111):""),
					                		(values[0].length()>=122?values[0].substring(112, 123):""),
					                		(values[0].length()>=139?values[0].substring(124, 140):""),
					                		(values[0].length()>=151?values[0].substring(141, 152):""),
					                		(values[0].length()>=168?values[0].substring(153, 169):""),
					                		(values[0].length()>=185?values[0].substring(170, 186):""),
					                		(values[0].length()>=195?values[0].substring(187, 196):""),
					                		(values[0].length()>=209?values[0].substring(197, 210):""),
					                		(values[0].length()>=211?values[0].substring(211, 212):""),
					                		(values[0].length()>=213?values[0].substring(213, 214):""),
					                		(values[0].length()>=215?values[0].substring(215, 245):""),
					                		(values[0].length()>=246?values[0].substring(246, 247):""),
					                		(values[0].length()>=248?values[0].substring(248, 249):""),
					                		(values[0].length()>=250?values[0].substring(250, 251):""),
					                		(values[0].length()>=252?values[0].substring(252, 253):""),
					                		(values[0].length()>=254?values[0].substring(254, 255):""),
					                		(values[0].length()>=266?values[0].substring(256, 267):""),
					                		(values[0].length()>=283?values[0].substring(268, 284):""),
					                		(values[0].length()>=295?values[0].substring(285, 296):""),
					                		(values[0].length()>=312?values[0].substring(297, 313):""),
					                		(values[0].length()>=322?values[0].substring(314, 323):""),
					                		(values[0].length()>=322?values[0].substring(314, 323):""),
					                		(values[0].length()>=336?values[0].substring(324, 337):""),
					                		(values[0].length()>=353?values[0].substring(338, 354):""),
					                		(values[0].length()>=370?values[0].substring(355, 371):""),
					                		(values[0].length()>=387?values[0].substring(372, 388):""),
					                		(values[0].length()>=397?values[0].substring(389, 398):""),
					                		(values[0].length()>=458?values[0].substring(399, 459):""),
					                		(values[0].length()>=593?values[0].substring(460, 594):""),
					                		(values[0].length()>=619?values[0].substring(595, 620):""),
					                		(values[0].length()>=621?values[0].substring(621, 622):""),
					                		(values[0].length()>=633?values[0].substring(623, 634):""),
					                		(values[0].length()>=650?values[0].substring(635, 651):""),
					                		(values[0].length()>=662?values[0].substring(652, 663):""),
					                		(values[0].length()>=679?values[0].substring(664, 680):""),
					                		(values[0].length()>=696?values[0].substring(681, 697):""),
					                		(values[0].length()>=722?values[0].substring(698, 723):""),
					                		(values[0].length()>=739?values[0].substring(724, 740):""),
					                		(values[0].length()>=770?values[0].substring(741, 771):""),
					                		(values[0].length()>=801?values[0].substring(772, 802):"")
					                		));
					                    //if(blnIsValidDate)
					                	//System.out.println(values[0].length());
			                    	//if(filter.getMerchantId().toString().equals("2") || (!filter.getMerchantId().toString().equals("2") && values[0].substring(26, 42).contains(filter.getMerchantId())))
					                    	csvPrinter.printRecord(data);
	                		}
	                	}
	                	
	                }
	            csvPrinter.flush();

	            return FileDomain.builder()
	                    .file(new ByteArrayResource(new ByteArrayInputStream(out.toByteArray()).readAllBytes()))
	                    .fileName(
	                            String.format(
	                                    "TIPPAYPRD.MONTHLY_RESIDUAL.2.csv",	             
	                                    filter.getMerchantId()
	                            )
	                    )
	                    .build();
	        }
		 }
	}
	
	@Override
	public FileDomain exportMonthlyResidualReportType3(String localFilePath, MonthlyResidualReportsFilter filter)
			throws IOException {
		 try (
				 InputStream stream = this.getClass().getResourceAsStream(SAMPLE_CSV_FILE_PATH_PPM_TYPE_3);
				 
				 CSVReader csvReader = new CSVReader(new BufferedReader(new InputStreamReader(stream)));
		        ) {
	            @SuppressWarnings("rawtypes")
				ColumnPositionMappingStrategy strategy = new ColumnPositionMappingStrategy();
	            strategy.setType(PPMCSVDataType1.class);
	            String[] memberFieldsToBindTo = {
	            		"RP Agent ID",
		                "Group",
		                "Association",
		                "Merchant ID",
		                "Record Type",
		                "Fee Category Description",
		                "Fee Category",
		                "Fee Item Name",
		                "Fee Item Hierarchy Level",
		                "Start Date",
		                "Stop Date",
		                "Frequency Flag - January",
		                "Frequency Flag - February",
		                "Frequency Flag - March",
		                "Frequency Flag - April",
		                "Frequency Flag - May",
		                "Frequency Flag - June",
		                "Frequency Flag - July",
		                "Frequency Flag - August",
		                "Frequency Flag - September",
		                "Frequency Flag - October",
		                "Frequency Flag - November",
		                "Frequency Flag - December",
		                "Include In Monthly Minimum Indicator",
		                "Reserved",
		                "Daily Fee Indicator",
		                "Billing Method",
		                "Income Rate Group Name"
		                };
	            strategy.setColumnMapping(memberFieldsToBindTo);
    

	            String[] HEADERS = {
	            		 	"RP Agent ID",
			                "Group",
			                "Association",
			                "Merchant ID",
			                "Record Type",
			                "Fee Category Description",
			                "Fee Category",
			                "Fee Item Name",
			                "Fee Item Hierarchy Level",
			                "Start Date",
			                "Stop Date",
			                "Frequency Flag - January",
			                "Frequency Flag - February",
			                "Frequency Flag - March",
			                "Frequency Flag - April",
			                "Frequency Flag - May",
			                "Frequency Flag - June",
			                "Frequency Flag - July",
			                "Frequency Flag - August",
			                "Frequency Flag - September",
			                "Frequency Flag - October",
			                "Frequency Flag - November",
			                "Frequency Flag - December",
			                "Include In Monthly Minimum Indicator",
			                "Reserved",
			                "Daily Fee Indicator",
			                "Billing Method",
			                "Income Rate Group Name"
		        };

		        final CSVFormat format = CSVFormat.Builder.create()
		        		
		                .setHeader(HEADERS)
		                .setIgnoreEmptyLines(true)
		                .build();	
		        
		        List<List<String>> records = new ArrayList<List<String>>();
		        	     
	            try (
		                ByteArrayOutputStream out = new ByteArrayOutputStream();
		                CSVPrinter csvPrinter = new CSVPrinter(new PrintWriter(out), format)
		        ) {
	          
	            	String[] values = null;
	            		            	
	            	while ((values = csvReader.readNext()) != null) {
	                	
	            		boolean blnIsValidDate = true;
	                	blnIsValidDate = validateJavaDate(values[0].substring(45, 55));
	                	String strRecordType = values[0].substring(43, 44);
	                	
	                	if(!blnIsValidDate && strRecordType.toString().equals("3"))
	                	{
	                		List<String> data = (Arrays.asList((values[0].length()>=4?values[0].substring(0, 4):""),
			                		(values[0].length()>=10?values[0].substring(5, 11):""),
			                		(values[0].length()>=17?values[0].substring(12, 18):""),
			                		(values[0].length()>=24?values[0].substring(19, 25):""),
			                		(values[0].length()>=41?" " + values[0].substring(26, 42):""),
			                		(values[0].length()>=43?values[0].substring(43, 44):""),
			                		((values[0].length()>=64)?values[0].substring(45, 65):""),
			                		((values[0].length()>=68)?" " + values[0].substring(66, 69):""),
			                		((values[0].length()>=99)?" " + values[0].substring(70, 100):""),
			                		(values[0].length()>=101?values[0].substring(101, 102):""),
			                		(values[0].length()>=112?values[0].substring(103, 113):""),
			                		(values[0].length()>=123?values[0].substring(114, 124):""),
			                		(values[0].length()>=125?values[0].substring(125, 126):""),
			                		(values[0].length()>=127?values[0].substring(127, 128):""),
			                		(values[0].length()>=129?values[0].substring(129, 130):""),
			                		(values[0].length()>=131?values[0].substring(131, 132):""),
			                		(values[0].length()>=133?values[0].substring(133, 134):""),
			                		(values[0].length()>=135?values[0].substring(135, 136):""),
			                		(values[0].length()>=137?values[0].substring(137, 138):""),
			                		(values[0].length()>=139?values[0].substring(139, 140):""),
			                		(values[0].length()>=141?values[0].substring(141, 142):""),
			                		(values[0].length()>=143?values[0].substring(143, 144):""),
			                		(values[0].length()>=145?values[0].substring(145, 146):""),
			                		(values[0].length()>=147?values[0].substring(147, 148):""),
			                		(values[0].length()>=149?values[0].substring(149, 150):""),
			                		(values[0].length()>=151?values[0].substring(151, 152):""),
			                		(values[0].length()>=153?values[0].substring(153, 154):""),
			                		(values[0].length()>=155?values[0].substring(155, 156):""),
			                		(values[0].length()>=186?values[0].substring(157, 187):"")));
			                    //if(blnIsValidDate)
			                	//System.out.println(values[0].length());
	                    	//if(filter.getMerchantId().toString().equals("2") || (!filter.getMerchantId().toString().equals("2") && values[0].substring(26, 42).contains(filter.getMerchantId())))
			                    	csvPrinter.printRecord(data);
	                	}
	                	
	                	
	                }
	            csvPrinter.flush();

		            return FileDomain.builder()
		                    .file(new ByteArrayResource(new ByteArrayInputStream(out.toByteArray()).readAllBytes()))
		                    .fileName(
		                            String.format(
		                                    "TIPPAYPRD.MONTHLY_RESIDUAL.3.csv",	             
		                                    filter.getMerchantId()
		                            )
		                    )
		                    .build();
		        }
		 }
	}
	
	@Override
	public FileDomain exportMonthlyResidualReportType4(String localFilePath, MonthlyResidualReportsFilter filter)
			throws IOException {
		 try (
				 InputStream stream = this.getClass().getResourceAsStream(SAMPLE_CSV_FILE_PATH_PPM_TYPE_4);
				 
				 CSVReader csvReader = new CSVReader(new BufferedReader(new InputStreamReader(stream)));
		        ) {
	            @SuppressWarnings("rawtypes")
				ColumnPositionMappingStrategy strategy = new ColumnPositionMappingStrategy();
	            strategy.setType(PPMCSVDataType1.class);
	            String[] memberFieldsToBindTo = {
	            		"RP Agent ID",
		                "Group",
		                "Association",
		                "Merchant ID",
		                "Record Type",
		                "Fee Category Description",
		                "Fee Category",
		                "Fee Item Key 1",
		                "Fee Item Key 2",
		                "Fee Item Key 3",
		                "Fee Item Key 4",
		                "Fee Item Key 5",
		                "ERG ID",
		                "Fee Item Name",
		                "Sales Count",
		                "Sales Amount",
		                "Credit Count",
		                "Credit Amount",
		                "Interchange Fee",
		                "Count N/G/B Indicator",
		                "Amount N/G/B Indicator",
		                "Multiple Expense Volume Flag",
		                "Sales Count Applied To Expense",
		                "Sales Amount Applied To Expense",
		                "Credits Count Applied To Expense",
		                "Credits Amount Applied To  Expense",
		                "Expense Amount",
		                "Pass Through Indicator",
		                "Expense Fee Item Percent",
		                "Expense Fee Item Per Item",
		                "Cost Plus % of Fee",
		                "GL Item Code (Expense)",
		                "User Data Code (Expense)"
		                };
	            strategy.setColumnMapping(memberFieldsToBindTo);
   

	            String[] HEADERS = {
	            		"RP Agent ID",
		                "Group",
		                "Association",
		                "Merchant ID",
		                "Record Type",
		                "Fee Category Description",
		                "Fee Category",
		                "Fee Item Key 1",
		                "Fee Item Key 2",
		                "Fee Item Key 3",
		                "Fee Item Key 4",
		                "Fee Item Key 5",
		                "ERG ID",
		                "Fee Item Name",
		                "Sales Count",
		                "Sales Amount",
		                "Credit Count",
		                "Credit Amount",
		                "Interchange Fee",
		                "Count N/G/B Indicator",
		                "Amount N/G/B Indicator",
		                "Multiple Expense Volume Flag",
		                "Sales Count Applied To Expense",
		                "Sales Amount Applied To Expense",
		                "Credits Count Applied To Expense",
		                "Credits Amount Applied To  Expense",
		                "Expense Amount",
		                "Pass Through Indicator",
		                "Expense Fee Item Percent",
		                "Expense Fee Item Per Item",
		                "Cost Plus % of Fee",
		                "GL Item Code (Expense)",
		                "User Data Code (Expense)"
		        };

		        final CSVFormat format = CSVFormat.Builder.create()
		        		
		                .setHeader(HEADERS)
		                .setIgnoreEmptyLines(true)
		                .build();	
		        
		        List<List<String>> records = new ArrayList<List<String>>();
		        	     
	            try (
		                ByteArrayOutputStream out = new ByteArrayOutputStream();
		                CSVPrinter csvPrinter = new CSVPrinter(new PrintWriter(out), format)
		        ) {
	          
	            	String[] values = null;
	            	
	            	while ((values = csvReader.readNext()) != null) {
	                	boolean blnIsValidDate = true;
	                	blnIsValidDate = validateJavaDate(values[0].substring(45, 55));
	                	
	                	String strRecordType = values[0].substring(43, 44);
	                	if(!blnIsValidDate && strRecordType.contains("6"))
	                	{
	                		
	                		List<String> data = (Arrays.asList((values[0].length()>=4?values[0].substring(0, 4):""),
			                		(values[0].length()>=10?values[0].substring(5, 11):""),
			                		(values[0].length()>=17?values[0].substring(12, 18):""),
			                		(values[0].length()>=24?values[0].substring(19, 25):""),
			                		(values[0].length()>=41?" " + values[0].substring(26, 42):""),
			                		(values[0].length()>=43?values[0].substring(43, 44):""),
			                		((values[0].length()>=64)?values[0].substring(45, 65):""),
			                		((values[0].length()>=68)?" " + values[0].substring(66, 69):""),
			                		((values[0].length()>=76)?" " + values[0].substring(70, 77):""),
			                		(values[0].length()>=84?values[0].substring(78, 85):""),
			                		(values[0].length()>=92?values[0].substring(86, 93):""),
			                		(values[0].length()>=100?values[0].substring(94, 101):""),
			                		(values[0].length()>=108?values[0].substring(102, 109):""),
			                		(values[0].length()>=139?values[0].substring(110, 140):""),
			                		(values[0].length()>=170?values[0].substring(141, 171):""),
			                		(values[0].length()>=182?values[0].substring(172, 183):""),
			                		(values[0].length()>=199?values[0].substring(184, 200):""),
			                		(values[0].length()>=211?values[0].substring(201, 212):""),
			                		(values[0].length()>=228?values[0].substring(213, 229):""),
			                		(values[0].length()>=245?values[0].substring(230, 246):""),
			                		(values[0].length()>=247?values[0].substring(247, 248):""),
			                		(values[0].length()>=249?values[0].substring(249, 250):""),
			                		(values[0].length()>=251?values[0].substring(251, 252):""),
			                		(values[0].length()>=263?values[0].substring(253, 264):""),
			                		(values[0].length()>=280?values[0].substring(265, 281):""),
			                		(values[0].length()>=293?values[0].substring(282, 293):""),
			                		(values[0].length()>=309?values[0].substring(294, 310):""),
			                		(values[0].length()>=326?values[0].substring(311, 327):""),
			                		(values[0].length()>=328?values[0].substring(328, 329):""),
			                		(values[0].length()>=338?values[0].substring(330, 339):""),
			                		(values[0].length()>=352?values[0].substring(340, 353):""),
			                		(values[0].length()>=362?values[0].substring(354, 363):""),
			                		(values[0].length()>=388?values[0].substring(364, 389):""),
			                		(values[0].length()>=414?values[0].substring(390, 415):"")));
			                    //if(blnIsValidDate)
			                	//System.out.println(values[0].length());
		                    	//if(filter.getMerchantId().toString().equals("2") || (!filter.getMerchantId().toString().equals("2") && values[0].substring(26, 42).contains(filter.getMerchantId())))
			                    	csvPrinter.printRecord(data);
	                	}
	                	
	                	
	                }
	            csvPrinter.flush();

		            return FileDomain.builder()
		                    .file(new ByteArrayResource(new ByteArrayInputStream(out.toByteArray()).readAllBytes()))
		                    .fileName(
		                            String.format(
		                                    "TIPPAYPRD.MONTHLY_RESIDUAL.4.csv",	             
		                                    filter.getMerchantId()
		                            )
		                    )
		                    .build();
		        }
		 }
	}

	@Override
	public FileDomain minimumDiscountReport(String localFilePath, MonthlyResidualReportsFilter filter)
			throws IOException {
		try (
				 InputStream stream = this.getClass().getResourceAsStream(localFilePath);
				 
				 CSVReader csvReader = new CSVReader(new BufferedReader(new InputStreamReader(stream)));
		        ) {
	            @SuppressWarnings("rawtypes")
				ColumnPositionMappingStrategy strategy = new ColumnPositionMappingStrategy();
	            strategy.setType(PPMCSVDataType1.class);
	            String[] memberFieldsToBindTo = {
	            		"Bank",
		                "Group",
		                "Assoc",
		                "Merchant Number",
		                "Merchant DBA",
		                "Minimum Discount",
		                "Min Discount GL Item Code",
		                "Min Discount User Data Code",
		                "Start Date",
		                "Stop Date",
		                "Bill Method"
		                };
	            strategy.setColumnMapping(memberFieldsToBindTo);
  

	            String[] HEADERS = {
	            		"Bank",
		                "Group",
		                "Assoc",
		                "Merchant Number",
		                "Merchant DBA",
		                "Minimum Discount",
		                "Min Discount GL Item Code",
		                "Min Discount User Data Code",
		                "Start Date",
		                "Stop Date",
		                "Bill Method"
		        };

	            
   final CSVFormat format = CSVFormat.Builder.create()
		        		
		                .setHeader(HEADERS)
		                .setIgnoreEmptyLines(true)
		                .build();	

List<List<String>> records = new ArrayList<List<String>>();
	     
try (
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        CSVPrinter csvPrinter = new CSVPrinter(new PrintWriter(out), format)
) {

	String[] values = null;
	List<String> dataType1 = new ArrayList<String>();
	List<String> dataType2 = new ArrayList<String>();
	List<String> data = new ArrayList<String>();
	boolean blnType2Added = false;
	boolean blnType3Added = false;
	recordsList.clear();
	while ((values = csvReader.readNext()) != null) {
    	boolean blnIsValidDate = true;
    	blnIsValidDate = validateJavaDate(values[0].substring(45, 55));
    	
    	
    	if(blnIsValidDate)
    	{
    		
    		
    		//System.out.println("Bank - "+ values[0].substring(0, 4) + "- value");
    		dataType1 = new ArrayList<String>();
    		dataType1.add((values[0].length()>=4?values[0].substring(0, 4):""));
    		dataType1.add((values[0].length()>=87?values[0].substring(56, 88):""));
    		dataType1.add((values[0].length()>=188?values[0].substring(173, 189):""));
    	
    	}
    	else	
    	{
    		String strRecordType = values[0].substring(43, 44);
    		
    		
    		if(strRecordType.contains("3"))
    		{
    			//System.out.println("record type 3 - "+ values[0].substring(155, 156) + "- value");
    			
    			if(blnType3Added)
    			{
    				/*recordsMap.put("BankId", data.get(0).toString());
    				recordsMap.put("GroupId", data.get(1).toString());
    				recordsMap.put("AssociationId", data.get(2).toString());
    				recordsMap.put("MerchantNumber", data.get(3).toString());
    				recordsMap.put("MerchantDBA", data.get(4).toString());
    				recordsMap.put("MinimumDiscount", data.get(5).toString());
    				recordsMap.put("MinDiscountGLItemCode", data.get(6).toString());
    				recordsMap.put("MinDiscountUserDataCode", data.get(7).toString());
    				recordsMap.put("StartDate", data.get(8).toString());
    				recordsMap.put("StopDate", data.get(9).toString());
    				recordsMap.put("BillingMethod", data.get(10).toString());
    			
    				List<Map> mapList = new ArrayList<Map>();
    				mapList.add(recordsMap); */
    				if(filter.getMerchantId().toString().equals("0") || (!filter.getMerchantId().toString().equals("0") && data.get(3).toString().contains(filter.getMerchantId())))
    				{
    					recordsMap = new HashMap<>();
        				recordsMap.clear();
        				recordsMap.put("BankId", data.get(0).toString().trim());
        				recordsMap.put("GroupId", data.get(1).toString().trim());
        				recordsMap.put("AssociationId", data.get(2).toString().trim());
        				recordsMap.put("MerchantNumber", data.get(3).toString().trim());
        				recordsMap.put("MerchantDBA", data.get(4).toString().trim());
        				recordsMap.put("MinimumDiscount", data.get(5).toString().trim());
        				recordsMap.put("MinDiscountGLItemCode", data.get(6).toString().trim());
        				recordsMap.put("MinDiscountUserDataCode", data.get(7).toString().trim());
        				recordsMap.put("StartDate", data.get(8).toString().trim());
        				recordsMap.put("StopDate", data.get(9).toString().trim());
        				recordsMap.put("BillingMethod", data.get(10).toString().trim());
        			
        				//List<Map> mapList = new ArrayList<Map>();
        				//mapList.add(recordsMap);  				                  
        				recordsList.add(recordsMap); 		
    				}
    				
    				List<String> record = (Arrays.asList(
                    		data.get(0).toString(),
                    		data.get(1).toString(),
                    		data.get(2).toString(),
                    		data.get(3).toString(),
                    		data.get(4).toString(),
                    		data.get(5).toString(),
                    		data.get(6).toString(),
                    		data.get(7).toString(),
                    		data.get(8).toString(),
                    		data.get(9).toString(),
                    		data.get(10).toString()));	
    				if(filter.getMerchantId().toString().equals("0") || (!filter.getMerchantId().toString().equals("0") && data.get(3).toString().contains(filter.getMerchantId())))
            		csvPrinter.printRecord(record);	
    				//recordsList.add(mapList);
    				//recordList = record;
            		blnType3Added = false;
    			}
    			
    			data = new ArrayList<String>();
    			data.add((values[0].length()>=4?values[0].substring(0, 4):""));
    			data.add((values[0].length()>=17?values[0].substring(12, 18):""));
        		data.add((values[0].length()>=24?values[0].substring(19, 25):""));
        		data.add((values[0].length()>=41?values[0].substring(26, 42):""));
        		if(dataType1.contains(values[0].substring(0, 4)))
        		{
        			data.add(dataType1.get(1).toString());
        			data.add(dataType1.get(2).toString());
        		}
        		data.add("");
        		data.add("");
    			data.add((values[0].length()>=112?values[0].substring(103, 113):""));
        		data.add((values[0].length()>=123?values[0].substring(114, 124):"12-31-9999"));
        		
        		String strBillMethod = (values[0].length()>=155?values[0].substring(155, 156):"");
        		if(strBillMethod.equals("D"))
        		{
        			strBillMethod = "Debit";
        		}
        		else if(strBillMethod.equals("R"))
        		{
        			strBillMethod = "Remit";
        		}
        		else
        		{
        			strBillMethod = "";
        		}
        		data.add(strBillMethod);
        		
        		blnType3Added = true;
              
    		}
    		   		
    	
    	}
    		
	}

csvPrinter.flush();

    return FileDomain.builder()
            .file(new ByteArrayResource(new ByteArrayInputStream(out.toByteArray()).readAllBytes()))
            .fileName(
                    String.format(
                            "Minimum_Discount_Report.csv",	             
                            filter.getMerchantId()
                    )
            )
            .build();
		}

	}
}
	

	@Override
	public FileDomain feeItemDetailReport(String localFilePath, MonthlyResidualReportsFilter filter)
			throws IOException {

		try (
				 InputStream stream = this.getClass().getResourceAsStream(localFilePath);
				 
				 CSVReader csvReader = new CSVReader(new BufferedReader(new InputStreamReader(stream)));
				 CSVReader csvReaderRepeat = new CSVReader(new BufferedReader(new InputStreamReader(stream)));  
		        ) {
	            @SuppressWarnings("rawtypes")
				ColumnPositionMappingStrategy strategy = new ColumnPositionMappingStrategy();
	            strategy.setType(PPMCSVDataType1.class);
	            String[] memberFieldsToBindTo = {
	            		"Bank",
		                "Group",
		                "Assoc",
		                "Merchant Number",
		                "Hierarchy Level",
		                "DBA Name",
		                "Pricing Template ID",
		                "Template Type",
		                "Fee Item Location",
		                "Fee Category",
		                "Fee Item Name",
		                "FIM Fee Mode",
		                "Start Date",
		                "Stop Date",
		                "Percent",
		                "Per Item($)",
		                "Exp Per Item($)",
		                "Qual(%)",
		                "Qual Per Item($)",
		                "Tier Type",
		                "Pass Through Rate Method",
		                "DDA Account Type",
		                "DDA Number",
		                "Transit Routing Number",
		                "Incl in Mnth Min",
		                "Daily Fee Indicator",
		                "Billing Method",
		                "Name & Addr Usage Cd",
		                "Display Count on Stmt",
		                "Display Amt on Stmt",
		                "Display Rates on Stmt",
		                "Display Detail on Stmt",
		                "Comb Fee on Rate Change",
		                "Stmt Fee Descript",
		                "Combine Code",
		                "GL Item Code",
		                "User Data Code",
		                "Count N/G/B",
		                "Amount N/G/B"
		                };
	            strategy.setColumnMapping(memberFieldsToBindTo);


	            String[] HEADERS = {
	            		"Bank",
		                "Group",
		                "Assoc",
		                "Merchant Number",
		                "Hierarchy Level",
		                "DBA Name",
		                "Pricing Template ID",
		                "Template Type",
		                "Fee Item Location",
		                "Fee Category",
		                "Fee Item Name",
		                "FIM Fee Mode",
		                "Start Date",
		                "Stop Date",
		                "Percent",
		                "Per Item($)",
		                "Exp Per Item($)",
		                "Qual(%)",
		                "Qual Per Item($)",
		                "Tier Type",
		                "Pass Through Rate Method",
		                "DDA Account Type",
		                "DDA Number",
		                "Transit Routing Number",
		                "Incl in Mnth Min",
		                "Daily Fee Indicator",
		                "Billing Method",
		                "Name & Addr Usage Cd",
		                "Display Count on Stmt",
		                "Display Amt on Stmt",
		                "Display Rates on Stmt",
		                "Display Detail on Stmt",
		                "Comb Fee on Rate Change",
		                "Stmt Fee Descript",
		                "Combine Code",
		                "Frequency",
		                "GL Item Code",
		                "User Data Code",
		                "Count N/G/B",
		                "Amount N/G/B"
		        };

	            
 final CSVFormat format = CSVFormat.Builder.create()
		        		
		                .setHeader(HEADERS)
		                .setIgnoreEmptyLines(true)
		                .build();	

List<List<String>> records = new ArrayList<List<String>>();
String strMerchantName = "";
Double dcPerItem = 0.0;
Double dcExpPerItem = 0.0;
Double dcQualPerItem = 0.0;
	     
try (
      ByteArrayOutputStream out = new ByteArrayOutputStream();
      CSVPrinter csvPrinter = new CSVPrinter(new PrintWriter(out), format)
) {

	String[] values = null;
	String[] valuesRepeat = null;
	
	Dictionary<String, String> dictReportType3= new Hashtable<>();
	Dictionary<String, String> dictReportType6= new Hashtable<>();
	
	List<String> dataType1 = new ArrayList<String>();
	List<String> dataType2 = new ArrayList<String>();
	List<String> data = new ArrayList<String>();
	boolean blnType2Added = false;
	boolean blnType3Added = false;
	recordsListFeeItemDetail.clear();
	
	/*while ((valuesRepeat = csvReaderRepeat.readNext()) != null) {
		
		String strRecordType = valuesRepeat[0].substring(43, 44);
		
		if(strRecordType == "3")
		{
			//(values[0].length()>=4?values[0].substring(0, 4):"")
			if(dictReportType3.get(valuesRepeat[0].substring(26, 42)) == null)
				dictReportType3.put(valuesRepeat[0].substring(26, 42), valuesRepeat[0].substring(152, 154));
		}
		
	}
	*/
	while ((values = csvReader.readNext()) != null) {
  	boolean blnIsValidDate = true;
  	blnIsValidDate = validateJavaDate(values[0].substring(45, 55));
  	
  	
  	if(blnIsValidDate)
  	{
  		
  		//System.out.println("Array length - "+ data.size() + "- value");
  				
  		//System.out.println("Bank - "+ values[0].substring(0, 4) + "- value");
  		dataType1 = new ArrayList<String>();
  		dataType1.add((values[0].length()>=4?values[0].substring(0, 4):""));
  		dataType1.add((values[0].length()>=87?values[0].substring(56, 88):""));  		  	
  	}
  	else	
  	{
  		String strRecordType = values[0].substring(43, 44);
  		
  		if(strRecordType.contains("2")) // was 2
  		{
  			if(blnType2Added)
  			{
  				if(filter.getMerchantId().toString().equals("0") || (!filter.getMerchantId().toString().equals("0") && data.get(3).toString().contains(filter.getMerchantId())))
  				{
  					recordsMapFeeItemDetail = new HashMap<>();
  	 				recordsMapFeeItemDetail.clear();
  	 				recordsMapFeeItemDetail.put("BankId", data.get(0).toString().trim());
  	 				recordsMapFeeItemDetail.put("GroupId", data.get(1).toString().trim());
  	 				recordsMapFeeItemDetail.put("AssociationId", data.get(2).toString().trim());
  	 				recordsMapFeeItemDetail.put("MerchantNumber", data.get(3).toString().trim());
  	 				recordsMapFeeItemDetail.put("HierarchyLevel", data.get(4).toString().trim());
  	 				recordsMapFeeItemDetail.put("DBAName", data.get(5).toString().trim());
  	 				recordsMapFeeItemDetail.put("PricingTemplateId", data.get(6).toString().trim());
  	 				recordsMapFeeItemDetail.put("TemplateType", data.get(7).toString().trim());
  	 				recordsMapFeeItemDetail.put("FeeItemLocation", data.get(8).toString().trim());
  	 				recordsMapFeeItemDetail.put("FeeCategory", data.get(9).toString().trim());
  	 				recordsMapFeeItemDetail.put("FeeItemName", data.get(10).toString().trim());
  	 				recordsMapFeeItemDetail.put("FIMFeeMode", data.get(11).toString().trim());
  	 				recordsMapFeeItemDetail.put("StartDate", data.get(12).toString().trim());
  	 				recordsMapFeeItemDetail.put("StopDate", data.get(13).toString().trim());
  	 				recordsMapFeeItemDetail.put("Percent", data.get(14).toString().trim());
  	 				recordsMapFeeItemDetail.put("PerItemDollar", data.get(15).toString().trim());
  					recordsMapFeeItemDetail.put("ExpPerItemDollar", data.get(16).toString().trim());
  					recordsMapFeeItemDetail.put("QualPercentage", data.get(17).toString().trim());
  					recordsMapFeeItemDetail.put("QualPerItemDollar", data.get(18).toString().trim());
  					recordsMapFeeItemDetail.put("TierType", data.get(19).toString().trim());
  					recordsMapFeeItemDetail.put("PassThroughRateMethod", data.get(20).toString().trim());
  					
  					recordsMapFeeItemDetail.put("DDAAccountType", data.get(21).toString().trim());
  	 				recordsMapFeeItemDetail.put("DDANumber", data.get(22).toString().trim());
  	 				recordsMapFeeItemDetail.put("TransitRoutingNo", data.get(23).toString().trim());
  	 				recordsMapFeeItemDetail.put("IncludedInMnthlyMin", data.get(24).toString().trim());
  	 				recordsMapFeeItemDetail.put("DailyFeeIndicator", data.get(25).toString().trim());
  	 				recordsMapFeeItemDetail.put("BillingMethod", data.get(26).toString().trim());
  	 				recordsMapFeeItemDetail.put("NameAddressUsageCd", data.get(27).toString().trim());
  	 				recordsMapFeeItemDetail.put("DisplayCountOnStmt", data.get(28).toString().trim());
  	 				recordsMapFeeItemDetail.put("DisplayAmtOnStmt", data.get(29).toString().trim());
  	 				recordsMapFeeItemDetail.put("DisplayRateOnStmt", data.get(30).toString().trim());
  	 				recordsMapFeeItemDetail.put("DisplayDetailOnStmt", data.get(31).toString().trim());
  	 				recordsMapFeeItemDetail.put("CombFeeOnRateChange", data.get(32).toString().trim());
  	 				recordsMapFeeItemDetail.put("StmtFeeDescription", data.get(33).toString().trim());
  	 				recordsMapFeeItemDetail.put("CombineCode", data.get(34).toString().trim());
  	 				recordsMapFeeItemDetail.put("Frequency", data.get(35).toString().trim());
  	 				recordsMapFeeItemDetail.put("GLItemCode", data.get(36).toString().trim());
  					recordsMapFeeItemDetail.put("UserDataCode", data.get(37).toString().trim());
  					recordsMapFeeItemDetail.put("CountNGB", data.get(38).toString().trim());
  					recordsMapFeeItemDetail.put("amountNGB", data.get(39).toString().trim());
  					
  					recordsListFeeItemDetail.add(recordsMapFeeItemDetail); 
  				}
  				
				
  				List<String> record = (Arrays.asList(
                  		data.get(0).toString(),
                  		data.get(1).toString(),
                  		data.get(2).toString(),
                  		data.get(3).toString(),
                  		data.get(4).toString(),
                  		data.get(5).toString(),
                  		data.get(6).toString(),
                  		data.get(7).toString(),
                  		data.get(8).toString(),
                  		data.get(9).toString(),
                  		data.get(10).toString(),
                  		data.get(11).toString(),
                  		data.get(12).toString(),
                  		data.get(13).toString(),
                  		data.get(14).toString(),
                  		data.get(15).toString(),
                  		data.get(16).toString(),
                  		data.get(17).toString(),
                  		data.get(18).toString(),
                  		data.get(19).toString(),
                  		data.get(20).toString(),
  						data.get(21).toString(),
                  		data.get(22).toString(),
                  		data.get(23).toString(),
                  		data.get(24).toString(),
                  		data.get(25).toString(),
                  		data.get(26).toString(),
                  		data.get(27).toString(),
                  		data.get(28).toString(),
                  		data.get(29).toString(),
                  		data.get(30).toString(),
                  		data.get(31).toString(),
                  		data.get(32).toString(),
                  		data.get(33).toString(),
                  		data.get(34).toString(),
                  		data.get(35).toString(),
                  		data.get(36).toString(),
                  		data.get(37).toString(),
                  		data.get(38).toString(),
                  		data.get(39).toString()));	
  				if(filter.getMerchantId().toString().equals("0") || (!filter.getMerchantId().toString().equals("0") && data.get(3).toString().contains(filter.getMerchantId())))
          		csvPrinter.printRecord(record);	
          		blnType2Added = false;
  			}
  			
  			if(!strMerchantName.equals(""))
  			{
  				if(!strMerchantName.equals((values[0].length()>=41?values[0].substring(26, 42):"")))
  				{
  					data = new ArrayList<String>();
  		  			data.add((values[0].length()>=4?values[0].substring(0, 4):""));
  		  			data.add((values[0].length()>=17?values[0].substring(12, 18):""));
  		      		data.add((values[0].length()>=24?values[0].substring(19, 25):""));
  		      		data.add((values[0].length()>=41?values[0].substring(26, 42):""));
  		      		data.add((values[0].length()>=246?values[0].substring(246, 247):""));
  		      		if(dataType1.contains(values[0].substring(0, 4)))
  		      		{
  		      			data.add(dataType1.get(1).toString());
  		      		}
  		      		data.add("Flat Rate Pricing");
  		      		data.add("Pricing Plan");
  		      		data.add("MPP");
  		      		String strFeeCategory = (values[0].length()>=68?values[0].substring(66, 69):"");
  		      		if(strFeeCategory.equals("001"))
  		      		{
  		      			strFeeCategory = "Authorization Txns";
  		      		}
  		      		else if(strFeeCategory.equals("002"))
  		      		{
  		      			strFeeCategory = "Authorization Other";
  		      		}
  		      		else if(strFeeCategory.equals("003"))
  		      		{
  		      			strFeeCategory = "Data Capture Txns";
  		      		}
  		      		else if(strFeeCategory.equals("004"))
  		      		{
  		      			strFeeCategory = "Data Capture Other";
  		      		}
  		      		else if(strFeeCategory.equals("005"))
  		      		{
  		      			strFeeCategory = "Exceptions";
  		      		}
  		      		else if(strFeeCategory.equals("006"))
  		      		{
  		      			strFeeCategory = "Debit Network";
  		      		}
  		      		else if(strFeeCategory.equals("007"))
  		      		{
  		      			strFeeCategory = "EBT";
  		      		}
  		      		else if(strFeeCategory.equals("008"))
  		      		{
  		      			strFeeCategory = "MISC Per Item Fees";
  		      		}
  		      		else if(strFeeCategory.equals("009"))
  		      		{
  		      			strFeeCategory = "MISC Fixed Fees";
  		      		}
  		      		else if(strFeeCategory.equals("010"))
  		      		{
  		      			strFeeCategory = "Interchange";
  		      		}
  		      		else if(strFeeCategory.equals("011"))
  		      		{
  		      			strFeeCategory = "Individual Plan";
  		      		}
  		      		else if(strFeeCategory.equals("012"))
  		      		{
  		      			strFeeCategory = "Discount";
  		      		}
  		      		else if(strFeeCategory.equals("013"))
  		      		{
  		      			strFeeCategory = "Card Brand Fees";
  		      		}
  		      		else if(strFeeCategory.equals("015"))
  		      		{
  		      			strFeeCategory = "Transaction Method";
  		      		}
  		      		else
  		      		{
  		      			strFeeCategory = "";
  		      		}
  		      		
  		  			data.add(strFeeCategory);
  		      		data.add((values[0].length()>=244?values[0].substring(215, 245):""));
  		      		data.add("");
  		      		data.add("13-04-2021");
  		      		data.add("31-12-9999");
  		      	
  		      		data.add(dcPerItem.toString());
  		      		data.add(dcExpPerItem.toString());
  		      	
  		      		//data.add((values[0].length()>=323?values[0].substring(314, 323):""));
  		      		//data.add((values[0].length()>=336?values[0].substring(324, 337):""));
  		      		data.add("");
  		      		
  		      		
  		      		
  		      		data.add((values[0].length()>=195?values[0].substring(187, 196):""));
  		      		
  		      		data.add(dcQualPerItem.toString());
  		       		//data.add((values[0].length()>=210?values[0].substring(197, 210):""));
  		      		dcPerItem = 0.0;
		      		dcExpPerItem = 0.0;
		      		dcQualPerItem = 0.0;
  		      		data.add("");
  		      		if(strFeeCategory.equals("Card Brand Fees") || strFeeCategory.equals("Debit Network") || strFeeCategory.equals("Interchange"))
  		      		{
  		      			data.add(strFeeCategory);
  		      		}
  		      		else
  		      		{
  		      			data.add("");
  		      		}
  		      		data.add("");
  		      		data.add("");
  		      		data.add("");
  		      		data.add((values[0].length()>=252?values[0].substring(252, 253):""));
  		      		data.add("N");
  		      		data.add("Debit");
  		      		data.add("Discount");
  		      		data.add("X");
  		      		data.add("X");
  		      		data.add("X");
  		      		data.add("X");
  		      		data.add((values[0].length()>=213?values[0].substring(213, 214):""));
  		      		data.add((values[0].length()>=64?values[0].substring(44, 65):""));
  		      		data.add("");
  		      		data.add("All");
  		      		data.add("");
  		      		data.add("");
  		      		String strNGBCount = (values[0].length()>=248?values[0].substring(248, 249):"");
  		      		if(strNGBCount.equals("N"))
  		      		{
  		      			strNGBCount = "Net";
  		      		}
  		      		else if(strNGBCount.equals("G"))
  		      		{
  		      			strNGBCount = "Gross";
  		      		}
  		      		else if(strNGBCount.equals("B"))
  		      		{
  		      			strNGBCount = "Both";
  		      		}
  		      		else
  		      		{
  		      			strNGBCount = "";
  		      		}
  		      		data.add(strNGBCount);
  		      		
  		      		String strNGBAmount = (values[0].length()>=250?values[0].substring(250, 251):"");
  		      		if(strNGBAmount.equals("N"))
  		      		{
  		      			strNGBAmount = "Net";
  		      		}
  		      		else if(strNGBAmount.equals("G"))
  		      		{
  		      			strNGBAmount = "Gross";
  		      		}
  		      		else if(strNGBAmount.equals("B"))
  		      		{
  		      			strNGBAmount = "Both";
  		      		}
  		      		else
  		      		{
  		      			strNGBAmount = "";
  		      		}
  		      		data.add(strNGBAmount);
  		      		blnType2Added = true;
  		      		
  		      		/*if(dictReportType3.get(values[0].substring(26, 42)) != null)
  		      		{
  		      			String strDailyFeeIndicator = dictReportType3.get(values[0].substring(26, 42));
  		      			data.add(strDailyFeeIndicator);
  		      		}
  		      		else
  		      		{
  		      			data.add("");
  		      		}*/
  		      		
  				}
  				
  			}
  			
  			dcPerItem += (values[0].length()>=322?Double.parseDouble(values[0].substring(314, 323)):0.00);
  			dcExpPerItem += (values[0].length()>=336?Double.parseDouble(values[0].substring(324, 337)):0.00);
  			dcQualPerItem += (values[0].length()>=210?Double.parseDouble(values[0].substring(197, 210)):0.00);
  			
  			strMerchantName = (values[0].length()>=41?values[0].substring(26, 42):"");
  			
  			
      		
              
  		}
  		if(strRecordType.contains("3"))
  		{
  			//System.out.println("record type 3 - "+ values[0].substring(155, 156) + "- value");
  			
  			
            
  		}
  		if(strRecordType.contains("6"))
  		{
  			//System.out.println("record type 3 - "+ values[0].substring(155, 156) + "- value");
  			
  			
            
  		}
  		   		
  	
  	}
  		
	}

csvPrinter.flush();

  return FileDomain.builder()
          .file(new ByteArrayResource(new ByteArrayInputStream(out.toByteArray()).readAllBytes()))
          .fileName(
                  String.format(
                          "Fee_Item_Detail_Report.csv",	             
                          filter.getMerchantId()
                  )
          )
          .build();
		}

	}
	}

	@Override
	public FileDomain feeItemSummaryReport(String localFilePath, MonthlyResidualReportsFilter filter)
			throws IOException {
		try (
				 InputStream stream = this.getClass().getResourceAsStream(localFilePath);
				 
				 CSVReader csvReader = new CSVReader(new BufferedReader(new InputStreamReader(stream)));
		        ) {
	            @SuppressWarnings("rawtypes")
				ColumnPositionMappingStrategy strategy = new ColumnPositionMappingStrategy();
	            strategy.setType(PPMCSVDataType1.class);
	            String[] memberFieldsToBindTo = {
	            		"Bank",
		                "Group",
		                "Assoc",
		                "Merchant Number",
		                "Hierarchy Level",
		                "DBA Name",
		                "Pricing Template ID",
		                "Template Type",
		                "Fee Item Location",
		                "Fee Category",
		                "Fee Item Name",
		                "FIM Fee Mode",
		                "Start Date",
		                "Stop Date",
		                "Percent",
		                "Per Item($)",
		                "Exp Per Item($)",
		                "Qual(%)",
		                "Qual Per Item($)",
		                "Tier Type",
		                "Pass Through Rate Method"
		                };
	            strategy.setColumnMapping(memberFieldsToBindTo);
 

	            String[] HEADERS = {
	            		"Bank",
		                "Group",
		                "Assoc",
		                "Merchant Number",
		                "Hierarchy Level",
		                "DBA Name",
		                "Pricing Template ID",
		                "Template Type",
		                "Fee Item Location",
		                "Fee Category",
		                "Fee Item Name",
		                "FIM Fee Mode",
		                "Start Date",
		                "Stop Date",
		                "Percent",
		                "Per Item($)",
		                "Exp Per Item($)",
		                "Qual(%)",
		                "Qual Per Item($)",
		                "Tier Type",
		                "Pass Through Rate Method"
		        };

	            
  final CSVFormat format = CSVFormat.Builder.create()
		        		
		                .setHeader(HEADERS)
		                .setIgnoreEmptyLines(true)
		                .build();	

List<List<String>> records = new ArrayList<List<String>>();
	     
try (
       ByteArrayOutputStream out = new ByteArrayOutputStream();
       CSVPrinter csvPrinter = new CSVPrinter(new PrintWriter(out), format)
) {

	String[] values = null;
	List<String> dataType1 = new ArrayList<String>();
	List<String> dataType2 = new ArrayList<String>();
	List<String> data = new ArrayList<String>();
	boolean blnType2Added = false;
	boolean blnType3Added = false;
	recordsListSummary.clear();
	
	while ((values = csvReader.readNext()) != null) {
   	boolean blnIsValidDate = true;
   	blnIsValidDate = validateJavaDate(values[0].substring(45, 55));
   	
   	
   	if(blnIsValidDate)
   	{
   		
   		//System.out.println("Array length - "+ data.size() + "- value");
   				
   		//System.out.println("Bank - "+ values[0].substring(0, 4) + "- value");
   		dataType1 = new ArrayList<String>();
   		dataType1.add((values[0].length()>=4?values[0].substring(0, 4):""));
   		dataType1.add((values[0].length()>=87?values[0].substring(56, 88):""));  		  	
   	}
   	else	
   	{
   		String strRecordType = values[0].substring(43, 44);
   		
   		if(strRecordType.contains("2")) // was 2
   		{
   			if(blnType2Added)
   			{
   				if(filter.getMerchantId().toString().equals("0") || (!filter.getMerchantId().toString().equals("0") && data.get(3).toString().contains(filter.getMerchantId())))
   				{
   					recordsMapSummary = new HashMap<>();
   	  				recordsMapSummary.clear();
   	  				recordsMapSummary.put("BankId", data.get(0).toString().trim());
   	  				recordsMapSummary.put("GroupId", data.get(1).toString().trim());
   	  				recordsMapSummary.put("AssociationId", data.get(2).toString().trim());
   	  				recordsMapSummary.put("MerchantNumber", data.get(3).toString().trim());
   	  				recordsMapSummary.put("HierarchyLevel", data.get(4).toString().trim());
   					recordsMapSummary.put("DBAName", data.get(5).toString().trim());
   					recordsMapSummary.put("PricingTemplateId", data.get(6).toString().trim());
   					recordsMapSummary.put("TemplateType", data.get(7).toString().trim());
   					recordsMapSummary.put("FeeItemLocation", data.get(8).toString().trim());
   					recordsMapSummary.put("FeeCategory", data.get(9).toString().trim());
   					recordsMapSummary.put("FeeItemName", data.get(10).toString().trim());
   					recordsMapSummary.put("FIMFeeMode", data.get(11).toString().trim());
   	  				recordsMapSummary.put("StartDate", data.get(12).toString().trim());
   	  				recordsMapSummary.put("StopDate", data.get(13).toString().trim());
   	  				recordsMapSummary.put("Percent", data.get(14).toString().trim());
   					recordsMapSummary.put("PerItemDollar", data.get(15).toString().trim());
   					recordsMapSummary.put("ExpPerItemDollar", data.get(16).toString().trim());
   					recordsMapSummary.put("QualPercentage", data.get(17).toString().trim());
   					recordsMapSummary.put("QualPerItemDollar", data.get(18).toString().trim());
   					recordsMapSummary.put("TierType", data.get(19).toString().trim());
   					recordsMapSummary.put("PassThroughRateMethod", data.get(20).toString().trim());
   				
   					//List<Map> mapList = new ArrayList<Map>();
   					//mapList.add(recordsMap);  				                  
   					recordsListSummary.add(recordsMapSummary);   	
   				}
   				
   				List<String> record = (Arrays.asList(
                   		data.get(0).toString(),
                   		data.get(1).toString(),
                   		data.get(2).toString(),
                   		data.get(3).toString(),
                   		data.get(4).toString(),
                   		data.get(5).toString(),
                   		data.get(6).toString(),
                   		data.get(7).toString(),
                   		data.get(8).toString(),
                   		data.get(9).toString(),
                   		data.get(10).toString(),
                   		data.get(11).toString(),
                   		data.get(12).toString(),
                   		data.get(13).toString(),
                   		data.get(14).toString(),
                   		data.get(15).toString(),
                   		data.get(16).toString(),
                   		data.get(17).toString(),
                   		data.get(18).toString(),
                   		data.get(19).toString(),
                   		data.get(20).toString()));	
   				if(filter.getMerchantId().toString().equals("0") || (!filter.getMerchantId().toString().equals("0") && data.get(3).toString().contains(filter.getMerchantId())))
           		csvPrinter.printRecord(record);	
           		blnType2Added = false;
   			}
   			
   			data = new ArrayList<String>();
   			data.add((values[0].length()>=4?values[0].substring(0, 4):""));
   			data.add((values[0].length()>=17?values[0].substring(12, 18):""));
       		data.add((values[0].length()>=24?values[0].substring(19, 25):""));
       		data.add((values[0].length()>=41?values[0].substring(26, 42):""));
       		data.add((values[0].length()>=246?values[0].substring(246, 247):""));
       		if(dataType1.contains(values[0].substring(0, 4)))
       		{
       			data.add(dataType1.get(1).toString());
       		}
       		data.add("Flat Rate Pricing");
       		data.add("Pricing Plan");
       		data.add("MPP");
       		String strFeeCategory = (values[0].length()>=68?values[0].substring(66, 69):"");
       		if(strFeeCategory.equals("001"))
       		{
       			strFeeCategory = "Authorization Txns";
       		}
       		else if(strFeeCategory.equals("002"))
       		{
       			strFeeCategory = "Authorization Other";
       		}
       		else if(strFeeCategory.equals("003"))
       		{
       			strFeeCategory = "Data Capture Txns";
       		}
       		else if(strFeeCategory.equals("004"))
       		{
       			strFeeCategory = "Data Capture Other";
       		}
       		else if(strFeeCategory.equals("005"))
       		{
       			strFeeCategory = "Exceptions";
       		}
       		else if(strFeeCategory.equals("006"))
       		{
       			strFeeCategory = "Debit Network";
       		}
       		else if(strFeeCategory.equals("007"))
       		{
       			strFeeCategory = "EBT";
       		}
       		else if(strFeeCategory.equals("008"))
       		{
       			strFeeCategory = "MISC Per Item Fees";
       		}
       		else if(strFeeCategory.equals("009"))
       		{
       			strFeeCategory = "MISC Fixed Fees";
       		}
       		else if(strFeeCategory.equals("010"))
       		{
       			strFeeCategory = "Interchange";
       		}
       		else if(strFeeCategory.equals("011"))
       		{
       			strFeeCategory = "Individual Plan";
       		}
       		else if(strFeeCategory.equals("012"))
       		{
       			strFeeCategory = "Discount";
       		}
       		else if(strFeeCategory.equals("013"))
       		{
       			strFeeCategory = "Card Brand Fees";
       		}
       		else if(strFeeCategory.equals("015"))
       		{
       			strFeeCategory = "Transaction Method";
       		}
       		else
       		{
       			strFeeCategory = "";
       		}
       		
   			data.add(strFeeCategory);
       		data.add((values[0].length()>=244?values[0].substring(215, 245):""));
       		data.add("");
       		data.add("13-04-2021");
       		data.add("31-12-9999");
       		data.add((values[0].length()>=323?values[0].substring(314, 323):""));
       		data.add((values[0].length()>=336?values[0].substring(324, 337):""));
       		data.add("");
       		data.add((values[0].length()>=195?values[0].substring(187, 196):""));
       		data.add((values[0].length()>=210?values[0].substring(197, 210):""));
       		data.add("");
       		if(strFeeCategory.equals("Card Brand Fees") || strFeeCategory.equals("Debit Network") || strFeeCategory.equals("Interchange"))
       		{
       			data.add(strFeeCategory);
       		}
       		else
       		{
       			data.add("");
       		}
       		
       		blnType2Added = true;
               
   		}
   		if(strRecordType.contains("3"))
   		{
   			//System.out.println("record type 3 - "+ values[0].substring(155, 156) + "- value");
   			
   			
             
   		}
   		if(strRecordType.contains("6"))
   		{
   			//System.out.println("record type 3 - "+ values[0].substring(155, 156) + "- value");
   			
   			
             
   		}
   		   		
   	
   	}
   		
	}

csvPrinter.flush();

   return FileDomain.builder()
           .file(new ByteArrayResource(new ByteArrayInputStream(out.toByteArray()).readAllBytes()))
           .fileName(
                   String.format(
                           "Fee_Item_Summary_Report.csv",	             
                           filter.getMerchantId()
                   )
           )
           .build();
		}

	}
	
}

	
	@Override
	public void generateSummaryMapData(String localFilePath) throws IOException
	{
		try (
				 InputStream stream = this.getClass().getResourceAsStream(localFilePath);
				 
				 CSVReader csvReader = new CSVReader(new BufferedReader(new InputStreamReader(stream)));
		        ) {
	           


	            String[] HEADERS = {
	            		"Bank",
		                "Group",
		                "Assoc",
		                "Merchant Number",
		                "Hierarchy Level",
		                "DBA Name",
		                "Pricing Template ID",
		                "Template Type",
		                "Fee Item Location",
		                "Fee Category",
		                "Fee Item Name",
		                "FIM Fee Mode",
		                "Start Date",
		                "Stop Date",
		                "Percent",
		                "Per Item($)",
		                "Exp Per Item($)",
		                "Qual(%)",
		                "Qual Per Item($)",
		                "Tier Type",
		                "Pass Through Rate Method"
		        };

	            
 final CSVFormat format = CSVFormat.Builder.create()
		        		
		                .setHeader(HEADERS)
		                .setIgnoreEmptyLines(true)
		                .build();	

List<List<String>> records = new ArrayList<List<String>>();
	     
try (
      ByteArrayOutputStream out = new ByteArrayOutputStream();
      CSVPrinter csvPrinter = new CSVPrinter(new PrintWriter(out), format)
) {

	String[] values = null;
	List<String> dataType1 = new ArrayList<String>();
	
	List<String> data = new ArrayList<String>();
	boolean blnType2Added = false;
	
	
	while ((values = csvReader.readNext()) != null) {
  	boolean blnIsValidDate = true;
  	blnIsValidDate = validateJavaDate(values[0].substring(45, 55));
  	
  	
  	if(blnIsValidDate)
  	{
  		
  		//System.out.println("Array length - "+ data.size() + "- value");
  				
  		//System.out.println("Bank - "+ values[0].substring(0, 4) + "- value");
  		dataType1 = new ArrayList<String>();
  		dataType1.add((values[0].length()>=4?values[0].substring(0, 4):""));
  		dataType1.add((values[0].length()>=87?values[0].substring(56, 88):""));  		  	
  	}
  	else	
  	{
  		String strRecordType = values[0].substring(43, 44);
  		
  		if(strRecordType.contains("2")) // was 2
  		{
  			if(blnType2Added)
  			{
  				recordsMapSummary = new HashMap<>();
  				recordsMapSummary.clear();
  				recordsMapSummary.put("BankId", data.get(0).toString().trim());
  				recordsMapSummary.put("GroupId", data.get(1).toString().trim());
  				recordsMapSummary.put("AssociationId", data.get(2).toString().trim());
  				recordsMapSummary.put("MerchantNumber", data.get(3).toString().trim());
  				recordsMapSummary.put("HierarchyLevel", data.get(4).toString().trim());
				recordsMapSummary.put("DBAName", data.get(5).toString().trim());
				recordsMapSummary.put("PricingTemplateId", data.get(6).toString().trim());
				recordsMapSummary.put("TemplateType", data.get(7).toString().trim());
				recordsMapSummary.put("FeeItemLocation", data.get(8).toString().trim());
				recordsMapSummary.put("FeeCategory", data.get(9).toString().trim());
				recordsMapSummary.put("FeeItemName", data.get(10).toString().trim());
				recordsMapSummary.put("FIMFeeMode", data.get(11).toString().trim());
  				recordsMapSummary.put("StartDate", data.get(12).toString().trim());
  				recordsMapSummary.put("StopDate", data.get(13).toString().trim());
  				recordsMapSummary.put("Percent", data.get(14).toString().trim());
				recordsMapSummary.put("PerItemDollar", data.get(15).toString().trim());
				recordsMapSummary.put("ExpPerItemDollar", data.get(16).toString().trim());
				recordsMapSummary.put("QualPercentage", data.get(17).toString().trim());
				recordsMapSummary.put("QualPerItemDollar", data.get(18).toString().trim());
				recordsMapSummary.put("TierType", data.get(19).toString().trim());
				recordsMapSummary.put("PassThroughRateMethod", data.get(20).toString().trim());
			
				//List<Map> mapList = new ArrayList<Map>();
				//mapList.add(recordsMap);  				                  
				recordsListSummary.add(recordsMapSummary);   							
  				
          		blnType2Added = false;
  			}
  			
  			data = new ArrayList<String>();
  			data.add((values[0].length()>=4?values[0].substring(0, 4):""));
  			data.add((values[0].length()>=17?values[0].substring(12, 18):""));
      		data.add((values[0].length()>=24?values[0].substring(19, 25):""));
      		data.add((values[0].length()>=41?values[0].substring(26, 42):""));
      		data.add((values[0].length()>=246?values[0].substring(246, 247):""));
      		if(dataType1.contains(values[0].substring(0, 4)))
      		{
      			data.add(dataType1.get(1).toString());
      		}
      		data.add("Flat Rate Pricing");
      		data.add("Pricing Plan");
      		data.add("MPP");
      		String strFeeCategory = (values[0].length()>=68?values[0].substring(66, 69):"");
      		if(strFeeCategory.equals("001"))
      		{
      			strFeeCategory = "Authorization Txns";
      		}
      		else if(strFeeCategory.equals("002"))
      		{
      			strFeeCategory = "Authorization Other";
      		}
      		else if(strFeeCategory.equals("003"))
      		{
      			strFeeCategory = "Data Capture Txns";
      		}
      		else if(strFeeCategory.equals("004"))
      		{
      			strFeeCategory = "Data Capture Other";
      		}
      		else if(strFeeCategory.equals("005"))
      		{
      			strFeeCategory = "Exceptions";
      		}
      		else if(strFeeCategory.equals("006"))
      		{
      			strFeeCategory = "Debit Network";
      		}
      		else if(strFeeCategory.equals("007"))
      		{
      			strFeeCategory = "EBT";
      		}
      		else if(strFeeCategory.equals("008"))
      		{
      			strFeeCategory = "MISC Per Item Fees";
      		}
      		else if(strFeeCategory.equals("009"))
      		{
      			strFeeCategory = "MISC Fixed Fees";
      		}
      		else if(strFeeCategory.equals("010"))
      		{
      			strFeeCategory = "Interchange";
      		}
      		else if(strFeeCategory.equals("011"))
      		{
      			strFeeCategory = "Individual Plan";
      		}
      		else if(strFeeCategory.equals("012"))
      		{
      			strFeeCategory = "Discount";
      		}
      		else if(strFeeCategory.equals("013"))
      		{
      			strFeeCategory = "Card Brand Fees";
      		}
      		else if(strFeeCategory.equals("015"))
      		{
      			strFeeCategory = "Transaction Method";
      		}
      		else
      		{
      			strFeeCategory = "";
      		}
      		
  			data.add(strFeeCategory);
      		data.add((values[0].length()>=244?values[0].substring(215, 245):""));
      		data.add("");
      		data.add("13-04-2021");
      		data.add("31-12-9999");
      		data.add((values[0].length()>=323?values[0].substring(314, 323):""));
      		data.add((values[0].length()>=336?values[0].substring(324, 337):""));
      		data.add("");
      		data.add((values[0].length()>=195?values[0].substring(187, 196):""));
      		data.add((values[0].length()>=210?values[0].substring(197, 210):""));
      		data.add("");
      		if(strFeeCategory.equals("Card Brand Fees") || strFeeCategory.equals("Debit Network") || strFeeCategory.equals("Interchange"))
      		{
      			data.add(strFeeCategory);
      		}
      		else
      		{
      			data.add("");
      		}
      		
      		blnType2Added = true;
              
  		}
  		if(strRecordType.contains("3"))
  		{
  			//System.out.println("record type 3 - "+ values[0].substring(155, 156) + "- value");
  			
  			
            
  		}
  		if(strRecordType.contains("6"))
  		{
  			//System.out.println("record type 3 - "+ values[0].substring(155, 156) + "- value");
  			
  			
            
  		}
  		   		
  	
  	}
  		
	}

csvPrinter.flush();

  
		}

	}
	
	}
	

	EntityManager entityManager;
	
	@Override
	public Page<Map> findData(MonthlyResidualReportsFilter filter, Pageable pageable) {
		
		Page<Map> page = new PageImpl<>(recordsList);
		return page;

	}
	
	private Specification<String> filterBy(MonthlyResidualReportsFilter filter) {
        return (r, rq, cb) -> {

            Predicate merchantIdPredicate = StringUtils.isNotBlank(filter.getMerchantId()) ?
                    cb.greaterThanOrEqualTo(
                            r.get("merchantId"),
                            filter.getMerchantId()
                    ) : cb.conjunction();

          

            return cb.and(
            		merchantIdPredicate
            );
        };
    }



	@Override
	public Page<Map> findSummaryData(MonthlyResidualReportsFilter filter, Pageable pageable) {

		Page<Map> page = new PageImpl<>(recordsListSummary);
		return page;
		
	}

	@Override
	public void generateFeeItemDetailMapData(String localFilePath) throws IOException {
		try (
				 InputStream stream = this.getClass().getResourceAsStream(localFilePath);
				 
				 CSVReader csvReader = new CSVReader(new BufferedReader(new InputStreamReader(stream)));
				 CSVReader csvReaderRepeat = new CSVReader(new BufferedReader(new InputStreamReader(stream)));  
		        ) {
	            @SuppressWarnings("rawtypes")
				ColumnPositionMappingStrategy strategy = new ColumnPositionMappingStrategy();
	            strategy.setType(PPMCSVDataType1.class);
	            String[] memberFieldsToBindTo = {
	            		"Bank",
		                "Group",
		                "Assoc",
		                "Merchant Number",
		                "Hierarchy Level",
		                "DBA Name",
		                "Pricing Template ID",
		                "Template Type",
		                "Fee Item Location",
		                "Fee Category",
		                "Fee Item Name",
		                "FIM Fee Mode",
		                "Start Date",
		                "Stop Date",
		                "Percent",
		                "Per Item($)",
		                "Exp Per Item($)",
		                "Qual(%)",
		                "Qual Per Item($)",
		                "Tier Type",
		                "Pass Through Rate Method",
		                "DDA Account Type",
		                "DDA Number",
		                "Transit Routing Number",
		                "Incl in Mnth Min",
		                "Daily Fee Indicator",
		                "Billing Method",
		                "Name & Addr Usage Cd",
		                "Display Count on Stmt",
		                "Display Amt on Stmt",
		                "Display Rates on Stmt",
		                "Display Detail on Stmt",
		                "Comb Fee on Rate Change",
		                "Stmt Fee Descript",
		                "Combine Code",
		                "GL Item Code",
		                "User Data Code",
		                "Count N/G/B",
		                "Amount N/G/B"
		                };
	            strategy.setColumnMapping(memberFieldsToBindTo);


	            String[] HEADERS = {
	            		"Bank",
		                "Group",
		                "Assoc",
		                "Merchant Number",
		                "Hierarchy Level",
		                "DBA Name",
		                "Pricing Template ID",
		                "Template Type",
		                "Fee Item Location",
		                "Fee Category",
		                "Fee Item Name",
		                "FIM Fee Mode",
		                "Start Date",
		                "Stop Date",
		                "Percent",
		                "Per Item($)",
		                "Exp Per Item($)",
		                "Qual(%)",
		                "Qual Per Item($)",
		                "Tier Type",
		                "Pass Through Rate Method",
		                "DDA Account Type",
		                "DDA Number",
		                "Transit Routing Number",
		                "Incl in Mnth Min",
		                "Daily Fee Indicator",
		                "Billing Method",
		                "Name & Addr Usage Cd",
		                "Display Count on Stmt",
		                "Display Amt on Stmt",
		                "Display Rates on Stmt",
		                "Display Detail on Stmt",
		                "Comb Fee on Rate Change",
		                "Stmt Fee Descript",
		                "Combine Code",
		                "Frequency",
		                "GL Item Code",
		                "User Data Code",
		                "Count N/G/B",
		                "Amount N/G/B"
		        };

	            
final CSVFormat format = CSVFormat.Builder.create()
		        		
		                .setHeader(HEADERS)
		                .setIgnoreEmptyLines(true)
		                .build();	

List<List<String>> records = new ArrayList<List<String>>();
	     
try (
     ByteArrayOutputStream out = new ByteArrayOutputStream();
     CSVPrinter csvPrinter = new CSVPrinter(new PrintWriter(out), format)
) {

	String[] values = null;
	String[] valuesRepeat = null;
	
	Dictionary<String, String> dictReportType3= new Hashtable<>();
	Dictionary<String, String> dictReportType6= new Hashtable<>();
	
	List<String> dataType1 = new ArrayList<String>();
	List<String> dataType2 = new ArrayList<String>();
	List<String> data = new ArrayList<String>();
	boolean blnType2Added = false;
	boolean blnType3Added = false;
	
	
	while ((values = csvReader.readNext()) != null) {
 	boolean blnIsValidDate = true;
 	blnIsValidDate = validateJavaDate(values[0].substring(45, 55));
 	
 	
 	if(blnIsValidDate)
 	{
 		
 		//System.out.println("Array length - "+ data.size() + "- value");
 				
 		//System.out.println("Bank - "+ values[0].substring(0, 4) + "- value");
 		dataType1 = new ArrayList<String>();
 		dataType1.add((values[0].length()>=4?values[0].substring(0, 4):""));
 		dataType1.add((values[0].length()>=87?values[0].substring(56, 88):""));  		  	
 	}
 	else	
 	{
 		String strRecordType = values[0].substring(43, 44);
 		
 		if(strRecordType.contains("2")) // was 2
 		{
 			if(blnType2Added)
 			{
 				recordsMapFeeItemDetail = new HashMap<>();
 				recordsMapFeeItemDetail.clear();
 				recordsMapFeeItemDetail.put("BankId", data.get(0).toString().trim());
 				recordsMapFeeItemDetail.put("GroupId", data.get(1).toString().trim());
 				recordsMapFeeItemDetail.put("AssociationId", data.get(2).toString().trim());
 				recordsMapFeeItemDetail.put("MerchantNumber", data.get(3).toString().trim());
 				recordsMapFeeItemDetail.put("HierarchyLevel", data.get(4).toString().trim());
 				recordsMapFeeItemDetail.put("DBAName", data.get(5).toString().trim());
 				recordsMapFeeItemDetail.put("PricingTemplateId", data.get(6).toString().trim());
 				recordsMapFeeItemDetail.put("TemplateType", data.get(7).toString().trim());
 				recordsMapFeeItemDetail.put("FeeItemLocation", data.get(8).toString().trim());
 				recordsMapFeeItemDetail.put("FeeCategory", data.get(9).toString().trim());
 				recordsMapFeeItemDetail.put("FeeItemName", data.get(10).toString().trim());
 				recordsMapFeeItemDetail.put("FIMFeeMode", data.get(11).toString().trim());
 				recordsMapFeeItemDetail.put("StartDate", data.get(12).toString().trim());
 				recordsMapFeeItemDetail.put("StopDate", data.get(13).toString().trim());
 				recordsMapFeeItemDetail.put("Percent", data.get(14).toString().trim());
 				recordsMapFeeItemDetail.put("PerItemDollar", data.get(15).toString().trim());
				recordsMapFeeItemDetail.put("ExpPerItemDollar", data.get(16).toString().trim());
				recordsMapFeeItemDetail.put("QualPercentage", data.get(17).toString().trim());
				recordsMapFeeItemDetail.put("QualPerItemDollar", data.get(18).toString().trim());
				recordsMapFeeItemDetail.put("TierType", data.get(19).toString().trim());
				recordsMapFeeItemDetail.put("PassThroughRateMethod", data.get(20).toString().trim());
				
				recordsMapFeeItemDetail.put("DDAAccountType", data.get(0).toString().trim());
 				recordsMapFeeItemDetail.put("DDANumber", data.get(1).toString().trim());
 				recordsMapFeeItemDetail.put("TransitRoutingNo", data.get(2).toString().trim());
 				recordsMapFeeItemDetail.put("IncludedInMnthlyMin", data.get(3).toString().trim());
 				recordsMapFeeItemDetail.put("DailyFeeIndicator", data.get(4).toString().trim());
 				recordsMapFeeItemDetail.put("BillingMethod", data.get(5).toString().trim());
 				recordsMapFeeItemDetail.put("NameAddressUsageCd", data.get(6).toString().trim());
 				recordsMapFeeItemDetail.put("DisplayCountOnStmt", data.get(7).toString().trim());
 				recordsMapFeeItemDetail.put("DisplayAmtOnStmt", data.get(8).toString().trim());
 				recordsMapFeeItemDetail.put("DisplayRateOnStmt", data.get(9).toString().trim());
 				recordsMapFeeItemDetail.put("DisplayDetailOnStmt", data.get(10).toString().trim());
 				recordsMapFeeItemDetail.put("CombFeeOnRateChange", data.get(11).toString().trim());
 				recordsMapFeeItemDetail.put("StmtFeeDescription", data.get(12).toString().trim());
 				recordsMapFeeItemDetail.put("CombineCode", data.get(13).toString().trim());
 				recordsMapFeeItemDetail.put("Frequency", data.get(14).toString().trim());
 				recordsMapFeeItemDetail.put("GLItemCode", data.get(15).toString().trim());
				recordsMapFeeItemDetail.put("UserDataCode", data.get(16).toString().trim());
				recordsMapFeeItemDetail.put("CountNGB", data.get(17).toString().trim());
				recordsMapFeeItemDetail.put("amountNGB", data.get(18).toString().trim());
				
				recordsListFeeItemDetail.add(recordsMapFeeItemDetail);   	
         		blnType2Added = false;
 			}
 			
 			data = new ArrayList<String>();
 			data.add((values[0].length()>=4?values[0].substring(0, 4):""));
 			data.add((values[0].length()>=17?values[0].substring(12, 18):""));
     		data.add((values[0].length()>=24?values[0].substring(19, 25):""));
     		data.add((values[0].length()>=41?values[0].substring(26, 42):""));
     		data.add((values[0].length()>=246?values[0].substring(246, 247):""));
     		if(dataType1.contains(values[0].substring(0, 4)))
     		{
     			data.add(dataType1.get(1).toString());
     		}
     		data.add("Flat Rate Pricing");
     		data.add("Pricing Plan");
     		data.add("MPP");
     		String strFeeCategory = (values[0].length()>=68?values[0].substring(66, 69):"");
     		if(strFeeCategory.equals("001"))
     		{
     			strFeeCategory = "Authorization Txns";
     		}
     		else if(strFeeCategory.equals("002"))
     		{
     			strFeeCategory = "Authorization Other";
     		}
     		else if(strFeeCategory.equals("003"))
     		{
     			strFeeCategory = "Data Capture Txns";
     		}
     		else if(strFeeCategory.equals("004"))
     		{
     			strFeeCategory = "Data Capture Other";
     		}
     		else if(strFeeCategory.equals("005"))
     		{
     			strFeeCategory = "Exceptions";
     		}
     		else if(strFeeCategory.equals("006"))
     		{
     			strFeeCategory = "Debit Network";
     		}
     		else if(strFeeCategory.equals("007"))
     		{
     			strFeeCategory = "EBT";
     		}
     		else if(strFeeCategory.equals("008"))
     		{
     			strFeeCategory = "MISC Per Item Fees";
     		}
     		else if(strFeeCategory.equals("009"))
     		{
     			strFeeCategory = "MISC Fixed Fees";
     		}
     		else if(strFeeCategory.equals("010"))
     		{
     			strFeeCategory = "Interchange";
     		}
     		else if(strFeeCategory.equals("011"))
     		{
     			strFeeCategory = "Individual Plan";
     		}
     		else if(strFeeCategory.equals("012"))
     		{
     			strFeeCategory = "Discount";
     		}
     		else if(strFeeCategory.equals("013"))
     		{
     			strFeeCategory = "Card Brand Fees";
     		}
     		else if(strFeeCategory.equals("015"))
     		{
     			strFeeCategory = "Transaction Method";
     		}
     		else
     		{
     			strFeeCategory = "";
     		}
     		
 			data.add(strFeeCategory);
     		data.add((values[0].length()>=244?values[0].substring(215, 245):""));
     		data.add("");
     		data.add("13-04-2021");
     		data.add("31-12-9999");
     		data.add((values[0].length()>=323?values[0].substring(314, 323):""));
     		data.add((values[0].length()>=336?values[0].substring(324, 337):""));
     		data.add("");
     		data.add((values[0].length()>=195?values[0].substring(187, 196):""));
      		data.add((values[0].length()>=210?values[0].substring(197, 210):""));
     		data.add("");
     		if(strFeeCategory.equals("Card Brand Fees") || strFeeCategory.equals("Debit Network") || strFeeCategory.equals("Interchange"))
     		{
     			data.add(strFeeCategory);
     		}
     		else
     		{
     			data.add("");
     		}
     		data.add("");
     		data.add("");
     		data.add("");
     		data.add((values[0].length()>=252?values[0].substring(252, 253):""));
     		data.add("N");
     		data.add("Debit");
     		data.add("Discount");
     		data.add("X");
     		data.add("X");
     		data.add("X");
     		data.add("X");
     		data.add((values[0].length()>=213?values[0].substring(213, 214):""));
     		data.add((values[0].length()>=64?values[0].substring(44, 65):""));
     		data.add("");
     		data.add("All");
     		data.add("");
     		data.add("");
     		String strNGBCount = (values[0].length()>=248?values[0].substring(248, 249):"");
     		if(strNGBCount.equals("N"))
     		{
     			strNGBCount = "Net";
     		}
     		else if(strNGBCount.equals("G"))
     		{
     			strNGBCount = "Gross";
     		}
     		else if(strNGBCount.equals("B"))
     		{
     			strNGBCount = "Both";
     		}
     		else
     		{
     			strNGBCount = "";
     		}
     		data.add(strNGBCount);
     		
     		String strNGBAmount = (values[0].length()>=250?values[0].substring(250, 251):"");
     		if(strNGBAmount.equals("N"))
     		{
     			strNGBAmount = "Net";
     		}
     		else if(strNGBAmount.equals("G"))
     		{
     			strNGBAmount = "Gross";
     		}
     		else if(strNGBAmount.equals("B"))
     		{
     			strNGBAmount = "Both";
     		}
     		else
     		{
     			strNGBAmount = "";
     		}
     		data.add(strNGBAmount);
     		
     		/*if(dictReportType3.get(values[0].substring(26, 42)) != null)
     		{
     			String strDailyFeeIndicator = dictReportType3.get(values[0].substring(26, 42));
     			data.add(strDailyFeeIndicator);
     		}
     		else
     		{
     			data.add("");
     		}*/
     		
     		blnType2Added = true;
             
 		}
 		if(strRecordType.contains("3"))
 		{
 			//System.out.println("record type 3 - "+ values[0].substring(155, 156) + "- value");
 			
 			
           
 		}
 		if(strRecordType.contains("6"))
 		{
 			//System.out.println("record type 3 - "+ values[0].substring(155, 156) + "- value");
 			
 			
           
 		}
 		   		
 	
 	}
 		
	}

csvPrinter.flush();

 
		}

	}
	}

	@Override
	public Page<Map> findFeeItemDetailData(MonthlyResidualReportsFilter filter, Pageable pageable) {
		
		Page<Map> page = new PageImpl<>(recordsListFeeItemDetail);
		return page;
	}
	
	
	
}