package com.osdb.ippay.secondary.partner.facade.impl;

import com.osdb.ippay.common.exception.business.NotFoundException;
import com.osdb.ippay.secondary.partner.facade.PartnerFacade;
import com.osdb.ippay.secondary.partner.facade.dto.PartnerDto;
import com.osdb.ippay.secondary.partner.facade.mapper.PartnerMapper;
import com.osdb.ippay.secondary.partner.repository.entity.Partner;
import com.osdb.ippay.secondary.partner.service.PartnerService;
import com.osdb.ippay.secondary.partner.service.filter.PartnerFilter;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

import static com.osdb.ippay.common.exception.ErrorMessage.PARTNER_ID_NOT_FOUND;
import static lombok.AccessLevel.PRIVATE;

@Component
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class PartnerFacadeImpl implements PartnerFacade {

    PartnerService partnerService;
    PartnerMapper partnerMapper;

    @Override
    public Page<PartnerDto> find(PartnerFilter filter, Pageable pageable) {
        Page<Partner> partners = partnerService.find(filter, pageable);
        return partners.map(partnerMapper::toDto);
    }

    @Override
    public List<PartnerDto> find(PartnerFilter filter, Sort sort) {
        List<Partner> partners = partnerService.find(filter, sort);

        return partners.stream()
                .map(partnerMapper::toDto)
                .collect(Collectors.toList());
    }

    @Override
    public PartnerDto find(Long id) {
        Partner partner = partnerService.find(id);

        if(partner == null) {
            throw new NotFoundException(PARTNER_ID_NOT_FOUND);
        }

        return partnerMapper.toDto(partner);
    }
}
