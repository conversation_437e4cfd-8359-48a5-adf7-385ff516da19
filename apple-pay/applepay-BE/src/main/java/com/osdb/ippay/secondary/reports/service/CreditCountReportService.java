package com.osdb.ippay.secondary.reports.service;

import com.osdb.ippay.secondary.reports.repository.entity.CreditCountReport;

import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface CreditCountReportService {
    
    List generateReport(String startDateTime, String endDateTime);
    public Page<CreditCountReport> find(String startDateTime,
			String endDateTime,
			Pageable pageable);

}
