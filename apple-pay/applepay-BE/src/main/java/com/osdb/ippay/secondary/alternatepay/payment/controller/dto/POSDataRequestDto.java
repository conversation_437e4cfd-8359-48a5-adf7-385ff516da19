package com.osdb.ippay.secondary.alternatepay.payment.controller.dto;

import lombok.*;
import lombok.experimental.FieldDefaults;

import static lombok.AccessLevel.PRIVATE;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class POSDataRequestDto {

    
    String customerName;
  
    String customerEmail;

    String address;

    String city;
    
    String state;
    
    String zip;
    
    String country;
    
}
