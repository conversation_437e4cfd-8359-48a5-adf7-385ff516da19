package com.osdb.ippay.secondary.alternatepay.refund.controller.facade;

import com.osdb.ippay.secondary.alternatepay.refund.controller.dto.RefundSaveDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionSaveDto;
import com.osdb.ippay.secondary.alternatepay.refund.controller.dto.TransactionRefundResultDto;


public interface RefundResponseFacade {
  
    RefundSaveDto create(TransactionRefundResultDto transactionRefundSaveDto);
    TransactionSaveDto update(TransactionRefundResultDto transactionRefundSaveDto, String Trnid);

    }