package com.osdb.ippay.secondary.retokenization.controller.dto;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.osdb.ippay.secondary.retokenization.controller.dto.enumeration.JobInfoSourceDatatype;
import com.osdb.ippay.secondary.retokenization.controller.dto.enumeration.JobInfoTokenType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

import static lombok.AccessLevel.PRIVATE;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class JobInfoDto {

    @JsonProperty(required = true)
    String jobId;

    String oldTerminalId;

    @JsonProperty(required = true)
    String newTerminalId;

    @JsonProperty(required = true)
    JobInfoTokenType tokenType;

    String orgFilename;

    JobInfoSourceDatatype sourceDataType;

    String sourceS3Bucket;

    String sourceS3BucketKey;

    @JsonProperty(value = "Description")
    @Schema(name = "description")
    @JsonAlias({"description"})
    String description;

}
