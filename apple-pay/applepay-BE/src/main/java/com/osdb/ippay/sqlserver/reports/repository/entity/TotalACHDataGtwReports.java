package com.osdb.ippay.sqlserver.reports.repository.entity;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import javax.persistence.*;
import static javax.persistence.GenerationType.IDENTITY;
import static lombok.AccessLevel.PRIVATE;

@Entity
@Table(name = "jetPay_CheckRealTime")
@NamedStoredProcedureQueries({
@NamedStoredProcedureQuery(resultClasses = TotalACHDataGtwReports.class, name = "TotalACHDataGtwReports.getTotalACHSummaryByModelEntiy", procedureName = "Get_Monthly_Total_ACH_Summary_Report", 
parameters = {@StoredProcedureParameter(mode = ParameterMode.IN,name = "ipTransactionId",type=Long.class),
		      @StoredProcedureParameter(mode = ParameterMode.IN,name = "authStartDateTime",type=String.class),
		      @StoredProcedureParameter(mode = ParameterMode.IN,name = "authEndDateTime",type=String.class)})})
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
public class TotalACHDataGtwReports {

	 @Id
	 @Column(name = "ipTransactionID")
	 @GeneratedValue(strategy = IDENTITY)
	 Long ipTransactionId;
	 
    @Column(name = "Merchant_ID")
    String merchantID;

    @Column(name = "ACHProcessor")
    String ccProcessor;

    @Column(name = "Request_Type")
    String requestType;

    @Column(name = "ActionCode")
    String actionCode;

    @Column(name = "TRANSACTION_COUNT", insertable=false, updatable = false )
    String transactionCount;

    @Column(name = "TOTAL_AMOUNT")
    String totalAmount;

    
    @Column(name = "AuthDateTime")
    String authDateTime;
    
    @Column(name = "SPSMID")
    String spsMid;
    
    @Column(name = "Transaction_ID")
    String trnId;
    
    @Column(name = "OrganizationName")
    String orgName;
    }

