package com.osdb.ippay.secondary.statement.service.filter;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotNull;

import static lombok.AccessLevel.PRIVATE;

@Data
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = PRIVATE)
public class MSFilter {

    @NotNull(message = "Missing required parameter: 'year'.")
    Integer year;

    @NotNull(message = "Missing required parameter: 'month'.")
    Integer month;

}
