package com.osdb.ippay.secondary.alternatepay.transaction.controller;

import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionCreditDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionInitDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionSaveDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionTokenizeCaptureDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TrasactionInitResultDto;
import com.osdb.ippay.hpp.alternatepay.transaction.facade.HapiTransactionResponseFacade;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.facade.TransactionCreditFacade;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.facade.TransactionInitFacade;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.facade.TransactionResponseFacade;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.facade.TransactionTokenizeCaptureFacade;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import static lombok.AccessLevel.PRIVATE;

@Tag(name = "Alternatepay/Transaction")
@RestController
@RequestMapping(value = "/api/v2/payment/alternatepay")
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class TransactionInitController {

	TransactionInitFacade transactionInitFacade;
	TransactionResponseFacade transactionResponseFacade;
	TransactionTokenizeCaptureFacade transactionTokenizeCaptureFacade;
	TransactionCreditFacade transactionCreditFacade;
	HapiTransactionResponseFacade hapiTransactionResponseFacade;

    @PostMapping("/transaction/init")
    public ResponseEntity<TrasactionInitResultDto> Init(@Valid @RequestBody TransactionInitDto transactionInitDto) {
    	TrasactionInitResultDto response = transactionInitFacade.create(transactionInitDto);
    	//TransactionSaveDto transactionSaveDto = transactionResponseFacade.create(response);
    	//hapiTransactionResponseFacade.create(transactionSaveDto);
        return ResponseEntity.ok(response);
    }

    @PostMapping("/transaction/init/tokenize")
    public ResponseEntity<TrasactionInitResultDto> Tokenize(@Valid @RequestBody TransactionTokenizeCaptureDto transactionInitDto) {
    	TrasactionInitResultDto response = transactionTokenizeCaptureFacade.create(transactionInitDto);
    	//TransactionSaveDto transactionSaveDto = transactionResponseFacade.create(response);
    	//hapiTransactionResponseFacade.create(transactionSaveDto);
        return ResponseEntity.ok(response);
    }

    @PostMapping("/transactions/init/capturetokenize")
    public ResponseEntity<TrasactionInitResultDto> CaptureTokenize(@Valid @RequestBody TransactionTokenizeCaptureDto transactionInitDto) {
    	TrasactionInitResultDto response = transactionTokenizeCaptureFacade.create(transactionInitDto);
    	//TransactionSaveDto transactionSaveDto = transactionResponseFacade.create(response);
    	//hapiTransactionResponseFacade.create(transactionSaveDto);
        return ResponseEntity.ok(response);
    }

    @PostMapping("/transactions/init/authorize")
    public ResponseEntity<TrasactionInitResultDto> Authorize(@Valid @RequestBody TransactionTokenizeCaptureDto transactionInitDto) {
    	TrasactionInitResultDto response = transactionTokenizeCaptureFacade.create(transactionInitDto);
    	//TransactionSaveDto transactionSaveDto = transactionResponseFacade.create(response);
    	//hapiTransactionResponseFacade.create(transactionSaveDto);
        return ResponseEntity.ok(response);
    }

    @PostMapping("/transaction/init/credit")
    public ResponseEntity<TrasactionInitResultDto> Credit(@Valid @RequestBody TransactionCreditDto transactionCreditDto) {
    	TrasactionInitResultDto response = transactionCreditFacade.create(transactionCreditDto);
    	//TransactionSaveDto transactionSaveDto = transactionResponseFacade.create(response);
    	//hapiTransactionResponseFacade.create(transactionSaveDto);
        return ResponseEntity.ok(response);
    }

}
