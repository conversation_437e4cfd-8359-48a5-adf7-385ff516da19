package com.osdb.ippay.primary.user.facade.impl;

import com.osdb.ippay.primary.user.facade.UserConfigFacade;
import com.osdb.ippay.primary.user.facade.dto.UserConfigDto;
import com.osdb.ippay.common.user.facade.dto.UserDto;
import com.osdb.ippay.primary.user.facade.mapper.UserConfigMapper;
import com.osdb.ippay.common.user.facade.mapper.UserMapper;
import com.osdb.ippay.primary.user.repository.entity.DefaultConfig;
import com.osdb.ippay.primary.user.repository.entity.User;
import com.osdb.ippay.primary.user.repository.entity.UserConfig;
import com.osdb.ippay.primary.user.service.DefaultConfigService;
import com.osdb.ippay.primary.user.service.UserConfigService;
import com.osdb.ippay.primary.user.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

import static lombok.AccessLevel.PRIVATE;

@Component
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class UserConfigFacadeImpl implements UserConfigFacade {

    // services
    UserService userService;
    UserConfigService userConfigService;
    DefaultConfigService defaultConfigService;

    // mappers
    UserConfigMapper userConfigMapper;
    UserMapper userMapper;

    @Override
    public UserDto getCurrentUser(String email) {
        User user = userService.findByEmail(email);
        return userMapper.toDto(user);
    }

    @Override
    public List<UserConfigDto> findOrSave(Long userId) {
        List<UserConfig> userConfigs = userConfigService.find(userId);

        if(CollectionUtils.isEmpty(userConfigs)) {
            List<DefaultConfig> defaultConfigs = defaultConfigService.find();

            userConfigs = userConfigService.saveAll(
                    defaultConfigs.stream()
                            .map(defaultConfig ->
                                    UserConfig.builder()
                                            .userId(userId)
                                            .type(defaultConfig.getType())
                                            .configs(defaultConfig.getConfigs())
                                            .build()
                            ).collect(Collectors.toList())
            );
        }

        return userConfigMapper.toDto(userConfigs);
    }

    @Override
    public UserConfigDto create(Long userId, UserConfigDto configDto) {
        UserConfig toSave = userConfigMapper.toEntity(userId, configDto);
        UserConfig saved = userConfigService.save(toSave);

        return userConfigMapper.toDto(saved);
    }

    @Override
    public UserConfigDto update(Long id, Long userId, UserConfigDto configDto) {
        UserConfig existed = userConfigService.find(id, userId);
        existed.setType(configDto.getType());
        existed.setConfigs(configDto.getConfigs().toPrettyString());

        UserConfig saved = userConfigService.save(existed);
        return userConfigMapper.toDto(saved);
    }

    @Transactional
    @Override
    public void delete(Long id, Long userId) {
        userConfigService.delete(id, userId);
    }
}
