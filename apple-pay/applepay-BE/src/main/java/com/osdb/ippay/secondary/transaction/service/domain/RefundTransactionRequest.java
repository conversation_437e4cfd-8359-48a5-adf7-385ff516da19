package com.osdb.ippay.secondary.transaction.service.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

import static lombok.AccessLevel.PRIVATE;

@Data
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = PRIVATE)
public class RefundTransactionRequest {

    String ud1;

    String ud2;

    String ud3;

    Long amount;

    String accountType;

}
