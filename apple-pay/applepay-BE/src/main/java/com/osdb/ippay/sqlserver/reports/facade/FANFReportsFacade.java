/*package com.osdb.ippay.sqlserver.reports.facade;

import com.osdb.ippay.common.domain.FileDomain;
import com.osdb.ippay.sqlserver.reports.service.filter.FANFReportsFilter;
import com.osdb.ippay.sqlserver.reports.service.filter.TotalCardDataGtwReportsFilter;

import java.io.IOException;


public interface FANFReportsFacade {
	
    FileDomain export(Integer merchantID, String authStartDateTime, String authEndDateTime,  FANFReportsFilter filter) throws IOException;

}*/