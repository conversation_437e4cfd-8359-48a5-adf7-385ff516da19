package com.osdb.ippay.secondary.alternatepay.payment.controller.dto;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;
import lombok.experimental.FieldDefaults;

import static lombok.AccessLevel.PRIVATE;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class PaymentSecureAuthResponseDto {

    @JsonProperty("status")
    String status;
  
    @JsonProperty("responseCode")
    String responseCode;

    @JsonProperty("responseMessage")
    String responseMessage;
    
    @JsonProperty("authCode")
    String authCode;
    
    @JsonProperty("hostReferenceNumber")
    String hostReferenceNumber;
    
    @JsonProperty("taskID")
    String taskID;
    
    @JsonProperty("transactionID")
    String transactionID;
    
    @JsonProperty("transactionTimestamp")
    String transactionTimestamp;
    
    @JsonProperty("processedAmount")
    String processedAmount;
    
    @JsonProperty("addressVerificationCode")
    String addressVerificationCode;
    
    @JsonProperty("cardType")
    String cardType;
    
    @JsonProperty("maskedCardNumber")
    String maskedCardNumber;
    
    @JsonProperty("cardTransactionIdentifier")
    String cardTransactionIdentifier;
    
    @JsonProperty("customerReceipt")
    String customerReceipt;
    
    @JsonProperty("merchantReceipt")
    String merchantReceipt;
    
    @JsonProperty("tokenAssuranceLevel")
    String tokenAssuranceLevel;
    
    @JsonProperty("maskedPAN")
    String maskedPAN;
    
    @JsonProperty("tokenAccRangeStatus")
    String tokenAccRangeStatus;
}
