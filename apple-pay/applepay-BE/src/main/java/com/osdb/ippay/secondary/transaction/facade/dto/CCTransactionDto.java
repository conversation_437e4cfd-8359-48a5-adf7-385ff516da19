package com.osdb.ippay.secondary.transaction.facade.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.apache.commons.lang3.StringUtils;

import java.time.Instant;
import java.util.Optional;

import static lombok.AccessLevel.PRIVATE;

@Getter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class CCTransactionDto {

    Long id;
    
    int processorId;

    String terminalId;

    String merchantId;

    String merchantName;

    String approvalStatus;

    String cardType;

    String cardNumber;

    String cardHolder;

    String address;

    String city;

    String state;

    String zipCode;

    String country;

    String phone;

    String email;

    String token;

    String transactionId;

    String transactionType;

    String authCode;

    String authAmount;

    Integer authAmountCents;

    Instant authDate;

    String avs;

    String cvv;

    String ud1;

    String ud2;

    String ud3;

    String requestType;

    String currencyCode;

    String orderNumber;

    Instant settlementDate;

    String responseCode;

    String responseText;

    String ipAddress;

    RefundVoidBtn refundVoidBtn;

    String ccProcessor;

    String posTerminalId;

    String posAdditionalData;

    String vNumber;

    Long retRefNumber;

    Long ipTransId;

    String safeCardNumber;

    Integer expiry;

    String bankTransID;

    String externalTransID;

    public String getCardType() {
        if (StringUtils.isBlank(cardType)) {
            return cardType;
        }

        return switch (cardType) {
            case "MC" -> "M";
            case "VS" -> "V";
            case "DS", "R" -> "D";
            case "AX", "S" -> "A";
            default -> cardType;
        };
    }

    public String getTransactionType() {
        transactionType = Optional.ofNullable(transactionType).orElse("");
        return "I".equalsIgnoreCase(transactionType) ? "INTERNET" : transactionType;
    }

}
