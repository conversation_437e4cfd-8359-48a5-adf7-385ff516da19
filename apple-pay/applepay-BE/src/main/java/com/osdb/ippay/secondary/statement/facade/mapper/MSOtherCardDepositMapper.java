package com.osdb.ippay.secondary.statement.facade.mapper;

import com.osdb.ippay.secondary.statement.facade.dto.MSOtherCardDepositDto;
import com.osdb.ippay.secondary.statement.repository.entity.MSOtherCardDeposit;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class MSOtherCardDepositMapper {

    public List<MSOtherCardDepositDto> toDto(List<MSOtherCardDeposit> msOtherCardDeposits) {
        return msOtherCardDeposits.stream()
                .map(this::toDto)
                .collect(Collectors.toList());
    }

    public MSOtherCardDepositDto toDto(MSOtherCardDeposit msOtherCardDeposit) {
        return MSOtherCardDepositDto.builder()
                .id(msOtherCardDeposit.getId())
                .date(msOtherCardDeposit.getDate())
                .batchAmount(msOtherCardDeposit.getBatchAmount())
                .paidBy(msOtherCardDeposit.getPaidBy())
                .netAmount(msOtherCardDeposit.getNetAmount())
                .build();
    }
}
