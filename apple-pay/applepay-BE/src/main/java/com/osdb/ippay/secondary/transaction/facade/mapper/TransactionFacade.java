package com.osdb.ippay.secondary.transaction.facade.mapper;

import com.ippay.global.clients.ipp.IppayResponse;
import com.osdb.ippay.common.domain.FileDomain;
import com.osdb.ippay.primary.user.repository.entity.User;
import com.osdb.ippay.secondary.alternatepay.payment.controller.dto.PaymentResultDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionGetResultDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionSaveDto;
import com.osdb.ippay.secondary.transaction.facade.dto.ACHSettlementResponse;
import com.osdb.ippay.secondary.transaction.facade.dto.ACHTransactionDto;
import com.osdb.ippay.secondary.transaction.facade.dto.CCSettlementResponse;
import com.osdb.ippay.secondary.transaction.facade.dto.CCTransactionDto;
import com.osdb.ippay.secondary.transaction.repository.entity.CPCCTransaction;
import com.osdb.ippay.secondary.transaction.service.domain.RefundTransactionRequest;
import com.osdb.ippay.secondary.transaction.service.domain.SaleTransactionRequest;
import com.osdb.ippay.secondary.transaction.service.domain.VoidTransactionRequest;
import com.osdb.ippay.secondary.transaction.service.filter.AuthDateFilter;
import com.osdb.ippay.secondary.transaction.service.filter.SettlementFilter;
import com.osdb.ippay.secondary.transaction.service.filter.TransactionFilter;
import com.osdb.ippay.secondary.transaction.service.filter.TransactionType;
import org.springframework.data.domain.Sort;

import java.io.IOException;
import java.util.List;

public interface TransactionFacade {

    CCSettlementResponse findCCSettlements(String email, SettlementFilter filter);

    ACHSettlementResponse findACHSettlements(String email, SettlementFilter filter);

    List<CCTransactionDto> findCCTransactions(User user, TransactionFilter filter, Sort sort) throws Exception;

    List<ACHTransactionDto> findACHTransactions(String email, TransactionFilter filter, Sort sort);

    CCTransactionDto findCCTransaction(User user, Long id);

    ACHTransactionDto findACHTransaction(User user, Long id);

    FileDomain exportCCSettlementsSummary(String email, SettlementFilter filter) throws IOException;

    FileDomain exportACHSettlementsSummary(String email, SettlementFilter filter) throws IOException;

    FileDomain exportCCTransactions(String email, TransactionFilter filter) throws Exception;

    FileDomain exportCCSettlements(String email, TransactionFilter filter) throws Exception;

    FileDomain exportACHTransactions(String email, TransactionFilter filter) throws IOException;

    FileDomain exportACHSettlements(String email, TransactionFilter filter) throws IOException;

    List<String> findExistingCCAuthDates(AuthDateFilter filter, String email);

    List<String> findExistingACHAuthDates(AuthDateFilter filter, String email);

    Double totalAchAmount(TransactionType type, List<ACHTransactionDto> transactions);

    Double totalCCAmount(List<CCTransactionDto> ccTransactionDtos);

    IppayResponse saleTransaction(SaleTransactionRequest saleTransactionRequest);

    IppayResponse voidACHTransaction(Long id, VoidTransactionRequest voidTransactionRequest);

    IppayResponse voidCCTransaction(Long id, VoidTransactionRequest voidTransactionRequest);

    IppayResponse refundACHTransaction(Long id, RefundTransactionRequest refundTransactionRequest);

    IppayResponse refundCCTransaction(Long id, RefundTransactionRequest refundTransactionRequest);
    
    CCTransactionDto toEntity(TransactionSaveDto transactionSaveDto, 
							 PaymentResultDto paymentResultDto,
							 TransactionGetResultDto paymentGetResultDto,String mid,
								String terminalId);

}
