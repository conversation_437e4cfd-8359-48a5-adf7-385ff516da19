package com.osdb.ippay.primary.user.repository.entity;

import com.osdb.ippay.common.entity.BaseEntity;
import lombok.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import lombok.experimental.SuperBuilder;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Enumerated;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;

import static javax.persistence.EnumType.STRING;
import static lombok.AccessLevel.PRIVATE;

@Entity
@Table(name = "user_config")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = PRIVATE)
@SuperBuilder(toBuilder = true)
public class UserConfig extends BaseEntity {

    @Enumerated(value = STRING)
    @NotNull(message = "Missing required parameter: 'type'")
    @Column(name = "type")
    ConfigType type;

    @Column(name = "user_id")
    Long userId;

    @Column(name = "configs")
    String configs;

}
