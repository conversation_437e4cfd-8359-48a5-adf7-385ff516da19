package com.osdb.ippay.sqlserveripcore.ipwhitelist.facade.mapper;

import com.osdb.ippay.sqlserveripcore.ipwhitelist.facade.dto.TerminalIpsBlockingDto;
import com.osdb.ippay.sqlserveripcore.ipwhitelist.facade.dto.TerminalIpsGlobalDto;
import com.osdb.ippay.sqlserveripcore.ipwhitelist.repository.entity.TerminalIpsBlocking;
import com.osdb.ippay.sqlserveripcore.ipwhitelist.repository.entity.TerminalIpsGlobal;

import org.springframework.stereotype.Component;

@Component
public class TerminalIpsBlockingMapper {

    public TerminalIpsBlockingDto toDto(TerminalIpsBlocking terminalIpsBlocking) {
        if (terminalIpsBlocking == null) {
            return null;
        }
        return TerminalIpsBlockingDto.builder()
                .id(terminalIpsBlocking.getId())
                .terminalId(terminalIpsBlocking.getTerminalId())
                .ipAddress(terminalIpsBlocking.getIpAddress())
                .username(terminalIpsBlocking.getUsername())
                .dateCreated(terminalIpsBlocking.getDateCreated())
                .dateModified(terminalIpsBlocking.getDateModified())
                .active(terminalIpsBlocking.getActive())
                .build();
    }

    public TerminalIpsBlocking toEntity(TerminalIpsBlockingDto terminalIpsBlockingDto) {
        if (terminalIpsBlockingDto == null) {
            return null;
        }
        TerminalIpsBlocking terminalIpsBlocking = new TerminalIpsBlocking();
        terminalIpsBlocking.setTerminalId(terminalIpsBlockingDto.getTerminalId());
        terminalIpsBlocking.setActive(terminalIpsBlockingDto.getActive());
        terminalIpsBlocking.setDateCreated(terminalIpsBlockingDto.getDateCreated());
        terminalIpsBlocking.setDateModified(terminalIpsBlockingDto.getDateModified());
        terminalIpsBlocking.setIpAddress(terminalIpsBlockingDto.getIpAddress());
        terminalIpsBlocking.setUsername(terminalIpsBlockingDto.getUsername());
       
        return terminalIpsBlocking;
    }

    public TerminalIpsBlockingDto putEntity(
    		TerminalIpsBlockingDto terminalIpsBlockingDto,
    		TerminalIpsBlocking terminalIpsBlocking) {
    	terminalIpsBlocking.setActive(terminalIpsBlockingDto.getActive());
    	terminalIpsBlocking.setTerminalId(terminalIpsBlockingDto.getTerminalId());
    	terminalIpsBlocking.setDateCreated(terminalIpsBlockingDto.getDateCreated());
    	terminalIpsBlocking.setDateModified(terminalIpsBlockingDto.getDateModified());
    	terminalIpsBlocking.setIpAddress(terminalIpsBlockingDto.getIpAddress());
    	terminalIpsBlocking.setUsername(terminalIpsBlockingDto.getUsername());
    	
        return toDto(terminalIpsBlocking);
    }

}