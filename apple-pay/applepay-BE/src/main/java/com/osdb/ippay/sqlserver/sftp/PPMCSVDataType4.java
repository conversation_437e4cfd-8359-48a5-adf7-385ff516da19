package com.osdb.ippay.sqlserver.sftp;

import static lombok.AccessLevel.PRIVATE;

import com.opencsv.bean.CsvBindByPosition;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

@Getter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class PPMCSVDataType4 {
    @CsvBindByPosition(position = 0)
    private String bankID;

    @CsvBindByPosition(position = 1)
    private String rpAgentId;

    @CsvBindByPosition(position = 2)
    private String groupId;

    @CsvBindByPosition(position = 3)
    private String associationId;
    
    @CsvBindByPosition(position = 4)
    private String merchantId;

    @CsvBindByPosition(position = 5)
    private String recordType;

    @CsvBindByPosition(position = 6)
    private String feeCategoryDescription;

    @CsvBindByPosition(position = 7)
    private String feeCategory;
    
    @CsvBindByPosition(position = 8)
    private String feeItemKey1;

    @CsvBindByPosition(position = 9)
    private String feeItemKey2;

    @CsvBindByPosition(position = 10)
    private String feeItemKey3;

    @CsvBindByPosition(position = 11)
    private String feeItemKey4;
    
    @CsvBindByPosition(position = 12)
    private String feeItemKey5;

    @CsvBindByPosition(position = 13)
    private String netCountAppliedToRPAgentExpense;

    @CsvBindByPosition(position = 14)
    private String netAmountAppliedToRPAgentExpense;
    
    @CsvBindByPosition(position = 15)
    private String rpAgentExpenseFeePercent;

    @CsvBindByPosition(position = 16)
    private String rpAgentExpenseFeePerItem;

    @CsvBindByPosition(position = 17)
    private String applyPassthrough;

    @CsvBindByPosition(position = 18)
    private String merchantIncome;
    
    @CsvBindByPosition(position = 19)
    private String agentIncome;
    
    @CsvBindByPosition(position = 20)
    private String rpAgentFeesPaid;

    @CsvBindByPosition(position = 21)
    private String rpAgentShare;
   
}