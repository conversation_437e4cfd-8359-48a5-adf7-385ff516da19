package com.osdb.ippay.secondary.chargeback.controller;

import com.osdb.ippay.common.domain.FileDomain;
import com.osdb.ippay.common.logging.LogExecutionTime;
import com.osdb.ippay.common.util.annotation.ApiPageable;
import com.osdb.ippay.common.util.response.PageResponse;
import com.osdb.ippay.secondary.chargeback.facade.ChargebackFacade;
import com.osdb.ippay.secondary.chargeback.facade.dto.ChargebackDto;
import com.osdb.ippay.secondary.chargeback.service.ChargebackFilter;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static lombok.AccessLevel.PRIVATE;

@Tag(name = "chargeback")
@RestController
@RequestMapping(value = "/api/v1/private/chargebacks")
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class ChargebackController {
/*
    ChargebackFacade chargebackFacade;

    @LogExecutionTime
    @ApiPageable
    @GetMapping
    public ResponseEntity<PageResponse<ChargebackDto>> get(@Valid @ParameterObject ChargebackFilter filter,
                                                           @Parameter(hidden = true) @AuthenticationPrincipal String email,
                                                           @Parameter(hidden = true) Pageable pageable) {

        Page<ChargebackDto> response = chargebackFacade.find(email, filter, pageable);
        return ResponseEntity.ok(new PageResponse<>(response.getContent(), response.getTotalElements()));
    }

    @LogExecutionTime
    @GetMapping("/{merchantId}/dates")
    public ResponseEntity<List<String>> getQuickDates(@PathVariable String merchantId,
                                                      @Parameter(hidden = true) @AuthenticationPrincipal String email) {
        List<String> response = chargebackFacade.findDates(email, merchantId);
        return ResponseEntity.ok(response);
    }

    @LogExecutionTime
    @GetMapping("/export")
    public ResponseEntity<ByteArrayResource> exportACHTransactions(
            @Valid @ParameterObject ChargebackFilter filter,
            @Parameter(hidden = true) @AuthenticationPrincipal String email) throws IOException {

        FileDomain response = chargebackFacade.export(email, filter);

        return ResponseEntity.ok()
                .contentType(new MediaType("application", "force-download"))
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=".concat(response.getFileName()))
                .body(response.getFile());
    }*/
}
