package com.osdb.ippay.secondary.alternatepay.transaction.controller.facade.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionCommitDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionGetDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionGetResultDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionInitDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TrasactionInitResultDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.facade.*;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.mapper.TransactionCommitGetControllerMapper;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.mapper.TransactionGetControllerMapper;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.mapper.TransactionInitControllerMapper;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.Base64;
import java.util.Objects;

import static lombok.AccessLevel.PRIVATE;

@Component
@RequiredArgsConstructor
@FieldDefaults(makeFinal = true, level = PRIVATE)
public class TransactionCommitGetFacadeImpl implements TransactionCommitGetFacade {

    public static final String INVALID_JOB_STATE = "Job can not be deleted, job state not 'Queued'";

    TransactionCommitGetControllerMapper mapper;

    @Autowired
    private ObjectMapper objectMapper;
    
    @NonFinal
    @Value("${transaction-commit.url.get}")
    String transactionUrlGet;


    @NonFinal
    @Value("${transaction-fetch.url.post}")
    String transactionUrlPost;
    
    @NonFinal
    boolean blnIsDecoded = true;
    
	@Override
	public TransactionGetResultDto create(String trnId, TransactionCommitDto transactionCommitDto) {
		RestTemplate restTemplate = new RestTemplate();

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        String objJackson = "";
        
        try {
			   objJackson = objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(transactionCommitDto);
			 //  System.out.println("Request body - 1 - " + objJackson);
			 
		} catch (JsonProcessingException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
        
        HttpEntity<String> entity = new HttpEntity<String>(objJackson, headers);
        
      
        if(!blnIsDecoded)
        {
        	byte[] decodedBytes = Base64.getDecoder().decode(transactionUrlGet);
            transactionUrlGet = new String(decodedBytes);
        }
        
        
        TransactionGetResultDto trnGetResultDto = restTemplate.exchange(
        		transactionUrlGet + trnId +"/commit",
                HttpMethod.POST,
                entity,
                new ParameterizedTypeReference<TransactionGetResultDto>() {
                },
                ""
        ).getBody();

        return Objects.nonNull(trnGetResultDto) ? mapper.toOutDto(trnGetResultDto) : null;
	}
}
