package com.osdb.ippay.email.service;

import java.io.IOException;
import java.io.InputStream;

import org.springframework.core.io.AbstractResource;
import java.io.IOException;
import java.io.InputStream;

import org.springframework.core.io.AbstractResource;


public class InputStreamResource extends AbstractResource {
	   //...
	   @Override
	   public boolean isOpen() {
	      return true;
	   }
	   //...

	@Override
	public String getDescription() {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public InputStream getInputStream() throws IOException {
		// TODO Auto-generated method stub
			return null;
	}
	
}