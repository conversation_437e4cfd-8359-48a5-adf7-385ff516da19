package com.osdb.ippay.secondary.alternatepay.refund.controller.facade;

import com.osdb.ippay.secondary.alternatepay.payment.controller.dto.PaymentDto;
import com.osdb.ippay.secondary.alternatepay.payment.controller.dto.PaymentResultDto;
import com.osdb.ippay.secondary.alternatepay.refund.controller.dto.MerchantPartialRefundDto;
import com.osdb.ippay.secondary.alternatepay.refund.controller.dto.MerchantRefundDto;
import com.osdb.ippay.secondary.alternatepay.refund.controller.dto.TransactionRefundResultDto;


public interface MerchantRefundFacade {

    TransactionRefundResultDto create(MerchantRefundDto merchantRefundDto);
    TransactionRefundResultDto createPartial(MerchantPartialRefundDto merchantPartialRefundDto);
}
