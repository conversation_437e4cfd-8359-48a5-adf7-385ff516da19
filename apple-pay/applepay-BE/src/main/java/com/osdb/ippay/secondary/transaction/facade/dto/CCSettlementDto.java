package com.osdb.ippay.secondary.transaction.facade.dto;

import lombok.*;
import lombok.experimental.FieldDefaults;

import java.time.Instant;

import static lombok.AccessLevel.PRIVATE;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class CCSettlementDto {

    Instant date;

    String salesAmount;

    Long salesCount;

    String refundAmount;

    Long refundCount;

    String salesRefundAmount;

    Long salesRefundCount;

    String axAmount;

    String axRefund;

    Long axCount;

    String bankCardSalesAmount;

    Long bankCardCount;

}
