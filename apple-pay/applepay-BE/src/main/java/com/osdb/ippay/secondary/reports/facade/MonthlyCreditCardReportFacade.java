package com.osdb.ippay.secondary.reports.facade;

import com.osdb.ippay.common.domain.FileDomain;
import com.osdb.ippay.secondary.alerts.facade.dto.SettledTransactionDto;
import com.osdb.ippay.secondary.alerts.service.filter.SettledTransactionFilter;
import com.osdb.ippay.secondary.reports.facade.dto.CreditCountReportDto;
import com.osdb.ippay.secondary.reports.facade.dto.MonthlyCreditCardReportDto;
import com.osdb.ippay.secondary.reports.service.filter.CreditCountReportFilter;
import com.osdb.ippay.sqlserver.reports.facade.dto.TotalACHGtwDataDto;
import com.osdb.ippay.sqlserver.reports.service.filter.TotalACHGtwDataFilter;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Map;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;


public interface MonthlyCreditCardReportFacade {
			
    FileDomain export(String startDateTime, String endDateTime) throws IOException;
    Page<MonthlyCreditCardReportDto> findData(String startDateTime, String endDateTime,  CreditCountReportFilter filter, Pageable pageable);
}