package com.osdb.ippay.primary.pdf.service.impl;

import com.osdb.ippay.primary.pdf.service.PdfService;
import com.osdb.ippay.primary.pdf.service.filter.PdfFilter;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.HttpClients;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Stream;

import static java.nio.charset.StandardCharsets.UTF_8;
import static lombok.AccessLevel.PRIVATE;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

@Slf4j
@Service
@FieldDefaults(level = PRIVATE)
public class PdfServiceImpl implements PdfService {

    private static final String AUTH_COOKIE = "AUTH_COOKIE";

 
    String PDF_SERVICE_URL;

    @Value("${allowed.domain}")
    String DOMAIN;

    @Override
    public byte[] getPdf(PdfFilter filter, HttpServletRequest request) throws IOException {
        HttpClient httpClient = HttpClients.createDefault();

        String requestURL = String.format(
                "%s?url=%s&cookie_domain=.%s",
                PDF_SERVICE_URL,
                URLEncoder.encode(filter.getUrl(), UTF_8),
                DOMAIN) +
                (isNotBlank(filter.getWaitForSelector()) ? "&wait_for_selector=".concat(filter.getWaitForSelector()) : "") +
                (isNotBlank(filter.getPdfFormat()) ? "&pdf_format=".concat(filter.getPdfFormat()) : "") +
                (isNotBlank(filter.getPdfLandscape()) ? "&pdf_landscape=".concat(filter.getPdfLandscape()) : "") +
                (isNotBlank(filter.getBrowserProduct()) ? "&browser_product=".concat(filter.getBrowserProduct()) : "");

        log.info(String.format("Pdf request URL: %s", requestURL));

        HttpGet httpGet = new HttpGet(requestURL);

        Cookie authCookie = Stream.of(
                Optional
                        .ofNullable(request.getCookies())
                        .orElse(new Cookie[0]))
                .filter(cookie -> AUTH_COOKIE.equals(cookie.getName()))
                .findFirst().orElse(null);

        if(Objects.isNull(authCookie)) {
            return null;
        }

        httpGet.setHeader("Cookie", String.format("%s=%s", AUTH_COOKIE, authCookie.getValue()));
        HttpResponse response = httpClient.execute(httpGet);

        return response.getEntity().getContent().readAllBytes();
    }
}
