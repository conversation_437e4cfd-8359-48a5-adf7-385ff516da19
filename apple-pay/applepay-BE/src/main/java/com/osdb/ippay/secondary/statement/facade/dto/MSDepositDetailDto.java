package com.osdb.ippay.secondary.statement.facade.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

import static lombok.AccessLevel.PRIVATE;

@Getter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class MSDepositDetailDto {

    Long id;

    String depositDate;

    Integer numOfTrans;

    String batchAmount;

    String ocBatch;

    String adjust;

    String chargebacks;

    String feePaid;

    String netDeposit;

}
