package com.osdb.ippay.sqlserver.sftp;

import static lombok.AccessLevel.PRIVATE;

import com.opencsv.bean.CsvBindByPosition;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

@Getter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class PPMCSVDataType1 {
    @CsvBindByPosition(position = 0)
    private String bankID;

    @CsvBindByPosition(position = 1)
    private String rpAgentId;

    @CsvBindByPosition(position = 2)
    private String groupId;

    @CsvBindByPosition(position = 3)
    private String associationId;
    
    @CsvBindByPosition(position = 4)
    private String merchantId;

    @CsvBindByPosition(position = 5)
    private String recordType;

    @CsvBindByPosition(position = 6)
    private String processingDate;

    @CsvBindByPosition(position = 7)
    private String dbaName;
    
    @CsvBindByPosition(position = 8)
    private String dbaCityAddress;

    @CsvBindByPosition(position = 9)
    private String dbaStateCountry;

    @CsvBindByPosition(position = 10)
    private String dbaZip;

    @CsvBindByPosition(position = 11)
    private String visaSIC;
    
    @CsvBindByPosition(position = 12)
    private String mcMcc;

    @CsvBindByPosition(position = 13)
    private String reserved1;

    @CsvBindByPosition(position = 14)
    private String reserved2;
    
    @CsvBindByPosition(position = 15)
    private String merchantStatus;

    @CsvBindByPosition(position = 16)
    private String dateClosed;

    @CsvBindByPosition(position = 17)
    private String minBillDiscDiff;

    @CsvBindByPosition(position = 18)
    private String minDiscAmount;
    
    @CsvBindByPosition(position = 19)
    private String expenseRateGroup;
 
}