package com.osdb.ippay.primary.user.repository;

import com.osdb.ippay.primary.user.repository.entity.User;
import com.osdb.ippay.primary.user.repository.entity.UserStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@EnableJpaRepositories
public interface UserRepository extends
        JpaRepository<User, Long>,
        JpaSpecificationExecutor<User> {
	
    @Override
    @EntityGraph(attributePaths = {"merchants"})
    Page<User> findAll(Specification<User> specification, Pageable pageable);

    Optional<User> findByEmail(String email);

    Optional<User> findByEmailAndStatus(String email, UserStatus status);

    Optional<User> findByEmailAndStatusAndSessionsHash(String email, UserStatus status, String sessionsHash);

    Optional<User> findByResetPasswordHash(String resetPasswordHash);

    Boolean existsByEmail(String email);

}
