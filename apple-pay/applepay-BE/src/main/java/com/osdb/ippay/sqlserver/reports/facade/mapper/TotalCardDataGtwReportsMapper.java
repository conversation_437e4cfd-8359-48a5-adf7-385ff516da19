package com.osdb.ippay.sqlserver.reports.facade.mapper;

import com.osdb.ippay.sqlserver.reports.facade.dto.TotalCardDataGtwReportsDto;
import com.osdb.ippay.sqlserver.reports.repository.entity.TotalCardDataGtwReports;

import org.springframework.data.domain.Page;
//import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;

@Component
public class TotalCardDataGtwReportsMapper {

    public Page<TotalCardDataGtwReportsDto> toDto(Page<TotalCardDataGtwReports> totalCardDataGtwReports) {
        return totalCardDataGtwReports.map(this::toDto);
    }

    public TotalCardDataGtwReportsDto toDto(TotalCardDataGtwReports totalCardDataGtwReports) {
       

        return TotalCardDataGtwReportsDto.builder()
        		.ipTransactionId(totalCardDataGtwReports.getIpTransactionId())
                .merchantID(totalCardDataGtwReports.getMerchantID())
                .ccProcessor(totalCardDataGtwReports.getCcProcessor())
                .actionCode(totalCardDataGtwReports.getActionCode())
                .requestType(totalCardDataGtwReports.getRequestType())
                .transactionCount(totalCardDataGtwReports.getTransactionCount())
                .totalAmount(totalCardDataGtwReports.getTotalAmount())
                .spsMid(totalCardDataGtwReports.getSpsMid())
                .trnId(totalCardDataGtwReports.getTrnId())
                .orgName(totalCardDataGtwReports.getOrgName())
                .build();
    }
}
