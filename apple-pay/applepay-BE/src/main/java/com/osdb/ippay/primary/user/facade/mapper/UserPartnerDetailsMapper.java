package com.osdb.ippay.primary.user.facade.mapper;

import com.osdb.ippay.primary.user.facade.dto.UserPartnerDetailsDto;
import com.osdb.ippay.primary.user.repository.entity.UserPartnerDetails;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;

@Component
public class UserPartnerDetailsMapper {

    public Page<UserPartnerDetailsDto> toDto(Page<UserPartnerDetails> userPartnerDetails) {
        return userPartnerDetails.map(this::toDto);
    }

    public UserPartnerDetailsDto toDto(UserPartnerDetails userPartnerDetails) {
       

        return UserPartnerDetailsDto.builder()
        	
        		.email(userPartnerDetails.getEmail())
               // .firstName(userPartnerDetails.getFirstName())
               // .lastName(userPartnerDetails.getLastName())
                .build();
    }
}
