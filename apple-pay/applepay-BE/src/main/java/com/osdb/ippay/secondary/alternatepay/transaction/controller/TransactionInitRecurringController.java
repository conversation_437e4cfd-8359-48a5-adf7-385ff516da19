package com.osdb.ippay.secondary.alternatepay.transaction.controller;

import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.SubscriptionCancelDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionGetResultDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionInitRecurringDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionInitRecurringResultDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.facade.TransactionInitRecurringFacade;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.facade.TransactionRecurringCancelFacade;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.mapper.TransactionInitRecurringControllerMapper;

import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import static lombok.AccessLevel.PRIVATE;

@Tag(name = "Alternatepay/Transaction")
@RestController
@RequestMapping(value = "/api/v2/payment/alternatepay")
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class TransactionInitRecurringController {

	TransactionInitRecurringFacade transactionInitRecurringFacade; 
	TransactionRecurringCancelFacade transactionRecurringCancelFacade; 

	/*
    @PostMapping("/transaction/init/recurring")
    public ResponseEntity<TransactionInitRecurringResultDto> InitRecurringTransaction(@Valid @RequestBody TransactionInitRecurringDto transactionInitRecurringDto) {
    	TransactionInitRecurringResultDto response = transactionInitRecurringFacade.create(transactionInitRecurringDto);
        return ResponseEntity.ok(response);
    }

    @PatchMapping("/transaction/recurring/cancel/{subscriptionId}")
    public ResponseEntity<TransactionGetResultDto> CancelRecurringTransaction(
    		@Valid @RequestBody SubscriptionCancelDto subscriptionCancelDto,
    		@PathVariable String subscriptionId, String branchApiKey, String merchantApiKey)
    {
    	TransactionGetResultDto response = transactionRecurringCancelFacade.cancelSubscription(subscriptionCancelDto,subscriptionId, branchApiKey, merchantApiKey);
        return ResponseEntity.ok(response);
    }
*/
}
