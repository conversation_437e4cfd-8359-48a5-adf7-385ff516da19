package com.osdb.ippay.secondary.transaction.service;

import com.osdb.ippay.primary.user.repository.entity.User;
import com.osdb.ippay.secondary.transaction.repository.entity.CCSettlement;
import com.osdb.ippay.secondary.transaction.repository.entity.CPCCTransaction;
import com.osdb.ippay.secondary.transaction.service.filter.SettlementFilter;
import com.osdb.ippay.secondary.transaction.service.filter.TransactionFilter;

import java.util.List;
import java.util.concurrent.Future;

public interface CPCCTransactionService {

    Future<List<CPCCTransaction>> find(User authUser, TransactionFilter filter);

    CPCCTransaction find(Long id);

    List<CCSettlement> findSettlements(SettlementFilter filter);

}
