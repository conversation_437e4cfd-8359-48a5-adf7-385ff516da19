package com.osdb.ippay.secondary.alternatepay.transaction.controller.facade;

import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionSaveDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionCancelResultDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionGetResultDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TrasactionInitResultDto;


public interface TransactionResponseFacade {
    
    TransactionSaveDto findByTrnid(String Trnid);
    
    TransactionSaveDto create(TrasactionInitResultDto transactionSaveDto);

    TransactionSaveDto update(TrasactionInitResultDto transactionSaveDto, String Trnid);
    
    TransactionSaveDto cancel(TransactionCancelResultDto transactionCancelResultDto, String Trnid);
    
    TransactionSaveDto commit(TransactionGetResultDto transactionGetResultDto, String Trnid);
}