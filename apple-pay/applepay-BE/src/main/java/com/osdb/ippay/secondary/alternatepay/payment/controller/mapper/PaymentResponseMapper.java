package com.osdb.ippay.secondary.alternatepay.payment.controller.mapper;

import com.osdb.ippay.secondary.alternatepay.payment.controller.dto.PaymentSaveDto;
import com.osdb.ippay.secondary.alternatepay.refund.controller.dto.RefundSaveDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionSaveDto;
import com.osdb.ippay.secondary.alternatepay.payment.controller.repository.entity.PaymentResponse;
import com.osdb.ippay.secondary.alternatepay.refund.controller.repository.entity.RefundResponse;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.Repository.Entity.TransactionResponse;
import com.osdb.ippay.secondary.alternatepay.payment.controller.dto.PaymentResultDto;
import com.osdb.ippay.secondary.alternatepay.refund.controller.dto.TransactionRefundResultDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionCancelResultDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionGetResultDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TrasactionInitResultDto;

import org.springframework.stereotype.Component;

@Component
public class PaymentResponseMapper {

    public PaymentSaveDto toDto(PaymentResponse paymentResponse) {
        if (paymentResponse == null) {
            return null;
        }
        return PaymentSaveDto.builder()
                .amount(paymentResponse.getAmount())
                .approvalDate(paymentResponse.getApprovalDate())
                .approvedBy(paymentResponse.getApprovedBy())
                .confirmationNumber(paymentResponse.getConfirmationNumber())
                .paymentMethod(paymentResponse.getPaymentMethod())
                .paymentMethodCode(paymentResponse.getPaymentMethodCode())
                .paymentMethodName(paymentResponse.getPaymentMethodName())
                .paymentResult(paymentResponse.isPaymentResult())
                .providerId(paymentResponse.getProviderId())
                .providerName(paymentResponse.getProviderName())
                .requestId(paymentResponse.getRequestId())
                .transactionNumber(paymentResponse.getTransactionNumber())
                .transactionStatus(paymentResponse.getTransactionStatus())
                
                .build();
    }

    public PaymentResponse toEntity(PaymentResultDto paymentSaveDto) {
        if (paymentSaveDto == null) {
            return null;
        }
        PaymentResponse paymentResponse = new PaymentResponse();
        paymentResponse.setAmount(paymentSaveDto.getConfirmation().getAmount());
        paymentResponse.setApprovalDate(paymentSaveDto.getConfirmation().getApprovalDate());
        paymentResponse.setApprovedBy(paymentSaveDto.getConfirmation().getApprovedBy());
        paymentResponse.setConfirmationNumber(paymentSaveDto.getConfirmation().getConfirmationNumber());
        paymentResponse.setPaymentMethod(paymentSaveDto.getConfirmation().getPaymentMethod());
        paymentResponse.setPaymentType(paymentSaveDto.getPaymentType());
        paymentResponse.setPaymentMethodCode(paymentSaveDto.getConfirmation().getPaymentMethodCode());
        paymentResponse.setPaymentMethodName(paymentSaveDto.getConfirmation().getPaymentMethodName());
        paymentResponse.setPaymentResult(paymentSaveDto.isSuccess());
        paymentResponse.setProviderId(paymentSaveDto.getConfirmation().getProviderId());
        paymentResponse.setProviderName(paymentSaveDto.getConfirmation().getProviderName());
        paymentResponse.setRequestId(paymentSaveDto.getConfirmation().getRequestID());
        paymentResponse.setTransactionNumber(paymentSaveDto.getTransactionNumber());
        paymentResponse.setTransactionStatus(paymentSaveDto.getTransactionStatus());
        
        return paymentResponse;
    }

    public TransactionResponse toTransactionEntity(PaymentResultDto paymentSaveDto) {
        if (paymentSaveDto == null) {
            return null;
        }
        
        long transactionId = (long) Math.floor(Math.random() * 900000000000000000L) + 100000000000000000L;
        
        //Payment related fields
        TransactionResponse transactionResponse = new TransactionResponse();
        transactionResponse.setAmount(paymentSaveDto.getConfirmation().getAmount());
        transactionResponse.setApprovalDate(paymentSaveDto.getConfirmation().getApprovalDate());
        transactionResponse.setApprovedBy(paymentSaveDto.getConfirmation().getApprovedBy());
        transactionResponse.setConfirmationNumber(paymentSaveDto.getConfirmation().getConfirmationNumber());
        transactionResponse.setPaymentMethod(paymentSaveDto.getConfirmation().getPaymentMethod());
        transactionResponse.setPaymentType(paymentSaveDto.getPaymentType());
        transactionResponse.setPaymentMethodCode(paymentSaveDto.getConfirmation().getPaymentMethodCode());
        transactionResponse.setPaymentMethodName(paymentSaveDto.getConfirmation().getPaymentMethodName());
        transactionResponse.setProviderId(paymentSaveDto.getConfirmation().getProviderId());
        transactionResponse.setProviderName(paymentSaveDto.getConfirmation().getProviderName());
        transactionResponse.setRequestId(paymentSaveDto.getConfirmation().getRequestID());
        transactionResponse.setTransactionNumber(paymentSaveDto.getTransactionNumber());
        transactionResponse.setStatusName(paymentSaveDto.getTransactionStatus());
        
        //Transaction related fields
        transactionResponse.setBaseAmount(paymentSaveDto.getConfirmation().getAmount());
        transactionResponse.setCurrency("USD");
        transactionResponse.setDate(paymentSaveDto.getConfirmation().getApprovalDate());
        transactionResponse.setMethodType(0);
        transactionResponse.setPaymentPage("");
        transactionResponse.setPaymentType(paymentSaveDto.getPaymentType());
        transactionResponse.setReason("");
        transactionResponse.setStatus(Integer.parseInt(paymentSaveDto.getConfirmation().getPaymentMethodCode()));
        transactionResponse.setTimeoutDate(paymentSaveDto.getConfirmation().getApprovalDate());
        transactionResponse.setTransactionNumber(paymentSaveDto.getTransactionNumber());
        transactionResponse.setTransactionId(transactionId);
        
        return transactionResponse;
    }

    public PaymentResponse putEntityToken(
    		TransactionGetResultDto transactionGetResultDto,
    		PaymentResponse paymentResponse) {
    	
    	 
        if(transactionGetResultDto.getPaymentDetails() != null)
        	paymentResponse.setToken(transactionGetResultDto.getPaymentDetails().getToken());
            
        return paymentResponse;
    }

    public TransactionResponse putEntityAddress(
    		TransactionGetResultDto transactionGetResultDto,
    		TransactionResponse transactionResponse) {
    	
    	 
        if(transactionGetResultDto != null && transactionGetResultDto.getPosData() != null)
        {
        	transactionResponse.setAddress(transactionGetResultDto.getPosData().getValueJSON().getAddress());  
        	transactionResponse.setCity(transactionGetResultDto.getPosData().getValueJSON().getCity());  
        	transactionResponse.setCountry(transactionGetResultDto.getPosData().getValueJSON().getCountry());  
        	transactionResponse.setState(transactionGetResultDto.getPosData().getValueJSON().getState());  
        	transactionResponse.setZip(transactionGetResultDto.getPosData().getValueJSON().getZip());  
        }
        	
        
        return transactionResponse;
    }

}