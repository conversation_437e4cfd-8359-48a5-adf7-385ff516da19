package com.osdb.ippay.sqlserver.sftp.facade.impl;

import com.osdb.ippay.common.domain.FileDomain;
import com.osdb.ippay.sqlserver.sftp.facade.MerchantPANDetailsFacade;
import com.osdb.ippay.sqlserver.sftp.repository.entity.MerchantPANDetails;
import com.osdb.ippay.sqlserver.sftp.service.MerchantPANDetailsService;

import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.stereotype.Component;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.PrintWriter;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static lombok.AccessLevel.PRIVATE;

@Component
@RequiredArgsConstructor
@FieldDefaults(makeFinal = true, level = PRIVATE)
public class MerchantPANDetailsFacadeImpl implements MerchantPANDetailsFacade {

    // services
	MerchantPANDetailsService merchantPANDetailsService;

	@Override
	public InputStream exportPANDetailsStream(String mid) throws IOException {

		 String[] HEADERS = {
	                "Merchant_ID",
	                "Merchant_Name",
	                "Terminal_ID",
	                "Transaction_ID",
	                "Request_Type",
	                "TotalAmount",
	                "FeeAmount",
	                "Tax_Amount",
	                "Auth_Code",
	                "AuthDateTime",
	                "CardNum",
	                "Token",
	                "CVV2",
	                "CardHolderName",
	                "Address",
	                "City",
	                "State",
	                "Zip",
	                "Country",
	                "Phone",
	                "Email",
	                "TokenSourceValue",
	                "TokenTypeCode",
	                "ExpMonth",
	                "ExpYear"
	        };

	        final CSVFormat format = CSVFormat.Builder.create()
	                .setHeader(HEADERS)
	                .setIgnoreEmptyLines(true)
	                .build();
	        
	       
	        List<MerchantPANDetails> merchantPANDetails  = merchantPANDetailsService.FetchPANDetails(mid);
	       
	        try (
	                ByteArrayOutputStream out = new ByteArrayOutputStream();
	                CSVPrinter csvPrinter = new CSVPrinter(new PrintWriter(out), format)
	        ) {
	        	

	            for (MerchantPANDetails merchantPANDetailsObj : merchantPANDetails) {
	                List<String> data = Arrays.asList(
	                		merchantPANDetailsObj.getMerchant_ID(),
	                		merchantPANDetailsObj.getMerchant_Name(),
	                		merchantPANDetailsObj.getTerminal_ID(),
	                		merchantPANDetailsObj.getTransaction_ID(),
	                		merchantPANDetailsObj.getRequest_Type(),
	                		merchantPANDetailsObj.getTotalAmount(),
	                		merchantPANDetailsObj.getFeeAmount(),
	                		merchantPANDetailsObj.getTax_Amount(),
	                		merchantPANDetailsObj.getAuth_Code(),
	                		merchantPANDetailsObj.getAuthDateTime(),
	                		merchantPANDetailsObj.getCardNum(),
	                		merchantPANDetailsObj.getToken(),
	                		merchantPANDetailsObj.getCVV2(),
	                		merchantPANDetailsObj.getCardHolderName(),
	                		merchantPANDetailsObj.getAddress(),
	                		merchantPANDetailsObj.getCity(),
	                		merchantPANDetailsObj.getState(),
	                		merchantPANDetailsObj.getZip(),
	                		merchantPANDetailsObj.getCountry(),
	                		merchantPANDetailsObj.getPhone(),
	                		merchantPANDetailsObj.getEmail(),
	                		merchantPANDetailsObj.getTokenSourceValue(),
	                		merchantPANDetailsObj.getTokenTypeCode(),
	                		merchantPANDetailsObj.getExpMonth(),
	                		merchantPANDetailsObj.getExpYear()
	                		
	                );

	                csvPrinter.printRecord(data);
	                // Convert List to stream
	                //Stream<String> stream = convertListToStream(data);
	                //input = convert(stream);
	            }
	           
	            csvPrinter.flush();
	            byte[] input = new ByteArrayInputStream(out.toByteArray()).readAllBytes();
	            return new ByteArrayInputStream(input);

	           
	        }

	}
	
	 private static <T> Stream<T> convertListToStream(List<T> list)
	    {
	        return list.stream();
	    }
	 
	 public static InputStream convert(Stream<String> stringStream) {
	        String content = stringStream.collect(Collectors.joining(System.lineSeparator()));
	        byte[] bytes = content.getBytes(StandardCharsets.UTF_8);
	        return new ByteArrayInputStream(bytes);
	    }

	 @Override
	 public FileDomain export(String mid) throws IOException {
		 
		String[] HEADERS = {
	                "Merchant_ID",
	                "Merchant_Name",
	                "Terminal_ID",
	                "Transaction_ID",
	                "Request_Type",
	                "TotalAmount",
	                "FeeAmount",
	                "Tax_Amount",
	                "Auth_Code",
	                "AuthDateTime",
	                "CardNum",
	                "Token",
	                "CVV2",
	                "CardHolderName",
	                "Address",
	                "City",
	                "State",
	                "Zip",
	                "Country",
	                "Phone",
	                "Email",
	                "TokenSourceValue",
	                "TokenTypeCode",
	                "ExpMonth",
	                "ExpYear"
	        };

	        final CSVFormat format = CSVFormat.Builder.create()
	                .setHeader(HEADERS)
	                .setIgnoreEmptyLines(true)
	                .build();
	        
	        InputStream input = null;
	        List<MerchantPANDetails> merchantPANDetails  = merchantPANDetailsService.FetchPANDetails(mid);
	        for (int i = 0; i < merchantPANDetails.size(); i++) {

	            // Print all elements of List
	            System.out.println(merchantPANDetails.get(i).toString());
	        }
		
	        try (
	                ByteArrayOutputStream out = new ByteArrayOutputStream();
	                CSVPrinter csvPrinter = new CSVPrinter(new PrintWriter(out), format)
	        ) {
	        	

	            for (MerchantPANDetails merchantPANDetailsObj : merchantPANDetails) {
	                List<String> data = Arrays.asList(
	                		merchantPANDetailsObj.getMerchant_ID(),
	                		merchantPANDetailsObj.getMerchant_Name(),
	                		merchantPANDetailsObj.getTerminal_ID(),
	                		merchantPANDetailsObj.getTransaction_ID(),
	                		merchantPANDetailsObj.getRequest_Type(),
	                		merchantPANDetailsObj.getTotalAmount(),
	                		merchantPANDetailsObj.getFeeAmount(),
	                		merchantPANDetailsObj.getTax_Amount(),
	                		merchantPANDetailsObj.getAuth_Code(),
	                		merchantPANDetailsObj.getAuthDateTime(),
	                		merchantPANDetailsObj.getCardNum(),
	                		merchantPANDetailsObj.getToken(),
	                		merchantPANDetailsObj.getCVV2(),
	                		merchantPANDetailsObj.getCardHolderName(),
	                		merchantPANDetailsObj.getAddress(),
	                		merchantPANDetailsObj.getCity(),
	                		merchantPANDetailsObj.getState(),
	                		merchantPANDetailsObj.getZip(),
	                		merchantPANDetailsObj.getCountry(),
	                		merchantPANDetailsObj.getPhone(),
	                		merchantPANDetailsObj.getEmail(),
	                		merchantPANDetailsObj.getTokenSourceValue(),
	                		merchantPANDetailsObj.getTokenTypeCode(),
	                		merchantPANDetailsObj.getExpMonth(),
	                		merchantPANDetailsObj.getExpYear()
	                		
	                );

	                csvPrinter.printRecord(data);
	                
	            }
	           
	            csvPrinter.flush();
	            
	            return FileDomain.builder()
	                    .file(new ByteArrayResource(new ByteArrayInputStream(out.toByteArray()).readAllBytes()))
	                    .fileName(
	                            String.format(
	                                    "MerchantPANDetailsReport.csv"            
	                                    
	                            )
	                    )
	                    .build();

	           
	        }
	}
}
