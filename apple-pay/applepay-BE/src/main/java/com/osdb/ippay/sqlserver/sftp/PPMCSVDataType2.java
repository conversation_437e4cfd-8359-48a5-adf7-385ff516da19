package com.osdb.ippay.sqlserver.sftp;

import static lombok.AccessLevel.PRIVATE;

import com.opencsv.bean.CsvBindByPosition;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

@Getter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class PPMCSVDataType2 {
    @CsvBindByPosition(position = 0)
    private String bankID;

    @CsvBindByPosition(position = 1)
    private String rpAgentId;

    @CsvBindByPosition(position = 2)
    private String groupId;

    @CsvBindByPosition(position = 3)
    private String associationId;
    
    @CsvBindByPosition(position = 4)
    private String merchantId;

    @CsvBindByPosition(position = 5)
    private String recordType;

    @CsvBindByPosition(position = 6)
    private String feeCategoryDescription;

    @CsvBindByPosition(position = 7)
    private String feeCategory;
    
    @CsvBindByPosition(position = 8)
    private String feeItemKey1;

    @CsvBindByPosition(position = 9)
    private String feeItemKey2;

    @CsvBindByPosition(position = 10)
    private String feeItemKey3;

    @CsvBindByPosition(position = 11)
    private String feeItemKey4;
    
    @CsvBindByPosition(position = 12)
    private String feeItemKey5;

    @CsvBindByPosition(position = 13)
    private String includeInVolTotalInd;

    @CsvBindByPosition(position = 14)
    private String salesCount;
    
    @CsvBindByPosition(position = 15)
    private String salesAmount;

    @CsvBindByPosition(position = 16)
    private String creditCount;

    @CsvBindByPosition(position = 17)
    private String creditAmount;

    @CsvBindByPosition(position = 18)
    private String interchangeFee;
    
    @CsvBindByPosition(position = 19)
    private String interchangePercentRate;
    
    @CsvBindByPosition(position = 20)
    private String interchangePerItemRate;

    @CsvBindByPosition(position = 21)
    private String multipleFeeItemsFlag;

    @CsvBindByPosition(position = 22)
    private String multiplePricingFlag;

    @CsvBindByPosition(position = 23)
    private String feeItemName;
    
    @CsvBindByPosition(position = 24)
    private String feeItemHiararchyLevel;

    @CsvBindByPosition(position = 25)
    private String countNGBInd;

    @CsvBindByPosition(position = 26)
    private String amountNGBInd;

    @CsvBindByPosition(position = 27)
    private String IncludeInMonthlyMinInd;
    
    @CsvBindByPosition(position = 28)
    private String passthroughInd;

    @CsvBindByPosition(position = 29)
    private String salesCountToIncome;
    
    @CsvBindByPosition(position = 30)
    private String salesAmountToIncome;

    @CsvBindByPosition(position = 31)
    private String ccCountToIncome;
    
    @CsvBindByPosition(position = 32)
    private String ccAmountToIncome;

    @CsvBindByPosition(position = 33)
    private String pricingFeeItemPerc;
    
    @CsvBindByPosition(position = 34)
    private String pricingFeeItemPerItem;

    @CsvBindByPosition(position = 35)
    private String incomeAmount;

    @CsvBindByPosition(position = 36)
    private String interPassThrIncome;
    
    @CsvBindByPosition(position = 37)
    private String pricingFeeItemSTaxAmount;

    @CsvBindByPosition(position = 38)
    private String reserved1;

    @CsvBindByPosition(position = 39)
    private String statementDescription;

    @CsvBindByPosition(position = 40)
    private String reserved2;
    
    @CsvBindByPosition(position = 41)
    private String glItemCode;
    
    @CsvBindByPosition(position = 42)
    private String multipleExpVolFlag;

    @CsvBindByPosition(position = 43)
    private String salesCountToExp;
    
    @CsvBindByPosition(position = 44)
    private String salesAmountToExp;

    @CsvBindByPosition(position = 45)
    private String ccCountToExp;

    @CsvBindByPosition(position = 46)
    private String ccAmountToExp;
    
    @CsvBindByPosition(position = 47)
    private String expenseAmount;

    @CsvBindByPosition(position = 48)
    private String userDataCode;

    @CsvBindByPosition(position = 49)
    private String feesPaid;

    @CsvBindByPosition(position = 50)
    private String expenseRateGroupName;
    
    @CsvBindByPosition(position = 51)
    private String incomeRateGroupName;
 
}