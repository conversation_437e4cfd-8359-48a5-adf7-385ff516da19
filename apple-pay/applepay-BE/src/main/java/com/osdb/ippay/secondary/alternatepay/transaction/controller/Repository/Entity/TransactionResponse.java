package com.osdb.ippay.secondary.alternatepay.transaction.controller.Repository.Entity;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import javax.persistence.*;
import static lombok.AccessLevel.PRIVATE;

@Entity
@Table(name = "AlternatepayTrnData")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
public class TransactionResponse {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    Long TransId;


    @Column(name = "TransactionNumber")
    String transactionNumber;
    
    @Column(name = "TransactionId")
    long transactionId;

    @Column(name = "Date")
    String date;

    @Column(name = "Amount")
    String amount;

    @Column(name = "BaseAmount")
    String baseAmount;

    @Column(name = "Currency")
    String currency;

    @Column(name = "Status")
    int status;

    @Column(name = "StatusName")
    String statusName;

    @Column(name = "TimeoutDate")
    String timeoutDate;

    @Column(name = "Reason")
    String reason;
    
    @Column(name = "MethodType")
    int methodType;
    
    @Column(name = "PaymentType")
    String paymentType;
    
    @Column(name = "PaymentPage")
    String paymentPage;
    
    @Column(name = "ApprovalDate")
    String approvalDate;
    
    @Column(name = "ProviderId")
    String providerId;
    
    @Column(name = "ApprovedBy")
    String approvedBy;
    
    @Column(name = "PaymentMethodCode")
    String paymentMethodCode;
    
    @Column(name = "PaymentMethodName")
    String paymentMethodName;
    
    @Column(name = "RequestId")
    String requestId;
    
    @Column(name = "ProviderName")
    String providerName;
    
    @Column(name = "PaymentMethod")
    String paymentMethod;
    
    @Column(name = "ConfirmationNumber")
    String confirmationNumber;
    
    @Column(name = "Address")
    String address;
    
    @Column(name = "City")
    String city;
    
    @Column(name = "State")
    String state;
    
    @Column(name = "Zip")
    String zip;
    
    @Column(name = "Country")
    String country;
}
