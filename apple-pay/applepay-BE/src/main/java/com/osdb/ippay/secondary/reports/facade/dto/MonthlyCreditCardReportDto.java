package com.osdb.ippay.secondary.reports.facade.dto;

import lombok.*;
import lombok.experimental.FieldDefaults;


import static lombok.AccessLevel.PRIVATE;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class MonthlyCreditCardReportDto {

	String merchantId;

	String merchantName;
	
	String totalSales;
	 
    String visaSalesAmount;
   
    String visaSalesCount;

    String visaCreditAmount;
   
    String visaCreditCount;
   
    String amexSalesAmount;
   
    String amexSalesCount;

    String amexCreditAmount;
   
    String amexCreditCount;
   
    String mcSalesAmount;
   
    String mcSalesCount;

    String mcCreditAmount;
   
    String mcCreditCount;
   
    String discoverSalesAmount;
   
    String discoverSalesCount;
    
    String discoverCreditAmount;
   
    String discoverCreditCount;
   
    String netSales;
   
   
}
