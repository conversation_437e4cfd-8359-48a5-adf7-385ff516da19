package com.osdb.ippay.primary.user.service;

import com.osdb.ippay.primary.user.repository.entity.User;
import com.osdb.ippay.primary.user.service.filter.UserFilter;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface UserService {

    List<User> find(UserFilter userFilter);

    Page<User> find(UserFilter userFilter, Pageable pageable);

    User find(Long id);

    User findByEmail(String email);

    User findActive(String email);

    User findActive(String email, String sessionsHash);

    User findByResetPasswordHash(String resetPasswordHash);

    User save(User user);

    <PERSON>olean exists(String email);

}
