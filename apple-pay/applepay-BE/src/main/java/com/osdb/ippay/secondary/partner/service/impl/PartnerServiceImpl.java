package com.osdb.ippay.secondary.partner.service.impl;

import com.osdb.ippay.secondary.partner.repository.PartnerRepository;
import com.osdb.ippay.secondary.partner.repository.entity.Partner;
import com.osdb.ippay.secondary.partner.service.PartnerService;
import com.osdb.ippay.secondary.partner.service.filter.PartnerFilter;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.util.List;

import static lombok.AccessLevel.PRIVATE;

@Service
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class PartnerServiceImpl implements PartnerService {

    PartnerRepository partnerRepository;

    @Override
    public Page<Partner> find(PartnerFilter filter, Pageable pageable) {
        Specification<Partner> specification = filterBy(filter);
        return partnerRepository.findAll(specification, pageable);
    }

    @Override
    public List<Partner> find(PartnerFilter filter, Sort sort) {
        Specification<Partner> specification = filterBy(filter);
        return partnerRepository.findAll(specification, sort);
    }

    @Override
    public List<Partner> find(List<Long> partnerIds) {
        return partnerRepository.findAllByIdIn(partnerIds);
    }

    @Override
    public Partner find(Long id) {
        return partnerRepository
                .findById(id)
                .orElse(null);
    }

    private Specification<Partner> filterBy(PartnerFilter filter) {
        return (r, rq, cb) -> StringUtils.isNotBlank(filter.getSearch()) ?
                cb.like(cb.lower(r.get("partnerName")), "%" + filter.getSearch().toLowerCase() + "%") :
                cb.conjunction();
    }
}
