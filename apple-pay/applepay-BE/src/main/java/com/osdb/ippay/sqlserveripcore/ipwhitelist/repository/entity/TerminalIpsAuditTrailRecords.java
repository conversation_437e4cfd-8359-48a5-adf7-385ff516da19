package com.osdb.ippay.sqlserveripcore.ipwhitelist.repository.entity;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import javax.persistence.*;

import static lombok.AccessLevel.PRIVATE;


@Entity(name = "TerminalIpsAuditTrailRecords")
@Table(name = "AUDIT_TerminalIpBlocking")


@NamedNativeQueries({
	@NamedNativeQuery(resultClass = TerminalIpsAuditTrailRecords.class, name = "TerminalIpsAuditTrailRecords.getAuditTrailRecords", query = "SELECT [AUDIT_ID],[RECORD_ID],[OLD_TerminalID] as deletedTerminalId,[NEW_TerminalID] as addedTerminalId,[OLD_IPAddress] as deletedIPAddress,[NEW_IPAddress] as addedIPAddress,[OLD_Active] as deletedStatus,[NEW_Active] as addedStatus,[AUDIT_DateCreated] as audittrailDate,[NEW_Username] as newUsername,[OLD_Username] as oldUsername FROM [ipCore].[dbo].[AUDIT_TerminalIpBlocking] where [AUDIT_DateCreated] >= '2024-11-12' Order by [SSUsername]" 
)})


@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
public class TerminalIpsAuditTrailRecords {

	@Id
	@Column(name = "AUDIT_ID")
    public Long auditId;
	
	@Column(name = "RECORD_ID")
    public Long recordId;

	@Column(name = "OLD_TerminalID")
	public String deletedTerminalId;

	@Column(name = "NEW_TerminalID")
	public String addedTerminalId;
	 
    @Column(name = "OLD_IPAddress")
    public String deletedIpAddress;
    
    @Column(name = "NEW_IPAddress")
    public String addedIpAddress;

    @Column(name = "OLD_Active")
    public Boolean deletedStatus;

    @Column(name = "NEW_Active")
    public Boolean addedStatus;
    
    @Column(name = "OLD_Username")
    public String oldUsername;
    
    @Column(name = "NEW_Username")
    public String newUsername;

    @Column(name = "AUDIT_DateCreated")
    public java.sql.Timestamp audittrailDate;

    }

