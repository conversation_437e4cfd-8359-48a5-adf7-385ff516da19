package com.osdb.ippay.secondary.statement.facade.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

import static lombok.AccessLevel.PRIVATE;

@Getter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class MSOtherCardDepositDto {

    Long id;

    String date;

    String batchAmount;

    String paidBy;

    String netAmount;

}
