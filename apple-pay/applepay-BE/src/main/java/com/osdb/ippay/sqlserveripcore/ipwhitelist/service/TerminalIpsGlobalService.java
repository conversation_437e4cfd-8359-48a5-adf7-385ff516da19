package com.osdb.ippay.sqlserveripcore.ipwhitelist.service;

import com.osdb.ippay.sqlserveripcore.ipwhitelist.repository.entity.TerminalIpsBlocking;
import com.osdb.ippay.sqlserveripcore.ipwhitelist.repository.entity.TerminalIpsGlobal;
import com.osdb.ippay.sqlserveripcore.ipwhitelist.service.filter.TerminalIpsBlockingFilter;
import com.osdb.ippay.sqlserveripcore.ipwhitelist.service.filter.TerminalIpsGlobalFilter;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface TerminalIpsGlobalService {

	Page<TerminalIpsGlobal> findData(TerminalIpsGlobalFilter filter, Pageable pageable);
	Page<TerminalIpsBlocking> findTerminalData(TerminalIpsBlockingFilter filter, Pageable pageable);
	 void delete(Long id);
}

