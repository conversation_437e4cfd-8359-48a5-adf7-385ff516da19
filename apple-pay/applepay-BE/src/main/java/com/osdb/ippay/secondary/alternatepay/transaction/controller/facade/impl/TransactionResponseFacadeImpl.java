package com.osdb.ippay.secondary.alternatepay.transaction.controller.facade.impl;

import com.osdb.ippay.secondary.alternatepay.transaction.controller.Repository.Entity.*;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.Repository.TransactionResponseRepository;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionSaveDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionCancelResultDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionGetResultDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TrasactionInitResultDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.facade.TransactionResponseFacade;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.mapper.TransactionResponseMapper;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.stereotype.Component;

import static lombok.AccessLevel.PRIVATE;

@Component
@RequiredArgsConstructor
@FieldDefaults(makeFinal = true, level = PRIVATE)
public class TransactionResponseFacadeImpl implements TransactionResponseFacade {
   
    TransactionResponseMapper transactionResponseMapper;
    TransactionResponseRepository transactionResponseRepository;

  
	@Override
	public TransactionSaveDto create(TrasactionInitResultDto transactionSaveDto) {
		TransactionResponse obj = transactionResponseMapper.toEntity(transactionSaveDto);
		TransactionResponse objGlobal = transactionResponseRepository.save(obj);
				
        return transactionResponseMapper.toDto(objGlobal);	
	}

	@Override
	public TransactionSaveDto update(TrasactionInitResultDto transactionSaveDto, String trnid) {
		TransactionResponse trnData = transactionResponseRepository.findBytransactionNumber(trnid);
		
		transactionResponseMapper.putEntity(transactionSaveDto, trnData);
		TransactionResponse updatedTrnData = transactionResponseRepository.save(trnData);
				
	    return transactionResponseMapper.toDto(updatedTrnData);
	}

	@Override
	public TransactionSaveDto findByTrnid(String trnid) {
		
		TransactionResponse trnData = transactionResponseRepository.findBytransactionNumber(trnid);
		return transactionResponseMapper.toDto(trnData);

	}

	@Override
	public TransactionSaveDto cancel(TransactionCancelResultDto transactionCancelResultDto, String trnid) {

		TransactionResponse trnData = transactionResponseRepository.findBytransactionNumber(trnid);
		
		transactionResponseMapper.putEntityCancelled("Transaction Cancelled", transactionCancelResultDto, trnData);
		TransactionResponse updatedTrnData = transactionResponseRepository.save(trnData);
	    return transactionResponseMapper.toDto(updatedTrnData);
	}

	@Override
	public TransactionSaveDto commit(TransactionGetResultDto transactionGetResultDto, String trnid) {

		TransactionResponse trnData = transactionResponseRepository.findBytransactionNumber(trnid);
		
		transactionResponseMapper.putEntityCommitted("Payment Committed", transactionGetResultDto, trnData);
		TransactionResponse updatedTrnData = transactionResponseRepository.save(trnData);
	    return transactionResponseMapper.toDto(updatedTrnData);
	}
}
