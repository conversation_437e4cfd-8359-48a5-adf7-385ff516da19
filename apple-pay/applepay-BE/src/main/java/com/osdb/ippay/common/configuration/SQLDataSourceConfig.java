package com.osdb.ippay.common.configuration;

import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.jdbc.datasource.DriverManagerDataSource;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.persistence.EntityManagerFactory;
import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Objects;

import static lombok.AccessLevel.PRIVATE;

@Configuration
@EnableTransactionManagement
@EnableJpaRepositories(
        entityManagerFactoryRef = "sqlserverEmFactory",
        transactionManagerRef = "sqlserverTm",
        basePackages = { "com.osdb.ippay.sqlserver" }
)
@RequiredArgsConstructor
@FieldDefaults(makeFinal = true, level = PRIVATE)
public class SQLDataSourceConfig {

    Environment env;

    @Bean(name = "sqlserverDataSource")
    public DataSource sqlServerDataSource() {
        DriverManagerDataSource dataSource = new DriverManagerDataSource();

        String driverClassName = env.getProperty("spring.sqlserver-datasource.driver-class-name");

        dataSource.setDriverClassName(Objects.requireNonNull(driverClassName));
        //DESKTOP-HA3NPS7\SQLEXPRESS
        //**************:1433
        dataSource.setUrl("*******************************************************************");
        dataSource.setUsername(env.getProperty("spring.sqlserver-datasource.username"));
        dataSource.setPassword(env.getProperty("spring.sqlserver-datasource.password"));

        return dataSource;
    }

    @Bean(name = "sqlserverEmFactory")
    public LocalContainerEntityManagerFactoryBean sqlServerEmFactory(
            EntityManagerFactoryBuilder builder,
            @Qualifier("sqlserverDataSource") DataSource dataSource) {

        HashMap<String, Object> properties = new HashMap<>();
        properties.put("hibernate.dialect", "org.hibernate.dialect.SQLServer2012Dialect");

        return builder
                .dataSource(dataSource)
                .packages("com.osdb.ippay.sqlserver")
                .persistenceUnit("sqlserver")
                .properties(properties)
                .build();
    }

    @Bean(name = "sqlserverTm")
    public PlatformTransactionManager sqlserverTM(
            @Qualifier("sqlserverEmFactory") EntityManagerFactory emFactory) {

        return new JpaTransactionManager(emFactory);
    }
}
