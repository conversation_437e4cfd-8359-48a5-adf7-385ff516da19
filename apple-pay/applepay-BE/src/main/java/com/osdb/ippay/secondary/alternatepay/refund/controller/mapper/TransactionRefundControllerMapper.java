package com.osdb.ippay.secondary.alternatepay.refund.controller.mapper;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.osdb.ippay.common.util.response.PageResponse;
import com.osdb.ippay.secondary.alternatepay.payment.controller.dto.PaymentDto;
import com.osdb.ippay.secondary.alternatepay.payment.controller.dto.PaymentResultDto;
import com.osdb.ippay.secondary.alternatepay.refund.controller.dto.MerchantRefundDto;
import com.osdb.ippay.secondary.alternatepay.refund.controller.dto.TransactionRefundResultDto;
import com.osdb.ippay.secondary.alternatepay.tokenization.controller.dto.*;


import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import static lombok.AccessLevel.PRIVATE;

import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class TransactionRefundControllerMapper {

    @NonFinal
    @Value("${tokenization.api-key}")
    String apiKey;
    
    private static final Integer MAX_RUNTIME_HOURS = 2;

    ObjectMapper objectMapper;

    public MerchantRefundDto toDto(MerchantRefundDto merchantRefundDto) {
        return MerchantRefundDto.builder()
                .transactionNumber(merchantRefundDto.getTransactionNumber())
                .apiKey(apiKey)
              
                .build();
    }

  
    public TransactionRefundResultDto toOutDto(TransactionRefundResultDto transactionRefundResultDto) {
        return TransactionRefundResultDto.builder()
                .approvedBy(transactionRefundResultDto.getApprovedBy())
                .amount(transactionRefundResultDto.getAmount())
                .dateApproval(transactionRefundResultDto.getDateApproval())
                .providerId(transactionRefundResultDto.getProviderId())
                .refundConfirmation(transactionRefundResultDto.getRefundConfirmation())
                .result(transactionRefundResultDto.getResult())
                .transaction(transactionRefundResultDto.getTransaction())
                
                .build();
    }

  }
