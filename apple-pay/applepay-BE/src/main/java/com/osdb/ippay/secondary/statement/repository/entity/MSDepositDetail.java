package com.osdb.ippay.secondary.statement.repository.entity;

import com.osdb.ippay.common.entity.BaseEntity;
import lombok.*;
import lombok.experimental.FieldDefaults;
import lombok.experimental.SuperBuilder;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import static lombok.AccessLevel.PRIVATE;

@Entity
@Table(name = "MERCHANT_STATEMENT_DEPOSIT_DETAIL")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = PRIVATE)
@SuperBuilder(toBuilder = true)
public class MSDepositDetail extends BaseEntity {

    @Column(name = "MERCHANT_ID")
    String merchantId;

    @Column(name = "DEPOSIT_DATE")
    String depositDate;

    @Column(name = "NUM_OF_TRANS")
    Integer numOfTrans;

    @Column(name = "BATCH_AMOUNT")
    String batchAmount;

    @Column(name = "OC_BATCH")
    String ocBatch;

    @Column(name = "ADJUST")
    String adjust;

    @Column(name = "CHARGEBACKS")
    String chargebacks;

    @Column(name = "FEE_PAID")
    String feePaid;

    @Column(name = "NET_DEPOSIT")
    String netDeposit;

}
