package com.osdb.ippay.primary.user.repository.entity;

import com.osdb.ippay.common.entity.BaseEntity;
import com.osdb.ippay.secondary.alerts.repository.entity.SettledTransaction;

import lombok.*;
import lombok.experimental.FieldDefaults;
import lombok.experimental.SuperBuilder;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.Instant;
import java.util.List;

import static com.osdb.ippay.primary.user.repository.entity.UserRole.ADMIN_READ_ONLY_ROLE;
import static com.osdb.ippay.primary.user.repository.entity.UserRole.ADMIN_ROLE;
import static javax.persistence.EnumType.STRING;
import static lombok.AccessLevel.PRIVATE;


@Entity(name = "UserPartnerDetails")
@Table(name = "user")


@NamedNativeQueries({
	@NamedNativeQuery(resultClass = UserPartnerDetails.class, name = "UserPartnerDetails.getPartnerDetails", query = "SELECT id, email  from user where id IN (select user_id from user_merchant_ref where merchant_id = ?1) limit 1" 
)})


@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = PRIVATE)
@SuperBuilder(toBuilder = true)
public class UserPartnerDetails extends BaseEntity {

    @NotBlank
    @Column(name = "email")
    String email;
    
    @NotBlank
    @Column(name = "id")
    Long id;

   // @NotBlank(message = "Missing required parameter: 'firstName'")
   // @Column(name = "first_name")
   // String firstName;

  //  @NotBlank(message = "Missing required parameter: 'lastName'")
  //  @Column(name = "last_name")
  //  String lastName;
    
}
