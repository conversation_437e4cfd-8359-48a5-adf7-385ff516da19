package com.osdb.ippay.sqlserveripcore.ipwhitelist.facade.mapper;

import com.osdb.ippay.sqlserveripcore.ipwhitelist.facade.dto.TerminalIpsGlobalDto;
import com.osdb.ippay.sqlserveripcore.ipwhitelist.repository.entity.TerminalIpsGlobal;

import org.springframework.stereotype.Component;

@Component
public class TerminalIpsGlobalMapper {

    public TerminalIpsGlobalDto toDto(TerminalIpsGlobal thresholdSetting) {
        if (thresholdSetting == null) {
            return null;
        }
        return TerminalIpsGlobalDto.builder()
                .id(thresholdSetting.getId())
                //.terminalId(thresholdSetting.getTerminalId())
                .ipAddress(thresholdSetting.getIpAddress())
                .username(thresholdSetting.getUsername())
                .dateCreated(thresholdSetting.getDateCreated())
                .dateModified(thresholdSetting.getDateModified())
                .active(thresholdSetting.getActive())
                .build();
    }

    public TerminalIpsGlobal toEntity(TerminalIpsGlobalDto thresholdSettingDto) {
        if (thresholdSettingDto == null) {
            return null;
        }
        TerminalIpsGlobal thresholdSetting = new TerminalIpsGlobal();
        //thresholdSetting.setTerminalId(thresholdSettingDto.getTerminalId());
        thresholdSetting.setActive(thresholdSettingDto.getActive());
        thresholdSetting.setDateCreated(thresholdSettingDto.getDateCreated());
        thresholdSetting.setDateModified(thresholdSettingDto.getDateModified());
        thresholdSetting.setIpAddress(thresholdSettingDto.getIpAddress());
        thresholdSetting.setUsername(thresholdSettingDto.getUsername());
       
        return thresholdSetting;
    }

    public TerminalIpsGlobalDto putEntity(
    		TerminalIpsGlobalDto thresholdSettingDto,
    		TerminalIpsGlobal thresholdSetting) {
    	thresholdSetting.setActive(thresholdSettingDto.getActive());
    	//thresholdSetting.setTerminalId(thresholdSettingDto.getTerminalId());
        thresholdSetting.setDateCreated(thresholdSettingDto.getDateCreated());
        thresholdSetting.setDateModified(thresholdSettingDto.getDateModified());
        thresholdSetting.setIpAddress(thresholdSettingDto.getIpAddress());
        thresholdSetting.setUsername(thresholdSettingDto.getUsername());
        
        return toDto(thresholdSetting);
    }

}