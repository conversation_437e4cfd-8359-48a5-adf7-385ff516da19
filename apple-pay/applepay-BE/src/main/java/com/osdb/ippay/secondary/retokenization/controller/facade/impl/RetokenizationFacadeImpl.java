package com.osdb.ippay.secondary.retokenization.controller.facade.impl;

import com.osdb.ippay.common.util.response.PageResponse;
import com.osdb.ippay.secondary.retokenization.controller.dto.*;
import com.osdb.ippay.secondary.retokenization.controller.facade.RetokenizationFacade;
import com.osdb.ippay.secondary.retokenization.controller.mapper.RetokenizationMapper;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.io.BufferedOutputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.security.NoSuchProviderException;
import java.util.Objects;
import org.bouncycastle.*;



import static lombok.AccessLevel.PRIVATE;

@Component
@RequiredArgsConstructor
@FieldDefaults(makeFinal = true, level = PRIVATE)
public class RetokenizationFacadeImpl implements RetokenizationFacade {

    public static final String INVALID_JOB_STATE = "Job can not be deleted, job state not 'Queued'";

    RetokenizationMapper mapper;

    @NonFinal
    @Value("${retokenization.url.get}")
    String retokenizationUrlGet;

    @NonFinal
    @Value("${retokenization.url.getById}")
    String retokenizationUrlGetById;

    @NonFinal
    @Value("${retokenization.url.post}")
    String retokenizationUrlPost;


    @NonFinal
    @Value("${retokenization.url.put}")
    String retokenizationUrlPut;

    @NonFinal
    @Value("${retokenization.url.delete}")
    String retokenizationUrlDelete;

    @NonFinal
    @Value("${retokenization.api-key}")
    String apiKey;

    @Override
    public PageResponse<RetokenizationOutDto> find(Pageable pageable) {
        RestTemplate restTemplate = new RestTemplate();

        StringBuilder retokenizationUrl = new StringBuilder(retokenizationUrlGet);
        Sort sort = pageable.getSort();
        if (sort.isSorted()) {
            sort.stream().findFirst().ifPresent(order -> {
                retokenizationUrl.append("&sortBy=").append(order.getProperty());
                retokenizationUrl.append("&sortDirection=").append(order.getDirection().name());
            });
        }

      
        
        PageResponse<RetokenizationResultDto> retokenizationResultDtoPageResponse = restTemplate.exchange(
                retokenizationUrl.toString(),
                HttpMethod.GET,
                null,
                new ParameterizedTypeReference<PageResponse<RetokenizationResultDto>>() {
                },
                pageable.getPageSize(),
                pageable.getOffset(),
                apiKey
        ).getBody();

        return mapper.toOutDtoPageResponse(Objects.nonNull(retokenizationResultDtoPageResponse) ? retokenizationResultDtoPageResponse : new PageResponse<>());
    }

   
    @Override
    public RetokenizationResultDto findById(Integer id) {
        RestTemplate restTemplate = new RestTemplate();

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        HttpEntity<RetokenizationDto> httpEntity = new HttpEntity<>(headers);
        return restTemplate.exchange(
                retokenizationUrlGetById.replace("{id}", String.valueOf(id)),
                HttpMethod.GET,
                httpEntity,
                new ParameterizedTypeReference<RetokenizationResultDto>() {
                },
                apiKey
        ).getBody();
    }

    @Override
    public RetokenizationOutDto create(RetokenizationInDto retokenizationInDto) {
        RestTemplate restTemplate = new RestTemplate();

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        RetokenizationDto retokenizationDto = mapper.toDto(retokenizationInDto);
        HttpEntity<RetokenizationDto> httpEntity = new HttpEntity<>(retokenizationDto, headers);

        RetokenizationResultDto retokenizationResultDto = restTemplate.exchange(
                retokenizationUrlPost,
                HttpMethod.POST,
                httpEntity,
                new ParameterizedTypeReference<RetokenizationResultDto>() {
                },
                apiKey
        ).getBody();

        return Objects.nonNull(retokenizationResultDto) ? mapper.toOutDto(retokenizationResultDto) : null;
    }

    @Override
    public RetokenizationOutDto put(RetokenizationInDto retokenizationInDto, Integer id) {
        RetokenizationUpdateDto updateDto = mapper.toUpdateDto(retokenizationInDto);
        return update(updateDto, id);
    }

    @Override
    public RetokenizationOutDto patch(RetokenizationPatchDto retokenizationPatchDto, Integer id) {
        RetokenizationResultDto currentRetokenizationDto = findById(id);
        return update(mapper.toUpdateDto(retokenizationPatchDto, currentRetokenizationDto), id);
    }

    @Override
    public void delete(Integer id) {
        RestTemplate restTemplate = new RestTemplate();

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        HttpEntity<Void> httpEntity = new HttpEntity<>(headers);
        try {
            restTemplate.exchange(
                    retokenizationUrlDelete.replace("{id}", String.valueOf(id)),
                    HttpMethod.DELETE,
                    httpEntity,
                    Void.class,
                    apiKey
            );
        } catch (RestClientException exception) {
            throw new IllegalStateException(INVALID_JOB_STATE);
        }

    }

    private RetokenizationOutDto update(RetokenizationUpdateDto retokenizationUpdateDto, Integer id) {
        RestTemplate restTemplate = new RestTemplate();

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        HttpEntity<RetokenizationUpdateDto> httpEntity = new HttpEntity<>(retokenizationUpdateDto, headers);
        RetokenizationResultDto retokenizationResultDto = restTemplate.exchange(
                retokenizationUrlPut.replace("{id}", String.valueOf(id)),
                HttpMethod.PUT,
                httpEntity,
                new ParameterizedTypeReference<RetokenizationResultDto>() {
                },
                apiKey
        ).getBody();
        return Objects.nonNull(retokenizationResultDto) ? mapper.toOutDto(retokenizationResultDto) : null;
    }
}
