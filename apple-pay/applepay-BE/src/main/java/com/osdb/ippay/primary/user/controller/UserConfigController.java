package com.osdb.ippay.primary.user.controller;

import com.osdb.ippay.common.logging.LogExecutionTime;
import com.osdb.ippay.primary.user.facade.UserConfigFacade;
import com.osdb.ippay.primary.user.facade.dto.UserConfigDto;
import com.osdb.ippay.common.user.facade.dto.UserDto;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

import static lombok.AccessLevel.PRIVATE;

@Tag(name = "user-config")
@RestController
@RequestMapping(value = "/api/v1/private/user-configs")
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class UserConfigController {
/*
    UserConfigFacade userConfigFacade;

    @LogExecutionTime
    @GetMapping
    public ResponseEntity<List<UserConfigDto>> getUserConfigs(
            @Parameter(hidden = true) @AuthenticationPrincipal String email) {

        UserDto user = userConfigFacade.getCurrentUser(email);
        List<UserConfigDto> response = userConfigFacade.findOrSave(user.getId());

        return ResponseEntity.ok(response);
    }

    @LogExecutionTime
    @PostMapping
    public ResponseEntity<UserConfigDto> create(
            @Valid @RequestBody UserConfigDto userConfigDto,
            @Parameter(hidden = true) @AuthenticationPrincipal String email) {

        UserDto user = userConfigFacade.getCurrentUser(email);
        UserConfigDto response = userConfigFacade.create(user.getId(), userConfigDto);

        return ResponseEntity.ok(response);
    }

    @LogExecutionTime
    @PutMapping("/{id}")
    public ResponseEntity<UserConfigDto> update(
            @PathVariable Long id,
            @Valid @RequestBody UserConfigDto userConfigDto,
            @Parameter(hidden = true) @AuthenticationPrincipal String email) {

        UserDto user = userConfigFacade.getCurrentUser(email);
        UserConfigDto response = userConfigFacade.update(id, user.getId(), userConfigDto);

        return ResponseEntity.ok(response);
    }

    @LogExecutionTime
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> delete(
            @PathVariable Long id,
            @Parameter(hidden = true) @AuthenticationPrincipal String email) {

        UserDto user = userConfigFacade.getCurrentUser(email);
        userConfigFacade.delete(id, user.getId());

        return ResponseEntity.noContent().build();
    }*/
}
