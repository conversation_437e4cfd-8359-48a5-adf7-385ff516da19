package com.osdb.ippay.secondary.alternatepay.transaction.controller.dto;

import lombok.*;
import lombok.experimental.FieldDefaults;

import static lombok.AccessLevel.PRIVATE;

import com.fasterxml.jackson.annotation.JsonProperty;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class TransactionPageUrl {

	 @JsonProperty("merchantPage")
	 MerchantPageUrl merchantPage;
  
}
