package com.osdb.ippay.secondary.merchant.repository.entity;

import com.osdb.ippay.common.entity.BaseEntity;
import com.osdb.ippay.secondary.partner.repository.entity.Partner;
import lombok.*;
import lombok.experimental.FieldDefaults;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import javax.persistence.*;
import java.util.List;

import static lombok.AccessLevel.PRIVATE;

@Entity
@Table(name = "MERCHANTS")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = PRIVATE)
@SuperBuilder(toBuilder = true)
public class Merchant extends BaseEntity {

    @Column(name = "mid")
    String merchantId;

    @Column(name = "merchant_dba_name")
    String merchantName;

    @Column(name = "mid", insertable=false, updatable = false)
    String cbMid;

    @Column(name = "jetpay_mid")
    String jetpayMID;

    @Column(name = "ach_mid")
    String achMID;

    @Column(name = "mid" , insertable=false, updatable = false)
    String ccSetlMID;

    @Column(name = "ach_mid" , insertable=false, updatable = false)
    String achSetlMID;

    @Column(name = "merchant_desc")
    String merchantDesc;

    @Column(name = "active")
    Boolean isActive;

    @Column(name = "partner_id_fk", updatable = false, insertable = false)
    Long partnerId;

    @NotFound(action = NotFoundAction.IGNORE)
    @ManyToOne
    @JoinColumn(name = "partner_id_fk")
    Partner partner;

    @Column(name = "bank_id_fk", updatable = false, insertable = false)
    Long bankId;

    @NotFound(action = NotFoundAction.IGNORE)
    @ManyToOne
    @JoinColumn(name = "bank_id_fk")
    Bank bank;

    @OneToMany
    @JoinColumn(name = "mid_id_fk")
    List<MerchantTerminal> merchantTerminals;

    @Column(name = "bank_id_fk" , insertable=false, updatable = false)
    Integer transSource;

}
