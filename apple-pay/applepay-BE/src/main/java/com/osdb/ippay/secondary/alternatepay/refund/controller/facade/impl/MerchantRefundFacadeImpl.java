package com.osdb.ippay.secondary.alternatepay.refund.controller.facade.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.osdb.ippay.secondary.alternatepay.payment.controller.dto.PaymentDto;
import com.osdb.ippay.secondary.alternatepay.payment.controller.dto.PaymentResultDto;
import com.osdb.ippay.secondary.alternatepay.payment.controller.facade.PaymentFacade;
import com.osdb.ippay.secondary.alternatepay.payment.controller.mapper.PaymentControllerMapper;
import com.osdb.ippay.secondary.alternatepay.refund.controller.dto.MerchantPartialRefundDto;
import com.osdb.ippay.secondary.alternatepay.refund.controller.dto.MerchantRefundDto;
import com.osdb.ippay.secondary.alternatepay.refund.controller.dto.TransactionRefundResultDto;
import com.osdb.ippay.secondary.alternatepay.refund.controller.facade.MerchantRefundFacade;
import com.osdb.ippay.secondary.alternatepay.refund.controller.mapper.TransactionRefundControllerMapper;

import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.Base64;
import java.util.Objects;

import static lombok.AccessLevel.PRIVATE;

@Component
@RequiredArgsConstructor
@FieldDefaults(makeFinal = true, level = PRIVATE)
public class MerchantRefundFacadeImpl implements MerchantRefundFacade {

    TransactionRefundControllerMapper mapper;
    
    @Autowired
    private ObjectMapper objectMapper;

    @NonFinal
    @Value("${transaction-refund.url.post}")
    String transactionRefundUrlPost;

    @NonFinal
    boolean blnIsDecoded = true;
    
	@Override
	public TransactionRefundResultDto create(MerchantRefundDto merchantRefundDto) {

		RestTemplate restTemplate = new RestTemplate();

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        
        String objJackson = "";
        
        try {
			   objJackson = objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(merchantRefundDto);
			 
		} catch (JsonProcessingException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
               
       
        HttpEntity<String> entity = new HttpEntity<String>(objJackson, headers);
        
        if(!blnIsDecoded)
        {
        	 byte[] decodedBytes = Base64.getDecoder().decode(transactionRefundUrlPost);
             transactionRefundUrlPost = new String(decodedBytes);
        }
       
        
        TransactionRefundResultDto trnRefundResultDto = restTemplate.exchange(
        		transactionRefundUrlPost,
                HttpMethod.POST,
                entity,
                new ParameterizedTypeReference<TransactionRefundResultDto>() {
                },
                ""
        ).getBody();

        return Objects.nonNull(trnRefundResultDto) ? mapper.toOutDto(trnRefundResultDto) : null;
	}

	@Override
	public TransactionRefundResultDto createPartial(MerchantPartialRefundDto merchantPartialRefundDto) {

		RestTemplate restTemplate = new RestTemplate();

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        
        String objJackson = "";
        
        try {
			   objJackson = objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(merchantPartialRefundDto);
			 
		} catch (JsonProcessingException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
               
       
        HttpEntity<String> entity = new HttpEntity<String>(objJackson, headers);
        
        TransactionRefundResultDto trnRefundResultDto = restTemplate.exchange(
        		transactionRefundUrlPost,
                HttpMethod.POST,
                entity,
                new ParameterizedTypeReference<TransactionRefundResultDto>() {
                },
                ""
        ).getBody();

        return Objects.nonNull(trnRefundResultDto) ? mapper.toOutDto(trnRefundResultDto) : null;
	}
}
