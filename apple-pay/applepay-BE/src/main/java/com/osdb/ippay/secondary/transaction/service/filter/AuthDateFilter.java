package com.osdb.ippay.secondary.transaction.service.filter;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotNull;

import static lombok.AccessLevel.PRIVATE;

@Data
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = PRIVATE)
public class AuthDateFilter {

    @NotNull(message = "Missing required parameter: 'type'.")
    TransactionType type;

    @NotNull(message = "Missing required parameter: 'merchantId'.")
    String merchantId;

}
