package com.osdb.ippay.sqlserver.reports.service.filter;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import javax.validation.constraints.NotNull;
import static lombok.AccessLevel.PRIVATE;

@Data
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = PRIVATE)
public class TotalACHDataGtwReportsFilter {

	@NotNull(message = "Missing required parameter: 'ipTransactionID'.")
    @Parameter(example = "21698032")
	Long ipTransactionID;

    @NotNull(message = "Missing required parameter: 'authStartDateTime'.")
    @Parameter(example = "2020-01-01 00:00:00")
    String authStartDateTime;

    @NotNull(message = "Missing required parameter: 'authEndDateTime'.")
    @Parameter(example = "2020-02-01 00:00:00")
    String authEndDateTime;

}