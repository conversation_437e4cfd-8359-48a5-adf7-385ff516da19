package com.osdb.ippay.primary.auth.facade.dto;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;

import static lombok.AccessLevel.PRIVATE;

@Getter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
public class ForgotPasswordDto {

    @Parameter(required = true)
    @Email(message = "Invalid email format")
    @NotBlank(message = "Email is required")
    String email;

}
