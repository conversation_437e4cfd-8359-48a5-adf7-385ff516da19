package com.osdb.ippay.secondary.reports.repository.entity;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import javax.persistence.*;

import static lombok.AccessLevel.PRIVATE;


@Entity(name = "MonthlyCreditCardReport")
@Table(name = "CCTransactions")


@NamedNativeQueries({
	@NamedNativeQuery(resultClass = MonthlyCreditCardReport.class, name = "MonthlyCreditCardReport.getMonthlyCreditCardReport", query = "select MerchantID as merchantId, MerchantName as merchantName, SUM(IF(RequestType='SALE',TransAmount,0)) as totalSales, SUM(IF(RequestType='SALE' and CardType='V',TransAmount,0)) as visaSalesAmount, SUM(IF(RequestType='CREDIT' and CardType='V',TransAmount,0)) as visaCreditAmount, SUM(IF(RequestType='SALE' and CardType='M',TransAmount,0)) as mcSalesAmount, SUM(IF(RequestType='CREDIT' and CardType='M',TransAmount,0)) as mcCreditAmount, SUM(IF(RequestType='SALE' and CardType='A',TransAmount,0)) as amexSalesAmount,SUM(IF(RequestType='CREDIT' and CardType='A',TransAmount,0)) as amexCreditAmount, SUM(IF(RequestType='SALE' and CardType='D',TransAmount,0)) as discoverSalesAmount, SUM(IF(RequestType='CREDIT' and CardType='D',TransAmount,0)) as discoverCreditAmount, SUM(IF(RequestType='SALE' and CardType='V',1,0)) as visaSalesCount, SUM(IF(RequestType='CREDIT' and CardType='V',1,0)) as visaCreditCount, SUM(IF(RequestType='SALE' and CardType='M',1,0)) as mcSalesCount, SUM(IF(RequestType='CREDIT' and CardType='M',1,0)) as mcCreditCount, SUM(IF(RequestType='SALE' and CardType='A',1,0)) as amexSalesCount, SUM(IF(RequestType='CREDIT' and CardType='A',1,0)) as amexCreditCount, SUM(IF(RequestType='SALE' and CardType='D',1,0)) as discoverSalesCount, SUM(IF(RequestType='CREDIT' and CardType='D',1,0)) as discoverCreditCount, 0 as netSales from CCTransactions where SettlementDate >= ?1 AND SettlementDate <= ?2 Group By MerchantID, MerchantName" 
)})



@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)

public class MonthlyCreditCardReport{

	//@Id
	//@Column(name = "id")
   // Long ipTransId;
	 
	 @Id
	 @Column(name = "merchantId", insertable=false, updatable = false)
	 String merchantId;

	 @Column(name = "merchantName")
	 String merchantName;

	 @Column(name = "totalSales")
	 String totalSales;
	 
    @Column(name = "visaSalesAmount")
    String visaSalesAmount;
    
    @Column(name = "visaSalesCount")
    String visaSalesCount;

    @Column(name = "visaCreditAmount")
    String visaCreditAmount;
    
    @Column(name = "visaCreditCount")
    String visaCreditCount;
    
    @Column(name = "amexSalesAmount")
    String amexSalesAmount;
    
    @Column(name = "amexSalesCount")
    String amexSalesCount;

    @Column(name = "amexCreditAmount")
    String amexCreditAmount;
    
    @Column(name = "amexCreditCount")
    String amexCreditCount;
    
    @Column(name = "mcSalesAmount")
    String mcSalesAmount;
    
    @Column(name = "mcSalesCount")
    String mcSalesCount;

    @Column(name = "mcCreditAmount")
    String mcCreditAmount;
    
    @Column(name = "mcCreditCount")
    String mcCreditCount;
    
    @Column(name = "discoverSalesAmount")
    String discoverSalesAmount;
    
    @Column(name = "discoverSalesCount")
    String discoverSalesCount;

    @Column(name = "discoverCreditAmount")
    String discoverCreditAmount;
    
    @Column(name = "discoverCreditCount")
    String discoverCreditCount;
    
    @Column(name = "netSales")
    String netSales;
    
    }

