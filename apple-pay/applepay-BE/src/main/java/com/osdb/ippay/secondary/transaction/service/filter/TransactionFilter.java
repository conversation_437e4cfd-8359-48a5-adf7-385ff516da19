package com.osdb.ippay.secondary.transaction.service.filter;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static com.osdb.ippay.secondary.transaction.service.filter.TransactionType.SETTLEMENT;
import static lombok.AccessLevel.PRIVATE;

@Data
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = PRIVATE)
public class TransactionFilter {

    @NotNull(message = "Missing required parameter: 'fromDate'.")
    @Parameter(example = "2017-01-25")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    LocalDate fromDate;

    @NotNull(message = "Missing required parameter: 'toDate'.")
    @Parameter(example = "2020-07-16")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    LocalDate toDate;

    @NotBlank(message = "Missing required parameter: 'merchantId'.")
    String merchantId;

    Boolean showOnlyApproved;

    Boolean includeTokenize;

    StType stType;

    TransactionType type;

    String terminalId;

    String transactionId;

    String cardType;

    String cardNumber;

    String cardHolder;

    String authCode;

    String ud1;

    String ud2;

    String ud3;

    String orderNumber;

    String transactionType;

    String approvalStatus;

    Double amount;
    
    String funding;

    @Parameter(hidden = true)
    @JsonIgnore
    List<String> merchantIds;
    
    String status;

    public Boolean getIncludeTokenize() {
        return Optional.ofNullable(includeTokenize).orElse(false);
    }

    public String getFromDateStr() {
        return String.format("%s %s", getFromDate(), "00:00:00");
    }

    public String getToDateStr() {
        return String.format("%s %s", getToDate(), "23:59:59");
    }

    public List<String> getMerchantIds() {
        return Optional
                .ofNullable(merchantIds)
                .orElse(Collections.emptyList());
    }

    public TransactionType getType() {
        return Optional.ofNullable(this.type).orElse(SETTLEMENT);
    }

    public String getCardType() {
        return cardType != null ? String.valueOf(cardType.charAt(0)) : null;
    }

    public Integer getAmountInUSD() {
        return this.amount != null ? (int) (amount * 100) : null;
    }
}
