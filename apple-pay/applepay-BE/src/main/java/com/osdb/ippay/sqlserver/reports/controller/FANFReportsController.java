package com.osdb.ippay.sqlserver.reports.controller;

import com.ippay.global.clients.ipp.IppayResponse;
import com.ippay.global.clients.ipp.data.ResponseType;
import com.osdb.ippay.common.domain.FileDomain;
import com.osdb.ippay.common.exception.handling.Error;
import com.osdb.ippay.common.exception.handling.ErrorResponse;
import com.osdb.ippay.common.logging.LogExecutionTime;
import com.osdb.ippay.common.security.service.AuthenticationService;
import com.osdb.ippay.common.util.annotation.ApiPageable;
import com.osdb.ippay.common.util.number.DoubleUtil;
import com.osdb.ippay.primary.user.repository.entity.User;
import com.osdb.ippay.secondary.transaction.facade.mapper.TransactionFacade;
import com.osdb.ippay.secondary.transaction.facade.dto.*;
import com.osdb.ippay.secondary.transaction.service.domain.RefundTransactionRequest;
import com.osdb.ippay.secondary.transaction.service.domain.SaleTransactionRequest;
import com.osdb.ippay.secondary.transaction.service.domain.VoidTransactionRequest;
import com.osdb.ippay.secondary.transaction.service.filter.AuthDateFilter;
import com.osdb.ippay.secondary.transaction.service.filter.SettlementFilter;
import com.osdb.ippay.secondary.transaction.service.filter.TransactionFilter;
import com.osdb.ippay.sqlserver.reports.facade.TotalACHDataGtwReportsFacade;
import com.osdb.ippay.sqlserver.reports.service.filter.MonthlyResidualReportsFilter;
import com.osdb.ippay.sqlserver.reports.service.filter.TotalACHDataGtwReportsFilter;
import com.osdb.ippay.sqlserver.sftp.service.FileTransferService;

import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.apache.commons.lang3.ObjectUtils;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.io.IOException;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.osdb.ippay.secondary.transaction.service.filter.TransactionType.TRANSACTION;
import static lombok.AccessLevel.PRIVATE;

@Tag(name = "Reports")
@RestController
@RequestMapping(value = "/api/v1/private")
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class FANFReportsController {
/*
	AuthenticationService authenticationService;
    TransactionFacade transactionFacade;

    FileTransferService fileTransferService;
    
    static final String TRANSACTION_FAILED_ERR_MSG = "Transaction declined, with error: %s";
    
	private static final String SAMPLE_CSV_FILE_PATH_MRR_2 = "/testdata1.csv";
 
   
    @LogExecutionTime
    @GetMapping("/fanf-report")
    public ResponseEntity<ByteArrayResource> exportFANFReport(
            @Valid @ParameterObject MonthlyResidualReportsFilter filter) throws Exception {

        FileDomain response = fileTransferService.exportMonthlyResidualReport("", filter);

        return ResponseEntity.ok()
                .contentType(new MediaType("application", "force-download"))
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=".concat(response.getFileName()))
                .body(response.getFile());
        
     
    }*/
}
    