package com.osdb.ippay.sqlserver.reports.facade.mapper;

import com.osdb.ippay.sqlserver.reports.facade.dto.MinimumDiscountDataDto;
import com.osdb.ippay.sqlserver.reports.facade.dto.TotalACHDataGtwReportsDto;
import com.osdb.ippay.sqlserver.reports.facade.dto.TotalACHGtwDataDto;
import com.osdb.ippay.sqlserver.reports.repository.entity.MinimumDiscountData;
import com.osdb.ippay.sqlserver.reports.repository.entity.TotalACHDataGtwReports;
import com.osdb.ippay.sqlserver.reports.repository.entity.TotalACHGtwData;

import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;

@Component
public class MinimumDiscountDataMapper {

    public Page<MinimumDiscountDataDto> toDto(Page<MinimumDiscountData> minimumDiscountData) {
        return minimumDiscountData.map(this::toDto);
    }

    public MinimumDiscountDataDto toDto(MinimumDiscountData minDiscountData) {
       

        return MinimumDiscountDataDto.builder()
        		.Id(minDiscountData.getId())
        		.bankId(minDiscountData.getBankId())
        		.groupId(minDiscountData.getGroupId())
                .associationId(minDiscountData.getAssociationId())
                .merchantId(minDiscountData.getMerchantId())
                .merchantDBAName(minDiscountData.getMerchantDBAName())
                .minDiscount(minDiscountData.getMinDiscount())
                .minDiscountGLCode(minDiscountData.getMinDiscountGLCode())
                .minDiscountUserDataCode(minDiscountData.getMinDiscountUserDataCode())
                .billingMethod(minDiscountData.getBillingMethod())
                .startDate(minDiscountData.getStartDate())
                .stopDate(minDiscountData.getStopDate())
                .build();
    }
}
