package com.osdb.ippay.secondary.alternatepay.payment.controller.facade.impl;

import com.osdb.ippay.sqlserveriptrans.repository.entity.*;
import com.osdb.ippay.secondary.alternatepay.payment.controller.repository.PaymentResponseRepository;
import com.osdb.ippay.secondary.alternatepay.payment.controller.repository.entity.PaymentResponse;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.Repository.TransactionResponseRepository;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.Repository.Entity.TransactionResponse;
import com.osdb.ippay.secondary.alternatepay.payment.controller.dto.PaymentSaveDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionSaveDto;
import com.osdb.ippay.secondary.alternatepay.payment.controller.dto.PaymentResultDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionGetResultDto;
import com.osdb.ippay.secondary.alternatepay.payment.controller.facade.PaymentResponseFacade;
import com.osdb.ippay.secondary.alternatepay.payment.controller.mapper.PaymentResponseMapper;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.mapper.TransactionResponseMapper;

import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.stereotype.Component;

import static lombok.AccessLevel.PRIVATE;

@Component
@RequiredArgsConstructor
@FieldDefaults(makeFinal = true, level = PRIVATE)
public class PaymentResponseFacadeImpl implements PaymentResponseFacade {
   
    PaymentResponseMapper paymentResponseMapper;
    TransactionResponseMapper transactionResponseMapper;
    PaymentResponseRepository paymentResponseRepository;
    TransactionResponseRepository transactionResponseRepository;

	@Override
	public PaymentSaveDto create(PaymentResultDto paymentSaveDto) {

		PaymentResponse obj = paymentResponseMapper.toEntity(paymentSaveDto);
		PaymentResponse objGlobal = paymentResponseRepository.save(obj);
        return paymentResponseMapper.toDto(objGlobal);
	}

	@Override
	public TransactionSaveDto createTransaction(PaymentResultDto paymentSaveDto) {

		TransactionResponse obj = paymentResponseMapper.toTransactionEntity(paymentSaveDto);
		TransactionResponse objGlobal = transactionResponseRepository.save(obj);
        return transactionResponseMapper.toDto(objGlobal);
	}

	@Override
	public PaymentSaveDto findByTrnid(String trnid) {

		PaymentResponse paymentData = paymentResponseRepository.findBytransactionNumber(trnid);
		return paymentResponseMapper.toDto(paymentData);
	}

	@Override
	public PaymentSaveDto updateToken(TransactionGetResultDto transactionGetResultDto, String trnid) {

		PaymentResponse trnData = paymentResponseRepository.findBytransactionNumber(trnid);
		
		paymentResponseMapper.putEntityToken(transactionGetResultDto, trnData);
		PaymentResponse updatedTrnData = paymentResponseRepository.save(trnData);
	    return paymentResponseMapper.toDto(updatedTrnData);
	}

	@Override
	public TransactionSaveDto findByTrnNumber(String trnid) {

		TransactionResponse trnData = transactionResponseRepository.findBytransactionNumber(trnid);
		return transactionResponseMapper.toDto(trnData);
	}

	@Override
	public TransactionSaveDto updateAddress(TransactionGetResultDto transactionGetResultDto, String trnid) {

		TransactionResponse trnData = transactionResponseRepository.findBytransactionNumber(trnid);
		
		paymentResponseMapper.putEntityAddress(transactionGetResultDto, trnData);
		TransactionResponse updatedTrnData = transactionResponseRepository.save(trnData);
	    return transactionResponseMapper.toDto(updatedTrnData);
	    
	}
}
