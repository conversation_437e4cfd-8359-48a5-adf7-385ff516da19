package com.osdb.ippay.secondary.alerts.service.filter;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import javax.validation.constraints.NotNull;
import static lombok.AccessLevel.PRIVATE;
import lombok.Builder;
import com.osdb.ippay.secondary.alerts.repository.entity.AlertType;

@Data
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class ThresholdSettingFilter {
    AlertType alertType;
}
