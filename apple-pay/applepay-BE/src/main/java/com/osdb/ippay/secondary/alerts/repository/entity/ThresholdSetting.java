package com.osdb.ippay.secondary.alerts.repository.entity;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import javax.persistence.*;
import static javax.persistence.GenerationType.IDENTITY;
import static javax.persistence.EnumType.STRING;
import static lombok.AccessLevel.PRIVATE;

@Entity
@Table(name = "alertThresholdSetting")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
public class ThresholdSetting {

    @Id
    @GeneratedValue
    Long id;

    
    @Column(name = "alert_type")
    AlertType alertType;

    @Column(name = "monthly_settled_limit_amount")
    Integer monthlySettledLimitAmount;

    @Column(name = "monthly_settled_limit_number")
    Integer monthlySettledLimitNumber;

    @Column(name = "trigger_alert_one")
    Integer triggerAlertOne;

    @Column(name = "trigger_alert_two")
    Integer triggerAlertTwo;

    @Column(name = "trigger_alert_three")
    Integer triggerAlertThree;

    @Column(name = "alert_to_support")
    Boolean alertToSupport;

    @Column(name = "alert_to_risk")
    Boolean alertToRisk;

    @Column(name = "alert_to_merchant")
    Boolean alertToMerchant;

    @Column(name = "alert_to_partner")
    Boolean alertToPartner;
    
    @Column(name = "triggerAlertTime")
    String triggerAlertTime;
    
    @Column(name = "mid")
    String mid;
}
