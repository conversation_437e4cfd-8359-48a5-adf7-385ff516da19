package com.osdb.ippay.secondary.alternatepay.transaction.controller.facade;

import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionReceiptDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionReceiptResultDto;



public interface TransactionReceiptFacade {

	TransactionReceiptResultDto create(TransactionReceiptDto transactionReceiptInDto);
}
