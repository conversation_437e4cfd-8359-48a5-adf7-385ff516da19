package com.osdb.ippay.secondary.alternatepay.tokenization.controller.facade.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.osdb.ippay.common.util.response.PageResponse;
import com.osdb.ippay.secondary.alternatepay.tokenization.controller.dto.PaymentTokenDto;
import com.osdb.ippay.secondary.alternatepay.tokenization.controller.dto.PaymentTokenResultDto;
import com.osdb.ippay.secondary.alternatepay.tokenization.controller.facade.*;
import com.osdb.ippay.secondary.alternatepay.tokenization.controller.mapper.*;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.Base64;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import static lombok.AccessLevel.PRIVATE;

@Component
@RequiredArgsConstructor
@FieldDefaults(makeFinal = true, level = PRIVATE)
public class PaymentTokenFacadeImpl implements PaymentTokenFacade {

    PaymentTokenControllerMapper mapper;
    @Autowired
    private ObjectMapper objectMapper;
    
    @NonFinal
    @Value("${tokenization.url.get}")
    String tokenizationUrlGet;


    @NonFinal
    @Value("${tokenization.url.post}")
    String tokenizationUrlPost;

    @NonFinal
    boolean blnIsDecoded = true;
    
    @Override
    public PageResponse<PaymentTokenResultDto> find(Pageable pageable) {
        RestTemplate restTemplate = new RestTemplate();

        StringBuilder retokenizationUrl = new StringBuilder(tokenizationUrlGet);
        Sort sort = pageable.getSort();
        if (sort.isSorted()) {
            sort.stream().findFirst().ifPresent(order -> {
                retokenizationUrl.append("&sortBy=").append(order.getProperty());
                retokenizationUrl.append("&sortDirection=").append(order.getDirection().name());
            });
        }

        PageResponse<PaymentTokenResultDto> tokenizationResultDtoPageResponse = restTemplate.exchange(
                retokenizationUrl.toString(),
                HttpMethod.POST,
                null,
                new ParameterizedTypeReference<PageResponse<PaymentTokenResultDto>>() {
                },
                pageable.getPageSize(),
                pageable.getOffset(),
                ""
        ).getBody();

        return mapper.toOutDtoPageResponse(Objects.nonNull(tokenizationResultDtoPageResponse) ? tokenizationResultDtoPageResponse : new PageResponse<>());
    }

   
	@Override
	public PaymentTokenResultDto[] create(PaymentTokenDto paymentTokenizationInDto) {
		RestTemplate restTemplate = new RestTemplate();

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        PaymentTokenDto paymentTokenDto = mapper.toDto(paymentTokenizationInDto);
        HttpEntity<PaymentTokenDto> httpEntity = new HttpEntity<>(paymentTokenDto, headers);
        String objJackson = "";
     
        try {
			   objJackson = objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(paymentTokenizationInDto);
			   
			 
		} catch (JsonProcessingException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
        
       
        HttpEntity<String> entity = new HttpEntity<String>(objJackson, headers);
               
        if(!blnIsDecoded)
        {
        	 byte[] decodedBytes = Base64.getDecoder().decode(tokenizationUrlPost);
             tokenizationUrlPost = new String(decodedBytes);
        }
       
        
        ResponseEntity<PaymentTokenResultDto[]> result = restTemplate.postForEntity(tokenizationUrlPost, entity, PaymentTokenResultDto[].class);
        
     /*   PaymentTokenResultDto retokenizationResultDto = restTemplate.exchange(
        		tokenizationUrlPost,
                HttpMethod.POST,
                httpEntity,
                new ParameterizedTypeReference<PaymentTokenResultDto>() {
                },
                ""
        ).getBody();*/

        return Objects.nonNull(result.getBody()) ?(result.getBody()) : null;
	}
}
