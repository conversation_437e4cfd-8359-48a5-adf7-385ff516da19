package com.osdb.ippay.secondary.alternatepay.payment.controller.facade.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.osdb.ippay.secondary.alternatepay.payment.controller.dto.PaymentDto;
import com.osdb.ippay.secondary.alternatepay.payment.controller.dto.PaymentRequestDto;
import com.osdb.ippay.secondary.alternatepay.payment.controller.dto.PaymentResultDto;
import com.osdb.ippay.secondary.alternatepay.payment.controller.dto.TSYSPaymentRequestDto;
import com.osdb.ippay.secondary.alternatepay.payment.controller.dto.TSYSPaymentResponseDto;
import com.osdb.ippay.secondary.alternatepay.payment.controller.facade.PaymentFacade;
import com.osdb.ippay.secondary.alternatepay.payment.controller.facade.PaymentRequestFacade;
import com.osdb.ippay.secondary.alternatepay.payment.controller.mapper.PaymentControllerMapper;
import com.osdb.ippay.secondary.alternatepay.payment.controller.mapper.TSYSPaymentControllerMapper;

import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.Base64;
import java.util.Objects;

import static lombok.AccessLevel.PRIVATE;

@Component
@RequiredArgsConstructor
@FieldDefaults(makeFinal = true, level = PRIVATE)
public class PaymentFacadeImpl implements PaymentFacade {

    PaymentControllerMapper mapper;
    TSYSPaymentControllerMapper tsysMapper;
    PaymentRequestFacade paymentRequestFacade;
    
    @Autowired
    private ObjectMapper objectMapper;

    @NonFinal
    @Value("${transaction-pay.url.post}")
    String transactionPayUrlPost;
    
    @NonFinal
    @Value("${transaction-directpay.url.post}")
    String transactionDirectPayUrlPost;


    @NonFinal
    boolean blnIsDecoded = true;
    
	@Override
	public PaymentResultDto create(PaymentDto paymentInDto) {
		RestTemplate restTemplate = new RestTemplate();

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        PaymentDto paymentDto = mapper.toDto(paymentInDto);
        
        String objJackson = "";
        
        try {
			   objJackson = objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(paymentInDto);
			   System.out.println("Request body - 1 - " + objJackson);
			 
		} catch (JsonProcessingException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
               
       
        HttpEntity<String> entity = new HttpEntity<String>(objJackson, headers);
        
       // HttpEntity<PaymentDto> httpEntity = new HttpEntity<>(paymentDto, headers);

        
        if(!blnIsDecoded)
        {
        	 byte[] decodedBytes = Base64.getDecoder().decode(transactionPayUrlPost);
             transactionPayUrlPost = new String(decodedBytes);
             blnIsDecoded = true;
        }

        PaymentResultDto paymentResultDto = restTemplate.exchange(
        		transactionPayUrlPost,
                HttpMethod.POST,
                entity,
                new ParameterizedTypeReference<PaymentResultDto>() {
                },
                ""
        ).getBody();

        //paymentRequestFacade.create(paymentResultDto, objJackson);
        return Objects.nonNull(paymentResultDto) ? mapper.toOutDto(paymentResultDto) : null;
	}

	@Override
	public TSYSPaymentResponseDto createTSYSPaymentResponse(TSYSPaymentRequestDto paymentInDto) {

		RestTemplate restTemplate = new RestTemplate();

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        TSYSPaymentRequestDto paymentDto = tsysMapper.toDto(paymentInDto);
        
        String objJackson = "";
        
        try {
			   objJackson = objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(paymentInDto);
			   System.out.println("Request body - 2 - " + objJackson);
			 
		} catch (JsonProcessingException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
               
       
        HttpEntity<String> entity = new HttpEntity<String>(objJackson, headers);
        
       // HttpEntity<PaymentDto> httpEntity = new HttpEntity<>(paymentDto, headers);

        
        if(!blnIsDecoded)
        {
        	 byte[] decodedBytes = Base64.getDecoder().decode(transactionDirectPayUrlPost);
             transactionPayUrlPost = new String(decodedBytes);
             blnIsDecoded = true;
        }

        TSYSPaymentResponseDto paymentResultDto = restTemplate.exchange(
        		transactionDirectPayUrlPost,
                HttpMethod.POST,
                entity,
                new ParameterizedTypeReference<TSYSPaymentResponseDto>() {
                },
                ""
        ).getBody();

        return Objects.nonNull(paymentResultDto) ? tsysMapper.toOutDto(paymentResultDto) : null;
        
	}
}
