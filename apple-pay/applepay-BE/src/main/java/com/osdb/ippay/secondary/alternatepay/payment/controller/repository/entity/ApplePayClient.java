package com.osdb.ippay.secondary.alternatepay.payment.controller.repository.entity;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.net.URI;
import java.net.URISyntaxException;
import java.io.FileInputStream;
import java.security.KeyStore;
import java.security.SecureRandom;
import javax.net.ssl.SSLContext;
import javax.net.ssl.KeyManagerFactory;
import javax.net.ssl.TrustManagerFactory;
import javax.annotation.PostConstruct;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.security.PrivateKey;
import java.security.KeyFactory;
import java.security.spec.PKCS8EncodedKeySpec;


@Component
public class ApplePayClient {

    private static final Logger logger = LoggerFactory.getLogger(ApplePayClient.class);
    
    private ApplePaySession applePaySession;
    private RestTemplate restTemplate;
    
    @Value("${apple.merchant.identity.cert.path}")
    private String merchantIdentityCertPath;
    
    @Value("${apple.merchant.identity.key.path}")
    private String merchantIdentityKeyPath;
    
    @Value("${apple.payment.processing.cert.path}")
    private String paymentProcessingCertPath;
    
    @Value("${ch.datatrans.applepay.domainName}")
    private String configuredDomainName;
    
    @Autowired
    public ApplePayClient(ApplePaySession applePaySession) {
        this.applePaySession = applePaySession;
    }
    
    @PostConstruct
    public void initializeRestTemplate() {
        try {
            logger.info("Initializing Apple Pay RestTemplate with merchant ID: {}", applePaySession.getMerchantIdentifier());
            logger.info("Configured domain name: {}", configuredDomainName);
            logger.info("Certificate path: {}", merchantIdentityCertPath);
            logger.info("Private key path: {}", merchantIdentityKeyPath);
            
            // Configure SSL context with Apple certificates
            SSLContext sslContext = createSSLContext();
            
            CloseableHttpClient httpClient = HttpClients.custom()
                    .setSSLContext(sslContext)
                    .build();
            
            HttpComponentsClientHttpRequestFactory requestFactory = 
                    new HttpComponentsClientHttpRequestFactory();
            requestFactory.setHttpClient(httpClient);
            
            this.restTemplate = new RestTemplate(requestFactory);
            logger.info("RestTemplate configured successfully with Apple Pay certificates");
        } catch (Exception e) {
            logger.error("Failed to initialize Apple Pay RestTemplate", e);
            throw new RuntimeException("Failed to initialize Apple Pay client", e);
        }
    }
    
    private SSLContext createSSLContext() throws Exception {
        logger.info("Starting SSL context creation with certificate: {} and key: {}", merchantIdentityCertPath, merchantIdentityKeyPath);
        
        try {
            // Load the merchant identity certificate chain (.pem format)
            CertificateFactory cf = CertificateFactory.getInstance("X.509");
            java.util.Collection<? extends java.security.cert.Certificate> certChain;
            X509Certificate merchantCert;
            
            try (FileInputStream certFis = new FileInputStream(merchantIdentityCertPath)) {
                certChain = cf.generateCertificates(certFis);
                logger.info("Successfully loaded certificate chain from: {}", merchantIdentityCertPath);
                logger.info("Certificate chain contains {} certificates", certChain.size());
                
                // Get the first certificate (merchant certificate)
                merchantCert = (X509Certificate) certChain.iterator().next();
                logger.info("Merchant certificate subject: {}", merchantCert.getSubjectDN());
                logger.info("Certificate serial: {}", merchantCert.getSerialNumber());
                logger.info("Certificate validity: {} to {}", merchantCert.getNotBefore(), merchantCert.getNotAfter());
                
                // Log certificate fingerprints for debugging
                try {
                    logger.info("Calculating certificate fingerprints...");
                    java.security.MessageDigest sha1 = java.security.MessageDigest.getInstance("SHA-1");
                    byte[] sha1Bytes = sha1.digest(merchantCert.getEncoded());
                    StringBuilder sha1Hex = new StringBuilder();
                    for (byte b : sha1Bytes) {
                        sha1Hex.append(String.format("%02X", b));
                    }
                    logger.info("Certificate SHA1 fingerprint: {}", sha1Hex.toString());
                    
                    java.security.MessageDigest sha256 = java.security.MessageDigest.getInstance("SHA-256");
                    byte[] sha256Bytes = sha256.digest(merchantCert.getEncoded());
                    StringBuilder sha256Hex = new StringBuilder();
                    for (byte b : sha256Bytes) {
                        sha256Hex.append(String.format("%02X", b));
                    }
                    logger.info("Certificate SHA256 fingerprint: {}", sha256Hex.toString());
                    
                    // Also log the expected hash from Apple's error
                    logger.info("Apple's expected SHA256 hash: 2BA8AF828D93E74EC76655D09C9132B7445BA789D674E970873CCCD85E5A8A5E");
                    logger.info("Fingerprints match: {}", sha256Hex.toString().equals("2BA8AF828D93E74EC76655D09C9132B7445BA789D674E970873CCCD85E5A8A5E"));
                } catch (Exception e) {
                    logger.error("Failed to calculate certificate fingerprints", e);
                }
        }
        
            // Load the private key
            PrivateKey privateKey;
            try (FileInputStream keyFis = new FileInputStream(merchantIdentityKeyPath)) {
                byte[] keyBytes = keyFis.readAllBytes();
                String keyContent = new String(keyBytes);
                
                // Remove PEM headers/footers and whitespace
                keyContent = keyContent
                    .replace("-----BEGIN PRIVATE KEY-----", "")
                    .replace("-----END PRIVATE KEY-----", "")
                    .replace("-----BEGIN RSA PRIVATE KEY-----", "")
                    .replace("-----END RSA PRIVATE KEY-----", "")
                    .replaceAll("\\s", "");
                
                byte[] decodedKey = java.util.Base64.getDecoder().decode(keyContent);
                PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(decodedKey);
                KeyFactory keyFactory = KeyFactory.getInstance("RSA");
                privateKey = keyFactory.generatePrivate(keySpec);
                logger.info("Successfully loaded private key from: {}", merchantIdentityKeyPath);
            }
            
            // Create a keystore with the certificate chain and private key
            KeyStore keyStore = KeyStore.getInstance(KeyStore.getDefaultType());
            keyStore.load(null, null); // Initialize empty keystore
            
            // Convert collection to array for keystore
            java.security.cert.Certificate[] certArray = certChain.toArray(new java.security.cert.Certificate[0]);
            logger.info("Adding certificate chain with {} certificates to keystore", certArray.length);
            keyStore.setKeyEntry("apple-pay-cert", privateKey, "".toCharArray(), certArray);
            
            // Initialize KeyManagerFactory
            KeyManagerFactory kmf = KeyManagerFactory.getInstance(KeyManagerFactory.getDefaultAlgorithm());
            kmf.init(keyStore, "".toCharArray());
        
            // Create trust manager
            TrustManagerFactory tmf = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
            tmf.init((KeyStore) null); // Use default trust store
            
            // Create SSL context
        SSLContext sslContext = SSLContext.getInstance("TLS");
            sslContext.init(kmf.getKeyManagers(), tmf.getTrustManagers(), new SecureRandom());
        
            logger.info("SSL context configured successfully for Apple Pay mutual TLS");
        return sslContext;
            
        } catch (Exception e) {
            logger.error("Failed to create SSL context", e);
            throw e;
        }
    }

    public String createSession(String validationUrl, String origin) {
        // Always use the configured domain name instead of extracting from origin
        // This ensures we use the correct domain that matches our certificate
        String domainName = configuredDomainName;
        applePaySession.setDomainName(domainName);
        
        logger.info("Making Apple Pay validation request to: {} with domain: {}", validationUrl, domainName);
        logger.info("Using configured domain: {} instead of origin domain: {}", configuredDomainName, origin);
        
        // Log the JSON payload being sent to Apple Pay for debugging
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            String jsonPayload = objectMapper.writeValueAsString(applePaySession);
            logger.info("Apple Pay validation JSON payload: {}", jsonPayload);
        } catch (Exception e) {
            logger.warn("Failed to serialize Apple Pay session for logging", e);
        }
        
        // Set headers for Apple Pay request
        org.springframework.http.HttpHeaders headers = new org.springframework.http.HttpHeaders();
        headers.setContentType(org.springframework.http.MediaType.APPLICATION_JSON);
        
        org.springframework.http.HttpEntity<ApplePaySession> request = 
            new org.springframework.http.HttpEntity<>(applePaySession, headers);
        logger.info("Apple Pay validation request: {}", request);
        return restTemplate.postForEntity(validationUrl, request, String.class).getBody();
    }
    
    private String extractDomainFromOrigin(String origin) {
        logger.info("Extracting domain from origin: '{}'", origin);
        
        if (origin == null || origin.isEmpty()) {
            logger.warn("Origin is null or empty, using domain from configuration");
            // Fallback to configuration domain
            return System.getProperty("ch.datatrans.applepay.domainName", "applepay-test.ippay.com");
        }
        
        String domain;
        try {
            // Check if origin contains protocol
            if (origin.contains("://")) {
                // Full URL with protocol
            URI uri = new URI(origin);
                domain = uri.getHost();
                logger.info("Extracted domain from full URL: '{}'", domain);
            } else {
                // Just domain name without protocol
                // Remove any path or query parameters
                int slashIndex = origin.indexOf("/");
                if (slashIndex > 0) {
                    domain = origin.substring(0, slashIndex);
                } else {
                    domain = origin;
                }
                
                // Remove port if present
                int portIndex = domain.indexOf(":");
                if (portIndex > 0) {
                    domain = domain.substring(0, portIndex);
                }
                
                logger.info("Extracted domain from domain-only origin: '{}'", domain);
            }
        } catch (URISyntaxException e) {
            logger.warn("Failed to parse origin as URI: '{}', extracting manually", origin);
            // Fallback: manual extraction
            if (origin.contains("://")) {
                String withoutProtocol = origin.substring(origin.indexOf("://") + 3);
                int portIndex = withoutProtocol.indexOf(":");
                int slashIndex = withoutProtocol.indexOf("/");
                
                int endIndex = withoutProtocol.length();
                if (portIndex > 0 && (slashIndex < 0 || portIndex < slashIndex)) {
                    endIndex = portIndex;
                } else if (slashIndex > 0) {
                    endIndex = slashIndex;
                }
                
                domain = withoutProtocol.substring(0, endIndex);
            } else {
                domain = origin; // Return as-is if no protocol found
            }
            
            logger.info("Manually extracted domain: '{}'", domain);
        }
        
        // Validate domain is not null or empty
        if (domain == null || domain.isEmpty()) {
            logger.warn("Extracted domain is null or empty, using fallback");
            domain = "applepay-test.ippay.com"; // Fallback to known working domain
        }
        
        logger.info("Final domain to use: '{}'", domain);
        return domain;
    }

    @Component
    static class ApplePaySession {

        @JsonProperty("merchantIdentifier")
        private final String merchantIdentifier;
        
        @JsonProperty("displayName")
        private final String displayName;
        
        private final String configuredDomainName;

        @JsonProperty("domainName")
        private String domainName;
        
        @JsonProperty("initiative")
        private final String initiative = "web";
        
        @JsonProperty("initiativeContext")
        private String initiativeContext;

        ApplePaySession(@Value("${ch.datatrans.applepay.merchantIdentifier}") String merchantIdentifier,
                        @Value("${ch.datatrans.applepay.displayName}") String displayName,
                        @Value("${ch.datatrans.applepay.domainName}") String configuredDomainName) {

            this.merchantIdentifier = merchantIdentifier;
            this.displayName = displayName;
            this.configuredDomainName = configuredDomainName;
            
            // Initialize with configured domain as fallback
            this.domainName = configuredDomainName;
            this.initiativeContext = "https://" + configuredDomainName;
        }

        public String getMerchantIdentifier() {
            return merchantIdentifier;
        }

        public String getDisplayName() {
            return displayName;
        }

        public String getDomainName() {
            return domainName;
        }

        public void setDomainName(String domainName) {
            // Use configured domain as fallback if extracted domain is null/empty
            if (domainName == null || domainName.trim().isEmpty()) {
                this.domainName = this.configuredDomainName;
                this.initiativeContext = "https://" + this.configuredDomainName;
            } else {
                this.domainName = domainName.trim();
                this.initiativeContext = "https://" + domainName.trim();
            }
        }
        
        public String getInitiative() {
            return initiative;
        }
        
        public String getInitiativeContext() {
            return initiativeContext;
        }
    }
}
