package com.osdb.ippay.secondary.alerts.service.impl;

import com.osdb.ippay.common.domain.FileDomain;
import com.osdb.ippay.common.exception.business.NotFoundException;
import com.osdb.ippay.primary.user.repository.entity.User;
import com.osdb.ippay.primary.user.service.UserMerchantService;
import com.osdb.ippay.secondary.alerts.repository.MerchantSettlementRepository;
import com.osdb.ippay.secondary.alerts.repository.entity.Merchants;
import com.osdb.ippay.secondary.alerts.service.MerchantSettlementService;
import com.osdb.ippay.secondary.merchant.repository.MerchantRepository;
import com.osdb.ippay.secondary.merchant.repository.entity.Bank;
import com.osdb.ippay.secondary.merchant.repository.entity.Merchant;
import com.osdb.ippay.secondary.merchant.repository.entity.Terminal;
import com.osdb.ippay.secondary.merchant.service.MerchantService;
import com.osdb.ippay.secondary.merchant.service.filter.MerchantFilter;
import com.osdb.ippay.secondary.partner.repository.entity.Partner;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.PrintWriter;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static com.osdb.ippay.common.exception.ErrorMessage.MERCHANT_ID_NOT_FOUND;

import static lombok.AccessLevel.PRIVATE;
import static org.apache.commons.collections.CollectionUtils.isEmpty;
import static org.apache.commons.collections.CollectionUtils.isNotEmpty;
import static org.apache.commons.lang3.StringUtils.EMPTY;

@Service
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class MerchantSettlementServiceImpl implements MerchantSettlementService {

    // repositories
    MerchantSettlementRepository merchantSettlementRepository;


  
	@Override
	public List<Merchants> find() {

		 return merchantSettlementRepository.findAll();
	}
}
