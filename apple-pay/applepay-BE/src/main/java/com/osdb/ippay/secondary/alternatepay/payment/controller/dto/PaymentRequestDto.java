package com.osdb.ippay.secondary.alternatepay.payment.controller.dto;

import lombok.*;
import lombok.experimental.FieldDefaults;

import static lombok.AccessLevel.PRIVATE;

import javax.persistence.GeneratedValue;
import javax.persistence.Id;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class PaymentRequestDto {

	@Id
    @GeneratedValue
    Long Id;
    
	String requestId;
  
    String requestPayload;
}
