package com.osdb.ippay.common.security.filter;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.osdb.ippay.common.exception.business.InvalidJwtException;
import com.osdb.ippay.common.exception.business.NotFoundException;
import com.osdb.ippay.common.exception.handling.Error;
import com.osdb.ippay.common.exception.handling.ErrorResponse;
import com.osdb.ippay.common.security.provider.JwtProvider;
import com.osdb.ippay.primary.user.repository.entity.User;
import com.osdb.ippay.primary.user.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.preauth.PreAuthenticatedAuthenticationToken;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Stream;

import static com.osdb.ippay.common.exception.ErrorMessage.AUTH_INVALID_TOKEN;
import static lombok.AccessLevel.PRIVATE;

@Component
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)

public class CookieAuthFilter extends OncePerRequestFilter {

    public static final String AUTH_COOKIE = "AUTH_COOKIE";

    JwtProvider jwtProvider;
    UserService userService;
    Environment environment;

    @Override
    protected void doFilterInternal(HttpServletRequest request,
                                    HttpServletResponse response,
                                    FilterChain filterChain)
            throws ServletException, IOException {

        try {
            if(request.getServletPath().contains("/api/v1/private")) {
                Optional<Cookie> authCookie = Stream.of(
                        Optional
                                .ofNullable(request.getCookies())
                                .orElse(new Cookie[0]))
                        .filter(cookie -> AUTH_COOKIE.equals(cookie.getName()))
                        .findFirst();

                authCookie.ifPresent(cookie -> {
                    String email = jwtProvider.getSubject(cookie.getValue()).split("::")[0];
                    String sessionsHash = jwtProvider.getSubject(cookie.getValue()).split("::")[1];

                    User user = userService.findActive(email, sessionsHash);

                    PreAuthenticatedAuthenticationToken authToken =
                            new PreAuthenticatedAuthenticationToken(user.getEmail(), null);

                    SecurityContextHolder.getContext().setAuthentication(authToken);

                    String accessToken = jwtProvider.createAccessToken(user.getEmail(), user.getSessionsHash());
                    addAuthCookie(accessToken, response);
                });
            }

            filterChain.doFilter(request, response);

        } catch (InvalidJwtException | NotFoundException exception) {
            SecurityContextHolder.clearContext();

            response.setStatus(HttpStatus.UNAUTHORIZED.value());
            response.setContentType("application/json");
            response.setCharacterEncoding("UTF-8");

            String errResponse = new ObjectMapper().writer().writeValueAsString(
                    ErrorResponse.builder()
                            .status(HttpStatus.UNAUTHORIZED.value())
                            .timestamp(System.currentTimeMillis())
                            .errors(List.of(Error.builder().message(AUTH_INVALID_TOKEN).build()))
                            .build()
            );

            response.getWriter().write(errResponse);
        }
    }

    private void addAuthCookie(String accessToken, HttpServletResponse response) {
        Cookie authCookie = new Cookie(CookieAuthFilter.AUTH_COOKIE, accessToken);

        authCookie.setHttpOnly(true);
        authCookie.setSecure(true);
        authCookie.setPath("/");
        authCookie.setDomain(environment.getProperty("allowed.domain"));

        authCookie.setMaxAge(Integer.parseInt(
                Objects.requireNonNull(
                        environment.getProperty("security.jwt.access-token.expire-length")
                )) / 1000
        );

        response.addCookie(authCookie);
    }
}
