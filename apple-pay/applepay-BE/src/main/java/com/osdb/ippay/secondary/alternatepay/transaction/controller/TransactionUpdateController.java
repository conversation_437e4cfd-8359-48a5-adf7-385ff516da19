package com.osdb.ippay.secondary.alternatepay.transaction.controller;

import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionCancelResultDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionGetDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionGetResultDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionUpdateDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.facade.TransactionUpdateFacade;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.facade.TransactionResponseFacade;

import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import static lombok.AccessLevel.PRIVATE;

@Tag(name = "Alternatepay/Transaction")
@RestController
@RequestMapping(value = "/api/v2/payment/alternatepay")
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class TransactionUpdateController {

	TransactionUpdateFacade transactionUpdateFacade; 
	TransactionResponseFacade transactionResponseFacade;
	
   /* @PatchMapping("/transaction/update/{transactionNumber}")
    public ResponseEntity<TransactionGetResultDto> UpdateTransactionById(@PathVariable("transactionNumber") String transactionNumber, @RequestBody TransactionUpdateDto trnUpdateDto) {
    	TransactionGetResultDto response = transactionUpdateFacade.update(transactionNumber, trnUpdateDto);
        return ResponseEntity.ok(response);
    }*/

    @PostMapping("/transaction/cancel/{transactionNumber}")
    public ResponseEntity<TransactionCancelResultDto> CancelTransactionById(@PathVariable("transactionNumber") String transactionNumber, @RequestBody TransactionGetDto trnCancelDto) {
    	TransactionCancelResultDto response = transactionUpdateFacade.cancel(transactionNumber, trnCancelDto);
    	//transactionResponseFacade.cancel(response, transactionNumber);
    	
        return ResponseEntity.ok(response);
    }


}
