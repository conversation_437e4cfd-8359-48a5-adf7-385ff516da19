	package com.osdb.ippay.secondary.alerts.service;

import com.osdb.ippay.secondary.alerts.repository.entity.ThresholdSetting;
import com.osdb.ippay.secondary.alerts.repository.entity.AlertType;

public interface ThresholdSettingService {

    ThresholdSetting find(AlertType alertType);
    
    ThresholdSetting findByMId(AlertType alertType, String strMerchantId);

    ThresholdSetting update(AlertType alertType, String strMerchantId, ThresholdSetting setting);
}