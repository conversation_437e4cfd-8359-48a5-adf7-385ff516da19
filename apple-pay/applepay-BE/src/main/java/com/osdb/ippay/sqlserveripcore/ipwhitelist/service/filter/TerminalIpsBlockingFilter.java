package com.osdb.ippay.sqlserveripcore.ipwhitelist.service.filter;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import static lombok.AccessLevel.PRIVATE;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = PRIVATE)
public class TerminalIpsBlockingFilter {


    @Parameter(example = "123456")
	String terminalID;
	
    @Parameter(example = "127.0.0.1")
	String ipAddress;
    
    Boolean activeStatus;
}