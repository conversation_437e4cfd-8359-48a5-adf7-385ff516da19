package com.osdb.ippay.sqlserveripcore.ipwhitelist.facade.dto;

import lombok.*;
import lombok.experimental.FieldDefaults;

import static lombok.AccessLevel.PRIVATE;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class TerminalIpsAuditTrailRecordsDto {

    public Long auditId;
	
    public Long recordId;

	public String deletedTerminalId;

	public String addedTerminalId;
	 
    public String deletedIpAddress;
    
    public String addedIpAddress;

    public Boolean deletedStatus;

    public Boolean addedStatus;
    
    public String oldUsername;
    
    public String newUsername;

    public java.sql.Timestamp audittrailDate;
   
}
