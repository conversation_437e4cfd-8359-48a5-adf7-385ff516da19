package com.osdb.ippay.secondary.alternatepay.transaction.controller.mapper;

import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionSaveDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.Repository.Entity.TransactionResponse;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionCancelResultDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionGetResultDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TrasactionInitResultDto;

import org.springframework.stereotype.Component;

@Component
public class TransactionResponseMapper {

    public TransactionSaveDto toDto(TransactionResponse transactionResponse) {
        if (transactionResponse == null) {
            return null;
        }
        return TransactionSaveDto.builder()
                .transactionNumber(transactionResponse.getTransactionNumber())
                .amount(transactionResponse.getAmount())
                .baseAmount(transactionResponse.getBaseAmount())
                .currency(transactionResponse.getCurrency())
                .date(transactionResponse.getDate())
                .methodType(transactionResponse.getMethodType())
                .timeoutDate(transactionResponse.getTimeoutDate())
                .paymentType(transactionResponse.getPaymentType())
                .reason(transactionResponse.getReason())
                .status(transactionResponse.getStatus())
                .statusName(transactionResponse.getStatusName())
                .paymentPage(transactionResponse.getPaymentPage())
                .approvalDate(transactionResponse.getApprovalDate())
                .approvedBy(transactionResponse.getApprovedBy())
                .confirmationNumber(transactionResponse.getConfirmationNumber())
                .paymentMethod(transactionResponse.getPaymentMethod())
                .paymentMethodCode(transactionResponse.getPaymentMethodCode())
                .paymentMethodName(transactionResponse.getPaymentMethodName())
                .providerId(transactionResponse.getProviderId())
                .providerName(transactionResponse.getProviderName())
                .requestId(transactionResponse.getRequestId())
                
                .build();
    }

    public TransactionResponse toEntity(TrasactionInitResultDto transactionSaveDto) {
        if (transactionSaveDto == null) {
            return null;
        }

		long transactionId = (long) Math.floor(Math.random() * 900000000000000000L) + 100000000000000000L;
		
        TransactionResponse transactionResponse = new TransactionResponse();
        transactionResponse.setAmount(transactionSaveDto.getAmount());
        transactionResponse.setBaseAmount(transactionSaveDto.getBaseAmount());
        transactionResponse.setCurrency(transactionSaveDto.getCurrency());
        transactionResponse.setDate(transactionSaveDto.getDate());
        transactionResponse.setMethodType(transactionSaveDto.getMethodType());
        transactionResponse.setPaymentPage(transactionSaveDto.getTransactionPage().getMerchantPage().getPaymentPage().getRegular().toString());
        transactionResponse.setPaymentType(transactionSaveDto.getPaymentType());
        transactionResponse.setReason(transactionSaveDto.getReason());
        transactionResponse.setStatus(transactionSaveDto.getStatus());
        transactionResponse.setStatusName(transactionSaveDto.getStatusName());
        transactionResponse.setTimeoutDate(transactionSaveDto.getTimeoutDate());
        transactionResponse.setTransactionNumber(transactionSaveDto.getTransactionNumber());
        transactionResponse.setTransactionId(transactionId);
        
        return transactionResponse;
    }

    public TransactionSaveDto putEntity(
    		TrasactionInitResultDto transactionSaveDto,
    		TransactionResponse transactionResponse) {
    	
    	 transactionResponse.setAmount(transactionSaveDto.getAmount());
         transactionResponse.setBaseAmount(transactionSaveDto.getBaseAmount());
         transactionResponse.setCurrency(transactionSaveDto.getCurrency());
         transactionResponse.setDate(transactionSaveDto.getDate());
         transactionResponse.setMethodType(transactionSaveDto.getMethodType());
         transactionResponse.setPaymentPage(transactionSaveDto.getTransactionPage().getMerchantPage().getPaymentPage().getRegular().toString());
         transactionResponse.setPaymentType(transactionSaveDto.getPaymentType());
         transactionResponse.setReason(transactionSaveDto.getReason());
         transactionResponse.setStatus(transactionSaveDto.getStatus());
         transactionResponse.setStatusName(transactionSaveDto.getStatusName());
         transactionResponse.setTimeoutDate(transactionSaveDto.getTimeoutDate());
         transactionResponse.setTransactionNumber(transactionSaveDto.getTransactionNumber());
        
        return toDto(transactionResponse);
    }
    
    public TransactionSaveDto putEntityCancelled(
    		String strStatusName,
    		TransactionCancelResultDto transactionCancelResultDto,
    		TransactionResponse transactionResponse) {
    	
    	 
         transactionResponse.setStatus(transactionCancelResultDto.getStatus());
         transactionResponse.setStatusName(strStatusName);
         
        return toDto(transactionResponse);
    }
    
    public TransactionSaveDto putEntityCommitted(
    		String strStatusName,
    		TransactionGetResultDto transactionGetResultDto,
    		TransactionResponse transactionResponse) {
    	
    	 
         transactionResponse.setStatus(transactionGetResultDto.getStatus());
         transactionResponse.setStatusName(strStatusName);
         
        return toDto(transactionResponse);
    }

}