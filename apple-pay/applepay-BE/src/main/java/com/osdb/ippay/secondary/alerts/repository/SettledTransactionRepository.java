package com.osdb.ippay.secondary.alerts.repository;

import com.osdb.ippay.secondary.alerts.repository.entity.SettledTransaction;
import com.osdb.ippay.secondary.transaction.repository.entity.CCSettlement;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.query.Procedure;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SettledTransactionRepository extends
        JpaRepository<SettledTransaction, Long>,
        JpaSpecificationExecutor<SettledTransaction> {

	@Procedure(name = "SettledTransaction.getOverlimitSettledTransactionsSP")
	List<SettledTransaction> getOverlimitSettledTransactions(
			@Param("startDateTime") String authStartDateTime,
			@Param("endDateTime") String authEndDateTime);


    List<SettledTransaction> getMonthlyOverlimitSettlements(String from, String to);
    List<SettledTransaction> getMonthlyOverlimitSettlementsCount(String from, String to);
	
}
