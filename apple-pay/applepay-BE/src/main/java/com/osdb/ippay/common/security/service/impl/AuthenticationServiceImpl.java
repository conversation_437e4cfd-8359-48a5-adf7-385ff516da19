package com.osdb.ippay.common.security.service.impl;

import com.osdb.ippay.common.exception.business.NoAccessException;
import com.osdb.ippay.common.exception.business.NotFoundException;
import com.osdb.ippay.common.exception.business.UnableToLoginException;
import com.osdb.ippay.common.exception.business.UnauthorizedException;
import com.osdb.ippay.common.security.service.AuthenticationService;
import com.osdb.ippay.primary.auth.facade.dto.SignInStepOneDto;
import com.osdb.ippay.primary.auth.facade.dto.SignInStepTwoDto;
import com.osdb.ippay.primary.user.repository.entity.User;
import com.osdb.ippay.primary.user.repository.entity.UserStatus;
import com.osdb.ippay.primary.user.service.UserMerchantService;
import com.osdb.ippay.primary.user.service.UserService;
import com.osdb.ippay.secondary.merchant.repository.entity.Merchant;
import com.osdb.ippay.secondary.merchant.service.MerchantService;
import com.osdb.ippay.secondary.transaction.service.domain.SaleTransactionRequest;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.time.Instant;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.ippay.global.clients.ipp.IppayRequest.TransactionType.REFUND;
import static com.osdb.ippay.common.exception.ErrorMessage.*;
import static com.osdb.ippay.primary.user.repository.entity.UserRole.*;
import static lombok.AccessLevel.PRIVATE;

@Slf4j
@Service
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class AuthenticationServiceImpl implements AuthenticationService {

    UserService userService;
    UserMerchantService userMerchantService;
    MerchantService merchantService;

    PasswordEncoder passwordEncoder;

    @NonFinal
    @Value(value = "${ippay.trans.service.client-ip-http-header}")
    String clientIpHttpHeader;

    @Override
    public List<String> getMerchantIds(User authUser) {
        if(Objects.nonNull(authUser.getPartnerId())) {
            Stream<Merchant> merchants = merchantService.find(authUser.getPartnerId()).stream();
            return merchants.map(Merchant::getMerchantId).collect(Collectors.toList());
        }

        return userMerchantService.findMerchantIdsByUserId(authUser.getId());
    }

    @Override
    public List<String> getCCMerchantIds(User authUser) {
        if(Objects.nonNull(authUser.getPartnerId())) {
            Stream<Merchant> merchants = merchantService.find(authUser.getPartnerId()).stream();
            return merchants.map(Merchant::getCcSetlMID).collect(Collectors.toList());
        }

        List<String> merchantIds = userMerchantService.findMerchantIdsByUserId(authUser.getId());
        Stream<Merchant> merchants = merchantService.find(merchantIds).stream();

        return merchants.map(Merchant::getCcSetlMID).collect(Collectors.toList());
    }

    @Override
    public List<String> getACHMerchantIds(User authUser) {
        if(Objects.nonNull(authUser.getPartnerId())) {
            Stream<Merchant> merchants = merchantService.find(authUser.getPartnerId()).stream();
            return merchants.map(Merchant::getAchSetlMID).collect(Collectors.toList());
        }

        List<String> merchantIds = userMerchantService.findMerchantIdsByUserId(authUser.getId());
        Stream<Merchant> merchants = merchantService.find(merchantIds).stream();

        return merchants.map(Merchant::getAchSetlMID).collect(Collectors.toList());
    }

    @Override
    public User signInStepOne(SignInStepOneDto stepOneDto) {
        try {
            User user = userService.findByEmail(stepOneDto.getEmail());
            throwIfNonActive(user);

          /*  if (!passwordEncoder.matches(stepOneDto.getPassword(), user.getPassword())) {
                updateTotalFailedLoginAttempts(user);
                throw new UnauthorizedException(AUTH_INVALID_USER_CREDS);
            }*/

            return user;

        } catch (NotFoundException ex) {
            throw new NotFoundException(AUTH_INVALID_USER_CREDS);
        }
    }

    @Override
    public User signInStepTwo(SignInStepTwoDto stepTwoDto, HttpServletRequest request) {
        User user = userService.findByEmail(stepTwoDto.getEmail());
        throwIfNonActive(user);

       /* if (!passwordEncoder.matches(stepTwoDto.getOtp(), user.getOtp())) {
            updateTotalFailedLoginAttempts(user);
            throw new UnauthorizedException(AUTH_INVALID_OTP);
        }*/

        updateSessionDetails(user, request);

        return user;
    }

    @Override
    public User getCurrentUser(String email) {
        return userService.findActive(email);
    }

    @Override
    public void throwIfNotInternal(String email) {
        User user = userService.findByEmail(email);
        boolean isInternal = ADMIN_ROLE.equals(user.getRole()) || ADMIN_READ_ONLY_ROLE.equals(user.getRole());

        if(!isInternal) {
            throw new NoAccessException(AUTH_NO_ACCESS);
        }
    }

    @Override
    public void throwIfNotFullAdmin(String email) {
        User user = userService.findByEmail(email);
        boolean isFullAdminAccess = ADMIN_ROLE.equals(user.getRole());

        if(!isFullAdminAccess) {
            throw new NoAccessException(AUTH_NO_ACCESS);
        }
    }

    @Override
    public void throwIfNoAccessToProcessTransaction(String email, SaleTransactionRequest transactionRequest) {
        User user = userService.findByEmail(email);

        boolean isRefund = REFUND.equals(transactionRequest.getTransactionType());

        boolean isAdminReadOnly = ADMIN_READ_ONLY_ROLE.equals(user.getRole());
        boolean isMerchantAdmin = MERCHANT_ADMIN_ROLE.equals(user.getRole());
        boolean isISOAdmin = ISO_ADMIN.equals(user.getRole());
        boolean isIsoReadOnly = ISO_READ_ONLY_ROLE.equals(user.getRole());
        boolean isAdminRecurring = MERCHANT_ADMIN_RECURRING_ROLE.equals(user.getRole());
        boolean isMerchantReadOnly = MERCHANT_READ_ONLY_ROLE.equals(user.getRole());

        if(isRefund && (isMerchantAdmin || isISOAdmin || isAdminRecurring)) {
            throw new NoAccessException(AUTH_NO_ACCESS);
        }

        if(isMerchantReadOnly || isAdminReadOnly || isIsoReadOnly) {
            throw new NoAccessException(AUTH_NO_ACCESS);
        }

        List<String> merchantIds = getMerchantIds(user);
        if(user.isNotAdmin() && !merchantIds.contains(transactionRequest.getMerchantId())) {
            throw new NoAccessException(AUTH_NO_ACCESS);
        }
    }

    @Override
    public void throwIfNoAccessToVoidAndRefund(User user) {
        boolean isAdminReadOnly = ADMIN_READ_ONLY_ROLE.equals(user.getRole());
        boolean isMerchantReadOnly = MERCHANT_READ_ONLY_ROLE.equals(user.getRole());
        boolean isoReadOnly = ISO_READ_ONLY_ROLE.equals(user.getRole());

        if(isAdminReadOnly || isMerchantReadOnly || isoReadOnly) {
            throw new NoAccessException(AUTH_NO_ACCESS);
        }
    }

    @Override
    public void throwIfNoAccessToExport(User user) {
        boolean isInternal = ADMIN_ROLE.equals(user.getRole()) || ADMIN_READ_ONLY_ROLE.equals(user.getRole());

        if(!isInternal) {
            throw new NoAccessException(AUTH_NO_ACCESS);
        }
    }

    private void updateTotalFailedLoginAttempts(User user) {
        int totalFailedLogins = Optional.ofNullable(user.getTotalFailedLogins()).orElse(0) + 1;
        int lastSessionFailedLogins = Optional.ofNullable(user.getLastSessionFailedLogins()).orElse(0) + 1;

        user.setTotalFailedLogins(totalFailedLogins);
        user.setLastSessionFailedLogins(lastSessionFailedLogins);

        if(lastSessionFailedLogins >= 3) {
            user.setStatus(UserStatus.LOCKED);
        }

        userService.save(user);
    }

    private void updateSessionDetails(User user, HttpServletRequest request) {
        user.setLastLogin(Instant.now());
        user.setLastSessionFailedLogins(0);
        user.setLastSessionIpAddress(getIpAddress(request));
        user.setOtp(null);

        userService.save(user);
    }

    private String getIpAddress(HttpServletRequest request) {
        if (StringUtils.isBlank(clientIpHttpHeader)) {
            clientIpHttpHeader = "X-FORWARDED-FOR";
        }

        String ipAddress = Optional
                .ofNullable(request.getHeader(clientIpHttpHeader))
                .orElse(request.getRemoteAddr())
                .split(",")[0]
                .trim();

        return "0:0:0:0:0:0:0:1".equals(ipAddress) ? "127.0.0.1" : ipAddress;
    }

    private void throwIfNonActive(User user) {
        switch (user.getStatus()) {
            case LOCKED -> throw new UnableToLoginException(AUTH_LOCKED_ACCOUNT);
            case DISABLED -> throw new UnableToLoginException(AUTH_DISABLED_ACCOUNT);
        }
    }
}
