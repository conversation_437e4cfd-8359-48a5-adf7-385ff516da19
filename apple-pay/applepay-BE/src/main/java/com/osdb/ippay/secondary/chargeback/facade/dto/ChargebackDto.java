package com.osdb.ippay.secondary.chargeback.facade.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.time.LocalDate;

import static lombok.AccessLevel.PRIVATE;

@Getter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class ChargebackDto {

    Long id;

    String merchantName;

    String merchantId;

    String cardType;

    String creditCard;

    String caseNumber;

    String amount;

    String reason;

    String resolutionTo;

    String debitCredit;

    String type;

    String originRef;

    LocalDate dateTransaction;

    LocalDate dateResolved;

}
