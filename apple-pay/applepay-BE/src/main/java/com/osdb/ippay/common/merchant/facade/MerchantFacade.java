package com.osdb.ippay.common.merchant.facade;

import com.osdb.ippay.common.domain.FileDomain;
import com.osdb.ippay.common.merchant.facade.dto.MerchantDto;
import com.osdb.ippay.primary.user.repository.entity.User;
import com.osdb.ippay.secondary.merchant.service.filter.MerchantFilter;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;

import java.io.IOException;
import java.util.List;

public interface MerchantFacade {

    Page<MerchantDto> find(String email, MerchantFilter filter, Pageable pageable);

    List<MerchantDto> find(String email, MerchantFilter filter, Sort sort);

    MerchantDto find(String email, String merchantId);

    FileDomain export(User user, MerchantFilter filter) throws IOException;

}
