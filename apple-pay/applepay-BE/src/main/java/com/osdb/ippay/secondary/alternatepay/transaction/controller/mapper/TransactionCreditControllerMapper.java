package com.osdb.ippay.secondary.alternatepay.transaction.controller.mapper;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.*;

import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import static lombok.AccessLevel.PRIVATE;

@Component
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class TransactionCreditControllerMapper {

    @NonFinal
    @Value("${tokenization.api-key}")
    String apiKey;


    public TransactionCreditDto toDto(TransactionCreditDto transactioncreditDto) {
        return TransactionCreditDto.builder()
               
                .apiKey(apiKey)
                .amount(transactioncreditDto.getAmount())
                .currency(transactioncreditDto.getCurrency())
                .isCent(transactioncreditDto.isCent())
                .methodType(transactioncreditDto.getMethodType())
                .build();
    }

    public TrasactionInitResultDto toOutDto(TrasactionInitResultDto trnInitResultDto) {
        return TrasactionInitResultDto.builder()
                .amount(trnInitResultDto.getAmount())
                .currency(trnInitResultDto.getCurrency())
                .date(trnInitResultDto.getDate())
               // .maxInstallments(trnInitResultDto.getMaxInstallments())
                .methodType(trnInitResultDto.getMethodType())
                .reason(trnInitResultDto.getReason())
                .status(trnInitResultDto.getStatus())
                .statusName(trnInitResultDto.getStatusName())
                .timeoutDate(trnInitResultDto.getTimeoutDate())
                .TransactionNumber(trnInitResultDto.getTransactionNumber())
                .paymentType("Apple Pay")
                .build();
    }
}
