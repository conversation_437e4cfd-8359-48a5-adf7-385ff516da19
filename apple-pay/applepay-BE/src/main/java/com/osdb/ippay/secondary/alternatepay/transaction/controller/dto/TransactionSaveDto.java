package com.osdb.ippay.secondary.alternatepay.transaction.controller.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import javax.persistence.*;

import static lombok.AccessLevel.PRIVATE;


@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class TransactionSaveDto {

    @Id
    @GeneratedValue
    Long Id;

    String transactionNumber;
    
    String transactionId;

    String date;

    String amount;

    String baseAmount;

    String currency;

    int status;

    String statusName;

    String timeoutDate;

    String reason;
    
    int methodType;
    
    String paymentType;
    
    String paymentPage;
    
    String approvalDate;
    
    String providerId;
    
    String approvedBy;
    
    String paymentMethodCode;
    
    String paymentMethodName;
    
    String requestId;
    
    String providerName;
    
    String paymentMethod;
    
    String confirmationNumber;
    
    String address;
    
    String city;
    
    String state;
    
    String zip;
    
    String country;
}
