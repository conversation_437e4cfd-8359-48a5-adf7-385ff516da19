package com.osdb.ippay.secondary.alternatepay.transaction.controller.dto;

import lombok.*;
import lombok.experimental.FieldDefaults;

import static lombok.AccessLevel.PRIVATE;

import com.fasterxml.jackson.annotation.JsonProperty;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class TransactionPaymentUrl {

	@JsonProperty("regular")
    String regular;

	@JsonProperty("medium")
    String medium;
 
    @JsonProperty("small")
    String small;

  
}
