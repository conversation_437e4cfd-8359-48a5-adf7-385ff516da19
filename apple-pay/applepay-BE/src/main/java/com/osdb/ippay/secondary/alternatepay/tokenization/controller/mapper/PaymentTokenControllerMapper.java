package com.osdb.ippay.secondary.alternatepay.tokenization.controller.mapper;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.osdb.ippay.common.util.response.PageResponse;
import com.osdb.ippay.secondary.alternatepay.tokenization.controller.dto.*;


import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import static lombok.AccessLevel.PRIVATE;

import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class PaymentTokenControllerMapper {

    @NonFinal
    @Value("${tokenization.api-key}")
    String apiKey;
    
    @NonFinal
    @Value("${tokenization.branch-code}")
    String branchCode;

    
    private static final Integer MAX_RUNTIME_HOURS = 2;

    ObjectMapper objectMapper;

    public PaymentTokenDto toDto(PaymentTokenDto paymentTokenDto) {
        return PaymentTokenDto.builder()
                .branchNumber(branchCode)
                .merchantApiKey(apiKey)
                .creditCardDetails(paymentTokenDto.getCreditCardDetails())
                
                .build();
    }

  
    public PaymentTokenResultDto toOutDto(PaymentTokenResultDto tokenizationResultDto) {
        return PaymentTokenResultDto.builder()
                .token(tokenizationResultDto.getToken())
                .cardExpiration(tokenizationResultDto.getCardExpiration())
                .cardNumber(tokenizationResultDto.getCardNumber())
                .securityCode(tokenizationResultDto.getSecurityCode())
                .uid(tokenizationResultDto.getUid())
                .build();
    }

    public PageResponse<PaymentTokenResultDto> toOutDtoPageResponse(PageResponse<PaymentTokenResultDto> tokenizationResultDtoPageResponse) {
        return new PageResponse<>(
        		tokenizationResultDtoPageResponse.getResult().stream()
                        .map(this::toOutDto)
                        .collect(Collectors.toList()),
                        tokenizationResultDtoPageResponse.getTotal()
        );
    }

  
    public PaymentTokenDto toCreateDto(PaymentTokenDto paymentTokenDto) {
        return PaymentTokenDto.builder()
                .branchNumber(branchCode)
                .merchantApiKey(apiKey)
                .creditCardDetails(paymentTokenDto.getCreditCardDetails())
              
                .build();
    }  

}
