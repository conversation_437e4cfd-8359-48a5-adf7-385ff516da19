package com.osdb.ippay.common.merchant.facade.impl;

import com.osdb.ippay.common.merchant.facade.BankFacade;
import com.osdb.ippay.common.merchant.facade.dto.BankDto;
import com.osdb.ippay.common.merchant.facade.mapper.BankMapper;
import com.osdb.ippay.secondary.merchant.repository.entity.Bank;
import com.osdb.ippay.secondary.merchant.service.BankService;
import com.osdb.ippay.secondary.merchant.service.filter.BankFilter;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Component;

import java.util.List;

import static lombok.AccessLevel.PRIVATE;

@Component
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class BankFacadeImpl implements BankFacade {

    BankService bankService;
    BankMapper bankMapper;

    @Override
    public Page<BankDto> find(BankFilter filter, Pageable pageable) {
        Page<Bank> banks = bankService.find(filter, pageable);
        return bankMapper.toDto(banks);
    }

    @Override
    public List<BankDto> find(BankFilter filter, Sort sort) {
        List<Bank> banks = bankService.find(filter, sort);
        return bankMapper.toDto(banks);
    }
}
