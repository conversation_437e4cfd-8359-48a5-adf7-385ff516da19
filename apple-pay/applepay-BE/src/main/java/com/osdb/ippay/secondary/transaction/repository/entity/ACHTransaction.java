package com.osdb.ippay.secondary.transaction.repository.entity;

import com.osdb.ippay.common.entity.BaseEntity;
import com.osdb.ippay.common.util.date.DateUtil;
import com.osdb.ippay.secondary.merchant.repository.entity.Merchant;
import lombok.*;
import lombok.experimental.FieldDefaults;
import lombok.experimental.SuperBuilder;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import javax.persistence.*;
import java.time.Instant;

import static lombok.AccessLevel.PRIVATE;

@Entity
@Table(name = "ACHTransactions")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = PRIVATE)
@SuperBuilder(toBuilder = true)
public class ACHTransaction extends BaseEntity {

    @Column(name = "IPTransID")
    Long ipTransID;

    @Column(name = "TerminalID")
    String terminalId;

    @Column(name = "MerchantID")
    String merchantId;

    @NotFound(action = NotFoundAction.IGNORE)
    @ManyToOne
    @JoinColumn(name = "MerchantID", referencedColumnName = "ach_mid", updatable = false, insertable = false)
    Merchant merchant;

    @Column(name = "MerchantName")
    String merchantName;

    @Column(name = "TransactionID")
    String transactionId;

    @Column(name = "TransactionType")
    String transactionType;

    @Column(name = "AccountNumber")
    String accountNumber;

    @Column(name = "CardHolderName")
    String cardHolder;

    @Column(name = "Address")
    String address;

    @Column(name = "City")
    String city;

    @Column(name = "State")
    String state;

    @Column(name = "ZipCode")
    String zipCode;

    @Column(name = "Country")
    String country;

    @Column(name = "Phone")
    String phone;

    @Column(name = "Email")
    String email;

    @Column(name = "Status")
    String status;

    @Column(name = "TotalAmount")
    Double amount;

    @Column(name = "FeeAmount")
    Integer feeAmount;

    @Column(name = "UserField1")
    String ud1;

    @Column(name = "UserField2")
    String ud2;

    @Column(name = "UserField3")
    String ud3;

    @Column(name = "AuthCode")
    String authCode;

    @Column(name = "AuthTransDate")
    String authDate;

    @Column(name = "ACHProcessor")
    String achProcessor;

    @Column(name = "RequestType")
    String requestType;

    @Column(name = "CurrencyCode")
    String currencyCode;

    @Column(name = "OrderNumber")
    String orderNumber;

    @Column(name = "checkNumber")
    String checkNumber;

    @Column(name = "Invoice")
    String invoiceNumber;

    @Column(name = "ABA")
    String routing;

    @Column(name = "ActionCode")
    String responseCode;

    @Column(name = "ResponseText")
    String responseText;

    @Column(name = "SettleDate")
    String settlementDate;

    @Column(name = "CompletionDate")
    Instant completionDate;

    @Column(name = "ReturnStatusFlag")
    String returnStatusFlag;

    @Column(name = "Token")
    String token;

    public Instant getAuthDateInstant() {
        return DateUtil.parseToInstant(authDate);
    }

    public Instant getSettlementDateInstant() {
        return DateUtil.parseToInstant(settlementDate);
    }

    public String getReturnStatusFlag() {
        if(StringUtils.isBlank(returnStatusFlag)) {
            return StringUtils.EMPTY;
        }

        return switch (returnStatusFlag) {
            case "A" -> "AFTER";
            case "B" -> "BEFORE";
            default -> returnStatusFlag;
        };
    }

    public String getStatus() {
        if(StringUtils.isBlank(status)) {
            return StringUtils.EMPTY;
        }

        String first3Chars = status.substring(0, 3);

        return switch (first3Chars) {
            case "R01" -> "R01 Insufficient Funds";
            case "R02" -> "R02 Account Closed";
            case "R03" -> "R03 No Account/Unable to Locate Account";
            case "R04" -> "R04 Invalid Account Number";
            case "R05" -> "R05 Unauthorized Debit Entry";
            case "R06" -> "R06 Returned per ODFI’s Request";
            case "R07" -> "R07 Authorization Revoked by Customer";
            case "R08" -> "R08 Payment Stopped or Stop Payment on Item";
            case "R09" -> "R09 Uncollected Funds";
            case "R10" -> "R10 Customer Advises Not Authorized; Item Is Ineligible, Notice Not Provided, Signatures Not Genuine, or Item Altered (adjustment entries)";
            case "R11" -> "R11 Customer Advises Entry Not in Accordance with the Terms of the Authorization";
            case "R12" -> "R12 Branch Sold to Another DFI";
            case "R13" -> "R13 RDFI not qualified to participate";
            case "R14" -> "R14 Representative Payee Deceased or Unable to Continue in that Capacity";
            case "R15" -> "R15 Beneficiary or Account Holder (Other Than a Representative Payee) Deceased";
            case "R16" -> "R16 Account Frozen";
            case "R17" -> "R17 File Record Edit Criteria";
            case "R20" -> "R20 Non-Transaction Account";
            case "R21" -> "R21 Invalid Company Identification";
            case "R22" -> "R22 Invalid Individual ID Number";
            case "R23" -> "R23 Credit Entry Refused by Receiver";
            case "R24" -> "R24 Duplicate Entry";
            case "R29" -> "R29 Corporate Customer Advises Not Authorized";
            case "R31" -> "R31 Permissible Return Entry (CCD and CTX only)";
            case "R33" -> "R33 Return of XCK Entry";
            default -> status;
        };
    }

    public String getType() {
        boolean isSettlement = StringUtils.isNotBlank(getSettlementDate());

        if(isSettlement) {
            boolean isNotCompleted = ObjectUtils.notEqual(getStatus(), "CMP");

            if(isNotCompleted) {
                return getStatus();
            }
        }

        return StringUtils.EMPTY;
    }

    public String getAuthStatus() {
        if("000".equals(responseCode)) {
            return "CHECK ACCEPTED";
        }

        return "DECLINED";
    }

    public String getSettlementStatus() {
        boolean isSettlement = StringUtils.isNotBlank(getSettlementDate());

        if(isSettlement) {
            boolean isCompleted = "CMP".equalsIgnoreCase(getStatus());
            boolean isCheck = "CHECK".equalsIgnoreCase(requestType);
            boolean isRefund = "REFUND".equalsIgnoreCase(requestType);

            if(isCompleted && isCheck) {
                return "COMPLETED DEBIT";
            }

            if(isCompleted && isRefund) {
                return "COMPLETED CREDIT";
            }

            boolean isRejected = getType().matches("R\\d{2}.+");
            if(isRejected) {
                return "REJECT";
            }

            return StringUtils.EMPTY;
        }

        return StringUtils.isBlank(getStatus()) ? responseText : status;
    }
}
