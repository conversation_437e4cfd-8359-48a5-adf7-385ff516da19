package com.osdb.ippay.secondary.chargeback.service;

import com.osdb.ippay.primary.user.repository.entity.User;
import com.osdb.ippay.secondary.chargeback.repository.entity.Chargeback;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface ChargebackService {

    Page<Chargeback> find(User authUser, ChargebackFilter filter, Pageable pageable);

    List<Chargeback> find(User authUser, ChargebackFilter filter);

    List<String> findDates(String merchantId);

}
