package com.osdb.ippay.common.security.service;

import com.osdb.ippay.primary.auth.facade.dto.SignInStepOneDto;
import com.osdb.ippay.primary.auth.facade.dto.SignInStepTwoDto;
import com.osdb.ippay.primary.user.repository.entity.User;
import com.osdb.ippay.secondary.transaction.service.domain.SaleTransactionRequest;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

public interface AuthenticationService {

    List<String> getMerchantIds(User authUser);

    List<String> getCCMerchantIds(User authUser);

    List<String> getACHMerchantIds(User authUser);

    User signInStepOne(SignInStepOneDto stepTwoDto);

    User signInStepTwo(SignInStepTwoDto stepTwoDto, HttpServletRequest request);

    User getCurrentUser(String email);

    void throwIfNotInternal(String email);

    void throwIfNotFullAdmin(String email);

    void throwIfNoAccessToProcessTransaction(String email, SaleTransactionRequest transactionRequest);

    void throwIfNoAccessToVoidAndRefund(User user);

    void throwIfNoAccessToExport(User user);

}
