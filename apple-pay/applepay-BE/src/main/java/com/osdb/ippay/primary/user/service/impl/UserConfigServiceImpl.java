package com.osdb.ippay.primary.user.service.impl;

import com.osdb.ippay.common.exception.business.AlreadyExistsException;
import com.osdb.ippay.common.exception.business.NotFoundException;
import com.osdb.ippay.primary.user.repository.UserConfigRepository;
import com.osdb.ippay.primary.user.repository.entity.UserConfig;
import com.osdb.ippay.primary.user.service.UserConfigService;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.osdb.ippay.common.exception.ErrorMessage.USER_CONFIG_ALREADY_EXISTS;
import static com.osdb.ippay.common.exception.ErrorMessage.USER_CONFIG_NOT_FOUND;
import static lombok.AccessLevel.PRIVATE;

@Service
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class UserConfigServiceImpl implements UserConfigService {

    UserConfigRepository userConfigRepository;

    @Override
    public List<UserConfig> find(Long userId) {
        return userConfigRepository.findAllByUserId(userId);
    }

    @Override
    public UserConfig find(Long id, Long userId) {
        return userConfigRepository
                .findByIdAndUserId(id, userId)
                .orElseThrow(() -> new NotFoundException(USER_CONFIG_NOT_FOUND));
    }

    @Override
    public UserConfig save(UserConfig config) {
        try {
            return userConfigRepository.save(config);

        } catch (DataIntegrityViolationException ex) {
            throw new AlreadyExistsException(USER_CONFIG_ALREADY_EXISTS);
        }
    }

    @Override
    public List<UserConfig> saveAll(List<UserConfig> config) {
        return userConfigRepository.saveAll(config);
    }

    @Override
    public void delete(Long id, Long userId) {
        userConfigRepository.deleteByIdAndUserId(id, userId);
    }
}
