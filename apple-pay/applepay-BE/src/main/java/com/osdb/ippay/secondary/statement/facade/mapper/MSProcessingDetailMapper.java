package com.osdb.ippay.secondary.statement.facade.mapper;

import com.osdb.ippay.secondary.statement.facade.dto.MSProcessingDetailDto;
import com.osdb.ippay.secondary.statement.repository.entity.MSProcessingDetail;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class MSProcessingDetailMapper {

    public List<MSProcessingDetailDto> toDto(List<MSProcessingDetail> msProcessingDetails) {
        return msProcessingDetails.stream()
                .map(this::toDto)
                .collect(Collectors.toList());
    }

    public MSProcessingDetailDto toDto(MSProcessingDetail msProcessingDetail) {
        return MSProcessingDetailDto.builder()
                .id(msProcessingDetail.getId())
                .description(msProcessingDetail.getDescription())
                .amount(msProcessingDetail.getAmount())
                .discountRate(msProcessingDetail.getDiscountRate())
                .numX(msProcessingDetail.getNumX())
                .transactionFee(msProcessingDetail.getTransactionFee())
                .processFee(msProcessingDetail.getProcessFee())
                .build();
    }
}
