package com.osdb.ippay.secondary.alternatepay.transaction.controller.mapper;

import com.osdb.ippay.secondary.alternatepay.payment.controller.dto.PaymentResultDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.Repository.Entity.HppToken;
import com.osdb.ippay.hpp.alternatepay.transaction.dto.HapiTransactionDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.HppTokenDto;

import org.springframework.stereotype.Component;

@Component
public class HppTokenMapper {

    public HppTokenDto toDto(HppToken hppToken) {
        if (hppToken == null) {
            return null;
        }
        return HppTokenDto.builder()
        		.hppTransId(hppToken.getHppTransId())
                .token(hppToken.getToken())
                .appleToken(hppToken.getAppleToken())
                
                .build();
    }

   
    public HppToken toEntity(HapiTransactionDto hapiTransactionResultDto, 
											PaymentResultDto paymentResultDto) 
    {
		HppToken hppTokenResponse = new HppToken();
		hppTokenResponse.setHppTransId(hapiTransactionResultDto.getId());
		hppTokenResponse.setToken(hapiTransactionResultDto.getToken());
		hppTokenResponse.setAppleToken(paymentResultDto.getToken());
        
        return hppTokenResponse;
    }

  

}