package com.osdb.ippay.secondary.alerts.facade.dto;

import lombok.*;
import lombok.experimental.FieldDefaults;
import com.osdb.ippay.secondary.alerts.repository.entity.AlertType;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import static lombok.AccessLevel.PRIVATE;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class ThresholdSettingDto {

    @NotBlank(message = "Alert type is required")
    AlertType alertType;

    @NotNull(message = "Limit amount is required")
    Integer monthlySettledLimitAmount;

    @NotNull(message = "Limit number is required")
    Integer monthlySettledLimitNumber;

    Integer triggerAlertOne;

    Integer triggerAlertTwo;

    Integer triggerAlertThree;

    Boolean alertToSupport;

    Boolean alertToRisk;

    Boolean alertToMerchant;

    Boolean alertToPartner;
    
    String triggerAlertTime;
    
    String mid;
}
