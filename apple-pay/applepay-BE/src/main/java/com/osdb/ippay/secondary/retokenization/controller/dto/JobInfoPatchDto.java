package com.osdb.ippay.secondary.retokenization.controller.dto;

import com.osdb.ippay.secondary.retokenization.controller.dto.enumeration.JobInfoSourceDatatype;
import com.osdb.ippay.secondary.retokenization.controller.dto.enumeration.JobInfoTokenType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotNull;

import static lombok.AccessLevel.PRIVATE;

@Getter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class JobInfoPatchDto {

    String oldTerminalId;

    String newTerminalId;

    JobInfoTokenType tokenType;

    String orgFilename;

    JobInfoSourceDatatype sourceDataType;

    String sourceS3BucketKey;

    String description;

}
