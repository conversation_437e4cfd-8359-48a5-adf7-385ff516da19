package com.osdb.ippay.sqlserver.reports.repository;

import com.osdb.ippay.secondary.alerts.repository.entity.SettledTransaction;
import com.osdb.ippay.secondary.transaction.repository.entity.CCSettlement;
import com.osdb.ippay.sqlserver.reports.repository.entity.MinimumDiscountData;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.query.Procedure;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface MinimumDiscountDataRepository extends
        JpaRepository<MinimumDiscountData, Long>,
        JpaSpecificationExecutor<MinimumDiscountData> {

    SettledTransaction insertMinimumDiscountData( String bankId, 
    													String groupId, 
    													String associationId, 
    													String merchantId, 
    													String merchantDBAName, 
    													String minDiscount,
    													String minDiscountGLCode,
    													String minDiscountUserDataCode,
    													String startDate,
    													String stopDate,
    													String billingMethod);

	
}
