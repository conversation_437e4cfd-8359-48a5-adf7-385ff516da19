package com.osdb.ippay.secondary.partner.facade;

import com.osdb.ippay.secondary.partner.facade.dto.PartnerDto;
import com.osdb.ippay.secondary.partner.service.filter.PartnerFilter;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;

import java.util.List;

public interface PartnerFacade {

    Page<PartnerDto> find(PartnerFilter filter, Pageable pageable);

    List<PartnerDto> find(PartnerFilter filter, Sort sort);

    PartnerDto find(Long id);

}
