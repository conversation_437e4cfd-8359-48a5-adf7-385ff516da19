package com.osdb.ippay.primary.auth.facade.dto;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;

import static lombok.AccessLevel.PRIVATE;

@Getter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class SignInStepTwoDto {

    @Parameter(required = true)
    @Email(message = "Invalid Email Format")
    @NotBlank(message = "Email is required")
    String email;

    @Parameter(required = true)
    @NotBlank(message = "Passcode is required")
    String otp;

}
