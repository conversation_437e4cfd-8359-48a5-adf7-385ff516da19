/*package com.osdb.ippay.sqlserver.reports.repository.entity;

import com.osdb.ippay.common.entity.BaseEntity;
import com.osdb.ippay.common.util.date.DateUtil;
import com.osdb.ippay.secondary.merchant.repository.entity.Merchant;
import lombok.*;
import lombok.experimental.FieldDefaults;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import javax.persistence.*;
import java.time.Instant;

import static lombok.AccessLevel.PRIVATE;

@Entity
@Table(name = "CCTransactions")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = PRIVATE)
@SuperBuilder(toBuilder = true)
public class FANFReports extends BaseEntity {

    @Column(name = "IPTransID")
    Long ipTransId;

    @Column(name = "TerminalID")
    String terminalId;

    @Column(name = "MerchantID")
    String merchantId;

    @NotFound(action = NotFoundAction.IGNORE)
    @ManyToOne
    @JoinColumn(name = "MerchantID", referencedColumnName = "cc_setl_mid", updatable = false, insertable = false)
    Merchant merchant;

    @Column(name = "MerchantName")
    String merchantName;

    @Column(name = "ApprovalStatus")
    String approvalStatus;

    @Column(name = "CardType")
    String cardType;

    @Column(name = "CardNumber")
    String cardNumber;

    @Column(name = "SafeCardNum")
    String safeCardNumber;

    @Column(name = "CardHolderName")
    String cardHolder;

    @Column(name = "ExpDate")
    Integer expiry;

    @Column(name = "Address")
    String address;

    @Column(name = "City")
    String city;

    @Column(name = "State")
    String state;

    @Column(name = "ZipCode")
    String zipCode;

    @Column(name = "Country")
    String country;

    @Column(name = "Phone")
    String phone;

    @Column(name = "Email")
    String email;

    @Column(name = "TransactionID")
    String transactionId;

    @Column(name = "TransactionType")
    String transactionType;

    @Column(name = "AuthCode")
    String authCode;

    @Column(name = "TransAmount")
    Integer authAmount;

    @Column(name = "TaxAmount")
    Integer taxAmount;

    @Column(name = "FeeAmount")
    Integer feeAmount;

    @Column(name = "AuthTransDate")
    String authDate;

    @Column(name = "AVSResponse")
    String avs;

    @Column(name = "CVVResponse")
    String cvv;

    @Column(name = "UserField1")
    String ud1;

    @Column(name = "UserField2")
    String ud2;

    @Column(name = "UserField3")
    String ud3;

    @Column(name = "RequestType")
    String requestType;

    @Column(name = "CurrencyCode")
    String currencyCode;

    @Column(name = "OrderNumber")
    String orderNumber;

    @Column(name = "Token")
    String token;

    @Column(name = "CCProcessor")
    String ccProcessor;

    @Column(name = "POSTerminalID")
    String posTerminalId;

    @Column(name = "POSAdditionalData")
    String posAdditionalData;

    @Column(name = "VNumber")
    String vNumber;

    @Column(name = "RetrievalReferenceNumber")
    Long retRefNumber;

    @Column(name = "BankTransID")
    String bankTransID;

    @Column(name = "ExternalTransID")
    String externalTransID;

    @Column(name = "ActionCode")
    String responseCode;

    @Column(name = "ResponseText")
    String responseText;

    @Column(name = "SettlementDate")
    String settlementDate;

    public Instant getSettlementDateInstant() {
        return DateUtil.parseToInstant(settlementDate);
    }

    public Instant getAuthDateInstant() {
        return DateUtil.parseToInstant(authDate);
    }

    public String getApprovalStatus() {
        if("APPROVED".equals(approvalStatus) || "00".equals(approvalStatus)) {
            return "APPROVED";
        }

        return "DECLINED";
    }
}*/