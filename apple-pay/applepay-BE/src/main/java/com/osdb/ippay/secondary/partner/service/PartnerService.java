package com.osdb.ippay.secondary.partner.service;

import com.osdb.ippay.secondary.partner.repository.entity.Partner;
import com.osdb.ippay.secondary.partner.service.filter.PartnerFilter;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;

import java.util.List;

public interface PartnerService {

    Page<Partner> find(PartnerFilter filter, Pageable pageable);

    List<Partner> find(PartnerFilter filter, Sort sort);

    List<Partner> find(List<Long> partnerIds);

    Partner find(Long id);

}
