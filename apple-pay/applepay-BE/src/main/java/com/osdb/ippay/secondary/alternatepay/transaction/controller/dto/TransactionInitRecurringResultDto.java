package com.osdb.ippay.secondary.alternatepay.transaction.controller.dto;

import lombok.*;
import lombok.experimental.FieldDefaults;

import static lombok.AccessLevel.PRIVATE;

import com.fasterxml.jackson.annotation.JsonProperty;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class TransactionInitRecurringResultDto {

	@JsonProperty("TransactionNumber")
    String TransactionNumber;

    String date;
 
    String amount;

    String baseAmount;

    String currency;
    
    int status;
    
    String statusName;
    
    String timeoutDate;  
    
    int methodType;
    
    SubscriptionOutDto subscription;
    
    @JsonProperty("paymentType")
    String paymentType;

}

