package com.osdb.ippay.secondary.alternatepay.transaction.controller.facade;

import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionInitRecurringDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionInitRecurringResultDto;


public interface TransactionInitRecurringFacade {

	TransactionInitRecurringResultDto create(TransactionInitRecurringDto transactionInitInDto);
}
