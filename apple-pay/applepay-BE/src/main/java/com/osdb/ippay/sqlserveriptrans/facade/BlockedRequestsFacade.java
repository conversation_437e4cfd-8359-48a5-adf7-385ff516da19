package com.osdb.ippay.sqlserveriptrans.facade;

import com.osdb.ippay.common.domain.FileDomain;
import com.osdb.ippay.secondary.chargeback.facade.dto.ChargebackDto;
import com.osdb.ippay.secondary.chargeback.service.ChargebackFilter;
import com.osdb.ippay.sqlserver.reports.facade.dto.TotalACHDataGtwReportsDto;
import com.osdb.ippay.sqlserver.reports.facade.dto.TotalACHGtwDataDto;
import com.osdb.ippay.sqlserver.reports.service.filter.TotalACHDataGtwReportsFilter;
import com.osdb.ippay.sqlserver.reports.service.filter.TotalACHGtwDataFilter;
import com.osdb.ippay.sqlserveriptrans.facade.dto.BlockedRequestsDto;
import com.osdb.ippay.sqlserveriptrans.repository.entity.BlockedRequests;
import com.osdb.ippay.sqlserveriptrans.service.filter.BlockedRequestsFilter;

import java.io.IOException;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface BlockedRequestsFacade {
	
	 Page<BlockedRequestsDto> findData(String terminalID, String ipAddress, String transactionType, java.util.Date startDateTime, java.util.Date endDateTime,
	    		BlockedRequestsFilter filter, Pageable pageable);
}