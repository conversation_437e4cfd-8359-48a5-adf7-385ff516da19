package com.osdb.ippay.primary.pdf.service.filter;

import lombok.*;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotBlank;

import static lombok.AccessLevel.PRIVATE;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class PdfFilter {

    @NotBlank(message = "URL is required")
    String url;

    String waitForSelector;

    String pdfFormat;

    String pdfLandscape;

    String browserProduct;

    String merchantId;

    Integer year;

    Integer month;

}
