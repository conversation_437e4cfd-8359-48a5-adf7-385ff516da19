package com.osdb.ippay.sqlserveripcore.ipwhitelist.facade.impl;

import com.osdb.ippay.secondary.alerts.facade.ThresholdSettingFacade;
import com.osdb.ippay.secondary.alerts.facade.dto.ThresholdSettingDto;
import com.osdb.ippay.secondary.alerts.facade.mapper.ThresholdSettingMapper;
import com.osdb.ippay.secondary.alerts.repository.entity.AlertType;
import com.osdb.ippay.secondary.alerts.repository.entity.ThresholdSetting;
import com.osdb.ippay.secondary.alerts.service.ThresholdSettingService;
import com.osdb.ippay.sqlserveripcore.ipwhitelist.facade.TerminalIpsGlobalFacade;
import com.osdb.ippay.sqlserveripcore.ipwhitelist.facade.dto.TerminalIpsBlockingDto;
import com.osdb.ippay.sqlserveripcore.ipwhitelist.facade.dto.TerminalIpsGlobalDto;
import com.osdb.ippay.sqlserveripcore.ipwhitelist.facade.mapper.TerminalIpsBlockingMapper;
import com.osdb.ippay.sqlserveripcore.ipwhitelist.facade.mapper.TerminalIpsGlobalMapper;
import com.osdb.ippay.sqlserveripcore.ipwhitelist.repository.GlobalWhitelistIpsRepository;
import com.osdb.ippay.sqlserveripcore.ipwhitelist.repository.TerminalIpsBlockingRepository;
import com.osdb.ippay.sqlserveripcore.ipwhitelist.repository.entity.ActiveStatusType;
import com.osdb.ippay.sqlserveripcore.ipwhitelist.repository.entity.TerminalIpsBlocking;
import com.osdb.ippay.sqlserveripcore.ipwhitelist.repository.entity.TerminalIpsGlobal;
import com.osdb.ippay.sqlserveripcore.ipwhitelist.service.TerminalIpsGlobalService;
import com.osdb.ippay.sqlserveripcore.ipwhitelist.service.filter.TerminalIpsBlockingFilter;
import com.osdb.ippay.sqlserveripcore.ipwhitelist.service.filter.TerminalIpsGlobalFilter;
import com.osdb.ippay.secondary.alerts.repository.ThresholdSettingRepository;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import static lombok.AccessLevel.PRIVATE;

import java.util.List;

@Component
@RequiredArgsConstructor
@FieldDefaults(makeFinal = true, level = PRIVATE)
public class TerminalIpsGlobalFacadeImpl implements TerminalIpsGlobalFacade {
    //ThresholdSettingService thresholdSettingService;
    TerminalIpsGlobalMapper thresholdSettingMapper;
    TerminalIpsBlockingMapper terminalIpsBlockingMapper;
    GlobalWhitelistIpsRepository thresholdSettingRepository;
    TerminalIpsGlobalService terminalIpsGlobalService;
    TerminalIpsBlockingRepository terminalIpsBlockingRepository;
    
	@Override
	public TerminalIpsGlobalDto update(TerminalIpsGlobalDto terminalIpsGlobalDto) {
		
		TerminalIpsGlobal obj = thresholdSettingMapper.toEntity(terminalIpsGlobalDto);
		TerminalIpsGlobal objGlobal = thresholdSettingRepository.save(obj);
        return thresholdSettingMapper.toDto(objGlobal);		
		//return null;
	}


	@Override
	public Page<TerminalIpsGlobal> findData(TerminalIpsGlobalFilter filter,
			Pageable pageable) {
		Page<TerminalIpsGlobal> terminalIpsGlobal = terminalIpsGlobalService.findData(filter, pageable);
        return terminalIpsGlobal;
	}


	@Override
	public TerminalIpsBlockingDto create(TerminalIpsBlockingDto terminalIpsBlockingDto) {

		TerminalIpsBlocking obj = terminalIpsBlockingMapper.toEntity(terminalIpsBlockingDto);
		TerminalIpsBlocking objGlobal = terminalIpsBlockingRepository.save(obj);
        return terminalIpsBlockingMapper.toDto(objGlobal);	
        
	}


	@Override
	public Page<TerminalIpsBlocking> findTerminalData(TerminalIpsBlockingFilter filter, Pageable pageable) {

		Page<TerminalIpsBlocking> terminalIpsGlobal = terminalIpsGlobalService.findTerminalData(filter, pageable);
        return terminalIpsGlobal;
	}
	
	@Override
	public void delete(Long id) {
		terminalIpsGlobalService.delete(id);
	}

	@Override
	public void batchDelete(List<Long> idList) {
		for (int i = 0; i < idList.size(); i++) {
			terminalIpsGlobalService.delete(idList.get(i));
		}
	}
}
