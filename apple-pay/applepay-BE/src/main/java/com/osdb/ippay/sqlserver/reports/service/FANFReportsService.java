/*package com.osdb.ippay.sqlserver.reports.service;

import com.osdb.ippay.sqlserver.reports.repository.entity.FANFReports;
import com.osdb.ippay.sqlserver.reports.service.filter.FANFReportsFilter;
import java.util.List;

public interface FANFReportsService {

    List<FANFReports> find( Integer merchantId, 
    									String authStartDateTime, 
    									String authEndDateTime,
    									FANFReportsFilter filter);

}
*/