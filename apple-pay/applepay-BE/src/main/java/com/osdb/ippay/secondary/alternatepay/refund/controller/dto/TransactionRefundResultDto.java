package com.osdb.ippay.secondary.alternatepay.refund.controller.dto;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;
import lombok.experimental.FieldDefaults;

import static lombok.AccessLevel.PRIVATE;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class TransactionRefundResultDto {

    String refundConfirmation;
  
    String dateApproval;
    
    String result;
    
    int amount;
    
    String providerId;
    
    String approvedBy;
    
    String refundBy;
    
    TransactionDto transaction;

}
