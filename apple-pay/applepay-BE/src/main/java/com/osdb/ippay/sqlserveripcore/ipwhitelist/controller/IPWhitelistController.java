package com.osdb.ippay.sqlserveripcore.ipwhitelist.controller;

import com.google.common.net.*;
import com.osdb.ippay.common.domain.FileDomain;
import com.osdb.ippay.common.logging.LogExecutionTime;
import com.osdb.ippay.common.util.response.PageResponse;
import com.osdb.ippay.secondary.alerts.facade.ThresholdSettingFacade;
import com.osdb.ippay.secondary.alerts.facade.dto.ThresholdSettingDto;
import com.osdb.ippay.secondary.alerts.repository.entity.AlertType;
import com.osdb.ippay.sqlserver.reports.facade.dto.TotalCardGtwDataDto;
import com.osdb.ippay.sqlserver.reports.service.filter.TotalCardGtwDataFilter;
import com.osdb.ippay.sqlserveripcore.ipwhitelist.facade.TerminalIpsAuditTrailRecordsFacade;
import com.osdb.ippay.sqlserveripcore.ipwhitelist.facade.TerminalIpsGlobalFacade;
import com.osdb.ippay.sqlserveripcore.ipwhitelist.facade.dto.BatchDeleteTerminalIpsGlobalDto;
import com.osdb.ippay.sqlserveripcore.ipwhitelist.facade.dto.TerminalIpsBlockingDto;
import com.osdb.ippay.sqlserveripcore.ipwhitelist.facade.dto.TerminalIpsGlobalDto;
import com.osdb.ippay.sqlserveripcore.ipwhitelist.repository.entity.ActiveStatusType;
import com.osdb.ippay.sqlserveripcore.ipwhitelist.repository.entity.TerminalIpsBlocking;
import com.osdb.ippay.sqlserveripcore.ipwhitelist.repository.entity.TerminalIpsGlobal;
import com.osdb.ippay.sqlserveripcore.ipwhitelist.service.TerminalIpsAuditTrailRecordsService;
import com.osdb.ippay.sqlserveripcore.ipwhitelist.service.TerminalIpsGlobalService;
import com.osdb.ippay.sqlserveripcore.ipwhitelist.service.filter.TerminalIpsBlockingFilter;
import com.osdb.ippay.sqlserveripcore.ipwhitelist.service.filter.TerminalIpsGlobalFilter;

import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import org.springdoc.api.annotations.ParameterObject;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import static lombok.AccessLevel.PRIVATE;

import java.io.IOException;

import javax.validation.Valid;

@Tag(name = "ip-whitelist")
@RestController
@RequestMapping(value = "/api/v1/private")
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class IPWhitelistController {

    TerminalIpsGlobalFacade terminalIpsGlobalFacade;
    TerminalIpsAuditTrailRecordsFacade terminalIpsAuditTrailRecordsFacade;
    TerminalIpsAuditTrailRecordsService terminalIpsAuditTrailServiceFacade;
    TerminalIpsGlobalService terminalIpsGlobalService;
   
    /*
    @LogExecutionTime
    @PostMapping("/whitelist-ip-address")
    public ResponseEntity<TerminalIpsGlobalDto> whitelistIpAddress(@RequestBody TerminalIpsGlobalDto terminalIpsGlobalDto) {
    	TerminalIpsGlobalDto updatedDto = terminalIpsGlobalFacade.update(terminalIpsGlobalDto);
        return ResponseEntity.ok(updatedDto);
    }
    
    @PostMapping("/whitelist-terminal-ip-address")
    public ResponseEntity<TerminalIpsBlockingDto> whitelistTerminalIpAddress(@RequestBody TerminalIpsBlockingDto terminalIpsGlobalDto) {
    	TerminalIpsBlockingDto updatedDto = terminalIpsGlobalFacade.create(terminalIpsGlobalDto);
        return ResponseEntity.ok(updatedDto);
    }
    
    @LogExecutionTime
    @GetMapping("/show-whitelist-ip-address")
    public ResponseEntity<PageResponse<TerminalIpsGlobal>> getWhitelistIPAddress(@Valid @ParameterObject TerminalIpsGlobalFilter filter,
             Pageable page) {

        Page<TerminalIpsGlobal> response = terminalIpsGlobalService.findData(filter, page);
        return ResponseEntity.ok(new PageResponse<>(response.getContent(), response.getTotalElements()));
    }
    
    @LogExecutionTime
    @GetMapping("/show-terminal-whitelist-ip-address")
    public ResponseEntity<PageResponse<TerminalIpsBlocking>> getWhitelistTerminalIPAddress(@Valid @ParameterObject TerminalIpsBlockingFilter filter,
             Pageable page) {

        Page<TerminalIpsBlocking> response = terminalIpsGlobalService.findTerminalData(filter, page);
        return ResponseEntity.ok(new PageResponse<>(response.getContent(), response.getTotalElements()));
    }
    
    @LogExecutionTime
    @DeleteMapping("/delete-whitelist-ip-address")
    public ResponseEntity<Void> delete(@Valid @ParameterObject Long id) {
        terminalIpsGlobalFacade.delete(id);
        return ResponseEntity.noContent().build();
    }

    @LogExecutionTime
    @DeleteMapping("/batch-delete-whitelist-ip-address")
    public ResponseEntity<Void> batchDelete(@RequestBody BatchDeleteTerminalIpsGlobalDto ipListDto) {
        terminalIpsGlobalFacade.batchDelete(ipListDto.getIdList());
        return ResponseEntity.noContent().build();
    }
    
    @LogExecutionTime
    @GetMapping("/generate-ip-whitelist-audit-trail")
    public ResponseEntity<ByteArrayResource> generateCSVData() throws IOException {

        FileDomain response = terminalIpsAuditTrailRecordsFacade.export();

        return ResponseEntity.ok()
                .contentType(new MediaType("application", "force-download"))
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=".concat(response.getFileName()))
                .body(response.getFile());
    }*/
    
}