package com.osdb.ippay.secondary.statement.facade.impl;

import com.osdb.ippay.secondary.statement.facade.MerchantStatementFacade;
import com.osdb.ippay.secondary.statement.facade.dto.*;
import com.osdb.ippay.secondary.statement.facade.mapper.*;
import com.osdb.ippay.secondary.statement.repository.entity.*;
import com.osdb.ippay.secondary.statement.service.MerchantStatementService;
import com.osdb.ippay.secondary.statement.service.filter.MSFilter;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.Future;

import static lombok.AccessLevel.PRIVATE;

@Component
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class MerchantStatementFacadeImpl implements MerchantStatementFacade {

    // services
    MerchantStatementService msService;

    // mappers
    MSActivitySummaryMapper activitySummaryMapper;
    MSDepositDetailMapper depositDetailMapper;
    MSOtherCardDepositMapper otherCardDepositMapper;
    MSProcessingDetailMapper processingDetailMapper;
    MSAuthDetailMapper authDetailMapper;
    MSOtherDetailMapper otherDetailMapper;
    MSTotalDetailMapper totalDetailMapper;

    @Async
    @Override
    public Future<List<MSActivitySummaryDto>> findActivitySummary(String merchantId, MSFilter msFilter) {
        List<MSActivitySummary> activitySummaries = msService.findActivitySummary(merchantId, msFilter);
        return new AsyncResult<>(activitySummaryMapper.toDto(activitySummaries));
    }

    @Async
    @Override
    public Future<List<MSDepositDetailDto>> findDepositDetails(String merchantId, MSFilter msFilter) {
        List<MSDepositDetail> depositDetails = msService.findDepositDetails(merchantId, msFilter);
        return new AsyncResult<>(depositDetailMapper.toDto(depositDetails));
    }

    @Async
    @Override
    public Future<List<MSOtherCardDepositDto>> findOtherCardDeposits(String merchantId, MSFilter msFilter) {
        List<MSOtherCardDeposit> otherCardDeposits = msService.findOtherCardDeposits(merchantId, msFilter);
        return new AsyncResult<>(otherCardDepositMapper.toDto(otherCardDeposits));
    }

    @Async
    @Override
    public Future<List<MSProcessingDetailDto>> findProcessingDetails(String merchantId, MSFilter msFilter) {
        List<MSProcessingDetail> processingDetails = msService.findProcessingDetail(merchantId, msFilter);
        return new AsyncResult<>(processingDetailMapper.toDto(processingDetails));
    }

    @Async
    @Override
    public Future<List<MSAuthDetailDto>> findAuthDetails(String merchantId, MSFilter msFilter) {
        List<MSAuthDetail> authDetails = msService.findAuthDetails(merchantId, msFilter);
        return new AsyncResult<>(authDetailMapper.toDto(authDetails));
    }

    @Async
    @Override
    public Future<List<MSOtherDetailDto>> findOtherDetails(String merchantId, MSFilter msFilter) {
        List<MSOtherDetail> otherDetails = msService.findOtherDetails(merchantId, msFilter);
        return new AsyncResult<>(otherDetailMapper.toDto(otherDetails));
    }

    @Async
    @Override
    public Future<List<MSTotalDetailDto>> findTotalDetail(String merchantId, MSFilter msFilter) {
        List<MSTotalDetail> totalDetails = msService.findTotalDetail(merchantId, msFilter);
        return new AsyncResult<>(totalDetailMapper.toDto(totalDetails));
    }
}
