package com.osdb.ippay.secondary.alerts.facade.impl;

import com.osdb.ippay.secondary.alerts.facade.ThresholdSettingFacade;
import com.osdb.ippay.secondary.alerts.facade.dto.ThresholdSettingDto;
import com.osdb.ippay.secondary.alerts.facade.mapper.ThresholdSettingMapper;
import com.osdb.ippay.secondary.alerts.repository.entity.AlertType;
import com.osdb.ippay.secondary.alerts.repository.entity.ThresholdSetting;
import com.osdb.ippay.secondary.alerts.service.ThresholdSettingService;
import com.osdb.ippay.sqlserveripcore.ipwhitelist.repository.entity.TerminalIpsBlocking;
import com.osdb.ippay.secondary.alerts.repository.ThresholdSettingRepository;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.stereotype.Component;

import static lombok.AccessLevel.PRIVATE;

@Component
@RequiredArgsConstructor
@FieldDefaults(makeFinal = true, level = PRIVATE)
public class ThresholdSettingFacadeImpl implements ThresholdSettingFacade {
    ThresholdSettingService thresholdSettingService;
    ThresholdSettingMapper thresholdSettingMapper;
    ThresholdSettingRepository thresholdSettingRepository;

    @Override
    public ThresholdSettingDto find(AlertType alertType) {
        ThresholdSetting thresholdSetting = thresholdSettingService.find(alertType);
        return thresholdSettingMapper.toDto(thresholdSetting);
    }

    @Override
    public ThresholdSettingDto update(AlertType alertType, ThresholdSettingDto thresholdSettingDto, String mid) {
        ThresholdSetting setting = thresholdSettingRepository.findByAlertTypeAndMid(alertType, mid);
           
        thresholdSettingMapper.putEntity(thresholdSettingDto, setting);
        ThresholdSetting updatedSetting = thresholdSettingRepository.save(setting);
        return thresholdSettingMapper.toDto(updatedSetting);
    }

	@Override
	public ThresholdSettingDto findByMid(AlertType alertType, String mid) {
		ThresholdSetting thresholdSetting = thresholdSettingRepository.findByAlertTypeAndMid(alertType, mid);
        return thresholdSettingMapper.toDto(thresholdSetting);
	}

	@Override
	public ThresholdSettingDto create(AlertType alertType, ThresholdSettingDto thresholdSettingDto) {
		ThresholdSetting obj = thresholdSettingMapper.toEntity(thresholdSettingDto);
		ThresholdSetting objGlobal = thresholdSettingRepository.save(obj);
        return thresholdSettingMapper.toDto(objGlobal);	
	}
}
