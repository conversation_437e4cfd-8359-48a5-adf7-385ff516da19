package com.osdb.ippay.sqlserveriptrans.service.filter;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import javax.validation.constraints.NotNull;
import static lombok.AccessLevel.PRIVATE;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = PRIVATE)
public class BlockedRequestsFilter {


    @Parameter(example = "123456")
	String terminalID;
	
    @Parameter(example = "127.0.0.1")
	String ipAddress;
    
    @Parameter(example = "SALE")
	String transactionType;
  
	@Parameter(example = "12/29/2016")
	java.util.Date startDateTime;

    @Parameter(example = "12/29/2024")
    java.util.Date endDateTime;

}