package com.osdb.ippay.sqlserver.sftp.controller;

import com.google.common.net.HttpHeaders;
import com.osdb.ippay.common.domain.FileDomain;
import com.osdb.ippay.common.logging.LogExecutionTime;
import com.osdb.ippay.secondary.alerts.facade.ThresholdSettingFacade;
import com.osdb.ippay.secondary.alerts.facade.dto.*;
import com.osdb.ippay.secondary.alerts.repository.entity.AlertType;
import com.osdb.ippay.secondary.retokenization.presignedurl.service.PresignedUrlFileService;
import com.osdb.ippay.sqlserver.sftp.dto.PGPEncryptFileUploadResultDto;
import com.osdb.ippay.sqlserver.sftp.facade.MerchantPANDetailsFacade;
import com.osdb.ippay.sqlserver.sftp.service.PGPEncryptFileUpload;

import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import static lombok.AccessLevel.PRIVATE;

import java.io.IOException;
import java.io.InputStream;
import static org.springframework.http.MediaType.MULTIPART_FORM_DATA_VALUE;

@Tag(name = "Detokenization")
@RestController
@RequestMapping(value = "/api/v1/private/encrypt-upload")
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class PGPEncryptFileUploadController {

	PGPEncryptFileUpload pgpEncryptFileUpload;
	MerchantPANDetailsFacade merchantPANDetailsFacade;
	
	  /*  @LogExecutionTime
	    @XsrfTokenHeader
	    @PostMapping(value = "/upload", consumes = MULTIPART_FORM_DATA_VALUE)
	    public ResponseEntity<UploadOutDto> generateUrl(@RequestBody MultipartFile multipartFile) {
	        String filename = UUID.randomUUID() + ".pgp";
	        presignedUrlFileService.upload(filename, multipartFile);
	        return ResponseEntity.ok(new UploadOutDto(filename, multipartFile.getOriginalFilename()));
	    }
*/
	    @LogExecutionTime
	    @PostMapping(value= "/encryptupload", consumes = MULTIPART_FORM_DATA_VALUE)
	    public ResponseEntity<PGPEncryptFileUploadResultDto> upload(String mid, @RequestBody MultipartFile multipartPublicKeyFile, @RequestBody MultipartFile multipartPrivateKeyFile) {
	    	InputStream input = null;
			try {
				input = merchantPANDetailsFacade.exportPANDetailsStream(mid);
			} catch (IOException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
	        String status = pgpEncryptFileUpload.EncryptUpload(mid, input, multipartPublicKeyFile, multipartPrivateKeyFile);
	        return ResponseEntity.ok(new PGPEncryptFileUploadResultDto(status));
	    }
	    
	    @LogExecutionTime
	    @GetMapping(value= "/generateCSV")
	    public ResponseEntity<ByteArrayResource> exportToCSV(String mid) throws IOException {
	    	
	    	  FileDomain response = merchantPANDetailsFacade.export(mid);

	          return ResponseEntity.ok()
	                  .contentType(new MediaType("application", "force-download"))
	                  .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=".concat(response.getFileName()))
	                  .body(response.getFile());
	    }
	    

  
}