package com.osdb.ippay.secondary.retokenization.presignedurl.service.impl;

import com.amazonaws.HttpMethod;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.PutObjectRequest;
import com.osdb.ippay.common.exception.business.InvalidFileException;
import com.osdb.ippay.secondary.retokenization.presignedurl.service.PresignedUrlFileService;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Calendar;
import java.util.Date;

import static lombok.AccessLevel.PRIVATE;

@RequiredArgsConstructor
@Service
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class PresignedUrlFileServiceImpl implements PresignedUrlFileService {

    private static final String INVALID_FILE_EXCEPTION = "File invalid or corrupt";

    AmazonS3 amazonS3;

    @NonFinal
    @Value("${aws.s3.bucket}")
    private String bucketName;

    @Override
    public String generatePreSignedUrl(String filePath, HttpMethod http) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date());
        cal.add(Calendar.MINUTE,2);
        return amazonS3.generatePresignedUrl(bucketName,filePath,cal.getTime(),http).toString();
    }

    @Override
    public void upload(String filename, MultipartFile file) {
        ObjectMetadata metadata = new ObjectMetadata();
        metadata.setContentLength(file.getSize());
        try {
            PutObjectRequest request = new PutObjectRequest(
                    bucketName,
                    filename,
                    file.getInputStream(),
                    metadata
            );
            amazonS3.putObject(request);
        }catch (Exception exception) {
            throw new InvalidFileException(INVALID_FILE_EXCEPTION);
        }
    }
}
