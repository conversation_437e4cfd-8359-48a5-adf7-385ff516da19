package com.osdb.ippay.secondary.retokenization.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

import static lombok.AccessLevel.PRIVATE;

@Getter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class JobResultDto {

    @JsonProperty(required = true)
    String jobId;

    String resultS3BucketKey;

    String resultS3Bucket;

    String resultFile;

    @JsonProperty(required = true)
    Double elapsedTime;

    String errorMessage;

    @JsonProperty(required = true)
    Integer tokenizeCount;

    @JsonProperty(required = true)
    Integer totalCcRecords;

    @JsonProperty(required = true)
    Integer totalAchRecords;

    @JsonProperty(required = true)
    Integer totalRecords;

}
