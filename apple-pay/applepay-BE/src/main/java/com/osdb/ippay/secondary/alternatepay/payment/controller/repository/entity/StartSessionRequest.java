package com.osdb.ippay.secondary.alternatepay.payment.controller.repository.entity;

import lombok.*;
import lombok.experimental.FieldDefaults;

import static lombok.AccessLevel.PRIVATE;

import javax.validation.constraints.NotNull;


@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder

public class StartSessionRequest {

    @NotNull
    private String validationUrl;

}
