package com.osdb.ippay.secondary.retokenization.controller.mapper;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.osdb.ippay.common.util.response.PageResponse;
import com.osdb.ippay.secondary.retokenization.controller.dto.*;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;

import static lombok.AccessLevel.PRIVATE;

@Component
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class RetokenizationMapper {

    @NonFinal
    @Value("${aws.s3.bucket}")
    String bucketName;

    private static final Integer MAX_RUNTIME_HOURS = 2;

    ObjectMapper objectMapper;

    public RetokenizationDto toDto(RetokenizationInDto retokenizationInDto) {
        return RetokenizationDto.builder()
                .jobId(UUID.randomUUID().toString())
                .user(retokenizationInDto.getUser())
                .jobDescription(retokenizationInDto.getJobDescription())
                .jobMaxRuntime(MAX_RUNTIME_HOURS * 3600)
                .jobType(retokenizationInDto.getJobType())
                .jobInfo(jobInfoToString(toDto(retokenizationInDto.getJobInfo())))
                .build();
    }

    public String jobInfoToString(JobInfoDto jobInfoDto) {
        try {
            String result = objectMapper.writeValueAsString(jobInfoDto);
            return result.replace("\n", "").replace("\r", "");
        } catch (Exception exception) {
            return "";
        }
    }

    public PageResponse<RetokenizationOutDto> toOutDtoPageResponse(PageResponse<RetokenizationResultDto> retokenizationResultDtoPageResponse) {
        return new PageResponse<>(
                retokenizationResultDtoPageResponse.getResult().stream()
                        .map(this::toOutDto)
                        .collect(Collectors.toList()),
                retokenizationResultDtoPageResponse.getTotal()
        );
    }

    public RetokenizationOutDto toOutDto(RetokenizationResultDto retokenizationResultDto) {
        return RetokenizationOutDto.builder()
                .id(retokenizationResultDto.getId())
                .dateModified(retokenizationResultDto.getDateModified())
                .jobId(retokenizationResultDto.getJobId())
                .dateCreated(retokenizationResultDto.getDateCreated())
                .jobType(retokenizationResultDto.getJobType())
                .jobMaxRuntime(retokenizationResultDto.getJobMaxRuntime())
                .user(retokenizationResultDto.getUser())
                .jobCompletedAt(retokenizationResultDto.getJobCompletedAt())
                .jobResult(stringToJobResult(retokenizationResultDto.getJobResult()))
                .jobDescription(retokenizationResultDto.getJobDescription())
                .state(retokenizationResultDto.getState())
                .jobErrorMsg(retokenizationResultDto.getJobErrorMsg())
                .jobRanHost(retokenizationResultDto.getJobRanHost())
                .jobStartedAt(retokenizationResultDto.getJobStartedAt())
                .jobInfo(stringToJobInfo(retokenizationResultDto.getJobInfo()))
                .build();
    }

    public JobInfoDto toDto(JobInfoInDto jobInfoInDto) {
        return JobInfoDto.builder()
                .jobId(UUID.randomUUID().toString())
                .orgFilename(jobInfoInDto.getOrgFilename())
                .sourceDataType(jobInfoInDto.getSourceDataType())
                .sourceS3Bucket(bucketName)
                .description(jobInfoInDto.getDescription())
                .oldTerminalId(jobInfoInDto.getOldTerminalId())
                .newTerminalId(jobInfoInDto.getNewTerminalId())
                .sourceS3BucketKey(jobInfoInDto.getSourceS3BucketKey())
                .tokenType(jobInfoInDto.getTokenType())
                .build();
    }

    public JobInfoDto stringToJobInfo(String string) {
        try {
            return objectMapper.readValue(string, JobInfoDto.class);
        } catch (Exception exception) {
            return null;
        }
    }

    public JobResultDto stringToJobResult(String string) {
        try {
            return objectMapper.readValue(string, JobResultDto.class);
        } catch (Exception exception) {
            return null;
        }
    }

    public RetokenizationUpdateDto toUpdateDto(RetokenizationInDto retokenizationInDto) {
        return RetokenizationUpdateDto.builder()
                .user(retokenizationInDto.getUser())
                .jobType(retokenizationInDto.getJobType())
                .jobDescription(retokenizationInDto.getJobDescription())
                .jobInfo(jobInfoToString(toDto(retokenizationInDto.getJobInfo())))
                .build();
    }

    public RetokenizationUpdateDto toUpdateDto(RetokenizationPatchDto retokenizationPatchDto, RetokenizationResultDto retokenizationResultDto) {
        return RetokenizationUpdateDto.builder()
                .user(Objects.isNull(retokenizationPatchDto.getUser()) ? retokenizationResultDto.getUser() : retokenizationPatchDto.getUser())
                .jobType(Objects.isNull(retokenizationPatchDto.getJobType()) ? retokenizationResultDto.getJobType() : retokenizationPatchDto.getJobType())
                .jobDescription(Objects.isNull(retokenizationPatchDto.getJobDescription()) ? retokenizationResultDto.getJobDescription() : retokenizationPatchDto.getJobDescription())
                .jobInfo(Objects.isNull(retokenizationPatchDto.getJobInfo()) ?
                                retokenizationResultDto.getJobInfo() :
                                jobInfoToString(toDto(retokenizationPatchDto.getJobInfo(), stringToJobInfo(retokenizationResultDto.getJobInfo())))
                        )
                .build();
    }

    public JobInfoDto toDto(JobInfoPatchDto patchDto, JobInfoDto currentDto) {
        if(Objects.nonNull(patchDto.getOldTerminalId())) {
            currentDto.setOldTerminalId(patchDto.getOldTerminalId());
        }
        if(Objects.nonNull(patchDto.getNewTerminalId())) {
            currentDto.setNewTerminalId(patchDto.getNewTerminalId());
        }
        if(Objects.nonNull(patchDto.getDescription())) {
            currentDto.setDescription(patchDto.getDescription());
        }
        if(Objects.nonNull(patchDto.getOrgFilename())) {
            currentDto.setOrgFilename(patchDto.getOrgFilename());
        }
        if(Objects.nonNull(patchDto.getSourceDataType())) {
            currentDto.setSourceDataType(patchDto.getSourceDataType());
        }
        if(Objects.nonNull(patchDto.getTokenType())) {
            currentDto.setTokenType(patchDto.getTokenType());
        }
        if(Objects.nonNull(patchDto.getSourceS3BucketKey())) {
            currentDto.setSourceS3BucketKey(patchDto.getSourceS3BucketKey());
        }
        return currentDto;
    }

    private String convertToSqlDate(Date date) {
        DateFormat dateTimeFormatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return dateTimeFormatter.format(date);
    }

}
