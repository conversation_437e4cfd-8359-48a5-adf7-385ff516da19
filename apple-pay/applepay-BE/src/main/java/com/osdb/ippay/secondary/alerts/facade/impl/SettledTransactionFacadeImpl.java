package com.osdb.ippay.secondary.alerts.facade.impl;

import com.osdb.ippay.common.domain.FileDomain;
import com.osdb.ippay.primary.user.repository.entity.User;
import com.osdb.ippay.secondary.alerts.facade.SettledTransactionFacade;
import com.osdb.ippay.secondary.alerts.facade.dto.SettledTransactionDto;
//import com.osdb.ippay.secondary.alerts.facade.mapper.SettledTransactionMapper;
import com.osdb.ippay.secondary.alerts.repository.entity.SettledTransaction;
import com.osdb.ippay.secondary.alerts.service.SettledTransactionAlertService;
import com.osdb.ippay.secondary.alerts.service.filter.SettledTransactionFilter;
import com.osdb.ippay.secondary.chargeback.facade.dto.ChargebackDto;
import com.osdb.ippay.secondary.chargeback.repository.entity.Chargeback;
import com.osdb.ippay.secondary.chargeback.service.ChargebackFilter;
import com.osdb.ippay.sqlserver.reports.facade.TotalACHDataGtwReportsFacade;
import com.osdb.ippay.sqlserver.reports.facade.dto.TotalACHDataGtwReportsDto;
import com.osdb.ippay.sqlserver.reports.facade.dto.TotalACHGtwDataDto;
import com.osdb.ippay.sqlserver.reports.facade.mapper.TotalACHDataGtwReportsMapper;
import com.osdb.ippay.sqlserver.reports.facade.mapper.TotalACHGtwDataMapper;
import com.osdb.ippay.sqlserver.reports.repository.entity.TotalACHDataGtwReports;
import com.osdb.ippay.sqlserver.reports.repository.entity.TotalACHGtwData;
import com.osdb.ippay.sqlserver.reports.service.TotalACHDataGtwReportsService;
import com.osdb.ippay.sqlserver.reports.service.filter.TotalACHDataGtwReportsFilter;
import com.osdb.ippay.sqlserver.reports.service.filter.TotalACHGtwDataFilter;

import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.ListIterator;
import java.util.Map;

import static lombok.AccessLevel.PRIVATE;

@Component
@RequiredArgsConstructor
@FieldDefaults(makeFinal = true, level = PRIVATE)
public class SettledTransactionFacadeImpl implements SettledTransactionFacade {

    // services
	SettledTransactionAlertService settledTransactionAlertService;

    // mappers
	//SettledTransactionMapper settledTransactionMapper;
	
	@Override
	public FileDomain export(String startDateTime, String endDateTime, SettledTransactionFilter filter)
			throws IOException {
		String[] HEADERS = {
                "MID",
                "Settlement date",
                "% (70, 90 or 100) limit hit",
                "$ Amount Hit",
                "Overlimit Amount",
                "Actual Amount"
        };

        final CSVFormat format = CSVFormat.Builder.create()
                .setHeader(HEADERS)
                .setIgnoreEmptyLines(true)
                .build();
        
        List<SettledTransaction> settledTransactionData  = settledTransactionAlertService.generateReport(startDateTime, endDateTime);
        for (int i = 0; i < settledTransactionData.size(); i++) {

            // Print all elements of List
            //System.out.println(settledTransactionData.get(i).toString());
        }
        try (
                ByteArrayOutputStream out = new ByteArrayOutputStream();
                CSVPrinter csvPrinter = new CSVPrinter(new PrintWriter(out), format)
        ) {
        	//List<String> result = new ArrayList<>(totalACHDataGtwReports.size());
        	//ListIterator<String> it = totalACHDataGtwReports.listIterator(totalACHDataGtwReports.size());
        	
       //  for(Object totalACHDataGtwReportsObj : totalACHDataGtwReports) {
        	// List<String> data = Arrays.asList(
        	 
         //}

            for (SettledTransaction settledTransactionObj : settledTransactionData) {
                List<String> data = Arrays.asList(
                		settledTransactionObj.getMerchantId(),
                		settledTransactionObj.getSettlementDate(),
                		settledTransactionObj.getPercentageHit(),
                		settledTransactionObj.getAmountHitOrExceeded(),
                		settledTransactionObj.getOverlimitAmount(),
                		settledTransactionObj.getActualAmount()
                );

                csvPrinter.printRecord(data);
            }
            //csvPrinter.printRecord(data);
            csvPrinter.flush();

            return FileDomain.builder()
                    .file(new ByteArrayResource(new ByteArrayInputStream(out.toByteArray()).readAllBytes()))
                    .fileName(
                            String.format(
                                    "Percentage-Monthly-Settlement-%s.csv",	             
                                    filter.getStartDateTime()
                            )
                    )
                    .build();
        }
        
	
	}
	
	@Override
	public Map<String, ByteArrayOutputStream> findMerchantsDataToExport(String startDateTime, String endDateTime, SettledTransactionFilter filter)
			throws IOException {
		String[] HEADERS = {
                "MID",
                "Settlement date",
                "% (70, 90 or 100) limit hit",
                "$ Amount Hit",
                "Overlimit Amount",
                "Actual Amount"
        };

		
		Map<String, ByteArrayOutputStream> retHashMap = new HashMap<>();
		
        final CSVFormat format = CSVFormat.Builder.create()
                .setHeader(HEADERS)
                .setIgnoreEmptyLines(true)
                .build();
        
        Map<String, List<SettledTransaction>> settledTransactionData  = settledTransactionAlertService.generateCSV(startDateTime, endDateTime);
        for (Map.Entry<String, List<SettledTransaction>> settledTransactoionSet :
        	settledTransactionData.entrySet()) {

           // Printing all elements of a Map
          // System.out.println(settledTransactoionSet.getKey() + " = "
                            //  + settledTransactoionSet.getValue());
           
           String Mid = settledTransactoionSet.getKey();
           List<SettledTransaction> settledTrnObjList = settledTransactoionSet.getValue();
           
           try (
        		   ByteArrayOutputStream retByteArrayOutputStream = new ByteArrayOutputStream();
                   CSVPrinter csvPrinter = new CSVPrinter(new PrintWriter(retByteArrayOutputStream), format)
           ) {
           

               for (SettledTransaction settledTransactionObj : settledTrnObjList) {
                   List<String> data = Arrays.asList(
                   		settledTransactionObj.getMerchantId(),
                   		settledTransactionObj.getSettlementDate(),
                   		settledTransactionObj.getPercentageHit(),
                   		settledTransactionObj.getAmountHitOrExceeded(),
                   		settledTransactionObj.getOverlimitAmount(),
                   		settledTransactionObj.getActualAmount()
                   );

                   csvPrinter.printRecord(data);
               }
               //csvPrinter.printRecord(data);
               csvPrinter.flush();
               retHashMap.put(Mid, retByteArrayOutputStream);
               
           }
           
       }
        
        return retHashMap;
	
	}
	
	//@Override
	/*public Page<SettledTransactionDto> findData(String startDateTime, String endDateTime,
			SettledTransactionFilter filter, Pageable pageable) {
		 Page<SettledTransaction> settledTransactionData = settledTransactionAlertService.find(startDateTime, endDateTime, filter);
	       return settledTransactionMapper.toDto(settledTransactionData);
	}*/

}
