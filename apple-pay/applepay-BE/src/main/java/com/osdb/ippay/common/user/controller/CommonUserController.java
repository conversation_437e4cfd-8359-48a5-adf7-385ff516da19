package com.osdb.ippay.common.user.controller;

import com.osdb.ippay.common.logging.LogExecutionTime;
import com.osdb.ippay.common.security.service.AuthenticationService;
import com.osdb.ippay.common.user.facade.CommonUserFacade;
import com.osdb.ippay.common.util.annotation.ApiPageable;
import com.osdb.ippay.common.util.response.PageResponse;
import com.osdb.ippay.common.user.facade.dto.EditUserDto;
import com.osdb.ippay.common.user.facade.dto.UserDto;
import com.osdb.ippay.primary.user.service.filter.UserFilter;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import static lombok.AccessLevel.PRIVATE;

@Tag(name = "user")
@RestController
@RequestMapping(value = "/api/v1/private")
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class CommonUserController {
/*
    AuthenticationService authenticationService;
    CommonUserFacade userFacade;

    @LogExecutionTime
    @ApiPageable
    @GetMapping("/users")
    public ResponseEntity<PageResponse<UserDto>> get(@Parameter(hidden = true) Pageable pageable,
                                                     @ParameterObject UserFilter userFilter,
                                                     @Parameter(hidden = true) @AuthenticationPrincipal String email) {

        authenticationService.throwIfNotInternal(email);

        Page<UserDto> response = userFacade.find(userFilter, pageable);
        return ResponseEntity.ok(new PageResponse<>(response.getContent(), response.getTotalElements()));
    }

    @LogExecutionTime
    @GetMapping("/users/{id}")
    public ResponseEntity<UserDto> get(@PathVariable Long id,
                                       @Parameter(hidden = true) @AuthenticationPrincipal String email) {

        authenticationService.throwIfNotInternal(email);

        UserDto response = userFacade.find(id);
        return ResponseEntity.ok(response);
    }

    @LogExecutionTime
    @PostMapping("/users")
    public ResponseEntity<UserDto> create(@Valid @RequestBody UserDto userDto,
                                          @Parameter(hidden = true) @AuthenticationPrincipal String email) {

        authenticationService.throwIfNotFullAdmin(email);

        UserDto response = userFacade.create(userDto);
        return ResponseEntity.ok(response);
    }

    @LogExecutionTime
    @PutMapping("/users/{id}")
    public ResponseEntity<UserDto> put(@PathVariable Long id, @Valid @RequestBody EditUserDto userDto,
                                       @Parameter(hidden = true) @AuthenticationPrincipal String email) {

        authenticationService.throwIfNotFullAdmin(email);

        UserDto response = userFacade.put(id, userDto);
        return ResponseEntity.ok(response);
    }

    @LogExecutionTime
    @PatchMapping("/users/{id}")
    public ResponseEntity<UserDto> patch(@PathVariable Long id,
                                         @RequestBody EditUserDto userDto,
                                         @Parameter(hidden = true) @AuthenticationPrincipal String email) {

        authenticationService.throwIfNotFullAdmin(email);

        UserDto response = userFacade.patch(id, userDto);
        return ResponseEntity.ok(response);
    }

    @LogExecutionTime
    @PostMapping("/users/{id}/resend-invitation")
    public ResponseEntity<Void> resendInvitation(@PathVariable Long id,
                                                 @Parameter(hidden = true) @AuthenticationPrincipal String email) {

        authenticationService.throwIfNotFullAdmin(email);

        userFacade.resendInvitation(id);
        return ResponseEntity.noContent().build();
    }*/
}
