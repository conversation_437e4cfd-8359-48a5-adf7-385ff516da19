package com.osdb.ippay.primary.user.facade.dto;

import com.osdb.ippay.primary.user.repository.entity.UserStatus;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotNull;

import static lombok.AccessLevel.PRIVATE;

@Getter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
public class UserStatusDto {

    @NotNull(message = "Status is required")
    UserStatus status;

}
