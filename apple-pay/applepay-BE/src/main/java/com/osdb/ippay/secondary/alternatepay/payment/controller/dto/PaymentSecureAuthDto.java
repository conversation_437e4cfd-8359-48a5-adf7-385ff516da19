package com.osdb.ippay.secondary.alternatepay.payment.controller.dto;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;
import lombok.experimental.FieldDefaults;

import static lombok.AccessLevel.PRIVATE;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class PaymentSecureAuthDto {

    @JsonProperty("deviceID")
    String deviceID;
  
    @JsonProperty("transactionKey")
    String transactionKey;

    @JsonProperty("cardDataSource")
    String cardDataSource;

    @JsonProperty("encryptedData")
    PaymentEncryptedDataDto encryptedData;
    
    @JsonProperty("developerID")
    String developerID;

}
