package com.osdb.ippay.sqlserver.sftp;

import static lombok.AccessLevel.PRIVATE;

import com.opencsv.bean.CsvBindByPosition;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

@Getter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class CSVData {
    @CsvBindByPosition(position = 0)
    private String merchantID;

    @CsvBindByPosition(position = 1)
    private String merchantName;

    @CsvBindByPosition(position = 2)
    private String totalSalesVolume;

    @CsvBindByPosition(position = 3)
    private String visaSalesAmount;
    
    @CsvBindByPosition(position = 4)
    private String visaSaleCount;

    @CsvBindByPosition(position = 5)
    private String visaCreditAmount;

    @CsvBindByPosition(position = 6)
    private String visaCreditCount;

    @CsvBindByPosition(position = 7)
    private String mcSalesAmount;
    
    @CsvBindByPosition(position = 8)
    private String mcSalesCount;

    @CsvBindByPosition(position = 9)
    private String mcCreditAmount;

    @CsvBindByPosition(position = 10)
    private String mcCreditCount;

    @CsvBindByPosition(position = 11)
    private String amexSalesAmount;
    
    @CsvBindByPosition(position = 12)
    private String amexSalesCount;

    @CsvBindByPosition(position = 13)
    private String amexCreditAmount;

    @CsvBindByPosition(position = 14)
    private String amexCreditCount;
    
    @CsvBindByPosition(position = 15)
    private String discoverSalesAmount;

    @CsvBindByPosition(position = 16)
    private String discoverSalesCount;

    @CsvBindByPosition(position = 17)
    private String discoverCreditAmount;

    @CsvBindByPosition(position = 18)
    private String discoverCreditCount;
    
    @CsvBindByPosition(position = 19)
    private String netSales;
 
}