package com.osdb.ippay.secondary.alternatepay.transaction.controller.facade.impl;

import com.osdb.ippay.secondary.alternatepay.transaction.controller.Repository.Entity.*;
import com.osdb.ippay.secondary.alternatepay.payment.controller.dto.PaymentResultDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.Repository.HppTokenRepository;
import com.osdb.ippay.hpp.alternatepay.transaction.dto.HapiTransactionDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.HppTokenDto;

import com.osdb.ippay.secondary.alternatepay.transaction.controller.facade.HppTokenFacade;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.mapper.HppTokenMapper;

import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.stereotype.Component;

import static lombok.AccessLevel.PRIVATE;

@Component
@RequiredArgsConstructor
@FieldDefaults(makeFinal = true, level = PRIVATE)
public class HppTokenFacadeImpl implements HppTokenFacade {
   
    HppTokenMapper hppTokenMapper;
    HppTokenRepository hppTokenRepository;

	@Override
	public HppTokenDto create(HapiTransactionDto hapiTransactionResultDto, PaymentResultDto paymentResultDto) {

		HppToken obj = hppTokenMapper.toEntity(hapiTransactionResultDto, paymentResultDto);
		HppToken objGlobal = hppTokenRepository.save(obj);
				
        return hppTokenMapper.toDto(objGlobal);	
	}
}
