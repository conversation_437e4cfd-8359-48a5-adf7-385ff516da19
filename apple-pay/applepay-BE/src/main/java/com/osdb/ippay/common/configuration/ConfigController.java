package com.osdb.ippay.common.configuration;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/api/v1")
@Slf4j
public class ConfigController
{
	/*
  @Autowired
  Environment env;

  @GetMapping("/version")
  public String getAppVersion()
  {
    try
    {
      return env.getProperty("app.version");
    }
    catch (Exception e)
    {
      
    }

    return null;
  }*/
}
