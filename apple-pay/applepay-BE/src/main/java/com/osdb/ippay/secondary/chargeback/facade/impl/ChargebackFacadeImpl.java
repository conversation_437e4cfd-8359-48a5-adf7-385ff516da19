package com.osdb.ippay.secondary.chargeback.facade.impl;

import com.osdb.ippay.common.domain.FileDomain;
import com.osdb.ippay.common.security.service.AuthenticationService;
import com.osdb.ippay.primary.user.repository.entity.User;
import com.osdb.ippay.secondary.chargeback.facade.ChargebackFacade;
import com.osdb.ippay.secondary.chargeback.facade.dto.ChargebackDto;
import com.osdb.ippay.secondary.chargeback.facade.mapper.ChargebackMapper;
import com.osdb.ippay.secondary.chargeback.repository.entity.Chargeback;
import com.osdb.ippay.secondary.chargeback.service.ChargebackFilter;
import com.osdb.ippay.secondary.chargeback.service.ChargebackService;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static lombok.AccessLevel.PRIVATE;

@Component
@RequiredArgsConstructor
@FieldDefaults(makeFinal = true, level = PRIVATE)
public class ChargebackFacadeImpl implements ChargebackFacade {

    // services
    ChargebackService chargebackService;
    AuthenticationService authenticationService;

    // mappers
    ChargebackMapper chargebackMapper;

    @Override
    public Page<ChargebackDto> find(String email, ChargebackFilter filter, Pageable pageable) {
        User authUser = authenticationService.getCurrentUser(email);

        if(authUser.isNotAdmin()) {
            List<String> merchantIds = authenticationService.getMerchantIds(authUser);
            filter.setMerchantIds(merchantIds);
        }

        Page<Chargeback> chargebacks = chargebackService.find(authUser, filter, pageable);
        return chargebackMapper.toDto(chargebacks);
    }

    @Override
    public List<String> findDates(String email, String merchantId) {
        User authUser = authenticationService.getCurrentUser(email);

        return hasAccess(authUser, merchantId) ?
                chargebackService.findDates(merchantId) :
                Collections.emptyList();
    }

    @Override
    public FileDomain export(String email, ChargebackFilter filter) throws IOException {
        String[] HEADERS = {
                "Merchant Name",
                "Merchant ID",
                "Card",
                "Case Number",
                "Credit Card",
                "Amount",
                "Reason",
                "ResolutionTo",
                "Debit/Credit",
                "Type",
                "OriginalRef#",
                "Initial",
                "Resolved"
        };

        final CSVFormat format = CSVFormat.Builder.create()
                .setHeader(HEADERS)
                .setIgnoreEmptyLines(true)
                .build();

        User authUser = authenticationService.getCurrentUser(email);

        if(authUser.isNotAdmin()) {
            List<String> merchantIds = authenticationService.getCCMerchantIds(authUser);
            filter.setMerchantIds(merchantIds);
        }

        List<Chargeback> chargebacks = chargebackService.find(authUser, filter);

        try (
                ByteArrayOutputStream out = new ByteArrayOutputStream();
                CSVPrinter csvPrinter = new CSVPrinter(new PrintWriter(out), format)
        ) {

            for (Chargeback chargeback : chargebacks) {
                List<String> data = Arrays.asList(
                        chargeback.getMerchantName(),
                        chargeback.getMerchantId(),
                        chargeback.getCardType(),
                        chargeback.getCaseNumber(),
                        chargeback.getCreditCard(),
                        chargeback.getAmount(),
                        chargeback.getReason(),
                        chargeback.getResolutionTo(),
                        chargeback.getDebitCredit(),
                        chargeback.getType(),
                        chargeback.getOriginRef(),
                        chargeback.getDateTransaction().toString(),
                        chargeback.getDateResolved().toString()
                );

                csvPrinter.printRecord(data);
            }

            csvPrinter.flush();

            return FileDomain.builder()
                    .file(new ByteArrayResource(new ByteArrayInputStream(out.toByteArray()).readAllBytes()))
                    .fileName(
                            String.format(
                                    "exported_chargebacks%s_%s_%s.csv",
                                    StringUtils.isNotBlank(filter.getMerchantId()) ? "_" + filter.getMerchantId() : "",
                                    filter.getFromDate(),
                                    filter.getToDate()
                            )
                    )
                    .build();
        }
    }

    private boolean hasAccess(User authUser, String merchantId) {
        if(authUser.isNotAdmin()) {
            List<String> merchantIds = authenticationService.getMerchantIds(authUser);

            return merchantIds.contains(merchantId);
        }

        return true;
    }
}
