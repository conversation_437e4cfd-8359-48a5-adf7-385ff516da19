package com.osdb.ippay.secondary.alternatepay.transaction.controller;

import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionReceiptDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionReceiptResultDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.facade.TransactionReceiptFacade;

import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import static lombok.AccessLevel.PRIVATE;

@Tag(name = "Alternatepay/Transaction")
@RestController
@RequestMapping(value = "/api/v2/payment/alternatepay")
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class TransactionReceiptController {

	TransactionReceiptFacade transactionReceiptFacade; 

   /* @GetMapping("/transaction/receipt")
    public ResponseEntity<TransactionReceiptResultDto> CreateTransactionReceipt(TransactionReceiptDto transactionReceiptDto) {
    	TransactionReceiptResultDto response = transactionReceiptFacade.create(transactionReceiptDto);
        return ResponseEntity.ok(response);
    }
*/
  

}
