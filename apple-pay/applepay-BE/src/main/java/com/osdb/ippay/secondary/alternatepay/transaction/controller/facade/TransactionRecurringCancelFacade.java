package com.osdb.ippay.secondary.alternatepay.transaction.controller.facade;

import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.SubscriptionCancelDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionGetResultDto;



public interface TransactionRecurringCancelFacade {

	TransactionGetResultDto cancelSubscription(SubscriptionCancelDto subscriptionCancelDto, String subscriptionId, String branchApiKey, String merchantApiKey);
}
