package com.osdb.ippay.secondary.partner.repository;

import com.osdb.ippay.secondary.partner.repository.entity.Partner;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

public interface PartnerRepository extends
        JpaRepository<Partner, Long>,
        JpaSpecificationExecutor<Partner> {

    List<Partner> findAllByIdIn(List<Long> partnerIds);

}
