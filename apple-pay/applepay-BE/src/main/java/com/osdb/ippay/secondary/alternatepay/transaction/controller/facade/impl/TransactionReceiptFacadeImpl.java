package com.osdb.ippay.secondary.alternatepay.transaction.controller.facade.impl;

import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionGetDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionGetResultDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionInitDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionReceiptDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionReceiptResultDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TrasactionInitResultDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.facade.*;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.mapper.TransactionGetControllerMapper;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.mapper.TransactionInitControllerMapper;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.mapper.TransactionReceiptControllerMapper;

import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.Base64;
import java.util.Objects;

import static lombok.AccessLevel.PRIVATE;

@Component
@RequiredArgsConstructor
@FieldDefaults(makeFinal = true, level = PRIVATE)
public class TransactionReceiptFacadeImpl implements TransactionReceiptFacade {

    public static final String INVALID_JOB_STATE = "Job can not be deleted, job state not 'Queued'";

    TransactionReceiptControllerMapper mapper;

    @NonFinal
    @Value("${transaction-fetch.url.get}")
    String transactionUrlGet;


    @NonFinal
    @Value("${transaction-fetch.url.post}")
    String transactionUrlPost;

    @NonFinal
    boolean blnIsDecoded = true;
    
	@Override
	public TransactionReceiptResultDto create(TransactionReceiptDto transactionReceiptInDto) {
		RestTemplate restTemplate = new RestTemplate();

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        TransactionReceiptDto trnReceiptDto = mapper.toDto(transactionReceiptInDto);
        HttpEntity<TransactionReceiptDto> httpEntity = new HttpEntity<>(trnReceiptDto, headers);

        if(!blnIsDecoded)
        {
        	byte[] decodedBytes = Base64.getDecoder().decode(transactionUrlGet);
            transactionUrlGet = new String(decodedBytes);
        }
        
        
        TransactionReceiptResultDto trnReceiptResultDto = restTemplate.exchange(
        		transactionUrlGet,
                HttpMethod.GET,
                httpEntity,
                new ParameterizedTypeReference<TransactionReceiptResultDto>() {
                },
                ""
        ).getBody();

        return Objects.nonNull(trnReceiptResultDto) ? mapper.toOutDto(trnReceiptResultDto) : null;
	}
}
