package com.osdb.ippay.sqlserveripcore.ipwhitelist.facade;

import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import com.osdb.ippay.sqlserver.reports.facade.dto.TotalACHGtwDataDto;
import com.osdb.ippay.sqlserver.reports.service.filter.TotalACHGtwDataFilter;
import com.osdb.ippay.sqlserveripcore.ipwhitelist.facade.dto.TerminalIpsBlockingDto;
import com.osdb.ippay.sqlserveripcore.ipwhitelist.facade.dto.TerminalIpsGlobalDto;
import com.osdb.ippay.sqlserveripcore.ipwhitelist.repository.entity.ActiveStatusType;
import com.osdb.ippay.sqlserveripcore.ipwhitelist.repository.entity.TerminalIpsBlocking;
import com.osdb.ippay.sqlserveripcore.ipwhitelist.repository.entity.TerminalIpsGlobal;
import com.osdb.ippay.sqlserveripcore.ipwhitelist.service.filter.TerminalIpsBlockingFilter;
import com.osdb.ippay.sqlserveripcore.ipwhitelist.service.filter.TerminalIpsGlobalFilter;

public interface TerminalIpsGlobalFacade {

   // ThresholdSettingDto find(AlertType alertType);

	TerminalIpsGlobalDto update(TerminalIpsGlobalDto terminalIpsGlobalDto);
	TerminalIpsBlockingDto create(TerminalIpsBlockingDto terminalIpsBlockingDto);
	Page<TerminalIpsGlobal> findData(TerminalIpsGlobalFilter filter, Pageable pageable);
	Page<TerminalIpsBlocking> findTerminalData(TerminalIpsBlockingFilter filter, Pageable pageable);
	void delete(Long id);
	void batchDelete(List<Long> idList);
}
