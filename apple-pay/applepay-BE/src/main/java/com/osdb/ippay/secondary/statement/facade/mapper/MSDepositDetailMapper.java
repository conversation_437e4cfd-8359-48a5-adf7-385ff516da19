package com.osdb.ippay.secondary.statement.facade.mapper;

import com.osdb.ippay.secondary.statement.facade.dto.MSDepositDetailDto;
import com.osdb.ippay.secondary.statement.repository.entity.MSDepositDetail;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class MSDepositDetailMapper {

    public List<MSDepositDetailDto> toDto(List<MSDepositDetail> msDepositDetails) {
        return msDepositDetails.stream()
                .map(this::toDto)
                .collect(Collectors.toList());
    }

    public MSDepositDetailDto toDto(MSDepositDetail msDepositDetail) {
        return MSDepositDetailDto.builder()
                .id(msDepositDetail.getId())
                .depositDate(msDepositDetail.getDepositDate())
                .numOfTrans(msDepositDetail.getNumOfTrans())
                .batchAmount(msDepositDetail.getBatchAmount())
                .ocBatch(msDepositDetail.getOcBatch())
                .adjust(msDepositDetail.getAdjust())
                .chargebacks(msDepositDetail.getChargebacks())
                .feePaid(msDepositDetail.getFeePaid())
                .netDeposit(msDepositDetail.getNetDeposit())
                .build();
    }
}
