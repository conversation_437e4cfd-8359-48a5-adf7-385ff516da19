package com.osdb.ippay.secondary.transaction.service.impl;

import com.ippay.global.clients.ipp.IppayClient;
import com.ippay.global.clients.ipp.IppayHttpClientConfiguration;
import com.ippay.global.clients.ipp.IppayRequest;
import com.ippay.global.clients.ipp.IppayResponse;
import com.osdb.ippay.common.domain.FileDomain;
import com.osdb.ippay.common.exception.business.NotFoundException;
import com.osdb.ippay.common.util.number.DoubleUtil;
import com.osdb.ippay.common.util.string.StringUtil;
import com.osdb.ippay.primary.user.repository.entity.User;
import com.osdb.ippay.secondary.transaction.facade.dto.CCTransactionDto;
import com.osdb.ippay.secondary.transaction.repository.CCTransactionRepository;
import com.osdb.ippay.secondary.transaction.repository.entity.CCSettlement;
import com.osdb.ippay.secondary.transaction.repository.entity.CCTransaction;
import com.osdb.ippay.secondary.transaction.service.CCTransactionService;
import com.osdb.ippay.secondary.transaction.service.domain.RefundTransactionRequest;
import com.osdb.ippay.secondary.transaction.service.domain.VoidTransactionRequest;
import com.osdb.ippay.secondary.transaction.service.filter.AuthDateFilter;
import com.osdb.ippay.secondary.transaction.service.filter.SettlementFilter;
import com.osdb.ippay.secondary.transaction.service.filter.TransactionFilter;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.Predicate;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.PrintWriter;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

import static com.osdb.ippay.common.exception.ErrorMessage.CC_TRANSACTION_NOT_FOUND;
import static com.osdb.ippay.common.exception.ErrorMessage.TRANSACTION_ID_NOT_FOUND;
import static com.osdb.ippay.secondary.transaction.service.filter.TransactionType.SETTLEMENT;
import static com.osdb.ippay.secondary.transaction.service.filter.TransactionType.TRANSACTION;
import static java.lang.Boolean.FALSE;
import static java.lang.Boolean.TRUE;
import static lombok.AccessLevel.PRIVATE;
import static org.apache.commons.collections.CollectionUtils.isEmpty;
import static org.apache.commons.collections.CollectionUtils.isNotEmpty;
import static org.apache.commons.lang3.StringUtils.EMPTY;

@Service
@RequiredArgsConstructor
@FieldDefaults(makeFinal = true, level = PRIVATE)
public class CCTransactionServiceImpl implements CCTransactionService {

    CCTransactionRepository transactionRepository;

    @NonFinal
    @Value(value = "${ippay.trans.service.url}")
    String transURL;

    @NonFinal
    @Value(value = "${ippay.trans.service.connection.timeout}")
    Integer transConnTimeout;

    @NonFinal
    @Value(value = "${ippay.trans.service.response.timeout}")
    Integer transRespTimeout;

    static final String DATE_PATTERN = "MM/dd/yyyy HH:mm:ss";

    @Override
    public List<CCSettlement> findSettlements(SettlementFilter filter) {
        return transactionRepository.getSettlements(
                filter.getMerchantId(),
                filter.getFromDateStr(),
                filter.getToDateStr()
        );
    }

    @Async
    @Override
    public Future<List<CCTransaction>> find(User authUser, TransactionFilter filter) {
        List<CCTransaction> transactions = transactionRepository.findAll(filterBy(authUser, filter));
        return new AsyncResult<>(aFilterBy(transactions, filter));
    }

    public CCTransaction find(Long id) {
        return transactionRepository
                .findById(id)
                .orElseThrow(() -> new NotFoundException(CC_TRANSACTION_NOT_FOUND));
    }

    @Override
    public FileDomain transactionsExport(User user, List<CCTransactionDto> transactions, Double totalAmount) throws IOException {
        ByteArrayInputStream byteArrayInputStream = user.isAdmin() ?
                adminExport(transactions, totalAmount) :
                export(transactions, totalAmount);

        return FileDomain.builder()
                .file(new ByteArrayResource(byteArrayInputStream.readAllBytes()))
                .fileName(String.format("exported_cc_transactions_%s.csv", LocalDate.now()))
                .build();
    }

    private List<CCTransaction> aFilterBy(List<CCTransaction> transactions, TransactionFilter filter) {

        if(StringUtils.isNotBlank(filter.getTransactionId())) {
            transactions = transactions.stream()
                    .filter(t -> Objects.nonNull(t.getTransactionId()) && t.getTransactionId().contains(filter.getTransactionId()))
                    .collect(Collectors.toList());
        }

        if(Objects.nonNull(filter.getShowOnlyApproved()) && TRUE.equals(filter.getShowOnlyApproved())) {
            transactions = transactions.stream()
                    .filter(t ->
                            "RETURN ACCEPTED".equals(t.getApprovalStatus()) ||
                                    "APPROVED".equals(t.getApprovalStatus()) ||
                                    "VOID PROCESSED".equals(t.getApprovalStatus())
                    ).collect(Collectors.toList());
        }

        if(StringUtils.isNotBlank(filter.getTerminalId())) {
            transactions = transactions.stream()
                    .filter(t -> Objects.nonNull(t.getTerminalId()) && t.getTerminalId().contains(filter.getTerminalId()))
                    .collect(Collectors.toList());
        }


        if(StringUtils.isNotBlank(filter.getCardNumber())) {
            transactions = transactions.stream()
                    .filter(t -> Objects.nonNull(t.getToken()) && t.getToken().contains(filter.getCardNumber()))
                    .collect(Collectors.toList());
        }

        if(StringUtils.isNotBlank(filter.getCardHolder())) {
            transactions = transactions.stream()
                    .filter(t -> Objects.nonNull(t.getCardHolder()) && t.getCardHolder().contains(filter.getCardHolder()))
                    .collect(Collectors.toList());
        }


        if(StringUtils.isNotBlank(filter.getUd1())) {
            transactions = transactions.stream()
                    .filter(t -> Objects.nonNull(t.getUd1()) && t.getUd1().contains(filter.getUd1()))
                    .collect(Collectors.toList());
        }

        if(StringUtils.isNotBlank(filter.getUd2())) {
            transactions = transactions.stream()
                    .filter(t -> Objects.nonNull(t.getUd2()) && t.getUd2().contains(filter.getUd2()))
                    .collect(Collectors.toList());
        }

        if(StringUtils.isNotBlank(filter.getUd3())) {
            transactions = transactions.stream()
                    .filter(t -> Objects.nonNull(t.getUd3()) && t.getUd3().contains(filter.getUd3()))
                    .collect(Collectors.toList());
        }

        if(StringUtils.isNotBlank(filter.getOrderNumber())) {
            transactions = transactions.stream()
                    .filter(t -> Objects.nonNull(t.getOrderNumber()) && t.getOrderNumber().contains(filter.getOrderNumber()))
                    .collect(Collectors.toList());
        }

        if(StringUtils.isNotBlank(filter.getApprovalStatus())) {
            if ("APPROVED".equals(filter.getApprovalStatus())) {
                transactions = transactions.stream()
                        .filter(t ->
                                "RETURN ACCEPTED".equals(t.getApprovalStatus()) ||
                                        "APPROVED".equals(t.getApprovalStatus()) ||
                                        "VOID PROCESSED".equals(t.getApprovalStatus())
                        ).collect(Collectors.toList());
            } else {
                transactions = transactions.stream()
                        .filter(t ->
                                !"RETURN ACCEPTED".equals(t.getApprovalStatus()) ||
                                        !"APPROVED".equals(t.getApprovalStatus()) ||
                                        !"VOID PROCESSED".equals(t.getApprovalStatus())
                        ).collect(Collectors.toList());
            }
        }

        if(StringUtils.isNotBlank(filter.getCardType())) {
            transactions = transactions.stream()
                    .filter(t -> Objects.nonNull(t.getCardType()) && t.getCardType().equalsIgnoreCase(filter.getCardType()))
                    .collect(Collectors.toList());
        }

        if(StringUtils.isNotBlank(filter.getTransactionType())) {
            transactions = transactions.stream()
                    .filter(t -> Objects.nonNull(t.getRequestType()) && t.getRequestType().equalsIgnoreCase(filter.getTransactionType()))
                    .collect(Collectors.toList());
        }

        if(filter.getAmount() != null) {
            transactions = transactions.stream()
                    .filter(t -> Objects.nonNull(t.getAuthAmount()) && t.getAuthAmount().equals(filter.getAmountInUSD()))
                    .collect(Collectors.toList());
        }

        if(SETTLEMENT.equals(filter.getType())) {
            transactions = transactions.stream()
                    .filter(t -> Objects.nonNull(t.getSettlementDate()))
                    .collect(Collectors.toList());
        }

        if(FALSE.equals(filter.getIncludeTokenize())) {
            transactions = transactions.stream()
                    .filter(t ->
                            ObjectUtils.notEqual("TOKENIZE", t.getRequestType())
                    ).collect(Collectors.toList());
        }

        if(Objects.nonNull(filter.getStType())) {
            transactions = switch (filter.getStType()) {
                case SALE ->
                        transactions.stream()
                                .filter(t -> "SALE".equalsIgnoreCase(t.getRequestType()) || "CAPT".equalsIgnoreCase(t.getRequestType()))
                                .collect(Collectors.toList());

                case REFUND ->
                        transactions.stream()
                                .filter(t -> "CREDIT".equalsIgnoreCase(t.getRequestType()))
                                .collect(Collectors.toList());

                case SALE_REFUND ->
                        transactions.stream()
                                .filter(t ->
                                        "SALE".equalsIgnoreCase(t.getRequestType()) ||
                                                "CAPT".equalsIgnoreCase(t.getRequestType()) ||
                                                "CREDIT".equalsIgnoreCase(t.getRequestType())
                                ).collect(Collectors.toList());

                case AX_AMOUNT ->
                        transactions.stream()
                                .filter(t ->
                                        "SALE".equalsIgnoreCase(t.getRequestType()) ||
                                                "CAPT".equalsIgnoreCase(t.getRequestType()) ||
                                                "A".equalsIgnoreCase(t.getCardType())
                                ).collect(Collectors.toList());

                case AX_REFUND ->
                        transactions.stream()
                                .filter(t ->
                                        "CREDIT".equalsIgnoreCase(t.getRequestType()) ||
                                                "CAPT".equalsIgnoreCase(t.getRequestType()) ||
                                                "A".equalsIgnoreCase(t.getCardType())
                                ).collect(Collectors.toList());

                case BANK_CARDS ->
                        transactions.stream()
                                .filter(t ->
                                        "SALE".equalsIgnoreCase(t.getRequestType()) ||
                                                "CREDIT".equalsIgnoreCase(t.getRequestType()) ||
                                                "CAPT".equalsIgnoreCase(t.getRequestType()) ||
                                                "A".equalsIgnoreCase(t.getCardType())
                                ).collect(Collectors.toList());
            };
        }

        return transactions;
    }

    private Specification<CCTransaction> filterBy(User authUser, TransactionFilter filter) {
        return (r, rq, cb) -> {
            Predicate fromDatePredicate = filter.getFromDate() != null ?
                    cb.greaterThanOrEqualTo(
                            SETTLEMENT.equals(filter.getType()) ?
                                    r.get("settlementDate") :
                                    r.get("authDate"),
                            filter.getFromDateStr()
                    ) : cb.conjunction();

            Predicate toDatePredicate = filter.getToDate() != null ?
                    cb.lessThanOrEqualTo(
                            SETTLEMENT.equals(filter.getType()) ?
                                    r.get("settlementDate") :
                                    r.get("authDate"),
                            filter.getToDateStr()
                    ) : cb.conjunction();

            Predicate merchantIdPredication = StringUtils.isNotBlank(filter.getMerchantId()) ?
                    cb.equal(
                            r.get("merchantId"),
                            filter.getMerchantId()
                    ) : cb.conjunction();

            Predicate merchantIdsPredicate = isNotEmpty(filter.getMerchantIds()) && authUser.isNotAdmin() ?
                    cb.in(r.get("merchantId")).value(filter.getMerchantIds()) :
                    cb.conjunction();

            if(isEmpty(filter.getMerchantIds()) && authUser.isNotAdmin()) {
                merchantIdsPredicate = cb.disjunction();
            }

            return cb.and(
                    fromDatePredicate,
                    toDatePredicate,
                    merchantIdPredication,
                    merchantIdsPredicate
            );
        };
    }

    private ByteArrayInputStream adminExport(List<CCTransactionDto> transactions, Double totalAmount) throws IOException {
        String[] HEADERS = {
                "IP Trans ID",
                "Merchant Name",
                "Merchant ID",
                "Terminal ID",
                "Transaction ID",
                "Request Type",
                "Origin",
                "Amount",
                "Auth Code",
                "Response Code",
                "Auth Date",
                "Token",
                "Safe Card Number",
                "Expiry",
                "AVS",
                "CVV",
                "Cardholder",
                "Address",
                "City",
                "State",
                "Zip Code",
                "Country",
                "Phone",
                "Email",
                "UD1",
                "UD2",
                "UD3",
                "Order Number",
                "IP Address",
                "CC Processor",
                "Currency",
                "Agent",
                "Chain",
                "Store",
                "Terminal",
                "V Number",
                "Ret Ref Number",
                "Bank Trans ID",
                "External Trans ID",
                "POS Additional Data",
                "MessageType",
                "POSEntryMode",
                "POSConditionCode",
                "SystemTraceNumber",
                "TerminalType",
                "TerminalEntryCapability",
                "ChipConditionCode",
                "SpecialConditionCode",
                "CardDataInputCapability",
                "CardholderAuthentication Capability",
                "CardCaptureCapability",
                "TerminalOperationEnvironment",
                "CardholderPresentData",
                "CardPresentData",
                "CardData Input Mode",
                "CardholderAuthenticationMethod",
                "CardholderAuthenticationEntity",
                "CardDataOutputCapability",
                "PINCaptureCapability"
        };

        final CSVFormat format = CSVFormat.Builder.create()
                .setHeader(HEADERS)
                .setIgnoreEmptyLines(true)
                .build();

        try (
                ByteArrayOutputStream out = new ByteArrayOutputStream();
                CSVPrinter csvPrinter = new CSVPrinter(new PrintWriter(out), format)
        ) {

            DateTimeFormatter dateFormat = DateTimeFormatter.ofPattern(DATE_PATTERN);

            for (CCTransactionDto ccTransaction : transactions) {
                String authDate = ccTransaction.getAuthDate() != null ?
                        ccTransaction.getAuthDate().atZone(ZoneId.of("US/Central")).format(dateFormat) : "";

                List<String> data = Arrays.asList(
                        ccTransaction.getIpTransId() != null ? String.valueOf(ccTransaction.getIpTransId()) : "",
                        ccTransaction.getMerchantName(),
                        ccTransaction.getMerchantId(),
                        ccTransaction.getTerminalId(),
                        ccTransaction.getTransactionId(),
                        ccTransaction.getRequestType(),
                        ccTransaction.getTransactionType(),
                        ccTransaction.getAuthAmount(),
                        ccTransaction.getAuthCode(),
                        ccTransaction.getResponseCode(),
                        authDate,
                        ccTransaction.getToken(),
                        ccTransaction.getSafeCardNumber(),
                        ccTransaction.getExpiry() != null ? String.valueOf(ccTransaction.getExpiry()) : "",
                        ccTransaction.getAvs(),
                        ccTransaction.getCvv(),
                        ccTransaction.getCardHolder(),
                        ccTransaction.getAddress(),
                        ccTransaction.getCity(),
                        ccTransaction.getState(),
                        ccTransaction.getZipCode(),
                        ccTransaction.getCountry(),
                        ccTransaction.getPhone(),
                        ccTransaction.getEmail(),
                        ccTransaction.getUd1(),
                        ccTransaction.getUd2(),
                        ccTransaction.getUd3(),
                        ccTransaction.getOrderNumber(),
                        "",
                        ccTransaction.getCcProcessor(),
                        ccTransaction.getCurrencyCode(),
                        "",
                        "",
                        "",
                        ccTransaction.getPosTerminalId(),
                        ccTransaction.getVNumber(),
                        ccTransaction.getRetRefNumber() != null ? String.valueOf(ccTransaction.getRetRefNumber()) : "",
                        ccTransaction.getBankTransID(),
                        ccTransaction.getExternalTransID(),
                        ccTransaction.getPosAdditionalData()
                );

                csvPrinter.printRecord(data);
            }

            csvPrinter.printRecord(
                    EMPTY, EMPTY, EMPTY, EMPTY, EMPTY, EMPTY, EMPTY,
                    DoubleUtil.toPriceFormat(totalAmount, 2, 1)
            );

            csvPrinter.flush();
            return new ByteArrayInputStream(out.toByteArray());
        }
    }

    private ByteArrayInputStream export(List<CCTransactionDto> transactions, Double totalAmount) throws IOException {
        String[] HEADERS = {
                "Merchant Name",
                "Merchant ID",
                "Terminal ID",
                "Transaction ID",
                "Request Type",
                "Origin",
                "Amount",
                "Auth Code",
                "Response Code",
                "Auth Date",
                "Token",
                "Safe Card Number",
                "Expiry",
                "AVS",
                "CVV",
                "Cardholder",
                "Address",
                "City",
                "State",
                "Zip Code",
                "Country",
                "Phone",
                "Email",
                "UD1",
                "UD2",
                "UD3",
                "Order Number",
                "IP Address",
                "Currency"
        };

        final CSVFormat format = CSVFormat.Builder.create()
                .setHeader(HEADERS)
                .setIgnoreEmptyLines(true)
                .build();

        try (
                ByteArrayOutputStream out = new ByteArrayOutputStream();
                CSVPrinter csvPrinter = new CSVPrinter(new PrintWriter(out), format)
        ) {

            DateTimeFormatter dateFormat = DateTimeFormatter.ofPattern(DATE_PATTERN);

            for (CCTransactionDto ccTransaction : transactions) {
                String authDate = ccTransaction.getAuthDate() != null ?
                    ccTransaction.getAuthDate().atZone(ZoneId.of("US/Central")).format(dateFormat) : "";

                List<String> data = Arrays.asList(
                        ccTransaction.getMerchantName(),
                        ccTransaction.getMerchantId(),
                        StringUtil.hashString(ccTransaction.getTerminalId()),
                        ccTransaction.getTransactionId(),
                        ccTransaction.getRequestType(),
                        ccTransaction.getTransactionType(),
                        ccTransaction.getAuthAmount(),
                        ccTransaction.getAuthCode(),
                        ccTransaction.getResponseCode(),
                        authDate,
                        ccTransaction.getToken(),
                        ccTransaction.getSafeCardNumber(),
                        ccTransaction.getExpiry() != null ? String.valueOf(ccTransaction.getExpiry()) : "",
                        ccTransaction.getAvs(),
                        ccTransaction.getCvv(),
                        ccTransaction.getCardHolder(),
                        ccTransaction.getAddress(),
                        ccTransaction.getCity(),
                        ccTransaction.getState(),
                        ccTransaction.getZipCode(),
                        ccTransaction.getCountry(),
                        ccTransaction.getPhone(),
                        ccTransaction.getEmail(),
                        ccTransaction.getUd1(),
                        ccTransaction.getUd2(),
                        ccTransaction.getUd3(),
                        ccTransaction.getOrderNumber(),
                        "",
                        ccTransaction.getCurrencyCode()
                );

                csvPrinter.printRecord(data);
            }

            csvPrinter.printRecord(
                    EMPTY, EMPTY, EMPTY, EMPTY, EMPTY, EMPTY,
                    DoubleUtil.toPriceFormat(totalAmount, 2, 1)
            );

            csvPrinter.flush();
            return new ByteArrayInputStream(out.toByteArray());
        }
    }

    @Override
    public FileDomain settlementsExport(User user, TransactionFilter filter) throws Exception {
        List<CCTransaction> transactions = find(user, filter).get();

        ByteArrayInputStream byteArrayInputStream = user.isAdmin() ?
                adminSettlementsExport(transactions) :
                settlementsExport(transactions);

        return FileDomain.builder()
                .file(new ByteArrayResource(byteArrayInputStream.readAllBytes()))
                .fileName(String.format("exported_cc_settlements_%s.csv", LocalDate.now()))
                .build();
    }

    private ByteArrayInputStream adminSettlementsExport(List<CCTransaction> ccTransactions) throws IOException {
        String[] HEADERS = {
                "IP Trans ID",
                "Merchant Name",
                "Merchant ID",
                "Terminal ID",
                "Transaction ID",
                "Request Type",
                "Origin",
                "Amount",
                "Auth Code",
                "Response Code",
                "Auth Date",
                "Token",
                "Safe Card Number",
                "Expiry",
                "AVS",
                "CVV",
                "Cardholder",
                "Address",
                "City",
                "State",
                "Zip Code",
                "Country",
                "Phone",
                "Email",
                "UD1",
                "UD2",
                "UD3",
                "Order Number",
                "IP Address",
                "CC Processor",
                "Currency",
                "InternalTransactionReferenceNumber",
                "AuthorizationSourceCode",
                "CardholderIdentificationMethod",
                "CardholderActivatedTerminalCATIndicator",
                "ChipConditionCode",
                "MailTelephoneElectronicCommerceIndicator",
                "POSEntryMode",
                "VisaAuthorizationCharacteristicsIndicatorACIMasterCardACITSYSUseOnly",
                "Ps2000TransactionIdentifier",
                "BankNetAuthorizationReferenceNumber",
                "ValidationCode",
                "DebitNetworkIdentifier"
        };

        final CSVFormat format = CSVFormat.Builder.create()
                .setHeader(HEADERS)
                .setIgnoreEmptyLines(true)
                .build();

        try (
                ByteArrayOutputStream out = new ByteArrayOutputStream();
                CSVPrinter csvPrinter = new CSVPrinter(new PrintWriter(out), format)
        ) {

            DateTimeFormatter dateFormat = DateTimeFormatter.ofPattern(DATE_PATTERN);

            for (CCTransaction ccTransaction : ccTransactions) {
                String authDate = ccTransaction.getAuthDate() != null ?
                        ccTransaction.getAuthDateInstant().atZone(ZoneId.of("US/Central")).format(dateFormat) : "";

                List<String> data = Arrays.asList(
                        ccTransaction.getIpTransId() != null ? String.valueOf(ccTransaction.getIpTransId()) : "",
                        ccTransaction.getMerchantName(),
                        ccTransaction.getMerchantId(),
                        ccTransaction.getTerminalId(),
                        ccTransaction.getTransactionId(),
                        ccTransaction.getRequestType(),
                        ccTransaction.getTransactionType(),
                        toPriceFormat(ccTransaction.getAuthAmount()),
                        ccTransaction.getAuthCode(),
                        ccTransaction.getResponseCode(),
                        authDate,
                        ccTransaction.getToken(),
                        ccTransaction.getSafeCardNumber(),
                        ccTransaction.getExpiry() != null ? String.valueOf(ccTransaction.getExpiry()) : "",
                        ccTransaction.getAvs(),
                        ccTransaction.getCvv(),
                        ccTransaction.getCardHolder(),
                        ccTransaction.getAddress(),
                        ccTransaction.getCity(),
                        ccTransaction.getState(),
                        ccTransaction.getZipCode(),
                        ccTransaction.getCountry(),
                        ccTransaction.getPhone(),
                        ccTransaction.getEmail(),
                        ccTransaction.getUd1(),
                        ccTransaction.getUd2(),
                        ccTransaction.getUd3(),
                        ccTransaction.getOrderNumber(),
                        "",
                        ccTransaction.getCcProcessor(),
                        ccTransaction.getCurrencyCode()
                );

                csvPrinter.printRecord(data);
            }

            csvPrinter.flush();
            return new ByteArrayInputStream(out.toByteArray());
        }
    }

    private ByteArrayInputStream settlementsExport(List<CCTransaction> ccTransactions) throws IOException {
        String[] HEADERS = {
                "Merchant Name",
                "Merchant ID",
                "Terminal ID",
                "Transaction ID",
                "Request Type",
                "Origin",
                "Amount",
                "Auth Code",
                "Response Code",
                "Auth Date",
                "Token",
                "Safe Card Number",
                "Expiry",
                "AVS",
                "CVV",
                "Cardholder",
                "Address",
                "City",
                "State",
                "Zip Code",
                "Country",
                "Phone",
                "Email",
                "UD1",
                "UD2",
                "UD3",
                "Order Number",
                "IP Address"
        };

        final CSVFormat format = CSVFormat.Builder.create()
                .setHeader(HEADERS)
                .setIgnoreEmptyLines(true)
                .build();

        try (
                ByteArrayOutputStream out = new ByteArrayOutputStream();
                CSVPrinter csvPrinter = new CSVPrinter(new PrintWriter(out), format)
        ) {

            DateTimeFormatter dateFormat = DateTimeFormatter.ofPattern(DATE_PATTERN);

            for (CCTransaction ccTransaction : ccTransactions) {
                String authDate = ccTransaction.getAuthDate() != null ?
                        ccTransaction.getAuthDateInstant().atZone(ZoneId.of("US/Central")).format(dateFormat) : "";

                List<String> data = Arrays.asList(
                        ccTransaction.getMerchantName(),
                        ccTransaction.getMerchantId(),
                        StringUtil.hashString(ccTransaction.getTerminalId()),
                        ccTransaction.getTransactionId(),
                        ccTransaction.getRequestType(),
                        ccTransaction.getTransactionType(),
                        toPriceFormat(ccTransaction.getAuthAmount()),
                        ccTransaction.getAuthCode(),
                        ccTransaction.getResponseCode(),
                        authDate,
                        ccTransaction.getToken(),
                        ccTransaction.getSafeCardNumber(),
                        ccTransaction.getExpiry() != null ? String.valueOf(ccTransaction.getExpiry()) : "",
                        ccTransaction.getAvs(),
                        ccTransaction.getCvv(),
                        ccTransaction.getCardHolder(),
                        ccTransaction.getAddress(),
                        ccTransaction.getCity(),
                        ccTransaction.getState(),
                        ccTransaction.getZipCode(),
                        ccTransaction.getCountry(),
                        ccTransaction.getPhone(),
                        ccTransaction.getEmail(),
                        ccTransaction.getUd1(),
                        ccTransaction.getUd2(),
                        ccTransaction.getUd3(),
                        ccTransaction.getOrderNumber()
                );

                csvPrinter.printRecord(data);
            }

            csvPrinter.flush();
            return new ByteArrayInputStream(out.toByteArray());
        }
    }

    private String toPriceFormat(Integer value) {
        return value != null ? DoubleUtil.toPriceFormat(value, 2, 100) : null;
    }

    @Override
    public List<String> findExistingCCAuthDates(AuthDateFilter filter) {
        List<String> transactions = TRANSACTION.equals(filter.getType()) ?
                transactionRepository.findAuthDatesForTransactions(filter.getMerchantId()) :
                transactionRepository.findAuthDatesForSettlements(filter.getMerchantId());

        return transactions.stream()
                .sorted(Comparator.reverseOrder())
                .map(authDate -> authDate != null ? authDate.split(" ")[0] : null)
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public IppayResponse voidTransaction(Long id, VoidTransactionRequest voidTransactionRequest) {
        final IppayHttpClientConfiguration configuration = new IppayHttpClientConfiguration
                .Builder(transURL, transConnTimeout, transRespTimeout)
                .build();

        IppayClient.init(configuration);

        IppayClient client  = IppayClient.getInstance();
        IppayRequest request = new IppayRequest();

        CCTransaction ccTransaction = transactionRepository
                .findById(id)
                .orElseThrow(() -> new NotFoundException(TRANSACTION_ID_NOT_FOUND));

        request.setTerminalID(ccTransaction.getTerminalId())
                .setTransactionType(IppayRequest.TransactionType.VOID)
                .setTransactionID(ccTransaction.getTransactionId())
                .setUdField1(voidTransactionRequest.getUd1())
                .setUdField2(voidTransactionRequest.getUd2())
                .setUdField3(voidTransactionRequest.getUd3());

        return client.query(request);
    }

    @Override
    public IppayResponse refundTransaction(Long id, RefundTransactionRequest refundTransactionRequest) {
        final IppayHttpClientConfiguration configuration = new IppayHttpClientConfiguration
                .Builder(transURL, transConnTimeout, transRespTimeout)
                .build();

        IppayClient.init(configuration);

        IppayClient client  = IppayClient.getInstance();
        IppayRequest request = new IppayRequest();

        CCTransaction ccTransaction = transactionRepository
                .findById(id)
                .orElseThrow(() -> new NotFoundException(TRANSACTION_ID_NOT_FOUND));

        request.setTerminalID(ccTransaction.getTerminalId())
                .setUdField1(refundTransactionRequest.getUd1())
                .setUdField2(refundTransactionRequest.getUd2())
                .setUdField3(refundTransactionRequest.getUd3())
                .setTransactionType(IppayRequest.TransactionType.CREDIT)
                .setTotalAmount(refundTransactionRequest.getAmount())
                .setTransactionID(ccTransaction.getTransactionId());

        return client.query(request);
    }
}
