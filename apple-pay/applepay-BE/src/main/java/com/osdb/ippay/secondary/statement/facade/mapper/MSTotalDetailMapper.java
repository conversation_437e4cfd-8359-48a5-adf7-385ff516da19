package com.osdb.ippay.secondary.statement.facade.mapper;

import com.osdb.ippay.secondary.statement.facade.dto.MSTotalDetailDto;
import com.osdb.ippay.secondary.statement.repository.entity.MSTotalDetail;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class MSTotalDetailMapper {

    public List<MSTotalDetailDto> toDto(List<MSTotalDetail> msTotalDetails) {
        return msTotalDetails.stream()
                .map(this::toDto)
                .collect(Collectors.toList());
    }

    public MSTotalDetailDto toDto(MSTotalDetail msTotalDetail) {
        return MSTotalDetailDto.builder()
                .id(msTotalDetail.getId())
                .processingDetailTotal(msTotalDetail.getProcessingDetailTotal())
                .authorizationDetailTotal(msTotalDetail.getAuthorizationDetailTotal())
                .otherDetailTotal(msTotalDetail.getOtherDetailTotal())
                .discountDueTotal(msTotalDetail.getDiscountDueTotal())
                .statementMonth(msTotalDetail.getStatementMonth())
                .build();
    }
}
