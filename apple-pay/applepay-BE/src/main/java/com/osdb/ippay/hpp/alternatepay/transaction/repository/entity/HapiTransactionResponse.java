package com.osdb.ippay.hpp.alternatepay.transaction.repository.entity;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import javax.persistence.*;
import static lombok.AccessLevel.PRIVATE;

@Entity
@Table(name = "HppTransactions")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
public class HapiTransactionResponse {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    Long Id;


    @Column(name = "CustomerID")
    String customerId;
    
    @Column(name = "MID")
    String mid;

    @Column(name = "TerminalID")
    String terminalId;

    @Column(name = "ReferenceId")
    String referenceId;

    @Column(name = "Token")
    String token;

    @Column(name = "Amount")
    String amount;

    @Column(name = "CallbackURL")
    int callbackUrl;

    @Column(name = "CallbackURLText")
    String callbackUrlText;

    @Column(name = "CardToken")
    String cardToken;

    @Column(name = "CustomerEmail")
    String customerEmail;
    
    @Column(name = "PaymentType")
    String paymentType;
    
    @Column(name = "IsRecurring")
    int isRecurring;
    
    @Column(name = "ENQReferenceId")
    String enqReferenceId;
    
    @Column(name = "TransactionType")
    String transactionType;
    
    @Column(name = "TransactionID")
    String transactionID;
    
    @Column(name = "Address")
    String address;
    
    @Column(name = "City")
    String city;
    
    @Column(name = "State")
    String state;
    
    @Column(name = "Zip")
    String zip;
    
    @Column(name = "Country")
    String country;
    
    @Column(name = "CardExpiryMonth")
    String cardExpiryMonth;
    
    @Column(name = "CardExpiryYear")
    String cardExpiryYear;
    
    @Column(name = "CardHolderName")
    String cardHolderName;
    
    @Column(name = "TokenizeCardSelected")
    String tokenizeCardSelected;
    
    @Column(name = "SaveForFuture")
    int saveForFuture;
    
    @Column(name = "Valid")
    String valid;
    
    @Column(name = "AuthCode")
    String authCode;
    
    @Column(name = "IppTransactionID")
    String ippTransactionID;
    
    @Column(name = "ReturnToken")
    String returnToken;
    
    @Column(name = "ResponseText")
    String responseText;
    
    @Column(name = "ResponseCode")
    String responseCode;
    
    @Column(name = "ProcessedAmount")
    int processedAmount;
    
    @Column(name = "SaveCardForFuture")
    String saveCardForFuture;
    
    @Column(name = "CardNumberMasked")
    String cardNumberMasked;
    
    @Column(name = "CardType")
    String cardType;
}
