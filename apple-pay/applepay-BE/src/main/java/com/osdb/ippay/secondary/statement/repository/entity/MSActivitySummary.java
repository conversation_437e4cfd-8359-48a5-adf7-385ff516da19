package com.osdb.ippay.secondary.statement.repository.entity;

import com.osdb.ippay.common.entity.BaseEntity;
import lombok.*;
import lombok.experimental.FieldDefaults;
import lombok.experimental.SuperBuilder;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import static lombok.AccessLevel.PRIVATE;

@Entity
@Table(name = "MERCHANT_STATEMENT_ACTIVITY_SUMMARY")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = PRIVATE)
@SuperBuilder(toBuilder = true)
public class MSActivitySummary extends BaseEntity {

    @Column(name = "MERCHANT_ID")
    String merchantId;

    @Column(name = "PLAN_TYPE")
    String planType;

    @Column(name = "SALES")
    String sales;

    @Column(name = "NUM_SALES")
    String numSales;

    @Column(name = "CREDITS")
    String credits;

    @Column(name = "NUM_CREDITS")
    String numCredits;

    @Column(name = "NET_SALES")
    String netSales;

    @Column(name = "PER_ITEM_FEE")
    String perItemFee;

    @Column(name = "RATE")
    String rate;

    @Column(name = "DISCOUNT_DUE")
    String discountDue;

    @Column(name = "NUM_TOTAL_TRANS")
    String numTotalTrans;

}
