package com.osdb.ippay.primary.user.service.filter;

import com.osdb.ippay.primary.user.repository.entity.UserRole;
import com.osdb.ippay.primary.user.repository.entity.UserStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

import static lombok.AccessLevel.PRIVATE;

@Data
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class UserFilter {

    String search;

    UserStatus status;

    UserRole role;

    Long partnerId;

    String merchantId;

}
