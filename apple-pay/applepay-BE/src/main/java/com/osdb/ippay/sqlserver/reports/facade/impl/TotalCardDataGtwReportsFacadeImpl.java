package com.osdb.ippay.sqlserver.reports.facade.impl;

import com.osdb.ippay.common.domain.FileDomain;
import com.osdb.ippay.sqlserver.reports.facade.TotalCardDataGtwReportsFacade;
import com.osdb.ippay.sqlserver.reports.facade.dto.TotalCardDataGtwReportsDto;
import com.osdb.ippay.sqlserver.reports.facade.dto.TotalCardGtwDataDto;
import com.osdb.ippay.sqlserver.reports.facade.mapper.TotalCardDataGtwReportsMapper;
import com.osdb.ippay.sqlserver.reports.facade.mapper.TotalCardGtwDataMapper;
import com.osdb.ippay.sqlserver.reports.repository.entity.TotalACHDataGtwReports;
import com.osdb.ippay.sqlserver.reports.repository.entity.TotalACHGtwData;
import com.osdb.ippay.sqlserver.reports.repository.entity.TotalCardDataGtwReports;
import com.osdb.ippay.sqlserver.reports.repository.entity.TotalCardGtwData;
import com.osdb.ippay.sqlserver.reports.service.TotalCardDataGtwReportsService;
import com.osdb.ippay.sqlserver.reports.service.filter.TotalACHGtwDataFilter;
import com.osdb.ippay.sqlserver.reports.service.filter.TotalCardDataGtwReportsFilter;
import com.osdb.ippay.sqlserver.reports.service.filter.TotalCardGtwDataFilter;

import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.Arrays;

import java.util.List;

import static lombok.AccessLevel.PRIVATE;

@Component
@RequiredArgsConstructor
@FieldDefaults(makeFinal = true, level = PRIVATE)
public class TotalCardDataGtwReportsFacadeImpl implements TotalCardDataGtwReportsFacade {

    // services
	TotalCardDataGtwReportsService totalCardDataGtwReportsService;

    // mappers
	TotalCardDataGtwReportsMapper totalCardDataGtwReportsMapper;
	TotalCardGtwDataMapper totalCardGtwDataMapper;

    // Method to export Total CC Data into CSV format
	@Override
	public FileDomain export(Long ipTransactionID, String authStartDateTime, String authEndDateTime,
			TotalCardDataGtwReportsFilter filter) throws IOException {

		 String[] HEADERS = {
	                "Merchant_ID",
	                "JPS MID",
	                "Transaction_ID",
	                "DBA Name",
	                "CCProcessor",
	                "Request_Type",
	                "ActionCode",
	                "TRANSACTION_COUNT",
	                "TOTAL_AMOUNT"
	        };

	        final CSVFormat format = CSVFormat.Builder.create()
	                .setHeader(HEADERS)
	                .setIgnoreEmptyLines(true)
	                .build();
	        
	        List<TotalCardDataGtwReports> totalCardDataGtwReports  = totalCardDataGtwReportsService.generateReport(ipTransactionID, authStartDateTime, authEndDateTime);

	        try (
	                ByteArrayOutputStream out = new ByteArrayOutputStream();
	                CSVPrinter csvPrinter = new CSVPrinter(new PrintWriter(out), format)
	        ) {

	            for (TotalCardDataGtwReports totalCardDataGtwReportsObj : totalCardDataGtwReports) {
	                List<String> data = Arrays.asList(
	                		totalCardDataGtwReportsObj.getMerchantID(),
	                		totalCardDataGtwReportsObj.getSpsMid(),
	                		totalCardDataGtwReportsObj.getTrnId(),
	                		totalCardDataGtwReportsObj.getOrgName(),
	                		totalCardDataGtwReportsObj.getCcProcessor(),
	                		totalCardDataGtwReportsObj.getRequestType(),
	                		totalCardDataGtwReportsObj.getActionCode(),
	                		totalCardDataGtwReportsObj.getTransactionCount(),
	                		totalCardDataGtwReportsObj.getTotalAmount()
	                );

	                csvPrinter.printRecord(data);
	            }

	            csvPrinter.flush();

	            return FileDomain.builder()
	                    .file(new ByteArrayResource(new ByteArrayInputStream(out.toByteArray()).readAllBytes()))
	                    .fileName(
	                            String.format(
	                                    "Totals-CardData-%s.csv",	             
	                                    filter.getAuthStartDateTime()
	                            )
	                    )
	                    .build();
	        }
	        
	}

	@Override
	public Page<TotalCardDataGtwReportsDto> find(Long ipTransactionID, String authStartDateTime,
			String authEndDateTime, TotalCardDataGtwReportsFilter filter, Pageable pageable) {
		 Page<TotalCardDataGtwReports> totalCardDataGtwReports = totalCardDataGtwReportsService.find(ipTransactionID, authStartDateTime, authEndDateTime, filter, pageable);
	        return totalCardDataGtwReportsMapper.toDto(totalCardDataGtwReports);
	}

	@Override
	public Page<TotalCardGtwDataDto> findData(Long ipTransactionID, String authStartDateTime, String authEndDateTime,
			TotalCardGtwDataFilter filter, Pageable pageable) {

		Page<TotalCardGtwData> totalCardGtwData = totalCardDataGtwReportsService.findData(ipTransactionID, authStartDateTime, authEndDateTime, filter, pageable);
	       return totalCardGtwDataMapper.toDto(totalCardGtwData);
	}

}
