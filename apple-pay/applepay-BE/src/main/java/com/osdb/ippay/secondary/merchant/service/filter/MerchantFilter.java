package com.osdb.ippay.secondary.merchant.service.filter;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static lombok.AccessLevel.PRIVATE;

@Data
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = PRIVATE)
public class MerchantFilter {

    String search;

    String extendedSearch;

    Boolean isActive;

    Long partnerId;

    Long bankId;

    @Parameter(hidden = true)
    @JsonIgnore
    List<String> merchantIds;

    public List<String> getMerchantIds() {
        return Optional
                .ofNullable(merchantIds)
                .orElse(Collections.emptyList());
    }
}
