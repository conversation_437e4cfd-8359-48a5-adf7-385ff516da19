package com.osdb.ippay.secondary.alternatepay.transaction.controller.dto;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;
import lombok.experimental.FieldDefaults;

import static lombok.AccessLevel.PRIVATE;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class TransactionGetDto {

    @JsonProperty(required = true)
    String apiKey;
}
