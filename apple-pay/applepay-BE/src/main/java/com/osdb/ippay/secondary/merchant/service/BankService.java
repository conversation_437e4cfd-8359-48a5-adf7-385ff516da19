package com.osdb.ippay.secondary.merchant.service;

import com.osdb.ippay.secondary.merchant.repository.entity.Bank;
import com.osdb.ippay.secondary.merchant.service.filter.BankFilter;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;

import java.util.List;

public interface BankService {

    Page<Bank> find(BankFilter filter, Pageable pageable);

    List<Bank> find(BankFilter filter, Sort sort);

}
