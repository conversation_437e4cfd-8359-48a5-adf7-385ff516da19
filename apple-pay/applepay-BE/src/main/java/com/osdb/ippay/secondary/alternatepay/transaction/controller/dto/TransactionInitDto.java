package com.osdb.ippay.secondary.alternatepay.transaction.controller.dto;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;
import lombok.experimental.FieldDefaults;

import static lombok.AccessLevel.PRIVATE;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class TransactionInitDto {

    @JsonProperty(required = true)
    String apiKey;

    String branchId;
 
    int timeOut;

    int maxInstallments;

    boolean isCent;
    
    int amount;
    
    String currency;
    
    String returnURL;
    
    String cancelURL;
    
    String callbackURL;
    
    String clientPhoneNumber;
    
    String clientEmail;
    
    String clientName;
    
    String reason;
    
    String partner;
    
    boolean readyForPayment;

}
