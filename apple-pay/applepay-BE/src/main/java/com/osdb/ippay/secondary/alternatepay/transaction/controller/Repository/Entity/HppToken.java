package com.osdb.ippay.secondary.alternatepay.transaction.controller.Repository.Entity;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import javax.persistence.*;
import static lombok.AccessLevel.PRIVATE;

@Entity
@Table(name = "hpptokens")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
public class HppToken {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    Long Id;

    @Column(name = "HppTransId")
    Long hppTransId;
    
    @Column(name = "Token")
    String token;
    
    @Column(name = "AppleToken")
    String appleToken;

  
}
