package com.osdb.ippay.common.merchant.facade;

import com.osdb.ippay.common.merchant.facade.dto.BankDto;
import com.osdb.ippay.secondary.merchant.service.filter.BankFilter;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;

import java.util.List;

public interface BankFacade {

    Page<BankDto> find(BankFilter filter, Pageable pageable);

    List<BankDto> find(BankFilter filter, Sort sort);

}
