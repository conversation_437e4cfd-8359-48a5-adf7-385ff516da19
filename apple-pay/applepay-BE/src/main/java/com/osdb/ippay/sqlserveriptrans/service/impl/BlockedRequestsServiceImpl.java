package com.osdb.ippay.sqlserveriptrans.service.impl;

import com.osdb.ippay.primary.user.repository.entity.User;
import com.osdb.ippay.secondary.chargeback.repository.entity.Chargeback;
import com.osdb.ippay.secondary.chargeback.service.ChargebackFilter;
import com.osdb.ippay.secondary.transaction.repository.ACHTransactionRepository;
import com.osdb.ippay.sqlserver.reports.repository.TotalACHDataGtwReportsRepository;
import com.osdb.ippay.sqlserver.reports.repository.TotalACHGtwDataRepository;
import com.osdb.ippay.sqlserver.reports.repository.entity.TotalACHDataGtwReports;
import com.osdb.ippay.sqlserver.reports.repository.entity.TotalACHGtwData;
import com.osdb.ippay.sqlserver.reports.service.TotalACHDataGtwReportsService;
import com.osdb.ippay.sqlserver.reports.service.filter.TotalACHDataGtwReportsFilter;
import com.osdb.ippay.sqlserver.reports.service.filter.TotalACHGtwDataFilter;
import com.osdb.ippay.sqlserveriptrans.repository.BlockedRequestsRepository;
import com.osdb.ippay.sqlserveriptrans.repository.entity.BlockedRequests;
import com.osdb.ippay.sqlserveriptrans.service.BlockedRequestsService;
import com.osdb.ippay.sqlserveriptrans.service.filter.BlockedRequestsFilter;

import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.persistence.EntityManager;
import javax.persistence.criteria.Predicate;

import static lombok.AccessLevel.PRIVATE;

@Service
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class BlockedRequestsServiceImpl implements BlockedRequestsService {

	BlockedRequestsRepository objBlockedRequestsRepository;

	  EntityManager entityManager;

	    @Autowired
	    public BlockedRequestsServiceImpl(BlockedRequestsRepository blockedRequestsRepository,
	    		                           @Qualifier("sqlserverIPTransEmFactory") EntityManager entityManager) {

	        this.objBlockedRequestsRepository = blockedRequestsRepository;
	        this.entityManager = entityManager;
	    }
	    
	    
	
	private Specification<BlockedRequests> filterBy(BlockedRequestsFilter filter) {
        return (r, rq, cb) -> {

            Predicate terminalIdPredicate = (filter.getTerminalID() != null) ?
            		  cb.equal(
                              r.get("terminalId"),
                              filter.getTerminalID()
                      ) : cb.conjunction();
            
            Predicate ipAddressPredicate = (filter.getIpAddress() != null) ?
          		  cb.equal(
                            r.get("ipAddress"),
                            filter.getIpAddress()
                    ) : cb.conjunction();
            
            Predicate transactionTypePredicate = (filter.getTransactionType() != null) ?
            		  cb.equal(
                              r.get("transactionType"),
                              filter.getTransactionType()
                      ) : cb.conjunction();
            
            Predicate fromDatePredicate = (filter.getStartDateTime() != null) ?
                    cb.greaterThanOrEqualTo(
                            r.get("date"),
                            filter.getStartDateTime()
                    ) : cb.conjunction();

            Predicate toDatePredicate = (filter.getEndDateTime() != null) ?
                    cb.lessThanOrEqualTo(
                            r.get("date"),
                            filter.getEndDateTime()
                    ) : cb.conjunction();

          

            return cb.and(
            		terminalIdPredicate,
            		ipAddressPredicate,
            		transactionTypePredicate,
                    fromDatePredicate,
                    toDatePredicate
            );
        };
    }


    private Sort getSort(Pageable pageable) {
        Sort.Order order = pageable.getSort().get()
                .findFirst()
                .orElse(Sort.Order.desc("date"));

        Sort.Direction direction = order.getDirection();
        String property = order.getProperty();

        return switch (property) {
    
            default -> Sort.by(order);
        };
    }


	@Override
	public Page<BlockedRequests> findData(String terminalID, String ipAddress, String transactionType,
			java.util.Date startDateTime, java.util.Date endDateTime, BlockedRequestsFilter filter, Pageable pageable) {

		  Specification<BlockedRequests> specification = filterBy(filter);

	        PageRequest pageRequest = PageRequest.of(
	                0,
	                1000,
	                getSort(pageable)
	        );

	        return objBlockedRequestsRepository.findAll(specification, pageRequest);
	}
	
}
