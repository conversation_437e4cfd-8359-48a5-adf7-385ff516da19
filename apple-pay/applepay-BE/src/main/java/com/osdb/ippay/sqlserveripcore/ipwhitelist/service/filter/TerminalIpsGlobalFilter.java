package com.osdb.ippay.sqlserveripcore.ipwhitelist.service.filter;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import static lombok.AccessLevel.PRIVATE;

import javax.validation.constraints.NotNull;

import lombok.Builder;
import com.osdb.ippay.sqlserveripcore.ipwhitelist.repository.entity.ActiveStatusType;

import io.swagger.v3.oas.annotations.Parameter;

@Data
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class TerminalIpsGlobalFilter {
	 
   //ActiveStatusType activeStatusType;
    Boolean activeStatus;
   
}
