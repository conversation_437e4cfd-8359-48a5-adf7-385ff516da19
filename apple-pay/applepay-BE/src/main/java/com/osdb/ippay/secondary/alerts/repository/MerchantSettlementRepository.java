package com.osdb.ippay.secondary.alerts.repository;

import com.osdb.ippay.secondary.alerts.repository.entity.Merchants;
import com.osdb.ippay.secondary.merchant.repository.entity.Merchant;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.lang.Nullable;

import java.util.List;
import java.util.Optional;

public interface MerchantSettlementRepository extends
        JpaRepository<Merchants, Long>,
        JpaSpecificationExecutor<Merchants> {  

   
    @Override
    List<Merchants> findAll();

   

}
