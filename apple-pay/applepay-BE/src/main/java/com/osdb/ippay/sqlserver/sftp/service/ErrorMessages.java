package com.osdb.ippay.sqlserver.sftp.service;

public class ErrorMessages {

   public static String strEncryptErrorMsg = "Error while encrypting file.";
   public static String strNoRecordFoundMsg = "No PAN Records found for the Merchant.";
   public static String strEncryptSuccessMsg = "File Encryption succeded.";
   public static String strDecryptErrorMsg = "Error while decrypting file.";
   public static String strDecrypotSuccessMsg = "File Decryption succeded.";
   public static String strSFTPUploadErrorMsg = "Error while uploading file to FTP";
   public static String strSFTPUploadSuccessMsg = "File upload to FTP succeded.";

}
