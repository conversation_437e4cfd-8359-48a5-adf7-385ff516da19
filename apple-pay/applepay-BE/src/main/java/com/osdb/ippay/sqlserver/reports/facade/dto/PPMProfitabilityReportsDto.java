package com.osdb.ippay.sqlserver.reports.facade.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;


import static lombok.AccessLevel.PRIVATE;

@Getter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class PPMProfitabilityReportsDto {

	String bankId;
	
    String rpAgentID;

    String associationId;

    String merchantId;

    String recordType;

    String processingDate;

    String dbaName;
    
    String dbaCityAddress;
    
    String dbaStateCountry;
    
    long dbaZip;
    
    String visaSIC;
    
    String mastercardMCC;
    
    String merchantStatus;
    
    String dateClosed;
    
    String minimumBillDiscount;
    
    String minDiscountAmount;
    
    String expenseRateGroup;


}