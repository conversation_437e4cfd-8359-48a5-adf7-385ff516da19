package com.osdb.ippay.primary.pdf.controller;

import com.osdb.ippay.common.logging.LogExecutionTime;
import com.osdb.ippay.primary.pdf.service.PdfService;
import com.osdb.ippay.primary.pdf.service.filter.PdfFilter;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

import static lombok.AccessLevel.PRIVATE;
import static org.springframework.http.HttpHeaders.CONTENT_DISPOSITION;

@Tag(name = "pdf-prerender")
@RestController
@RequestMapping(value = "/api/v1/private/")
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class PdfController {
/*
    PdfService pdfService;

    @LogExecutionTime
    @GetMapping("/load-as-pdf")
    public ResponseEntity<byte[]> getPDF(@Parameter(hidden = true) HttpServletRequest request,
                                         @ParameterObject PdfFilter filter) throws IOException {

        byte[] content = pdfService.getPdf(filter, request);

        String fileName = filter.getUrl().contains("credit-card") ?
                String.format("merchant_statement_cc_%s_%s_%s.pdf", filter.getMerchantId(), filter.getMonth(), filter.getYear()) :
                String.format("merchant_statement_echeck_%s_%s_%s.pdf", filter.getMerchantId(), filter.getMonth(), filter.getYear());

        String contentDisposition = String.format("attachment; filename=\"%s\"", fileName);

        return ResponseEntity.ok()
                .header(CONTENT_DISPOSITION, contentDisposition)
                .body(content);
    }*/
}
