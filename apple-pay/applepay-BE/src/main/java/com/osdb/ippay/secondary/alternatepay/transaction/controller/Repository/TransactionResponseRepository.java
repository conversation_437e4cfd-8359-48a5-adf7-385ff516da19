package com.osdb.ippay.secondary.alternatepay.transaction.controller.Repository;

import com.osdb.ippay.secondary.alternatepay.transaction.controller.Repository.Entity.TransactionResponse;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

@Repository
public interface TransactionResponseRepository extends
        JpaRepository<TransactionResponse, Long>,
        JpaSpecificationExecutor<TransactionResponse> {

    //Optional<TransactionResponse> find();
    
    TransactionResponse findBytransactionNumber(String transactionNumber);

}
