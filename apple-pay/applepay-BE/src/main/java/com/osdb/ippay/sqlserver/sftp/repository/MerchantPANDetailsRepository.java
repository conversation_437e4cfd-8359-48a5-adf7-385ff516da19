package com.osdb.ippay.sqlserver.sftp.repository;

import com.osdb.ippay.sqlserver.sftp.repository.entity.MerchantPANDetails;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface MerchantPANDetailsRepository extends
        JpaRepository< MerchantPANDetails, Long>,
        JpaSpecificationExecutor< MerchantPANDetails> {

    List<MerchantPANDetails> getPANDetails(String mid);
    
	
}
