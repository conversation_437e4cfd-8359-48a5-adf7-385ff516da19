package com.osdb.ippay.sqlserver.reports.controller;

import com.ippay.global.clients.ipp.IppayResponse;
import com.osdb.ippay.sqlserver.reports.service.filter.MonthlyResidualReportsFilter;
import com.osdb.ippay.sqlserver.sftp.service.FileTransferService;
import com.ippay.global.clients.ipp.data.ResponseType;
import com.osdb.ippay.common.domain.FileDomain;
import com.osdb.ippay.common.exception.handling.Error;
import com.osdb.ippay.common.exception.handling.ErrorResponse;
import com.osdb.ippay.common.logging.LogExecutionTime;
import com.osdb.ippay.common.security.service.AuthenticationService;
import com.osdb.ippay.common.util.annotation.ApiPageable;
import com.osdb.ippay.common.util.number.DoubleUtil;
import com.osdb.ippay.common.util.response.PageResponse;
import com.osdb.ippay.primary.user.repository.entity.User;
import com.osdb.ippay.secondary.transaction.facade.dto.*;
import com.osdb.ippay.secondary.transaction.service.domain.RefundTransactionRequest;
import com.osdb.ippay.secondary.transaction.service.domain.SaleTransactionRequest;
import com.osdb.ippay.secondary.transaction.service.domain.VoidTransactionRequest;
import com.osdb.ippay.secondary.transaction.service.filter.AuthDateFilter;
import com.osdb.ippay.secondary.transaction.service.filter.SettlementFilter;
import com.osdb.ippay.secondary.transaction.service.filter.TransactionFilter;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.apache.commons.lang3.ObjectUtils;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.osdb.ippay.secondary.transaction.service.filter.TransactionType.TRANSACTION;
import static lombok.AccessLevel.PRIVATE;

@Tag(name = "Reports")
@RestController
@RequestMapping(value = "/api/v1/private")
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class MonthlyResidualReportsController {

/*
    FileTransferService fileTransferService;
    
    static final String TRANSACTION_FAILED_ERR_MSG = "Transaction declined, with error: %s";
    
	private static final String SAMPLE_CSV_FILE_PATH_MRR_2 = "/testdata1.csv";
	
    @LogExecutionTime
    @ApiPageable
    @GetMapping("/tsys-residual-report")
    public ResponseEntity<ByteArrayResource> exportMonthlyResidualReport(
            @Valid @ParameterObject MonthlyResidualReportsFilter filter) throws Exception {

        FileDomain response = fileTransferService.exportMonthlyResidualReport("", filter);

        return ResponseEntity.ok()
                .contentType(new MediaType("application", "force-download"))
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=".concat(response.getFileName()))
                .body(response.getFile());
        
     
    }

  
    
    @GetMapping("/tsys-residual-fee-item-detail-data")
    public ResponseEntity<PageResponse<Map>> getResidualFeeItemDetailData(@Valid @ParameterObject MonthlyResidualReportsFilter filter,
    		Pageable page) {
    	try {
			fileTransferService.feeItemDetailReport(SAMPLE_CSV_FILE_PATH_MRR_2, filter);
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
        Page<Map> response = fileTransferService.findFeeItemDetailData(filter, page);
        return ResponseEntity.ok(new PageResponse<>(response.getContent(), response.getTotalElements()));
    }
    
    @GetMapping("/tsys-residual-fee-item-summary-data")
    public ResponseEntity<PageResponse<Map>> getResidualFeeItemSummaryData(@Valid @ParameterObject MonthlyResidualReportsFilter filter,
    		Pageable page) {
    	try {
			fileTransferService.feeItemSummaryReport(SAMPLE_CSV_FILE_PATH_MRR_2, filter);
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
        Page<Map> response = fileTransferService.findSummaryData(filter, page);
        return ResponseEntity.ok(new PageResponse<>(response.getContent(), response.getTotalElements()));
    }
    
    @GetMapping("/tsys-residual-minimum-discount-data")
    public ResponseEntity<PageResponse<Map>> getResidualMinimumDiscountData(@Valid @ParameterObject MonthlyResidualReportsFilter filter,
    		Pageable page) {
    	try {
			fileTransferService.minimumDiscountReport(SAMPLE_CSV_FILE_PATH_MRR_2, filter);
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
        Page<Map> response = fileTransferService.findData(filter, page);
        return ResponseEntity.ok(new PageResponse<>(response.getContent(), response.getTotalElements()));
    }
    
    @GetMapping("/tsys-residual-minimum-discount-report")
    public ResponseEntity<ByteArrayResource> exportResidualMinDiscountReport(
            @Valid @ParameterObject MonthlyResidualReportsFilter filter) throws Exception {

        FileDomain response = fileTransferService.minimumDiscountReport(SAMPLE_CSV_FILE_PATH_MRR_2, filter);

        return ResponseEntity.ok()
                .contentType(new MediaType("application", "force-download"))
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=".concat(response.getFileName()))
                .body(response.getFile());
    }
    
    @GetMapping("/tsys-residual-fee-item-summary-report")
    public ResponseEntity<ByteArrayResource> exportResidualFeeItemSummaryReport(
            @Valid @ParameterObject MonthlyResidualReportsFilter filter) throws Exception {

        FileDomain response = fileTransferService.feeItemSummaryReport(SAMPLE_CSV_FILE_PATH_MRR_2, filter);

        return ResponseEntity.ok()
                .contentType(new MediaType("application", "force-download"))
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=".concat(response.getFileName()))
                .body(response.getFile());
    }
    
    @GetMapping("/tsys-residual-fee-item-detail-report")
    public ResponseEntity<ByteArrayResource> exportResidualFeeItemDetailReport(
            @Valid @ParameterObject MonthlyResidualReportsFilter filter) throws Exception {

        FileDomain response = fileTransferService.feeItemDetailReport(SAMPLE_CSV_FILE_PATH_MRR_2, filter);

        return ResponseEntity.ok()
                .contentType(new MediaType("application", "force-download"))
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=".concat(response.getFileName()))
                .body(response.getFile());
    }
    
   */
}
