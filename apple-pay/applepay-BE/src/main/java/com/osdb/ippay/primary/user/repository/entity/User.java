package com.osdb.ippay.primary.user.repository.entity;

import com.osdb.ippay.common.entity.BaseEntity;
import lombok.*;
import lombok.experimental.FieldDefaults;
import lombok.experimental.SuperBuilder;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.Instant;
import java.util.List;

import static com.osdb.ippay.primary.user.repository.entity.UserRole.ADMIN_READ_ONLY_ROLE;
import static com.osdb.ippay.primary.user.repository.entity.UserRole.ADMIN_ROLE;
import static javax.persistence.EnumType.STRING;
import static lombok.AccessLevel.PRIVATE;

@Entity
@Table(name = "user")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = PRIVATE)
@SuperBuilder(toBuilder = true)
public class User extends BaseEntity {

    @NotBlank
    @Column(name = "email")
    String email;

    @Column(name = "password")
    String password;

    @Column(name = "password_last_changed")
    Instant passwordLastChanged;

    @Column(name = "otp")
    String otp;

    @Column(name = "sessions_hash")
    String sessionsHash;

    @Column(name = "reset_password_hash")
    String resetPasswordHash;

    @NotBlank(message = "Missing required parameter: 'firstName'")
    @Column(name = "first_name")
    String firstName;

    @NotBlank(message = "Missing required parameter: 'lastName'")
    @Column(name = "last_name")
    String lastName;

    @Enumerated(value = STRING)
    @NotNull(message = "Missing required parameter: 'role'")
    @Column(name = "role")
    UserRole role;

    @Enumerated(value = STRING)
    @NotNull(message = "Missing required parameter: 'status'")
    @Column(name = "status")
    UserStatus status;

    @Column(name = "last_login")
    Instant lastLogin;

    @Column(name = "total_failed_logins")
    Integer totalFailedLogins;

    @Column(name = "last_session_failed_logins")
    Integer lastSessionFailedLogins;

    @Column(name = "last_session_ip_address")
    String lastSessionIpAddress;

    @OneToMany
    @Column(insertable = false, updatable = false)
    @JoinTable(
            name="user_merchant_ref",
            joinColumns = @JoinColumn( name="user_id"),
            inverseJoinColumns = @JoinColumn( name="id")
    )
    List<UserMerchant> merchants;

    @Column(name = "partner_id")
    Long partnerId;

    @Column(name = "receive_daily_settlement")
    Boolean receiveDailySettlement;

    @Column(name = "receive_monthly_settlement")
    Boolean receiveMonthlySettlement;

    @Column(name = "receive_echeck_reject_notices")
    Boolean receiveEcheckRejectNotices;

    @Column(name = "receive_chargebacks_notices")
    Boolean receiveChargebacksNotices;

    public String getName() {
        return String.format(
                "%s %s", this.firstName, this.lastName
        ).trim();
    }

    public boolean isAdmin() {
    	return true;
       // return ADMIN_ROLE.equals(this.getRole()) || ADMIN_READ_ONLY_ROLE.equals(this.getRole());
    }

    public boolean isNotAdmin() {
        return !isAdmin();
    }
}
