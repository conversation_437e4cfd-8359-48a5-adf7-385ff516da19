package com.osdb.ippay.secondary.transaction.repository;

import com.osdb.ippay.secondary.transaction.repository.entity.CCSettlement;
import com.osdb.ippay.secondary.transaction.repository.entity.CCTransaction;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Optional;

public interface CCTransactionRepository extends
        JpaRepository<CCTransaction, Long>,
        JpaSpecificationExecutor<CCTransaction> {

    @Override
    @EntityGraph(attributePaths = { "merchant" })
    List<CCTransaction> findAll(Specification<CCTransaction> specification);

    @Override
    @EntityGraph(attributePaths = { "merchant" })
    Page<CCTransaction> findAll(Specification<CCTransaction> specification, Pageable pageable);

    @Override
    @EntityGraph(attributePaths = { "merchant" })
    Optional<CCTransaction> findById(Long aLong);

    @Query(nativeQuery = true, value =
            "SELECT AuthTransDate " +
                    "FROM CCTransactions " +
                    "WHERE SettlementDate IS NULL AND MerchantID = ?1 LIMIT 15"
    )
    List<String> findAuthDatesForTransactions(String merchantId);

    @Query(nativeQuery = true, value =
            "SELECT SettlementDate " +
                    "FROM CCTransactions " +
                    "WHERE SettlementDate IS NOT NULL AND MerchantID = ?1 LIMIT 15"
    )
    List<String> findAuthDatesForSettlements(String merchantId);

    @Query(
            value = "SELECT " +
                    "SettlementDate as date, " +
                    "SUM(IF(RequestType = 'SALE' OR RequestType = 'CAPT', TransAmount, 0)) as salesAmount, " +
                    "COUNT(IF(RequestType = 'SALE' OR RequestType = 'CAPT', 1, NULL)) as salesCount, " +
                    "SUM(IF(RequestType = 'CREDIT', TransAmount, 0)) as refundAmount, " +
                    "COUNT(IF(RequestType = 'CREDIT', 1, NULL)) as refundCount, " +
                    "SUM(IF((RequestType = 'SALE' OR RequestType = 'CAPT') AND CardType = 'A', TransAmount, 0)) as axAmount, " +
                    "SUM(IF((RequestType = 'CREDIT' OR RequestType = 'CAPT') AND CardType = 'A', TransAmount, 0)) as axRefund, " +
                    "COUNT(IF((RequestType = 'SALE' OR RequestType = 'CREDIT' OR RequestType = 'CAPT') AND CardType = 'A', 1, NULL)) as axCount " +
                    "FROM CCTransactions " +
                    "WHERE SettlementDate >= ?2 AND SettlementDate <= ?3 AND MerchantID = ?1 " +
                    "GROUP BY date " +
                    "HAVING salesAmount != 0 " +
                    "ORDER BY date DESC",
            nativeQuery = true
    )
    List<CCSettlement> getSettlements(String merchantId, String from, String to);

}