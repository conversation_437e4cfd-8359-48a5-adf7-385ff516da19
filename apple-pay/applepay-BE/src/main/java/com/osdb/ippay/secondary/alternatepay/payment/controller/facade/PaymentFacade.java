package com.osdb.ippay.secondary.alternatepay.payment.controller.facade;

import com.osdb.ippay.secondary.alternatepay.payment.controller.dto.PaymentDto;
import com.osdb.ippay.secondary.alternatepay.payment.controller.dto.PaymentResultDto;
import com.osdb.ippay.secondary.alternatepay.payment.controller.dto.TSYSPaymentRequestDto;
import com.osdb.ippay.secondary.alternatepay.payment.controller.dto.TSYSPaymentResponseDto;


public interface PaymentFacade {

    PaymentResultDto create(PaymentDto paymentInDto);
    TSYSPaymentResponseDto createTSYSPaymentResponse(TSYSPaymentRequestDto paymentInDto);
}
