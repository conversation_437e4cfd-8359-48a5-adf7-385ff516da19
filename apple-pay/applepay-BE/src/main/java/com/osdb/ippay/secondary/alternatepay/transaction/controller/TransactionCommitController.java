package com.osdb.ippay.secondary.alternatepay.transaction.controller;

import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionCommitDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionGetDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionGetResultDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.facade.TransactionCommitFacade;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.facade.TransactionCommitGetFacade;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.facade.TransactionGetFacade;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.facade.TransactionResponseFacade;

import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;


import static lombok.AccessLevel.PRIVATE;

@Tag(name = "Alternatepay/Transaction")
@RestController
@RequestMapping(value = "/api/v2/payment/alternatepay")
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class TransactionCommitController {

	TransactionCommitFacade transactionCommitFacade;
	TransactionGetFacade transactionGetFacade; 
	TransactionResponseFacade transactionResponseFacade;

    @GetMapping("/commit/{transactionNumber}")
    public ResponseEntity<TransactionGetResultDto> GetTGransactionById(@PathVariable("transactionNumber") String transactionNumber) {
    	TransactionGetResultDto response = transactionCommitFacade.create(transactionNumber);
    	//transactionResponseFacade.commit(response, transactionNumber);
        return ResponseEntity.ok(response);
    }

  

}
