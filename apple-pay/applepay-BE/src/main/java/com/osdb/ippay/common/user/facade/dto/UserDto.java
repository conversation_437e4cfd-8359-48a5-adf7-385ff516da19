package com.osdb.ippay.common.user.facade.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.osdb.ippay.primary.user.facade.dto.UserMerchantDto;
import com.osdb.ippay.primary.user.repository.entity.UserRole;
import com.osdb.ippay.primary.user.repository.entity.UserStatus;
import com.osdb.ippay.secondary.partner.facade.dto.PartnerDto;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.*;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.Instant;
import java.util.List;

import static com.fasterxml.jackson.annotation.JsonProperty.Access.READ_ONLY;
import static lombok.AccessLevel.PRIVATE;

@Getter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class UserDto {

    @Parameter(hidden = true)
    @JsonProperty(access = READ_ONLY)
    Long id;

    @Parameter(required = true)
    @Email(message = "Invalid Email Format")
    @NotBlank(message = "Email is required")
    String email;

    @Parameter(required = true)
    @NotBlank(message = "First name is required")
    String firstName;

    @Parameter(required = true)
    @NotBlank(message = "Last name is required")
    String lastName;

    @Parameter(required = true)
    @NotNull(message = "Role is required")
    UserRole role;

    @Parameter(hidden = true)
    @JsonProperty(access = READ_ONLY)
    UserStatus status;

    @Setter
    @Parameter(hidden = true)
    @JsonProperty(access = READ_ONLY)
    Integer merchantsAssigned;

    @Setter
    @Parameter(hidden = true)
    @JsonProperty(access = READ_ONLY)
    List<UserMerchantDto> merchants;

    @Setter
    @Parameter(hidden = true)
    @JsonProperty(access = READ_ONLY)
    PartnerDto partner;

    Long partnerId;

    Boolean receiveDailySettlement;

    Boolean receiveMonthlySettlement;

    Boolean receiveEcheckRejectNotices;

    Boolean receiveChargebacksNotices;

    @Parameter(hidden = true)
    @JsonProperty(access = READ_ONLY)
    Instant lastLogin;

    @Parameter(hidden = true)
    @JsonProperty(access = READ_ONLY)
    Integer totalFailedLogins;

    @Parameter(hidden = true)
    @JsonProperty(access = READ_ONLY)
    String lastSessionIpAddress;

}
