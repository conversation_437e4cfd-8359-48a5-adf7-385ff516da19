package com.osdb.ippay.primary.user.repository;

import com.osdb.ippay.primary.user.repository.entity.UserMerchant;
import com.osdb.ippay.primary.user.repository.entity.UserMerchantNumber;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface UserMerchantRepository extends JpaRepository<UserMerchant, Long> {

    List<UserMerchant> findAllByUserId(Long userId);

    void deleteByUserId(Long userId);

    Long countAllByMerchantId(String merchantId);

    @Query(
            nativeQuery = true,
            value = "SELECT merchant_id as merchantId, count(id) as assignedUsers " +
                    "FROM user_merchant_ref " +
                    "WHERE merchant_id in (?1) " +
                    "GROUP BY merchant_id"
    )
    List<UserMerchantNumber> countAssignedUsers(List<String> merchantIds);

}
