package com.osdb.ippay.secondary.partner.facade.mapper;

import com.osdb.ippay.secondary.partner.facade.dto.PartnerDto;
import com.osdb.ippay.secondary.partner.repository.entity.Partner;
import org.springframework.stereotype.Component;

@Component
public class PartnerMapper {

    public PartnerDto toDto(Partner partner) {
        if(partner == null) {
            return null;
        }

        return PartnerDto.builder()
                .id(partner.getId())
                .partnerName(partner.getPartnerName())
                .build();
    }
}
