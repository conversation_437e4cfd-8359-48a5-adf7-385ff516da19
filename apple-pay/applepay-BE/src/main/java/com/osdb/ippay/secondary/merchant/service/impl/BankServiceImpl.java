package com.osdb.ippay.secondary.merchant.service.impl;

import com.osdb.ippay.secondary.merchant.repository.BankRepository;
import com.osdb.ippay.secondary.merchant.repository.entity.Bank;
import com.osdb.ippay.secondary.merchant.service.BankService;
import com.osdb.ippay.secondary.merchant.service.filter.BankFilter;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.util.List;

import static lombok.AccessLevel.PRIVATE;

@Service
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class BankServiceImpl implements BankService {

    BankRepository bankRepository;

    @Override
    public Page<Bank> find(BankFilter filter, Pageable pageable) {
        Specification<Bank> specification = filterBy(filter);
        return bankRepository.findAll(specification, pageable);
    }

    @Override
    public List<Bank> find(BankFilter filter, Sort sort) {
        Specification<Bank> specification = filterBy(filter);
        return bankRepository.findAll(specification, sort);
    }

    private Specification<Bank> filterBy(BankFilter filter) {
        return (r, rq, cb) -> StringUtils.isNotBlank(filter.getSearch()) ?
                cb.like(cb.lower(r.get("bankName")), "%" + filter.getSearch().toLowerCase() + "%") :
                cb.conjunction();
    }
}
