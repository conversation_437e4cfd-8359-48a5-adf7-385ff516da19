package com.osdb.ippay.common.merchant.facade.mapper;

import com.osdb.ippay.common.merchant.facade.dto.BankDto;
import com.osdb.ippay.secondary.merchant.repository.entity.Bank;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class BankMapper {

    public Page<BankDto> toDto(Page<Bank> banks) {
        return banks.map(this::toDto);
    }

    public List<BankDto> toDto(List<Bank> banks) {
        return banks.stream()
                .map(this::toDto)
                .collect(Collectors.toList());
    }

    public BankDto toDto(Bank bank) {
        if (bank == null) {
            return null;
        }

        return BankDto.builder()
                .id(bank.getId())
                .bankName(bank.getBankName())
                .build();
    }
}
