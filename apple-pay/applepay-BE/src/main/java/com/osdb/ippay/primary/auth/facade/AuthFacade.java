package com.osdb.ippay.primary.auth.facade;

import com.osdb.ippay.primary.auth.facade.dto.*;
import com.osdb.ippay.common.user.facade.dto.UserDto;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public interface AuthFacade {

    void signInStepOne(SignInStepOneDto stepOneDto);

    void signOut(HttpServletRequest request, HttpServletResponse response);

    void signOut(HttpServletRequest request, HttpServletResponse response, String email);

    void forgotPassword(ForgotPasswordDto forgotPasswordDto);

    void resetPassword(ResetPasswordDto resetPasswordDto);

    void changePassword(String email, ChangePasswordDto changePasswordDto);

    UserDto signInStepTwo(SignInStepTwoDto stepTwoDto, HttpServletRequest request, HttpServletResponse response);

    UserDto getCurrentUser(String email);

}
