package com.osdb.ippay.secondary.statement.facade.mapper;

import com.osdb.ippay.secondary.statement.facade.dto.MSAuthDetailDto;
import com.osdb.ippay.secondary.statement.repository.entity.MSAuthDetail;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class MSAuthDetailMapper {

    public List<MSAuthDetailDto> toDto(List<MSAuthDetail> msAuthDetails) {
        return msAuthDetails.stream()
                .map(this::toDto)
                .collect(Collectors.toList());
    }

    public MSAuthDetailDto toDto(MSAuthDetail msAuthDetail) {
        return MSAuthDetailDto.builder()
                .id(msAuthDetail.getId())
                .description(msAuthDetail.getDescription())
                .numX(msAuthDetail.getNumX())
                .rate(msAuthDetail.getRate())
                .authFee(msAuthDetail.getAuthFee())
                .build();
    }
}
