package com.osdb.ippay.secondary.alternatepay.transaction.controller.dto;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;
import lombok.experimental.FieldDefaults;

import static lombok.AccessLevel.PRIVATE;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class TransactionReceiptDto {

    @JsonProperty(required = true)
    String apiKey;

    int timeOut;

    boolean isCent;
    
    int amount;
    
    String currency;
    
    String returnURL;
    
    String cancelURL;
    
    String callbackURL;
    
    ReceiptDto receipt;
    
    ReceiptNotificationsDto receiptNotifications;
     
}

