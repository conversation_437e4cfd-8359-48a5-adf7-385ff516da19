package com.osdb.ippay.secondary.reports.facade.impl;

import com.osdb.ippay.common.domain.FileDomain;
import com.osdb.ippay.primary.user.repository.entity.User;
import com.osdb.ippay.secondary.alerts.facade.SettledTransactionFacade;
import com.osdb.ippay.secondary.alerts.facade.dto.SettledTransactionDto;
//import com.osdb.ippay.secondary.alerts.facade.mapper.SettledTransactionMapper;
import com.osdb.ippay.secondary.alerts.repository.entity.SettledTransaction;
import com.osdb.ippay.secondary.alerts.service.SettledTransactionAlertService;
import com.osdb.ippay.secondary.alerts.service.filter.SettledTransactionFilter;
import com.osdb.ippay.secondary.chargeback.facade.dto.ChargebackDto;
import com.osdb.ippay.secondary.chargeback.repository.entity.Chargeback;
import com.osdb.ippay.secondary.chargeback.service.ChargebackFilter;
import com.osdb.ippay.secondary.reports.facade.CreditCountReportFacade;
import com.osdb.ippay.secondary.reports.facade.MonthlyCreditCardReportFacade;
import com.osdb.ippay.secondary.reports.facade.dto.CreditCountReportDto;
import com.osdb.ippay.secondary.reports.facade.dto.MonthlyCreditCardReportDto;
import com.osdb.ippay.secondary.reports.facade.mapper.CreditCountReportMapper;
import com.osdb.ippay.secondary.reports.facade.mapper.MonthlyCreditCardReportMapper;
import com.osdb.ippay.secondary.reports.repository.entity.CreditCountReport;
import com.osdb.ippay.secondary.reports.repository.entity.MonthlyCreditCardReport;
import com.osdb.ippay.secondary.reports.service.CreditCountReportService;
import com.osdb.ippay.secondary.reports.service.MonthlyCreditCardReportService;
import com.osdb.ippay.secondary.reports.service.filter.CreditCountReportFilter;
import com.osdb.ippay.sqlserver.reports.facade.TotalACHDataGtwReportsFacade;
import com.osdb.ippay.sqlserver.reports.facade.dto.TotalACHDataGtwReportsDto;
import com.osdb.ippay.sqlserver.reports.facade.dto.TotalACHGtwDataDto;
import com.osdb.ippay.sqlserver.reports.facade.mapper.TotalACHDataGtwReportsMapper;
import com.osdb.ippay.sqlserver.reports.facade.mapper.TotalACHGtwDataMapper;
import com.osdb.ippay.sqlserver.reports.repository.entity.TotalACHDataGtwReports;
import com.osdb.ippay.sqlserver.reports.repository.entity.TotalACHGtwData;
import com.osdb.ippay.sqlserver.reports.service.TotalACHDataGtwReportsService;
import com.osdb.ippay.sqlserver.reports.service.filter.TotalACHDataGtwReportsFilter;
import com.osdb.ippay.sqlserver.reports.service.filter.TotalACHGtwDataFilter;

import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.ListIterator;
import java.util.Map;

import static lombok.AccessLevel.PRIVATE;

@Component
@RequiredArgsConstructor
@FieldDefaults(makeFinal = true, level = PRIVATE)
public class MonthlyCreditCardReportFacadeImpl implements MonthlyCreditCardReportFacade {

    // services
	MonthlyCreditCardReportService monthlyCreditCardReportService;
	MonthlyCreditCardReportMapper monthlyCreditCardReportMapper;
	
	@Override
	public Page<MonthlyCreditCardReportDto> findData(String startDateTime, String endDateTime, CreditCountReportFilter filter, Pageable pageable) {
		Page<MonthlyCreditCardReport> monthlyCreditCardReportData = monthlyCreditCardReportService.find(startDateTime,  endDateTime, pageable);
	       return monthlyCreditCardReportMapper.toDto(monthlyCreditCardReportData);
	}


	@Override
	public FileDomain export(String startDateTime, String endDateTime) throws IOException {
		String[] HEADERS = {
                "MerchantID",
                "Merchant Name",
                "Total Sales Volume",
                "Visa Sales Amount",
                "Visa Sales Count",
                "Visa Credit Amount",
                "Visa Credit Count",
                "MasterCard Sales Amount",
                "MasterCard Sales Count",
                "MasterCard Credit Amount",
                "MasterCard Credit Count",
                "Amex Sales Amount",
                "Amex Sales Count",
                "Amex Credit Amount",
                "Amex Credit Count",
                "Discover Sales Amount",
                "Discover Sales Count",
                "Discover Credit Amount",
                "Discover Credit Count",
                "Net Sales"
        };

        final CSVFormat format = CSVFormat.Builder.create()
                .setHeader(HEADERS)
                .setIgnoreEmptyLines(true)
                .build();
        
        List<MonthlyCreditCardReport> monthlyCreditCardReportData  = monthlyCreditCardReportService.generateReport(startDateTime, endDateTime);
        for (int i = 0; i < monthlyCreditCardReportData.size(); i++) {

          }
        try (
                ByteArrayOutputStream out = new ByteArrayOutputStream();
                CSVPrinter csvPrinter = new CSVPrinter(new PrintWriter(out), format)
        ) {
        	    for (MonthlyCreditCardReport monthlyCreditCardReportObj : monthlyCreditCardReportData) {
                List<String> data = Arrays.asList(
                		monthlyCreditCardReportObj.getMerchantId(),
                		monthlyCreditCardReportObj.getMerchantName(),
                		monthlyCreditCardReportObj.getTotalSales(),
                		monthlyCreditCardReportObj.getVisaSalesAmount(),
                		monthlyCreditCardReportObj.getVisaSalesCount(),
                		monthlyCreditCardReportObj.getVisaCreditAmount(),
                		monthlyCreditCardReportObj.getVisaCreditCount(),
                		monthlyCreditCardReportObj.getMcSalesAmount(),
                		monthlyCreditCardReportObj.getMcSalesCount(),
                		monthlyCreditCardReportObj.getMcCreditAmount(),
                		monthlyCreditCardReportObj.getMcCreditCount(),
                		monthlyCreditCardReportObj.getAmexSalesAmount(),
                		monthlyCreditCardReportObj.getAmexSalesCount(),
                		monthlyCreditCardReportObj.getAmexCreditAmount(),
                		monthlyCreditCardReportObj.getAmexCreditCount(),
                		monthlyCreditCardReportObj.getDiscoverSalesAmount(),
                		monthlyCreditCardReportObj.getDiscoverSalesCount(),
                		monthlyCreditCardReportObj.getDiscoverCreditAmount(),
                		monthlyCreditCardReportObj.getDiscoverCreditCount(),
                		monthlyCreditCardReportObj.getNetSales()
                		
                );

                csvPrinter.printRecord(data);
            }
            //csvPrinter.printRecord(data);
            csvPrinter.flush();

            return FileDomain.builder()
                    .file(new ByteArrayResource(new ByteArrayInputStream(out.toByteArray()).readAllBytes()))
                    .fileName(
                            String.format(
                                    "WAB_MonthlyCreditCardReport.csv"            
                                    
                            )
                    )
                    .build();
        }
        
	}
	
	
}
