package com.osdb.ippay.secondary.alerts.facade.dto;

import lombok.*;
import lombok.experimental.FieldDefaults;

import java.time.Instant;

import javax.persistence.Column;

import static lombok.AccessLevel.PRIVATE;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class SettledTransactionDto {

	Long ipTransId;
	
    String merchantId;

    String email;
    
    String settlementDate;

    String percentageHit;

    String actualAmount;

    String amountHitOrExceeded;

    String overlimitAmount;
    
    String triggerAlertTime;
    
    String partnerEmail;

  //  Boolean alert_to_support;

  //  Boolean alert_to_risk;

   
}
