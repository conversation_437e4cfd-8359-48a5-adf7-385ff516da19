package com.osdb.ippay.secondary.transaction.service;

import com.ippay.global.clients.ipp.IppayResponse;
import com.osdb.ippay.common.domain.FileDomain;
import com.osdb.ippay.primary.user.repository.entity.User;
import com.osdb.ippay.secondary.transaction.facade.dto.CCTransactionDto;
import com.osdb.ippay.secondary.transaction.repository.entity.CCSettlement;
import com.osdb.ippay.secondary.transaction.repository.entity.CCTransaction;
import com.osdb.ippay.secondary.transaction.service.domain.RefundTransactionRequest;
import com.osdb.ippay.secondary.transaction.service.domain.VoidTransactionRequest;
import com.osdb.ippay.secondary.transaction.service.filter.AuthDateFilter;
import com.osdb.ippay.secondary.transaction.service.filter.SettlementFilter;
import com.osdb.ippay.secondary.transaction.service.filter.TransactionFilter;

import java.io.IOException;
import java.util.List;
import java.util.concurrent.Future;

public interface CCTransactionService {

    List<CCSettlement> findSettlements(SettlementFilter filter);

    Future<List<CCTransaction>> find(User authUser, TransactionFilter filter);

    CCTransaction find(Long id);

    FileDomain transactionsExport(User user, List<CCTransactionDto> transactions, Double totalAmount) throws Exception;

    FileDomain settlementsExport(User user, TransactionFilter filter) throws Exception;

    List<String> findExistingCCAuthDates(AuthDateFilter filter);

    IppayResponse voidTransaction(Long id, VoidTransactionRequest voidTransactionRequest);

    IppayResponse refundTransaction(Long id, RefundTransactionRequest refundTransactionRequest);

}
