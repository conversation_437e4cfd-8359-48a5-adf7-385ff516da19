package com.osdb.ippay.secondary.statement.facade.mapper;

import com.osdb.ippay.secondary.statement.facade.dto.MSOtherDetailDto;
import com.osdb.ippay.secondary.statement.repository.entity.MSOtherDetail;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class MSOtherDetailMapper {

    public List<MSOtherDetailDto> toDto(List<MSOtherDetail> msOtherDetails) {
        return msOtherDetails.stream()
                .map(this::toDto)
                .collect(Collectors.toList());
    }

    public MSOtherDetailDto toDto(MSOtherDetail msOtherDetail) {
        return MSOtherDetailDto.builder()
                .id(msOtherDetail.getId())
                .description(msOtherDetail.getDescription())
                .amount(msOtherDetail.getAmount())
                .discountRate(msOtherDetail.getDiscountRate())
                .numX(msOtherDetail.getNumX())
                .transactionFee(msOtherDetail.getTransactionFee())
                .otherFee(msOtherDetail.getOtherFee())
                .build();
    }
}
