package com.osdb.ippay.secondary.statement.facade.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;

import static lombok.AccessLevel.PRIVATE;

@Getter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class CCMerchantStatementDto {

    List<MSActivitySummaryDto> activitySummary;

    List<MSDepositDetailDto> depositDetails;

    List<MSOtherCardDepositDto> otherCardDeposits;

    List<MSProcessingDetailDto> processingDetails;

    List<MSAuthDetailDto> authDetails;

    List<MSOtherDetailDto> otherDetails;

    List<MSTotalDetailDto> totalDetails;

}
