package com.osdb.ippay.sqlserver.reports.facade.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;


import static lombok.AccessLevel.PRIVATE;

import javax.persistence.Column;

@Getter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class TotalACHDataGtwReportsDto {

	Long ipTransactionId;
	
    String merchantID;

    String ccProcessor;

    String requestType;

    String actionCode;

    String transactionCount;

    String totalAmount;
    
    String authDateTime;

    String spsMid;

    String trnId;

    String orgName;
}