package com.osdb.ippay.secondary.alternatepay.refund.controller.facade.impl;

import com.osdb.ippay.secondary.alternatepay.payment.controller.repository.entity.*;
import com.osdb.ippay.secondary.alternatepay.refund.controller.repository.RefundResponseRepository;
import com.osdb.ippay.secondary.alternatepay.refund.controller.repository.entity.RefundResponse;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.Repository.TransactionResponseRepository;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.Repository.Entity.TransactionResponse;
import com.osdb.ippay.secondary.alternatepay.refund.controller.dto.RefundSaveDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionSaveDto;
import com.osdb.ippay.secondary.alternatepay.refund.controller.dto.TransactionRefundResultDto;
import com.osdb.ippay.secondary.alternatepay.refund.controller.facade.RefundResponseFacade;
import com.osdb.ippay.secondary.alternatepay.refund.controller.mapper.RefundResponseMapper;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.mapper.TransactionResponseMapper;

import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.stereotype.Component;

import static lombok.AccessLevel.PRIVATE;

@Component
@RequiredArgsConstructor
@FieldDefaults(makeFinal = true, level = PRIVATE)
public class RefundResponseFacadeImpl implements RefundResponseFacade {
   
    RefundResponseMapper refundResponseMapper;
    RefundResponseRepository refundResponseRepository;
    TransactionResponseMapper transactionResponseMapper;
    TransactionResponseRepository transactionResponseRepository;

	@Override
	public RefundSaveDto create(TransactionRefundResultDto transactionRefundSaveDto) {
		
		RefundResponse obj = refundResponseMapper.toEntity(transactionRefundSaveDto);
		RefundResponse objGlobal = refundResponseRepository.save(obj);
        return refundResponseMapper.toDto(objGlobal);
	}

	@Override
	public TransactionSaveDto update(TransactionRefundResultDto transactionRefundSaveDto, String trnid) {

		TransactionResponse trnData = transactionResponseRepository.findBytransactionNumber(trnid);
		
		refundResponseMapper.putTransactionEntity(transactionRefundSaveDto, trnData);
		TransactionResponse updatedTrnData = transactionResponseRepository.save(trnData);
	    return transactionResponseMapper.toDto(updatedTrnData);
	}
}
