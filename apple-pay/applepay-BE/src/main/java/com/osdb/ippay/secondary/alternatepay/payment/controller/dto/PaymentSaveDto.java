package com.osdb.ippay.secondary.alternatepay.payment.controller.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import javax.persistence.*;

import static lombok.AccessLevel.PRIVATE;


@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class PaymentSaveDto {

	@Id
    @GeneratedValue
    Long PaymentId;
    
    boolean paymentResult;
  
    String transactionNumber;
    
    String transactionStatus;
    
    String approvalDate;
    
    String providerId;
    
    String approvedBy;
    
    String paymentMethodCode;

    String paymentMethodName;
    
    String requestId;
    
    String amount;
    
    String providerName;
    
    String paymentMethod;
    
    String paymentType;
    
    String confirmationNumber;
    
    String token;
    
    String cardType;
    
    String lastFourDigits;
    
    String cardMask;
}
