package com.osdb.ippay.secondary.alternatepay.transaction.controller.dto;

import lombok.*;
import lombok.experimental.FieldDefaults;

import static lombok.AccessLevel.PRIVATE;

import com.fasterxml.jackson.annotation.JsonProperty;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class TransactionReceiptResultDto {

    String TransactionNumber;

    String date;
 
    String amount;

    String businessName;

    String currency;
    
    int status;
    
    String statusName;
    
    String timeoutDate;
    
    String businessId;
    
    String pos;
    
    String businessOwnerPhone;

    String businessCountry;
    
    String businessCity;
    
    String businessStreet;
    
    String businessAddress;
    
    String branchNumber;
    
    String branchId;
    
    String receiptPending;

    @JsonProperty("paymentType")
    String paymentType;
}
