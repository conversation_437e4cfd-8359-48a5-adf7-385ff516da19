package com.osdb.ippay.sqlserver.reports.controller;


import com.osdb.ippay.common.domain.FileDomain;
import com.osdb.ippay.common.logging.LogExecutionTime;
import com.osdb.ippay.common.util.response.PageResponse;
import com.osdb.ippay.sqlserver.reports.facade.TotalACHDataGtwReportsFacade;
import com.osdb.ippay.sqlserver.reports.facade.TotalCardDataGtwReportsFacade;
import com.osdb.ippay.sqlserver.reports.facade.dto.TotalACHDataGtwReportsDto;
import com.osdb.ippay.sqlserver.reports.facade.dto.TotalCardDataGtwReportsDto;
import com.osdb.ippay.sqlserver.reports.facade.dto.TotalCardGtwDataDto;
import com.osdb.ippay.sqlserver.reports.service.filter.TotalACHDataGtwReportsFilter;
import com.osdb.ippay.sqlserver.reports.service.filter.TotalACHGtwDataFilter;
import com.osdb.ippay.sqlserver.reports.service.filter.TotalCardDataGtwReportsFilter;
import com.osdb.ippay.sqlserver.reports.service.filter.TotalCardGtwDataFilter;

import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;
import java.io.IOException;
import static lombok.AccessLevel.PRIVATE;

@Tag(name = "Reports")
@RestController
@RequestMapping(value = "/api/v1/private")
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class TotalCardDataGtwReportsController {
/*
    TotalCardDataGtwReportsFacade totalCardDataGtwReportsFacade;
   
    
    static final String TRANSACTION_FAILED_ERR_MSG = "Report generation failure, with error: %s";
    
    @LogExecutionTime
    @GetMapping("/total-cc-data-gateway-report")
    public ResponseEntity<ByteArrayResource> exportCardDataSummary(
            @Valid @ParameterObject TotalCardDataGtwReportsFilter filter,
            Long ipTransactionID, String authStartDateTime, String authEndDateTime) throws IOException {

        FileDomain response = totalCardDataGtwReportsFacade.export(ipTransactionID, authStartDateTime, authEndDateTime, filter);

        return ResponseEntity.ok()
                .contentType(new MediaType("application", "force-download"))
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=".concat(response.getFileName()))
                .body(response.getFile());
    }

    @LogExecutionTime
    @GetMapping("/total-cc-data-gateway-data")
    public ResponseEntity<PageResponse<TotalCardGtwDataDto>> get(@Valid @ParameterObject TotalCardGtwDataFilter filter,
            Long ipTransactionID, String authStartDateTime, String authEndDateTime, Pageable page) {

        Page<TotalCardGtwDataDto> response = totalCardDataGtwReportsFacade.findData(ipTransactionID, authStartDateTime, authEndDateTime , filter, page);
        return ResponseEntity.ok(new PageResponse<>(response.getContent(), response.getTotalElements()));
    }*/
}