package com.osdb.ippay.sqlserver.reports.facade;

import com.osdb.ippay.common.domain.FileDomain;
import com.osdb.ippay.secondary.chargeback.facade.dto.ChargebackDto;
import com.osdb.ippay.secondary.chargeback.service.ChargebackFilter;
import com.osdb.ippay.sqlserver.reports.facade.dto.TotalACHDataGtwReportsDto;
import com.osdb.ippay.sqlserver.reports.facade.dto.TotalACHGtwDataDto;
import com.osdb.ippay.sqlserver.reports.service.filter.TotalACHDataGtwReportsFilter;
import com.osdb.ippay.sqlserver.reports.service.filter.TotalACHGtwDataFilter;

import java.io.IOException;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface TotalACHDataGtwReportsFacade {
	
    FileDomain export(Long ipTransactionID, String authStartDateTime, String authEndDateTime,  TotalACHDataGtwReportsFilter filter) throws IOException;
    Page<TotalACHGtwDataDto> findData(Long ipTransactionID, String authStartDateTime, String authEndDateTime,  TotalACHGtwDataFilter filter, Pageable pageable);
    Page<TotalACHDataGtwReportsDto> find(Long ipTransactionID, String authStartDateTime, String authEndDateTime,  TotalACHDataGtwReportsFilter filter, Pageable pageable);
}