package com.osdb.ippay.secondary.alerts.facade.mapper;

import com.osdb.ippay.secondary.alerts.facade.dto.SettledTransactionDto;
import com.osdb.ippay.secondary.alerts.repository.entity.SettledTransaction;

import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;

@Component
public class SettledTransactionMapper {

    public Page<SettledTransactionDto> toDto(Page<SettledTransaction> settledTransaction) {
        return settledTransaction.map(this::toDto);
    }

    public SettledTransactionDto toDto(SettledTransaction settledTransaction) {
       

        return SettledTransactionDto.builder()
        	//	.ipTransId(settledTransaction.getIpTransId())
        		.settlementDate(settledTransaction.getSettlementDate())
                .merchantId(settledTransaction.getMerchantId())
                .percentageHit(settledTransaction.getPercentageHit())
                .actualAmount(settledTransaction.getActualAmount())
                .amountHitOrExceeded(settledTransaction.getAmountHitOrExceeded())
                .overlimitAmount(settledTransaction.getOverlimitAmount())
                .triggerAlertTime(settledTransaction.getTriggerAlertTime())
                .build();
    }
}
