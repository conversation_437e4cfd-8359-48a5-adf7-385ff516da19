package com.osdb.ippay.secondary.alternatepay.payment.controller.dto;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;
import lombok.experimental.FieldDefaults;

import static lombok.AccessLevel.PRIVATE;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class PaymentDto {

    @JsonProperty(required = true)
    String apiKey;
  
    String token;

    String amount;

    POSDataRequestDto posData;

}
