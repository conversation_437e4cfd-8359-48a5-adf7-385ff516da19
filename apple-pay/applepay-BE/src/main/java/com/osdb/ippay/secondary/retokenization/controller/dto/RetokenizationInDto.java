package com.osdb.ippay.secondary.retokenization.controller.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotNull;

import static lombok.AccessLevel.PRIVATE;

@Getter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class RetokenizationInDto {

    @NotNull
    String user;

    @NotNull
    String jobType;

    @NotNull
    JobInfoInDto jobInfo;

    String jobDescription;

}
