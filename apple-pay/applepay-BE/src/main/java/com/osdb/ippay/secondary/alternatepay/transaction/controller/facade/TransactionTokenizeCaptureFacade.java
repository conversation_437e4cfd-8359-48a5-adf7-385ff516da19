package com.osdb.ippay.secondary.alternatepay.transaction.controller.facade;

import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionTokenizeCaptureDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TrasactionInitResultDto;


public interface TransactionTokenizeCaptureFacade {

	TrasactionInitResultDto create(TransactionTokenizeCaptureDto transactionTokenizeCaptureInDto);
}
