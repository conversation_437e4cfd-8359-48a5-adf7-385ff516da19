/*package com.osdb.ippay.sqlserver.reports.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import com.osdb.ippay.secondary.transaction.repository.entity.CCSettlement;
import com.osdb.ippay.secondary.transaction.repository.entity.CCTransaction;
import com.osdb.ippay.sqlserver.reports.repository.entity.FANFReports;

public interface FANFReportsRepository extends
JpaRepository<FANFReports, Long>,
JpaSpecificationExecutor<FANFReports> {

List<CCTransaction> getCCTransactions(String merchantId, String from, String to);
List<CCSettlement> getSettlements(String merchantId, String from, String to);

}*/