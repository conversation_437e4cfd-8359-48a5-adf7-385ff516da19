package com.osdb.ippay.secondary.statement.facade.mapper;

import com.osdb.ippay.secondary.statement.facade.dto.MSActivitySummaryDto;
import com.osdb.ippay.secondary.statement.repository.entity.MSActivitySummary;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class MSActivitySummaryMapper {

    public List<MSActivitySummaryDto> toDto(List<MSActivitySummary> msActivitySummaries) {
        return msActivitySummaries.stream()
                .map(this::toDto)
                .collect(Collectors.toList());
    }

    public MSActivitySummaryDto toDto(MSActivitySummary msActivitySummary) {
        return MSActivitySummaryDto.builder()
                .id(msActivitySummary.getId())
                .planType(msActivitySummary.getPlanType())
                .sales(msActivitySummary.getSales())
                .numSales(msActivitySummary.getNumSales())
                .credits(msActivitySummary.getCredits())
                .numCredits(msActivitySummary.getNumCredits())
                .netSales(msActivitySummary.getNetSales())
                .perItemFee(msActivitySummary.getPerItemFee())
                .rate(msActivitySummary.getRate())
                .discountDue(msActivitySummary.getDiscountDue())
                .numTotalTrans(msActivitySummary.getNumTotalTrans())
                .build();
    }
}
