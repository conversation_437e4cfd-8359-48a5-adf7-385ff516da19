package com.osdb.ippay.secondary.reports.facade.dto;

import lombok.*;
import lombok.experimental.FieldDefaults;

import java.time.Instant;

import javax.persistence.Column;

import static lombok.AccessLevel.PRIVATE;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class CreditCountReportDto {

	
    String merchantId;

    String jetpay_mid;
    
    String refundAmount;
    
    String refundCount;
   
}
