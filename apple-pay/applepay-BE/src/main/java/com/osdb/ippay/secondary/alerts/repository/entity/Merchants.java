package com.osdb.ippay.secondary.alerts.repository.entity;

import com.osdb.ippay.common.entity.BaseEntity;
import lombok.*;
import lombok.experimental.FieldDefaults;
import lombok.experimental.SuperBuilder;
import javax.persistence.*;


import static lombok.AccessLevel.PRIVATE;

@Entity
@Table(name = "MERCHANTS")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = PRIVATE)
@SuperBuilder(toBuilder = true)
public class Merchants extends BaseEntity {

    @Column(name = "mid")
    String merchantId;

    @Column(name = "merchant_dba_name")
    String merchantName;

  

}
