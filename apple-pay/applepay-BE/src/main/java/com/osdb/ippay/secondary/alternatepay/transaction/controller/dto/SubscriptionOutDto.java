package com.osdb.ippay.secondary.alternatepay.transaction.controller.dto;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;
import lombok.experimental.FieldDefaults;

import static lombok.AccessLevel.PRIVATE;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class SubscriptionOutDto {

	String id;
    
	boolean active;
	    
	String created;
	
	String updated;
	
	String expires;
	
	int count;
	
	int failure;
	
	int success;
	
	String start_date;
	
	String interval;
	
	String status;
	
    @JsonProperty("paymentType")
    String paymentType;
}
