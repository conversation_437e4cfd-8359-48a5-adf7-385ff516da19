package com.osdb.ippay.secondary.transaction.service.impl;

import com.osdb.ippay.common.exception.business.NotFoundException;
import com.osdb.ippay.primary.user.repository.entity.User;
import com.osdb.ippay.secondary.transaction.repository.CPCCTransactionRepository;
import com.osdb.ippay.secondary.transaction.repository.entity.CCSettlement;
import com.osdb.ippay.secondary.transaction.repository.entity.CPCCTransaction;
import com.osdb.ippay.secondary.transaction.service.CPCCTransactionService;
import com.osdb.ippay.secondary.transaction.service.filter.SettlementFilter;
import com.osdb.ippay.secondary.transaction.service.filter.TransactionFilter;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.Predicate;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

import static com.osdb.ippay.common.exception.ErrorMessage.CC_TRANSACTION_NOT_FOUND;
import static com.osdb.ippay.secondary.transaction.service.filter.TransactionType.SETTLEMENT;
import static java.lang.Boolean.FALSE;
import static java.lang.Boolean.TRUE;
import static lombok.AccessLevel.PRIVATE;
import static org.apache.commons.collections.CollectionUtils.isEmpty;
import static org.apache.commons.collections.CollectionUtils.isNotEmpty;

@Service
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class CPCCTransactionServiceImpl implements CPCCTransactionService {

    CPCCTransactionRepository repository;

    @Async
    @Override
    public Future<List<CPCCTransaction>> find(User authUser, TransactionFilter filter) {
        List<CPCCTransaction> transactions = repository.findAll(filterBy(authUser, filter));
        return new AsyncResult<>(aFilterBy(transactions, filter));
    }

    @Override
    public CPCCTransaction find(Long id) {
        return repository
                .findById(id)
                .orElseThrow(() -> new NotFoundException(CC_TRANSACTION_NOT_FOUND));
    }

    @Override
    public List<CCSettlement> findSettlements(SettlementFilter filter) {
        return repository.getSettlements(
                filter.getMerchantId(),
                filter.getFromDateStr(),
                filter.getToDateStr()
        );
    }

    private List<CPCCTransaction> aFilterBy(List<CPCCTransaction> transactions, TransactionFilter filter) {

        if(Objects.nonNull(filter.getShowOnlyApproved()) && TRUE.equals(filter.getShowOnlyApproved())) {
            transactions = transactions.stream()
                    .filter(t ->
                            "RETURN ACCEPTED".equals(t.getApprovalStatus()) ||
                                    "APPROVED".equals(t.getApprovalStatus()) ||
                                    "VOID PROCESSED".equals(t.getApprovalStatus())
                    ).collect(Collectors.toList());
        }

        if(StringUtils.isNotBlank(filter.getTerminalId())) {
            transactions = transactions.stream()
                    .filter(t -> Objects.nonNull(t.getTerminalId()) && t.getTerminalId().contains(filter.getTerminalId()))
                    .collect(Collectors.toList());
        }

        if(StringUtils.isNotBlank(filter.getTransactionId())) {
            transactions = transactions.stream()
                    .filter(t -> Objects.nonNull(t.getTransactionId()) && t.getTransactionId().contains(filter.getTransactionId()))
                    .collect(Collectors.toList());
        }

        if(StringUtils.isNotBlank(filter.getCardNumber())) {
            transactions = transactions.stream()
                    .filter(t -> Objects.nonNull(t.getSafeCardNumber()) && t.getSafeCardNumber().contains(filter.getCardNumber()))
                    .collect(Collectors.toList());
        }

        if(StringUtils.isNotBlank(filter.getCardHolder())) {
            transactions = transactions.stream()
                    .filter(t -> Objects.nonNull(t.getCardHolder()) && t.getCardHolder().contains(filter.getCardHolder()))
                    .collect(Collectors.toList());
        }

        if(StringUtils.isNotBlank(filter.getAuthCode())) {
            transactions = transactions.stream()
                    .filter(t -> Objects.nonNull(t.getAuthCode()) && t.getAuthCode().contains(filter.getAuthCode()))
                    .collect(Collectors.toList());
        }

        if(StringUtils.isNotBlank(filter.getUd1())) {
            transactions = transactions.stream()
                    .filter(t -> Objects.nonNull(t.getUd1()) && t.getUd1().contains(filter.getUd1()))
                    .collect(Collectors.toList());
        }

        if(StringUtils.isNotBlank(filter.getUd2())) {
            transactions = transactions.stream()
                    .filter(t -> Objects.nonNull(t.getUd2()) && t.getUd2().contains(filter.getUd2()))
                    .collect(Collectors.toList());
        }

        if(StringUtils.isNotBlank(filter.getUd3())) {
            transactions = transactions.stream()
                    .filter(t -> Objects.nonNull(t.getUd3()) && t.getUd3().contains(filter.getUd3()))
                    .collect(Collectors.toList());
        }

        if(StringUtils.isNotBlank(filter.getOrderNumber())) {
            transactions = transactions.stream()
                    .filter(t -> Objects.nonNull(t.getOrderNumber()) && t.getOrderNumber().contains(filter.getOrderNumber()))
                    .collect(Collectors.toList());
        }

        if(StringUtils.isNotBlank(filter.getApprovalStatus())) {
            if ("APPROVED".equals(filter.getApprovalStatus())) {
                transactions = transactions.stream()
                        .filter(t ->
                                "RETURN ACCEPTED".equals(t.getApprovalStatus()) ||
                                        "APPROVED".equals(t.getApprovalStatus()) ||
                                        "VOID PROCESSED".equals(t.getApprovalStatus())
                        ).collect(Collectors.toList());
            } else {
                transactions = transactions.stream()
                        .filter(t ->
                                !"RETURN ACCEPTED".equals(t.getApprovalStatus()) ||
                                        !"APPROVED".equals(t.getApprovalStatus()) ||
                                        !"VOID PROCESSED".equals(t.getApprovalStatus())
                        ).collect(Collectors.toList());
            }
        }

        if(StringUtils.isNotBlank(filter.getCardType())) {
            transactions = transactions.stream()
                    .filter(t -> Objects.nonNull(t.getCardType()) && t.getCardType().equalsIgnoreCase(filter.getCardType()))
                    .collect(Collectors.toList());
        }

        if(StringUtils.isNotBlank(filter.getTransactionType())) {
            transactions = transactions.stream()
                    .filter(t -> Objects.nonNull(t.getRequestType()) && t.getRequestType().equalsIgnoreCase(filter.getTransactionType()))
                    .collect(Collectors.toList());
        }

        if(filter.getAmount() != null) {
            transactions = transactions.stream()
                    .filter(t -> Objects.nonNull(t.getAuthAmount()) && t.getAuthAmount().equals(filter.getAmountInUSD()))
                    .collect(Collectors.toList());
        }

        if(SETTLEMENT.equals(filter.getType())) {
            transactions = transactions.stream()
                    .filter(t -> Objects.nonNull(t.getSettlementDate()))
                    .collect(Collectors.toList());
        }

        if(FALSE.equals(filter.getIncludeTokenize())) {
            transactions = transactions.stream()
                    .filter(t ->
                            ObjectUtils.notEqual("TOKENIZE", t.getRequestType())
                    ).collect(Collectors.toList());
        }

        if(Objects.nonNull(filter.getStType())) {
            transactions = switch (filter.getStType()) {
                case SALE ->
                        transactions.stream()
                                .filter(t -> "SALE".equalsIgnoreCase(t.getRequestType()) || "CAPT".equalsIgnoreCase(t.getRequestType()))
                                .collect(Collectors.toList());

                case REFUND ->
                        transactions.stream()
                                .filter(t -> "CREDIT".equalsIgnoreCase(t.getRequestType()))
                                .collect(Collectors.toList());

                case SALE_REFUND ->
                        transactions.stream()
                                .filter(t ->
                                        "SALE".equalsIgnoreCase(t.getRequestType()) ||
                                                "CAPT".equalsIgnoreCase(t.getRequestType()) ||
                                                "CREDIT".equalsIgnoreCase(t.getRequestType())
                                ).collect(Collectors.toList());

                case AX_AMOUNT ->
                        transactions.stream()
                                .filter(t ->
                                        "SALE".equalsIgnoreCase(t.getRequestType()) ||
                                                "CAPT".equalsIgnoreCase(t.getRequestType()) ||
                                                "A".equalsIgnoreCase(t.getCardType())
                                ).collect(Collectors.toList());

                case AX_REFUND ->
                        transactions.stream()
                                .filter(t ->
                                        "CREDIT".equalsIgnoreCase(t.getRequestType()) ||
                                                "CAPT".equalsIgnoreCase(t.getRequestType()) ||
                                                "A".equalsIgnoreCase(t.getCardType())
                                ).collect(Collectors.toList());

                case BANK_CARDS ->
                        transactions.stream()
                                .filter(t ->
                                        "SALE".equalsIgnoreCase(t.getRequestType()) ||
                                                "CREDIT".equalsIgnoreCase(t.getRequestType()) ||
                                                "CAPT".equalsIgnoreCase(t.getRequestType()) ||
                                                "A".equalsIgnoreCase(t.getCardType())
                                ).collect(Collectors.toList());
            };
        }

        return transactions;
    }

    private Specification<CPCCTransaction> filterBy(User authUser, TransactionFilter filter) {
        return (r, rq, cb) -> {
            Predicate fromDatePredicate = filter.getFromDate() != null ?
                    cb.greaterThanOrEqualTo(
                            SETTLEMENT.equals(filter.getType()) ?
                                    r.get("settlementDate") :
                                    r.get("authDate"),
                            filter.getFromDateStr()
                    ) : cb.conjunction();

            Predicate toDatePredicate = filter.getToDate() != null ?
                    cb.lessThanOrEqualTo(
                            SETTLEMENT.equals(filter.getType()) ?
                                    r.get("settlementDate") :
                                    r.get("authDate"),
                            filter.getToDateStr()
                    ) : cb.conjunction();

            Predicate merchantIdPredication = StringUtils.isNotBlank(filter.getMerchantId()) ?
                    cb.equal(
                            r.get("merchantId"),
                            filter.getMerchantId()
                    ) : cb.conjunction();

            Predicate merchantIdsPredicate = isNotEmpty(filter.getMerchantIds()) && authUser.isNotAdmin() ?
                    cb.in(r.get("merchantId")).value(filter.getMerchantIds()) :
                    cb.conjunction();

            if(isEmpty(filter.getMerchantIds()) && authUser.isNotAdmin()) {
                merchantIdsPredicate = cb.disjunction();
            }

            return cb.and(
                    fromDatePredicate,
                    toDatePredicate,
                    merchantIdPredication,
                    merchantIdsPredicate
            );
        };
    }
}
