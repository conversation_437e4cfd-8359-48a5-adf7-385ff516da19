package com.osdb.ippay.primary.user.service;

import com.osdb.ippay.primary.user.repository.entity.UserMerchantNumber;

import java.util.List;

public interface UserMerchantService {

    List<String> findMerchantIdsByUserId(Long userId);

    void deleteBy(Long userId);

    void saveAll(Long userId, List<String> merchantIds);

    Long countByMerchant(String merchantId);

    List<UserMerchantNumber> countByMerchantIds(List<String> merchantIds);

}
