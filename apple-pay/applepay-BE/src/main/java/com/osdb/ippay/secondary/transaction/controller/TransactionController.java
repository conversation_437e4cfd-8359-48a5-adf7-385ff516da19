package com.osdb.ippay.secondary.transaction.controller;

import com.ippay.global.clients.ipp.IppayResponse;
import com.ippay.global.clients.ipp.data.ResponseType;
import com.osdb.ippay.common.domain.FileDomain;
import com.osdb.ippay.common.exception.handling.Error;
import com.osdb.ippay.common.exception.handling.ErrorResponse;
import com.osdb.ippay.common.logging.LogExecutionTime;
import com.osdb.ippay.common.security.service.AuthenticationService;
import com.osdb.ippay.common.util.annotation.ApiPageable;
import com.osdb.ippay.common.util.number.DoubleUtil;
import com.osdb.ippay.primary.user.repository.entity.User;
import com.osdb.ippay.secondary.transaction.facade.mapper.TransactionFacade;
import com.osdb.ippay.secondary.transaction.facade.dto.*;
import com.osdb.ippay.secondary.transaction.service.domain.RefundTransactionRequest;
import com.osdb.ippay.secondary.transaction.service.domain.SaleTransactionRequest;
import com.osdb.ippay.secondary.transaction.service.domain.VoidTransactionRequest;
import com.osdb.ippay.secondary.transaction.service.filter.AuthDateFilter;
import com.osdb.ippay.secondary.transaction.service.filter.SettlementFilter;
import com.osdb.ippay.secondary.transaction.service.filter.TransactionFilter;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.apache.commons.lang3.ObjectUtils;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.io.IOException;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.osdb.ippay.secondary.transaction.service.filter.TransactionType.TRANSACTION;
import static lombok.AccessLevel.PRIVATE;

@Tag(name = "transaction")
@RestController
@RequestMapping(value = "/api/v1/private")
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class TransactionController {
/*
    AuthenticationService authenticationService;
    TransactionFacade transactionFacade;

    static final String TRANSACTION_FAILED_ERR_MSG = "Transaction declined, with error: %s";

    @LogExecutionTime
    @ApiPageable
    @GetMapping("/cc-transactions")
    public ResponseEntity<TransactionPageResponse<CCTransactionDto>> getCCTransactions(
            @Valid @ParameterObject TransactionFilter filter,
            @Parameter(hidden = true) Pageable pageable,
            @Parameter(hidden = true) @AuthenticationPrincipal String email) throws Exception {

        User authUser = authenticationService.getCurrentUser(email);

        List<CCTransactionDto> transactions = transactionFacade.findCCTransactions(authUser, filter, pageable.getSort());

        int total = transactions.size();
        double totalAmount = total > 0 ? transactionFacade.totalCCAmount(transactions) : 0.0;

        transactions = transactions.stream()
                .skip((long) pageable.getPageSize() * pageable.getPageNumber())
                .limit(pageable.getPageSize())
                .collect(Collectors.toList());

        return ResponseEntity.ok(new TransactionPageResponse<>(
                transactions,
                total,
                DoubleUtil.toPriceFormat(totalAmount, 2, 1)
        ));
    }

    @LogExecutionTime
    @GetMapping("/cc-settlements")
    public ResponseEntity<CCSettlementResponse> getCCSettlements(
            @ParameterObject SettlementFilter filter,
            @Parameter(hidden = true) @AuthenticationPrincipal String email) {

        CCSettlementResponse response = transactionFacade.findCCSettlements(email, filter);
        return ResponseEntity.ok(response);
    }

    @LogExecutionTime
    @GetMapping("/cc-transactions/{id}")
    public ResponseEntity<CCTransactionDto> getCCTransaction(
            @PathVariable Long id,
            @Parameter(hidden = true) @AuthenticationPrincipal String email) {

        User authUser = authenticationService.getCurrentUser(email);
        CCTransactionDto response = transactionFacade.findCCTransaction(authUser, id);

        return ResponseEntity.ok(response);
    }

    @LogExecutionTime
    @PostMapping("/cc-transactions/{id}/void")
    public ResponseEntity<ErrorResponse> voidCCTransaction(
            @PathVariable Long id,
            @Valid @RequestBody VoidTransactionRequest voidTransactionRequest,
            @Parameter(hidden = true) @AuthenticationPrincipal String email) {

        User authUser = authenticationService.getCurrentUser(email);

        transactionFacade.findCCTransaction(authUser, id);
        authenticationService.throwIfNoAccessToVoidAndRefund(authUser);

        IppayResponse response = transactionFacade.voidCCTransaction(id, voidTransactionRequest);
        ResponseType responseType = response.getResponse();

        if(ObjectUtils.notEqual(responseType.getActionCode(), "000")) {
            String errMsg = Optional.ofNullable(responseType.getResponseText()).orElse(responseType.getErrMsg());

            return ResponseEntity
                    .status(HttpStatus.BAD_REQUEST.value())
                    .body(new ErrorResponse(
                            System.currentTimeMillis(),
                            HttpStatus.BAD_REQUEST.value(),
                            List.of(Error.builder()
                                    .message(String.format(TRANSACTION_FAILED_ERR_MSG, errMsg))
                                    .build()
                            )
                    ));
        }

        return ResponseEntity.noContent().build();
    }

    @LogExecutionTime
    @PostMapping("/cc-transactions/{id}/refund")
    public ResponseEntity<ErrorResponse> refundCCTransaction(
            @PathVariable Long id,
            @Valid @RequestBody RefundTransactionRequest refundTransactionRequest,
            @Parameter(hidden = true) @AuthenticationPrincipal String email) {

        User authUser = authenticationService.getCurrentUser(email);

        transactionFacade.findCCTransaction(authUser, id);
        authenticationService.throwIfNoAccessToVoidAndRefund(authUser);

        IppayResponse response = transactionFacade.refundCCTransaction(id, refundTransactionRequest);
        ResponseType responseType = response.getResponse();

        if(ObjectUtils.notEqual(responseType.getActionCode(), "000")) {
            String errMsg = Optional.ofNullable(responseType.getResponseText()).orElse(responseType.getErrMsg());

            return ResponseEntity
                    .status(HttpStatus.BAD_REQUEST.value())
                    .body(new ErrorResponse(
                            System.currentTimeMillis(),
                            HttpStatus.BAD_REQUEST.value(),
                            List.of(Error.builder()
                                    .message(String.format(TRANSACTION_FAILED_ERR_MSG, errMsg))
                                    .build()
                            )
                    ));
        }

        return ResponseEntity.noContent().build();
    }

    @LogExecutionTime
    @GetMapping("/cc-settlements/export")
    public ResponseEntity<ByteArrayResource> exportCCSettlements(
            @Valid @ParameterObject SettlementFilter filter,
            @Parameter(hidden = true) @AuthenticationPrincipal String email) throws IOException {

        FileDomain response = transactionFacade.exportCCSettlementsSummary(email, filter);

        return ResponseEntity.ok()
                .contentType(new MediaType("application", "force-download"))
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=".concat(response.getFileName()))
                .body(response.getFile());
    }

    @LogExecutionTime
    @GetMapping("/cc-transactions/export")
    public ResponseEntity<ByteArrayResource> exportCCTransactions(
            @Valid @ParameterObject TransactionFilter filter,
            @Parameter(hidden = true) @AuthenticationPrincipal String email) throws Exception {

        FileDomain response = TRANSACTION.equals(filter.getType()) ?
                transactionFacade.exportCCTransactions(email, filter) :
                transactionFacade.exportCCSettlements(email, filter);

        return ResponseEntity.ok()
                .contentType(new MediaType("application", "force-download"))
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=".concat(response.getFileName()))
                .body(response.getFile());
    }

    @LogExecutionTime
    @ApiPageable
    @GetMapping("/ach-transactions")
    public ResponseEntity<TransactionPageResponse<ACHTransactionDto>> getACHTransactions(
            @Valid @ParameterObject TransactionFilter filter,
            @Parameter(hidden = true) Pageable pageable,
            @Parameter(hidden = true) @AuthenticationPrincipal String email) {

        List<ACHTransactionDto> transactions = transactionFacade.findACHTransactions(email, filter, pageable.getSort());

        int total = transactions.size();

        double totalAmount = total > 0 ? transactionFacade.totalAchAmount(filter.getType(), transactions) : 0.0;
        String totalAmountStr = DoubleUtil.toPriceFormat(totalAmount, 2, 1);

        transactions = transactions.stream()
                .skip((long) pageable.getPageSize() * pageable.getPageNumber())
                .limit(pageable.getPageSize())
                .collect(Collectors.toList());

        return ResponseEntity.ok(new TransactionPageResponse<>(
                transactions,
                total,
                totalAmountStr
        ));
    }

    @LogExecutionTime
    @GetMapping("/ach-settlements")
    public ResponseEntity<ACHSettlementResponse> getACHSettlements(
            @ParameterObject SettlementFilter filter,
            @Parameter(hidden = true) @AuthenticationPrincipal String email) {

        ACHSettlementResponse response = transactionFacade.findACHSettlements(email, filter);
        return ResponseEntity.ok(response);
    }

    @LogExecutionTime
    @GetMapping("/ach-transactions/{id}")
    public ResponseEntity<ACHTransactionDto> getACHTransaction(
            @PathVariable Long id,
            @Parameter(hidden = true) @AuthenticationPrincipal String email) {

        User authUser = authenticationService.getCurrentUser(email);

        ACHTransactionDto response = transactionFacade.findACHTransaction(authUser, id);
        return ResponseEntity.ok(response);
    }

    @LogExecutionTime
    @PostMapping("/ach-transactions/{id}/refund")
    public ResponseEntity<ErrorResponse> refundACHTransaction(
            @PathVariable Long id,
            @Valid @RequestBody RefundTransactionRequest refundTransactionRequest,
            @Parameter(hidden = true) @AuthenticationPrincipal String email) {

        User authUser = authenticationService.getCurrentUser(email);

        transactionFacade.findACHTransaction(authUser, id);
        authenticationService.throwIfNoAccessToVoidAndRefund(authUser);

        IppayResponse response = transactionFacade.refundACHTransaction(id, refundTransactionRequest);
        ResponseType responseType = response.getResponse();

        if(ObjectUtils.notEqual(responseType.getActionCode(), "000")) {
            String errMsg = Optional.ofNullable(responseType.getResponseText()).orElse(responseType.getErrMsg());

            return ResponseEntity
                    .status(HttpStatus.BAD_REQUEST.value())
                    .body(new ErrorResponse(
                            System.currentTimeMillis(),
                            HttpStatus.BAD_REQUEST.value(),
                            List.of(Error.builder()
                                    .message(String.format(TRANSACTION_FAILED_ERR_MSG, errMsg))
                                    .build()
                            )
                    ));
        }

        return ResponseEntity.noContent().build();
    }

    @LogExecutionTime
    @PostMapping("/ach-transactions/{id}/void")
    public ResponseEntity<ErrorResponse> voidACHTransaction(
            @PathVariable Long id,
            @Valid @RequestBody VoidTransactionRequest voidTransactionRequest,
            @Parameter(hidden = true) @AuthenticationPrincipal String email) {

        User authUser = authenticationService.getCurrentUser(email);

        transactionFacade.findACHTransaction(authUser, id);
        authenticationService.throwIfNoAccessToVoidAndRefund(authUser);

        IppayResponse response = transactionFacade.voidACHTransaction(id, voidTransactionRequest);
        ResponseType responseType = response.getResponse();

        if(ObjectUtils.notEqual(responseType.getActionCode(), "000")) {
            String errMsg = Optional.ofNullable(responseType.getResponseText()).orElse(responseType.getErrMsg());

            return ResponseEntity
                    .status(HttpStatus.BAD_REQUEST.value())
                    .body(new ErrorResponse(
                            System.currentTimeMillis(),
                            HttpStatus.BAD_REQUEST.value(),
                            List.of(Error.builder()
                                    .message(String.format(TRANSACTION_FAILED_ERR_MSG, errMsg))
                                    .build()
                            )
                    ));
        }

        return ResponseEntity.noContent().build();
    }

    @LogExecutionTime
    @GetMapping("/ach-settlements/export")
    public ResponseEntity<ByteArrayResource> exportACHSettlements(
            @Valid @ParameterObject SettlementFilter filter,
            @Parameter(hidden = true) @AuthenticationPrincipal String email) throws IOException {

        FileDomain response = transactionFacade.exportACHSettlementsSummary(email, filter);

        return ResponseEntity.ok()
                .contentType(new MediaType("application", "force-download"))
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=".concat(response.getFileName()))
                .body(response.getFile());
    }

    @LogExecutionTime
    @GetMapping("/ach-transactions/export")
    public ResponseEntity<ByteArrayResource> exportACHTransactions(
            @Valid @ParameterObject TransactionFilter filter,
            @Parameter(hidden = true) @AuthenticationPrincipal String email) throws IOException {

        FileDomain response = TRANSACTION.equals(filter.getType()) ?
                transactionFacade.exportACHTransactions(email, filter) :
                transactionFacade.exportACHSettlements(email, filter);

        return ResponseEntity.ok()
                .contentType(new MediaType("application", "force-download"))
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=".concat(response.getFileName()))
                .body(response.getFile());
    }

    @LogExecutionTime
    @PostMapping("/process-transaction")
    public ResponseEntity<?> processTransaction(
            @Valid @RequestBody SaleTransactionRequest saleTransactionRequest,
            @Parameter(hidden = true) @AuthenticationPrincipal String email) {

        authenticationService.throwIfNoAccessToProcessTransaction(email, saleTransactionRequest);

        IppayResponse ippayResponse = transactionFacade.saleTransaction(saleTransactionRequest);
        ResponseType responseType = ippayResponse.getResponse();

        if(ObjectUtils.notEqual(responseType.getActionCode(), "000")) {
            String errMsg = Optional.ofNullable(responseType.getResponseText()).orElse(responseType.getErrMsg());

            return ResponseEntity
                    .status(HttpStatus.BAD_REQUEST.value())
                    .body(new ErrorResponse(
                            System.currentTimeMillis(),
                            HttpStatus.BAD_REQUEST.value(),
                            List.of(Error.builder()
                                    .message(String.format(TRANSACTION_FAILED_ERR_MSG, errMsg))
                                    .build()
                            )
                    ));
        }

        return ResponseEntity.ok(
                ProcessTransactionSuccess.builder()
                        .transactionId(responseType.getTransactionID())
                        .build()
        );
    }

    @LogExecutionTime
    @GetMapping("/cc-transactions/auth-dates")
    public ResponseEntity<List<String>> getExistingCCAuthDates(
            @Valid @ParameterObject AuthDateFilter filter,
            @Parameter(hidden = true) @AuthenticationPrincipal String email) {

        List<String> existedDates = transactionFacade.findExistingCCAuthDates(filter, email);
        return ResponseEntity.ok(existedDates);
    }

    @LogExecutionTime
    @GetMapping("/ach-transactions/auth-dates")
    public ResponseEntity<List<String>> getExistingACHAuthDates(
            @Valid @ParameterObject AuthDateFilter filter,
            @Parameter(hidden = true) @AuthenticationPrincipal String email) {

        List<String> existedDates = transactionFacade.findExistingACHAuthDates(filter, email);
        return ResponseEntity.ok(existedDates);
    }*/
}
