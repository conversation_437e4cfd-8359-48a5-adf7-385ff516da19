package com.osdb.ippay.common.user.facade;

import com.osdb.ippay.common.user.facade.dto.EditUserDto;
import com.osdb.ippay.common.user.facade.dto.UserDto;
import com.osdb.ippay.primary.user.service.filter.UserFilter;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface CommonUserFacade {

    Page<UserDto> find(UserFilter userFilter, Pageable pageable);

    UserDto find(Long id);

    UserDto create(UserDto userDto);

    UserDto put(Long id, EditUserDto userDto);

    UserDto patch(Long id, EditUserDto userDto);

    void resendInvitation(Long id);

}
