package com.osdb.ippay.primary.user.facade.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import com.osdb.ippay.primary.user.repository.entity.ConfigType;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotNull;

import static com.fasterxml.jackson.annotation.JsonProperty.Access.READ_ONLY;
import static lombok.AccessLevel.PRIVATE;

@Getter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class UserConfigDto {

    @Parameter(hidden = true)
    @JsonProperty(access = READ_ONLY)
    Long id;

    @Parameter(required = true)
    @NotNull(message = "Type is required")
    ConfigType type;

    @Parameter(required = true)
    @NotNull(message = "Configs is required")
    JsonNode configs;

}
