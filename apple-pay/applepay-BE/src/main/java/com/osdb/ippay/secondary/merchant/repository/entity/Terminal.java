package com.osdb.ippay.secondary.merchant.repository.entity;

import com.osdb.ippay.common.entity.BaseEntity;
import lombok.*;
import lombok.experimental.FieldDefaults;
import lombok.experimental.SuperBuilder;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import static lombok.AccessLevel.PRIVATE;

@Entity
@Table(name = "TERMINALS")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = PRIVATE)
@SuperBuilder(toBuilder = true)
public class Terminal extends BaseEntity {

    @Column(name = "tid")
    String tid;

}
