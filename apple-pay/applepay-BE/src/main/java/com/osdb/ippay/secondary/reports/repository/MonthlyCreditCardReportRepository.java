package com.osdb.ippay.secondary.reports.repository;

import com.osdb.ippay.secondary.reports.repository.entity.MonthlyCreditCardReport;


import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface MonthlyCreditCardReportRepository extends
        JpaRepository< MonthlyCreditCardReport, Long>,
        JpaSpecificationExecutor< MonthlyCreditCardReport> {

    List<MonthlyCreditCardReport> getMonthlyCreditCardReport(String fromDate, String toDate);
    
	
}
