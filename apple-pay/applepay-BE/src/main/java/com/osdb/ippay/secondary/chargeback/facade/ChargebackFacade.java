package com.osdb.ippay.secondary.chargeback.facade;

import com.osdb.ippay.common.domain.FileDomain;
import com.osdb.ippay.secondary.chargeback.facade.dto.ChargebackDto;
import com.osdb.ippay.secondary.chargeback.service.ChargebackFilter;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.io.IOException;
import java.util.List;

public interface ChargebackFacade {

    Page<ChargebackDto> find(String email, ChargebackFilter filter, Pageable pageable);

    List<String> findDates(String email, String merchantId);

    FileDomain export(String email, ChargebackFilter filter) throws IOException;

}
