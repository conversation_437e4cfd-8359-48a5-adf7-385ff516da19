package com.osdb.ippay.sqlserveripcore.ipwhitelist.repository;

import com.osdb.ippay.sqlserveripcore.ipwhitelist.repository.entity.TerminalIpsAuditTrailRecords;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface TerminalIpsAuditTrailRecordsRepository extends
        JpaRepository<TerminalIpsAuditTrailRecords, Long>,
        JpaSpecificationExecutor<TerminalIpsAuditTrailRecords> {

    List<TerminalIpsAuditTrailRecords> getAuditTrailRecords();

	
}
