package com.osdb.ippay.secondary.alternatepay.transaction.controller.mapper;

import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.*;

import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import static lombok.AccessLevel.PRIVATE;

@Component
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class TransactionCancelControllerMapper {

    @NonFinal
    @Value("${tokenization.api-key}")
    String apiKey;

    public TransactionGetDto toDto(TransactionGetDto transactionGetDto) {
        return TransactionGetDto.builder()
               
                .apiKey(apiKey)
                .build();
    }

  
    public TransactionCancelResultDto toOutDto(TransactionCancelResultDto trnCancelResultDto) {
        return TransactionCancelResultDto.builder()
                .status(trnCancelResultDto.getStatus())
                .status_message(trnCancelResultDto.getStatus_message())
                
                .build();
    }

}
