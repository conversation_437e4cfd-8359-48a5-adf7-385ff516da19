package com.osdb.ippay.secondary.alternatepay.transaction.controller.mapper;

import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.*;

import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import static lombok.AccessLevel.PRIVATE;

@Component
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class TransactionGetControllerMapper {

    @NonFinal
    @Value("${tokenization.api-key}")
    String apiKey;
    
    @NonFinal
    @Value("${tokenization.branch-code}")
    String branchCode;

    public TransactionGetDto toDto(TransactionGetDto transactionGetDto) {
        return TransactionGetDto.builder()
               
                .apiKey(apiKey)
                .build();
    }

  
    public TransactionGetResultDto toOutDto(TransactionGetResultDto trnGetResultDto) {
        return TransactionGetResultDto.builder()
                .amount(trnGetResultDto.getAmount())
                .currency(trnGetResultDto.getCurrency())
                .date(trnGetResultDto.getDate())
                .branchId(trnGetResultDto.getBranchId())
                .branchNumber(trnGetResultDto.getBranchNumber())
                .businessAddress(trnGetResultDto.getBusinessAddress())
                .businessCity(trnGetResultDto.getBusinessCity())
                .businessCountry(trnGetResultDto.getBusinessCountry())
                .businessId(trnGetResultDto.getBusinessId())
                .businessName(trnGetResultDto.getBusinessName())
                .businessOwnerPhone(trnGetResultDto.getBusinessOwnerPhone())
                .businessStreet(trnGetResultDto.getBusinessStreet())
                .pos(trnGetResultDto.getPos())
                .receiptPending(trnGetResultDto.getReceiptPending())
                .status(trnGetResultDto.getStatus())
                .statusName(trnGetResultDto.getStatusName())
                .timeoutDate(trnGetResultDto.getTimeoutDate())
                .TransactionNumber(trnGetResultDto.getTransactionNumber())
                .message(trnGetResultDto.getMessage())
                .response(trnGetResultDto.getResponse())
                .paymentDetails(trnGetResultDto.getPaymentDetails())
                .paymentType("Apple Pay")
                .posData(trnGetResultDto.getPosData())
                
                .build();
    }

}
