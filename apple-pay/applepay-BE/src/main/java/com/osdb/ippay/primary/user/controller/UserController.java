package com.osdb.ippay.primary.user.controller;

import com.osdb.ippay.common.domain.FileDomain;
import com.osdb.ippay.common.logging.LogExecutionTime;
import com.osdb.ippay.common.security.service.AuthenticationService;
import com.osdb.ippay.primary.user.facade.UserFacade;
import com.osdb.ippay.primary.user.facade.dto.MerchantAssignDto;
import com.osdb.ippay.primary.user.facade.dto.UserExistsDto;
import com.osdb.ippay.primary.user.facade.dto.UserStatusDto;
import com.osdb.ippay.primary.user.repository.entity.User;
import com.osdb.ippay.primary.user.service.filter.UserFilter;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.io.IOException;

import static lombok.AccessLevel.PRIVATE;

@Tag(name = "user")
@RestController
@RequestMapping(value = "/api/v1/private")
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class UserController {
/*
    AuthenticationService authenticationService;
    UserFacade userFacade;

    @LogExecutionTime
    @PutMapping("/users/{id}/merchants")
    public ResponseEntity<Void> assignMerchants(@PathVariable Long id,
                                                @RequestBody MerchantAssignDto merchantAssignDto,
                                                @Parameter(hidden = true) @AuthenticationPrincipal String email) {

        authenticationService.throwIfNotFullAdmin(email);

        userFacade.assignMerchants(id, merchantAssignDto.getMerchantIds());
        return ResponseEntity.noContent().build();
    }

    @LogExecutionTime
    @PutMapping("/users/{id}/status")
    public ResponseEntity<Void> updateStatus(@PathVariable Long id,
                                             @Valid @RequestBody UserStatusDto statusDto,
                                             @Parameter(hidden = true) @AuthenticationPrincipal String email) {

        authenticationService.throwIfNotFullAdmin(email);

        userFacade.updateStatus(id, statusDto);
        return ResponseEntity.noContent().build();
    }

    @LogExecutionTime
    @GetMapping("/users/export")
    public ResponseEntity<ByteArrayResource> export(@ParameterObject UserFilter filter,
                                                    @Parameter(hidden = true) @AuthenticationPrincipal String email)
            throws IOException {

        User authUser = authenticationService.getCurrentUser(email);
        authenticationService.throwIfNoAccessToExport(authUser);

        FileDomain response = userFacade.export(authUser, filter);

        return ResponseEntity.ok()
                .contentType(new MediaType("application", "force-download"))
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=".concat(response.getFileName()))
                .body(response.getFile());
    }

    @LogExecutionTime
    @GetMapping("/user/exists")
    public ResponseEntity<UserExistsDto> exists(@RequestParam String email,
                                                @Parameter(hidden = true) @AuthenticationPrincipal String authEmail) {

        authenticationService.throwIfNotFullAdmin(authEmail);

        UserExistsDto response = UserExistsDto.builder()
                .exists(userFacade.exists(email))
                .build();

        return ResponseEntity.ok(response);
    }*/
}
