package com.osdb.ippay.sqlserveripcore.ipwhitelist.facade.impl;

import com.osdb.ippay.common.domain.FileDomain;
import com.osdb.ippay.sqlserveripcore.ipwhitelist.facade.TerminalIpsAuditTrailRecordsFacade;
import com.osdb.ippay.sqlserveripcore.ipwhitelist.repository.entity.TerminalIpsAuditTrailRecords;
import com.osdb.ippay.sqlserveripcore.ipwhitelist.service.TerminalIpsAuditTrailRecordsService;

import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.stereotype.Component;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.Arrays;
import java.util.List;


import static lombok.AccessLevel.PRIVATE;

@Component
@RequiredArgsConstructor
@FieldDefaults(makeFinal = true, level = PRIVATE)
public class TerminalIpsAuditTrailRecordsFacadeImpl implements TerminalIpsAuditTrailRecordsFacade {

    // services
	TerminalIpsAuditTrailRecordsService terminalIpsAuditTrailRecordsService;

    // mappers
	//SettledTransactionMapper settledTransactionMapper;
	
	
	@Override
	public FileDomain export() throws IOException {
		String[] HEADERS = {
                "AUDIT_ID",
                "RECORD_ID",
                "DeletedTerminalId",
                "AddedTerminalId",
                "DeletedIPAddress",
                "AddedIPAddress",
                "DeletedStatus",
                "AddedStatus",
                "AudittrailDate",
                "NewUsername",
                "OldUsername"
        };

        final CSVFormat format = CSVFormat.Builder.create()
                .setHeader(HEADERS)
                .setIgnoreEmptyLines(true)
                .build();
        
        List<TerminalIpsAuditTrailRecords> terminalIpsAuditTrailRecordsData  = terminalIpsAuditTrailRecordsService.generateReport();
       
        try (
                ByteArrayOutputStream out = new ByteArrayOutputStream();
                CSVPrinter csvPrinter = new CSVPrinter(new PrintWriter(out), format)
        ) {
        	
            for (TerminalIpsAuditTrailRecords terminalIpsAuditTrailRecordObj : terminalIpsAuditTrailRecordsData) {
                List<Object> data = Arrays.asList(
                		terminalIpsAuditTrailRecordObj.getAuditId().toString(),
                		terminalIpsAuditTrailRecordObj.getRecordId().toString(),
                		terminalIpsAuditTrailRecordObj.getDeletedTerminalId(),
                		terminalIpsAuditTrailRecordObj.getAddedTerminalId(),
                		terminalIpsAuditTrailRecordObj.getDeletedIpAddress(),
                		terminalIpsAuditTrailRecordObj.getAddedIpAddress(),
                		terminalIpsAuditTrailRecordObj.getDeletedStatus(),
                		terminalIpsAuditTrailRecordObj.getAddedStatus(),
                		terminalIpsAuditTrailRecordObj.getAudittrailDate().toString(),
                		terminalIpsAuditTrailRecordObj.getNewUsername(),
                		terminalIpsAuditTrailRecordObj.getOldUsername()
                );

                csvPrinter.printRecord(data);
            }
            //csvPrinter.printRecord(data);
            csvPrinter.flush();

            return FileDomain.builder()
                    .file(new ByteArrayResource(new ByteArrayInputStream(out.toByteArray()).readAllBytes()))
                    .fileName(
                            String.format(
                                    "IP-Whitelist-Audit-Trail-Records.csv"
                            )
                    )
                    .build();
        }
        
	}
	
	
}
