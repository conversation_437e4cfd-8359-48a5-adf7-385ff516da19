package com.osdb.ippay.common.merchant.facade.mapper;

import com.osdb.ippay.common.merchant.facade.dto.MerchantDto;
import com.osdb.ippay.primary.user.repository.entity.UserMerchantNumber;
import com.osdb.ippay.primary.user.service.UserMerchantService;
import com.osdb.ippay.secondary.merchant.repository.entity.Merchant;
import com.osdb.ippay.secondary.merchant.repository.entity.Terminal;
import com.osdb.ippay.secondary.partner.facade.mapper.PartnerMapper;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static lombok.AccessLevel.PRIVATE;

@Component
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class MerchantMapper {

    // services
    UserMerchantService userMerchantService;

    // mappers
    PartnerMapper partnerMapper;
    BankMapper bankMapper;

    public MerchantDto toDto(Merchant merchant) {
        return MerchantDto.builder()
                .merchantId(merchant.getMerchantId())
                .ccMerchantId(merchant.getCcSetlMID())
                .achMerchantId(merchant.getAchSetlMID())
                .merchantName(merchant.getMerchantName())
                .jetpayMID(merchant.getJetpayMID())
                .achMID(merchant.getAchMID())
                .isActive(merchant.getIsActive())
                .merchantDesc(merchant.getMerchantDesc())
                .partner(partnerMapper.toDto(merchant.getPartner()))
                .bank(bankMapper.toDto(merchant.getBank()))
                .tIds(
                        CollectionUtils.isNotEmpty(merchant.getMerchantTerminals()) ?
                                merchant.getMerchantTerminals().stream()
                                        .map(merchantTerminal -> {
                                            Terminal terminal = merchantTerminal.getTerminal();
                                            return terminal != null ? terminal.getTid() : null;
                                        }).collect(Collectors.toList()) :
                                Collections.emptyList()
                ).build();
    }

    public List<MerchantDto> toDto(List<Merchant> merchants) {
        List<MerchantDto> merchantDtos = merchants.stream()
                .map(this::toDto)
                .collect(Collectors.toList());

        List<String> merchantIds = merchantDtos.stream()
                .map(MerchantDto::getMerchantId)
                .collect(Collectors.toList());

        if(CollectionUtils.isNotEmpty(merchantIds)) {
            List<UserMerchantNumber> usersAssigned = userMerchantService.countByMerchantIds(merchantIds);

            merchantDtos.forEach(merchantDto -> {
                merchantDto.setUsersNumber(0L);

                usersAssigned.stream()
                        .filter(uAOpt -> merchantDto.getMerchantId().equals(uAOpt.getMerchantId()))
                        .findFirst()
                        .ifPresent(uA -> merchantDto.setUsersNumber(uA.getAssignedUsers()));
            });
        }

        return merchantDtos;
    }

    public Page<MerchantDto> toDto(Page<Merchant> merchants) {
        Page<MerchantDto> merchantDtos = merchants.map(this::toDto);

        List<String> merchantIds = merchantDtos.stream()
                .map(MerchantDto::getMerchantId)
                .collect(Collectors.toList());

        if(CollectionUtils.isNotEmpty(merchantIds)) {
            List<UserMerchantNumber> usersAssigned = userMerchantService.countByMerchantIds(merchantIds);

            merchantDtos.forEach(merchantDto -> {
                merchantDto.setUsersNumber(0L);

                usersAssigned.stream()
                        .filter(uAOpt -> merchantDto.getMerchantId().equals(uAOpt.getMerchantId()))
                        .findFirst()
                        .ifPresent(uA -> merchantDto.setUsersNumber(uA.getAssignedUsers()));
            });
        }

        return merchantDtos;
    }
}
