package com.osdb.ippay.common.configuration;

import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.jdbc.datasource.DriverManagerDataSource;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.persistence.EntityManagerFactory;
import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Objects;

import static lombok.AccessLevel.PRIVATE;

@Configuration
@EnableTransactionManagement
@EnableJpaRepositories(
        entityManagerFactoryRef = "hppEmFactory",
        transactionManagerRef = "hppTm",
        basePackages = { "com.osdb.ippay.hpp" }
)
@RequiredArgsConstructor
@FieldDefaults(makeFinal = true, level = PRIVATE)
public class HppDataSourceConfig {

    Environment env;

    @Bean(name = "hppDataSource")
    public DataSource secondaryDataSource() {
        DriverManagerDataSource dataSource = new DriverManagerDataSource();

        String driverClassName = env.getProperty("spring.secondary-datasource.driver-class-name");

        dataSource.setDriverClassName(Objects.requireNonNull(driverClassName));
        dataSource.setUrl(env.getProperty("spring.hpp-datasource.url"));
        dataSource.setUsername(env.getProperty("spring.hpp-datasource.username"));
        dataSource.setPassword(env.getProperty("spring.hpp-datasource.password"));

        return dataSource;
    }

    @Bean(name = "hppEmFactory")
    public LocalContainerEntityManagerFactoryBean secondaryEmFactory(
            EntityManagerFactoryBuilder builder,
            @Qualifier("hppDataSource") DataSource dataSource) {

        HashMap<String, Object> properties = new HashMap<>();
        properties.put("hibernate.dialect", "org.hibernate.dialect.MySQLDialect");

        return builder
                .dataSource(dataSource)
                .packages("com.osdb.ippay.hpp")
                .persistenceUnit("hpp")
                .properties(properties)
                .build();
    }

    @Bean(name = "hppTm")
    public PlatformTransactionManager secondaryTM(
            @Qualifier("hppEmFactory") EntityManagerFactory emFactory) {

        return new JpaTransactionManager(emFactory);
    }
}
