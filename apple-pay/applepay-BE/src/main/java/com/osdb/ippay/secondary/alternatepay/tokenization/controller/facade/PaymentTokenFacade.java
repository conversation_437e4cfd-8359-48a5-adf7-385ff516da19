package com.osdb.ippay.secondary.alternatepay.tokenization.controller.facade;

import com.osdb.ippay.common.util.response.PageResponse;
import com.osdb.ippay.secondary.alternatepay.tokenization.controller.dto.PaymentTokenDto;
import com.osdb.ippay.secondary.alternatepay.tokenization.controller.dto.PaymentTokenResultDto;

import org.springframework.data.domain.Pageable;

public interface PaymentTokenFacade {

    PageResponse<PaymentTokenResultDto> find(Pageable pageable);

    PaymentTokenResultDto[] create(PaymentTokenDto paymentTokenizationInDto);

  

}
