package com.osdb.ippay.sqlserver.sftp.service.impl;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Paths;


import org.bouncycastle.openpgp.PGPSecretKeyRing;
import org.pgpainless.PGPainless;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.osdb.ippay.sqlserver.sftp.service.PGPEncryptFileUpload;
import com.osdb.ippay.sqlserver.sftp.service.PgpPainlessUtil;
import com.osdb.ippay.sqlserver.sftp.service.ErrorMessages;

@Service
public class PGPEncryptFileUploadImpl implements PGPEncryptFileUpload {

	@Value("${filePath}")
	private String filePath;
	
	@Value("${ftpScriptPath}")
	private String ftpScriptPath;
	
	@Value("${pythonScriptCommand}")
	private String pythonScriptCommand;
	
	@Value("${pythonFileName}")
	private String pythonFileName;
	
	@Value("${targetFolder}")
	private String targetFolder;
	
	@Override
	public String EncryptUpload(String mid, InputStream input, MultipartFile multipartPublicKeyFile, MultipartFile multipartPrivateKeyFile) {
		
            String retVal = "";
            
		 	boolean isEncrypt = true;

	        PGPSecretKeyRing secretKeys = null;
	        PGPSecretKeyRing pk = null;

	        try (InputStream publicKey = multipartPublicKeyFile.getInputStream();
	             InputStream privateKey = multipartPrivateKeyFile.getInputStream()) {

	            assert publicKey != null;
	            secretKeys = PGPainless.readKeyRing().secretKeyRing(publicKey); // Load public key

	            assert privateKey != null;
	            pk = PGPainless.readKeyRing().secretKeyRing(privateKey); // Load private key

	            assert secretKeys != null;
	            assert pk != null;

	        } catch (Exception e) {
	            e.printStackTrace();
	        }

	        //Encrypt/Decrypt based on the Boolean value
	        
	        if (isEncrypt) {
	        	retVal += encrypt(mid, input, filePath, secretKeys, pk);
	        }     
	      
	        if(retVal.contains(ErrorMessages.strEncryptSuccessMsg))
	        	retVal += UploadFileToSFTP();
		    
	        return retVal;
	}

	@Override
	public String encrypt(String mid, InputStream input, String filePath, PGPSecretKeyRing secretKeys, PGPSecretKeyRing pk) {
		
		String retVal = "";
		
		// Obtaining an InputStream to the resource file
		
		File folder = new File(filePath + "\\" + mid + ".csv");
		 String absoluteFilePath = folder.getAbsolutePath();
		 absoluteFilePath = absoluteFilePath.replace("\\" + targetFolder, "");
		 
        try (InputStream inputStream = input;
             OutputStream os = Files.newOutputStream(Paths.get(absoluteFilePath))) {


            if (inputStream != null) {
                PgpPainlessUtil.encryptFile(inputStream,os, secretKeys.getEncoded(),
                        pk.getPublicKey().getEncoded());

                byte[] buffer = new byte[1024];
                int length;
                // Read from the InputStream and write to the FileOutputStream
                while ((length = inputStream.read(buffer)) != -1) {
                    os.write(buffer, 0, length);
                }
                
                retVal = ErrorMessages.strEncryptSuccessMsg;
            }
            else
            	retVal = ErrorMessages.strNoRecordFoundMsg;

            
            return retVal;
            
        } catch (Exception e) {
        	retVal = ErrorMessages.strEncryptErrorMsg;
            e.printStackTrace();
            return retVal;
        }
		
	}

	
	@Override
	public String UploadFileToSFTP() {

		String retVal = "";
		 try 
		 {
			    File folder = new File(ftpScriptPath);
			    String absoluteftpScriptPath = folder.getAbsolutePath();
			    
			    absoluteftpScriptPath = pythonScriptCommand + " \"" + absoluteftpScriptPath  + "\\" + pythonFileName +"\"";
			    absoluteftpScriptPath = absoluteftpScriptPath.replace("\\" + targetFolder, "");
			
			    Process process = Runtime.getRuntime().exec(absoluteftpScriptPath);
			 	int exitCode = process.waitFor(); // Wait for the process to complete
			 	
	            retVal = ErrorMessages.strSFTPUploadSuccessMsg;
	            return retVal;
	        } catch (IOException e) {
	           
	            retVal = ErrorMessages.strSFTPUploadErrorMsg;
	            return retVal;
	        } catch (InterruptedException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
				
		        retVal = ErrorMessages.strSFTPUploadErrorMsg;
		        return retVal;
			} 
		
	}
	

}