package com.osdb.ippay.secondary.transaction.service.filter;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;

import static lombok.AccessLevel.PRIVATE;

@Data
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = PRIVATE)
public class SettlementFilter {

    @NotBlank(message = "Missing required parameter: 'merchantId'.")
    String merchantId;

    @NotNull(message = "Missing required parameter: 'fromDate'.")
    @Parameter(example = "2017-01-25")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    LocalDate fromDate;

    @NotNull(message = "Missing required parameter: 'toDate'.")
    @Parameter(example = "2020-07-16")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    LocalDate toDate;

    public String getFromDateStr() {
        return String.format("%s %s", getFromDate(), "00:00:00");
    }

    public String getToDateStr() {
        return String.format("%s %s", getToDate(), "23:59:59");
    }

}
