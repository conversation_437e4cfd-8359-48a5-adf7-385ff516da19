package com.osdb.ippay.secondary.reports.service;

import com.osdb.ippay.secondary.reports.repository.entity.MonthlyCreditCardReport;

import java.util.List;


import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface MonthlyCreditCardReportService {
    
    List generateReport(String startDateTime, String endDateTime);
    public Page<MonthlyCreditCardReport> find(String startDateTime,
			String endDateTime,
			Pageable pageable);

}
