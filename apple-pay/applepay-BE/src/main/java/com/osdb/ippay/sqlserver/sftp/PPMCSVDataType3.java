package com.osdb.ippay.sqlserver.sftp;

import static lombok.AccessLevel.PRIVATE;

import com.opencsv.bean.CsvBindByPosition;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

@Getter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class PPMCSVDataType3 {
    @CsvBindByPosition(position = 0)
    private String bankID;

    @CsvBindByPosition(position = 1)
    private String rpAgentId;

    @CsvBindByPosition(position = 2)
    private String groupId;

    @CsvBindByPosition(position = 3)
    private String associationId;
    
    @CsvBindByPosition(position = 4)
    private String merchantId;

    @CsvBindByPosition(position = 5)
    private String recordType;

    @CsvBindByPosition(position = 6)
    private String feeCategoryDescription;

    @CsvBindByPosition(position = 7)
    private String feeCategory;
    
    @CsvBindByPosition(position = 8)
    private String feeItemName;
    
    @CsvBindByPosition(position = 9)
    private String feeItemHiararchyLevel;

    @CsvBindByPosition(position = 10)
    private String startDate;

    @CsvBindByPosition(position = 11)
    private String stopDate;

    @CsvBindByPosition(position = 12)
    private String freqFlagJan;
    
    @CsvBindByPosition(position = 13)
    private String freqFlagFeb;

    @CsvBindByPosition(position = 14)
    private String freqFlagMar;
    
    @CsvBindByPosition(position = 15)
    private String freqFlagApr;

    @CsvBindByPosition(position = 16)
    private String freqFlagMay;
    
    @CsvBindByPosition(position = 17)
    private String freqFlagJune;

    @CsvBindByPosition(position = 18)
    private String freqFlagJuly;
    
    @CsvBindByPosition(position = 19)
    private String freqFlagAug;

    @CsvBindByPosition(position = 20)
    private String freqFlagJanSep;

    @CsvBindByPosition(position = 21)
    private String freqFlagOct;
    
    @CsvBindByPosition(position = 22)
    private String freqFlagNov;

    @CsvBindByPosition(position = 23)
    private String freqFlagDec;

    @CsvBindByPosition(position = 24)
    private String reserved1;

    @CsvBindByPosition(position = 25)
    private String dailyFeeIndicator;
    
    @CsvBindByPosition(position = 26)
    private String billingMethod;
    
    @CsvBindByPosition(position = 27)
    private String incoemRateGroupName;

  
 
}