package com.osdb.ippay.secondary.alternatepay.transaction.controller.facade;

import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionCommitDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionGetResultDto;



public interface TransactionCommitGetFacade {

	TransactionGetResultDto create(String trnId, TransactionCommitDto transactionCommitDto);
}
