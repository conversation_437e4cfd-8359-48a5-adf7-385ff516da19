package com.osdb.ippay.secondary.alternatepay.payment.controller.facade;

import com.osdb.ippay.secondary.alternatepay.payment.controller.dto.PaymentSaveDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionSaveDto;
import com.osdb.ippay.secondary.alternatepay.payment.controller.dto.PaymentResultDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionGetResultDto;

public interface PaymentResponseFacade {
  
	PaymentSaveDto create(PaymentResultDto paymentSaveDto);
	TransactionSaveDto createTransaction(PaymentResultDto paymentSaveDto);
	PaymentSaveDto findByTrnid(String Trnid);
	PaymentSaveDto updateToken(TransactionGetResultDto transactionGetResultDto, String Trnid);
	TransactionSaveDto findByTrnNumber(String Trnid);
	TransactionSaveDto updateAddress(TransactionGetResultDto transactionGetResultDto, String Trnid);
	
    }