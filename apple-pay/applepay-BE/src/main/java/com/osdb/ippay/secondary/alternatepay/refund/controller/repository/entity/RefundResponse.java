package com.osdb.ippay.secondary.alternatepay.refund.controller.repository.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;
import lombok.experimental.FieldDefaults;

import static lombok.AccessLevel.PRIVATE;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "AlternatepayRefundData")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)

public class RefundResponse {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    Long RefundId;
    
    @Column(name = "RefundConfirmation")
    String refundConfirmation;
  
    @Column(name = "ApprovalDate")
    String dateApproval;
    
    @Column(name = "Result")
    String result;
    
    @Column(name = "RefundAmount")
    int amount;
    
    @Column(name = "ProviderId")
    String providerId;
    
    @Column(name = "ApprovedBy")
    String approvedBy;
    
    @Column(name = "RefundBy")
    String refundBy;
    
    @Column(name = "TransactionNumber")
    String TransactionNumber;
    
    @Column(name = "Date")
    String date;
    
    @Column(name = "TransactionAmount")
    int transactionAmount;
    
    @Column(name = "Status")
    int status;
    
    @Column(name = "Currency")
    String currency;
    
    @Column(name = "BusinessId")
    String businessId;
    
    @Column(name = "ConfirmationNumber")
    String confirmationNumber;
    
    @Column(name = "Type")
    int type;
    

}
