package com.osdb.ippay.secondary.alerts.facade.mapper;

import com.osdb.ippay.secondary.alerts.facade.dto.ThresholdSettingDto;
import com.osdb.ippay.secondary.alerts.repository.entity.ThresholdSetting;
import org.springframework.stereotype.Component;

@Component
public class ThresholdSettingMapper {

    public ThresholdSettingDto toDto(ThresholdSetting thresholdSetting) {
        if (thresholdSetting == null) {
            return null;
        }
        return ThresholdSettingDto.builder()
                .alertType(thresholdSetting.getAlertType())
                .monthlySettledLimitAmount(thresholdSetting.getMonthlySettledLimitAmount())
                .monthlySettledLimitNumber(thresholdSetting.getMonthlySettledLimitNumber())
                .triggerAlertOne(thresholdSetting.getTriggerAlertOne())
                .triggerAlertTwo(thresholdSetting.getTriggerAlertTwo())
                .triggerAlertThree(thresholdSetting.getTriggerAlertThree())
                .alertToSupport(thresholdSetting.getAlertToSupport())
                .alertToRisk(thresholdSetting.getAlertToRisk())
                .alertToMerchant(thresholdSetting.getAlertToMerchant())
                .alertToPartner(thresholdSetting.getAlertToPartner())
                .triggerAlertTime(thresholdSetting.getTriggerAlertTime())
                .mid(thresholdSetting.getMid())
                .build();
    }

    public ThresholdSetting toEntity(ThresholdSettingDto thresholdSettingDto) {
        if (thresholdSettingDto == null) {
            return null;
        }
        ThresholdSetting thresholdSetting = new ThresholdSetting();
        thresholdSetting.setAlertType(thresholdSettingDto.getAlertType());
        thresholdSetting.setMonthlySettledLimitAmount(thresholdSettingDto.getMonthlySettledLimitAmount());
        thresholdSetting.setMonthlySettledLimitNumber(thresholdSettingDto.getMonthlySettledLimitNumber());
        thresholdSetting.setTriggerAlertOne(thresholdSettingDto.getTriggerAlertOne());
        thresholdSetting.setTriggerAlertTwo(thresholdSettingDto.getTriggerAlertTwo());
        thresholdSetting.setTriggerAlertThree(thresholdSettingDto.getTriggerAlertThree());
        thresholdSetting.setAlertToSupport(thresholdSettingDto.getAlertToSupport());
        thresholdSetting.setAlertToRisk(thresholdSettingDto.getAlertToRisk());
        thresholdSetting.setAlertToMerchant(thresholdSettingDto.getAlertToMerchant());
        thresholdSetting.setAlertToPartner(thresholdSettingDto.getAlertToPartner());
        thresholdSetting.setTriggerAlertTime(thresholdSettingDto.getTriggerAlertTime());
        thresholdSetting.setMid(thresholdSettingDto.getMid());
        
        return thresholdSetting;
    }

    public ThresholdSettingDto putEntity(
            ThresholdSettingDto thresholdSettingDto,
            ThresholdSetting thresholdSetting) {
        thresholdSetting.setAlertType(thresholdSettingDto.getAlertType());
        thresholdSetting.setMonthlySettledLimitAmount(thresholdSettingDto.getMonthlySettledLimitAmount());
        thresholdSetting.setMonthlySettledLimitNumber(thresholdSettingDto.getMonthlySettledLimitNumber());
        thresholdSetting.setTriggerAlertOne(thresholdSettingDto.getTriggerAlertOne());
        thresholdSetting.setTriggerAlertTwo(thresholdSettingDto.getTriggerAlertTwo());
        thresholdSetting.setTriggerAlertThree(thresholdSettingDto.getTriggerAlertThree());
        thresholdSetting.setAlertToSupport(thresholdSettingDto.getAlertToSupport());
        thresholdSetting.setAlertToRisk(thresholdSettingDto.getAlertToRisk());
        thresholdSetting.setAlertToMerchant(thresholdSettingDto.getAlertToMerchant());
        thresholdSetting.setAlertToPartner(thresholdSettingDto.getAlertToPartner());
        thresholdSetting.setTriggerAlertTime(thresholdSettingDto.getTriggerAlertTime());
        thresholdSetting.setMid(thresholdSettingDto.getMid());
        
        return toDto(thresholdSetting);
    }

}