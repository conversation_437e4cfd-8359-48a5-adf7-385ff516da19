package com.osdb.ippay.secondary.alternatepay.payment.controller.dto;

import lombok.*;
import lombok.experimental.FieldDefaults;

import static lombok.AccessLevel.PRIVATE;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class PaymentConfirmationDto {

    
    String approvedBy;
  
    String approvalDate;

    String paymentType;

    String paymentMethodCode;
    
    String paymentMethodName;
    
    String providerId;
    
    String requestID;
    
    String sensePassReferenceId;
    
    String amount;
    
    String confirmationNumber;
        
    String providerName;
    
    String paymentMethod;
    
    String cardType;
        
    String lastFourDigits;
    
    String cardMask;
    

}
