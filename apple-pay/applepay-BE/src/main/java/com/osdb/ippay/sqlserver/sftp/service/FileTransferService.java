package com.osdb.ippay.sqlserver.sftp.service;

import java.io.IOException;
import java.util.List;
import java.util.Map;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import com.osdb.ippay.common.domain.FileDomain;
import com.osdb.ippay.sqlserver.reports.facade.dto.TotalACHGtwDataDto;
import com.osdb.ippay.sqlserver.reports.service.filter.MonthlyResidualReportsFilter;
import com.osdb.ippay.sqlserver.reports.service.filter.TotalACHGtwDataFilter;

public interface FileTransferService {
	
	//boolean uploadFile(String localFilePath, String remoteFilePath);
	
	//boolean downloadFile(String localFilePath, String remoteFilePath);
	//void generateMinDiscountMapData(String localFilePath) throws IOException;
	Page<Map> findData(MonthlyResidualReportsFilter filter, Pageable pageable);
	
	void generateSummaryMapData(String localFilePath) throws IOException;
	Page<Map> findSummaryData(MonthlyResidualReportsFilter filter, Pageable pageable);
	
	void generateFeeItemDetailMapData(String localFilePath) throws IOException;
	Page<Map> findFeeItemDetailData(MonthlyResidualReportsFilter filter, Pageable pageable);
	
	FileDomain exportMonthlyResidualReport(String localFilePath, MonthlyResidualReportsFilter filter) throws IOException;
	FileDomain exportMonthlyResidualReportType2(String localFilePath, MonthlyResidualReportsFilter filter) throws IOException;
	FileDomain exportMonthlyResidualReportType3(String localFilePath, MonthlyResidualReportsFilter filter) throws IOException;
	FileDomain exportMonthlyResidualReportType4(String localFilePath, MonthlyResidualReportsFilter filter) throws IOException;
	FileDomain exportPPMReport(String localFilePath, MonthlyResidualReportsFilter filter) throws IOException;
	FileDomain exportPPMReportType2(String localFilePath, MonthlyResidualReportsFilter filter) throws IOException;
	FileDomain exportPPMReportType3(String localFilePath, MonthlyResidualReportsFilter filter) throws IOException;
	FileDomain exportPPMReportType4(String localFilePath, MonthlyResidualReportsFilter filter) throws IOException;
	FileDomain exportPPMReportType5(String localFilePath, MonthlyResidualReportsFilter filter) throws IOException;
	FileDomain minimumDiscountReport(String localFilePath, MonthlyResidualReportsFilter filter) throws IOException;
	FileDomain feeItemDetailReport(String localFilePath, MonthlyResidualReportsFilter filter) throws IOException;
	FileDomain feeItemSummaryReport(String localFilePath, MonthlyResidualReportsFilter filter) throws IOException;
}