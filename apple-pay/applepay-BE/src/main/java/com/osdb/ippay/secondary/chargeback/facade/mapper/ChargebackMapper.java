package com.osdb.ippay.secondary.chargeback.facade.mapper;

import com.osdb.ippay.secondary.chargeback.facade.dto.ChargebackDto;
import com.osdb.ippay.secondary.chargeback.repository.entity.Chargeback;
import com.osdb.ippay.secondary.merchant.repository.entity.Merchant;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;

@Component
public class ChargebackMapper {

    public Page<ChargebackDto> toDto(Page<Chargeback> chargebacks) {
        return chargebacks.map(this::toDto);
    }

    public ChargebackDto toDto(Chargeback chargeback) {
        Merchant merchant = chargeback.getMerchant();

        return ChargebackDto.builder()
                .id(chargeback.getId())
                .merchantName(merchant != null ? merchant.getMerchantName() : null)
                .merchantId(merchant != null ? merchant.getMerchantId() : null)
                .cardType(chargeback.getCardType())
                .creditCard(chargeback.getCreditCard())
                .caseNumber(chargeback.getCaseNumber())
                .amount(chargeback.getAmount())
                .reason(chargeback.getReason())
                .resolutionTo(chargeback.getResolutionTo())
                .debitCredit(chargeback.getDebitCredit())
                .type(chargeback.getType())
                .originRef(chargeback.getOriginRef())
                .dateTransaction(chargeback.getDateTransaction())
                .dateResolved(chargeback.getDateResolved())
                .build();
    }
}
