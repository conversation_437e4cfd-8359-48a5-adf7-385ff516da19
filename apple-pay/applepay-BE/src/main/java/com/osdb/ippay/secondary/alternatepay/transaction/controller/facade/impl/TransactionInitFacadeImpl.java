package com.osdb.ippay.secondary.alternatepay.transaction.controller.facade.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionInitDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TrasactionInitResultDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.facade.*;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.mapper.TransactionInitControllerMapper;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.Base64;
import java.util.Objects;

import static lombok.AccessLevel.PRIVATE;

@Component
@RequiredArgsConstructor
@FieldDefaults(makeFinal = true, level = PRIVATE)
public class TransactionInitFacadeImpl implements TransactionInitFacade {

    public static final String INVALID_JOB_STATE = "Job can not be deleted, job state not 'Queued'";

    TransactionInitControllerMapper mapper;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    @NonFinal
    @Value("${transaction-init.url.get}")
    String transactionUrlGet;


    @NonFinal
    @Value("${transaction-init.url.post}")
    String transactionUrlPost;
 
    @NonFinal
    boolean blnIsDecoded = true;
    
	public TrasactionInitResultDto create(TransactionInitDto transactionInitInDto) {

		RestTemplate restTemplate = new RestTemplate();

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
    
        String objJackson = "";
        
        try {
			   objJackson = objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(transactionInitInDto);
			 
			 
		} catch (JsonProcessingException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
               
       
        HttpEntity<String> entity = new HttpEntity<String>(objJackson, headers);
        
        if(!blnIsDecoded)
        {
        	byte[] decodedBytes = Base64.getDecoder().decode(transactionUrlPost);
            transactionUrlPost = new String(decodedBytes);
        }
        
        
        TrasactionInitResultDto trnInitResultDto = restTemplate.exchange(
        		transactionUrlPost,
                HttpMethod.POST,
                entity,
                new ParameterizedTypeReference<TrasactionInitResultDto>() {
                },
                ""
        ).getBody();

        return Objects.nonNull(trnInitResultDto) ? mapper.toOutDto(trnInitResultDto) : null;
        
		//return null;
	}
}
