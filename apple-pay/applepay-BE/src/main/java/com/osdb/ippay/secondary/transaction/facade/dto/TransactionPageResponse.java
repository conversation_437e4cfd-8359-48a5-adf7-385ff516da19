package com.osdb.ippay.secondary.transaction.facade.dto;

import com.osdb.ippay.common.util.response.PageResponse;

import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.SuperBuilder;

import java.util.List;

import static lombok.AccessLevel.PRIVATE;

@Getter
@NoArgsConstructor
@FieldDefaults(level = PRIVATE)

public class TransactionPageResponse<T> extends PageResponse<T> {

    String totalAmount;

    public TransactionPageResponse(List<T> result, long total, String totalAmount) {
       // super(result, total);
        this.totalAmount = totalAmount;
    }
}
