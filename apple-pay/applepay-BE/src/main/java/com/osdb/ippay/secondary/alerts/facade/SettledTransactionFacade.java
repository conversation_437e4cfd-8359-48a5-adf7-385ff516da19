package com.osdb.ippay.secondary.alerts.facade;

import com.osdb.ippay.common.domain.FileDomain;
import com.osdb.ippay.secondary.alerts.facade.dto.SettledTransactionDto;
import com.osdb.ippay.secondary.alerts.service.filter.SettledTransactionFilter;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Map;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;


public interface SettledTransactionFacade {
	
	Map<String, ByteArrayOutputStream> findMerchantsDataToExport(String startDateTime, String endDateTime, SettledTransactionFilter filter)
			throws IOException;
			
    FileDomain export(String startDateTime, String endDateTime,  SettledTransactionFilter filter) throws IOException;
    //Page<SettledTransactionDto> findData(String authStartDateTime, String authEndDateTime, SettledTransactionFilter filter, Pageable pageable);
    //Page<TotalCardDataGtwReportsDto> find(String authStartDateTime, String authEndDateTime,  TotalCardDataGtwReportsFilter filter, Pageable pageable);
}