package com.osdb.ippay.secondary.merchant.service.impl;

import com.osdb.ippay.common.domain.FileDomain;
import com.osdb.ippay.common.exception.business.NotFoundException;
import com.osdb.ippay.primary.user.repository.entity.User;
import com.osdb.ippay.primary.user.service.UserMerchantService;
import com.osdb.ippay.secondary.merchant.repository.MerchantRepository;
import com.osdb.ippay.secondary.merchant.repository.entity.Bank;
import com.osdb.ippay.secondary.merchant.repository.entity.Merchant;
import com.osdb.ippay.secondary.merchant.repository.entity.Terminal;
import com.osdb.ippay.secondary.merchant.service.MerchantService;
import com.osdb.ippay.secondary.merchant.service.filter.MerchantFilter;
import com.osdb.ippay.secondary.partner.repository.entity.Partner;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.PrintWriter;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static com.osdb.ippay.common.exception.ErrorMessage.MERCHANT_ID_NOT_FOUND;
import static java.lang.Boolean.TRUE;
import static lombok.AccessLevel.PRIVATE;
import static org.apache.commons.collections.CollectionUtils.isEmpty;
import static org.apache.commons.collections.CollectionUtils.isNotEmpty;
import static org.apache.commons.lang3.StringUtils.EMPTY;

@Service
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class MerchantServiceImpl implements MerchantService {

    // repositories
    MerchantRepository merchantRepository;

    // services
    UserMerchantService userMerchantService;

    @Override
    public Page<Merchant> find(User authUser, MerchantFilter filter, Pageable pageable) {
        Specification<Merchant> specification = filterBy(authUser, filter);
        return merchantRepository.findAll(specification, pageable);
    }

    @Override
    public List<Merchant> find(User authUser, MerchantFilter filter, Sort sort) {
        Specification<Merchant> specification = filterBy(authUser, filter);
        return merchantRepository.findAll(specification, sort);
    }

    @Override
    public List<Merchant> find(User authUser, MerchantFilter filter) {
        Specification<Merchant> specification = filterBy(authUser, filter);
        return merchantRepository.findAll(specification);
    }

    @Override
    public List<Merchant> find(Long partnerId) {
        return merchantRepository.findAllByPartnerId(partnerId);
    }

    @Override
    public List<Merchant> find(List<String> merchantIds) {
        return merchantRepository.findAllByMerchantIdIn(merchantIds);
    }

    @Override
    public Merchant find(String merchantId) {
        return merchantRepository
                .findByMerchantId(merchantId)
                .orElseThrow(() -> new NotFoundException(MERCHANT_ID_NOT_FOUND));
    }

    @Override
    public Merchant findByAchMerchantId(String merchantId) {
        return merchantRepository
                .findByAchSetlMID(merchantId)
                .orElseThrow(() -> new NotFoundException(MERCHANT_ID_NOT_FOUND));
    }

    @Override
    public Merchant findByCCMerchantId(String merchantId) {
        return merchantRepository
                .findByCcSetlMID(merchantId)
                .orElseThrow(() -> new NotFoundException(MERCHANT_ID_NOT_FOUND));
    }

    @Override
    public FileDomain export(User authUser, MerchantFilter filter) throws IOException {
        List<Merchant> merchants = find(authUser, filter);
        ByteArrayInputStream byteArrayInputStream = export(merchants);

        return FileDomain.builder()
                .file(new ByteArrayResource(byteArrayInputStream.readAllBytes()))
                .fileName(String.format("exported_merchants_%s.csv", LocalDate.now()))
                .build();
    }

    @Override
    public Integer countBy(Long partnerId) {
        return merchantRepository.countByPartnerId(partnerId);
    }

    private Specification<Merchant> filterBy(User authUser, MerchantFilter filter) {
        return (r, rq, cb) ->  {
            final String merchantId = "merchantId";
            final String merchantName = "merchantName";
            final String jetpayMID = "jetpayMID";

            Predicate searchPredicate = StringUtils.isNotBlank(filter.getSearch()) ?
                    cb.or(
                            cb.like(cb.lower(r.get(merchantId)), "%" + filter.getSearch().toLowerCase() + "%"),
                            cb.like(cb.lower(r.get(merchantName)), "%" + filter.getSearch().toLowerCase() + "%"),
                            cb.like(cb.lower(r.get(jetpayMID)), "%" + filter.getSearch().toLowerCase() + "%")
                    ) :
                    cb.conjunction();

            Predicate extendedSearchPredicate = StringUtils.isNotBlank(filter.getExtendedSearch()) ?
                    cb.or(
                            cb.like(cb.lower(r.get(merchantId)), "%" + filter.getExtendedSearch().toLowerCase() + "%"),
                            cb.like(cb.lower(r.get(merchantName)), "%" + filter.getExtendedSearch().toLowerCase() + "%"),
                            cb.like(cb.lower(r.get(jetpayMID)), "%" + filter.getExtendedSearch().toLowerCase() + "%"),
                            cb.like(
                                    cb.lower(r.join("partner").get("partnerName").as(String.class)),
                                    "%" + filter.getExtendedSearch().toLowerCase() + "%"
                            ),
                            cb.like(
                                    cb.lower(
                                            r
                                                    .join("merchantTerminals", JoinType.LEFT)
                                                    .join("terminal", JoinType.LEFT)
                                                    .get("tid")
                                                    .as(String.class)
                                    ),
                                    "%" + filter.getExtendedSearch().toLowerCase() + "%"
                            )
                    ) :
                    cb.conjunction();

            Predicate partnerIdPredicate = filter.getPartnerId() != null ?
                    cb.equal(r.get("partnerId"), filter.getPartnerId()) :
                    cb.conjunction();

            Predicate bankIdPredicate = filter.getBankId() != null ?
                    cb.equal(r.get("bankId"), filter.getBankId()) :
                    cb.conjunction();

            Predicate isActivePredicate = filter.getIsActive() != null ?
                    cb.equal(r.get("isActive"), filter.getIsActive()) :
                    cb.conjunction();

            Predicate merchantIdsPredicate = isNotEmpty(filter.getMerchantIds()) && authUser.isNotAdmin() ?
                    cb.in(r.get(merchantId)).value(filter.getMerchantIds()) :
                    cb.conjunction();

            if(isEmpty(filter.getMerchantIds()) && authUser.isNotAdmin()) {
                merchantIdsPredicate = cb.disjunction();
            }

            return cb.and(
                    searchPredicate,
                    partnerIdPredicate,
                    isActivePredicate,
                    extendedSearchPredicate,
                    merchantIdsPredicate,
                    bankIdPredicate
            );
        };
    }

    private ByteArrayInputStream export(List<Merchant> merchants) throws IOException {
        String[] headers = {
                "MID",
                "Merchant Name",
                "Bank",
                "Jetpay MID",
                "ACH MID",
                "TIDs",
                "Partner",
                "Status",
                "Users"
        };

        final CSVFormat format = CSVFormat.Builder.create()
                .setHeader(headers)
                .setIgnoreEmptyLines(true)
                .build();

        try (
                ByteArrayOutputStream out = new ByteArrayOutputStream();
                CSVPrinter csvPrinter = new CSVPrinter(new PrintWriter(out), format)
        ) {

            for (Merchant merchant : merchants) {
                Bank bank = merchant.getBank();
                Partner partner = merchant.getPartner();
                Long usersNumber = userMerchantService.countByMerchant(merchant.getMerchantId());

                List<String> data = Arrays.asList(
                        merchant.getMerchantId(),
                        merchant.getMerchantName(),
                        bank != null ? bank.getBankName() : EMPTY,
                        merchant.getJetpayMID(),
                        merchant.getAchMID(),
                        isNotEmpty(merchant.getMerchantTerminals()) ?
                                merchant.getMerchantTerminals().stream()
                                        .map(merchantTerminal -> {
                                            Terminal terminal = merchantTerminal.getTerminal();
                                            return terminal != null ? terminal.getTid() : null;
                                        }).collect(Collectors.joining(",")) :
                                EMPTY,
                        partner != null ? partner.getPartnerName() : EMPTY,
                        TRUE.equals(merchant.getIsActive()) ? "Active" : "Inactive",
                        String.valueOf(usersNumber)
                );

                csvPrinter.printRecord(data);
            }

            csvPrinter.flush();
            return new ByteArrayInputStream(out.toByteArray());
        }
    }
}
