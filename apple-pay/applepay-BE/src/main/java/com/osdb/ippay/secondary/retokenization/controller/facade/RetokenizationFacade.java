package com.osdb.ippay.secondary.retokenization.controller.facade;

import com.osdb.ippay.common.util.response.PageResponse;
import com.osdb.ippay.secondary.retokenization.controller.dto.RetokenizationInDto;
import com.osdb.ippay.secondary.retokenization.controller.dto.RetokenizationOutDto;
import com.osdb.ippay.secondary.retokenization.controller.dto.RetokenizationPatchDto;
import com.osdb.ippay.secondary.retokenization.controller.dto.RetokenizationResultDto;
import org.springframework.data.domain.Pageable;

public interface RetokenizationFacade {

    PageResponse<RetokenizationOutDto> find(Pageable pageable);

    RetokenizationResultDto findById(Integer id);

    RetokenizationOutDto create(RetokenizationInDto retokenizationInDto);

    RetokenizationOutDto patch(RetokenizationPatchDto retokenizationPatchDto, Integer id);

    RetokenizationOutDto put(RetokenizationInDto retokenizationInDto, Integer id);

    void delete(Integer id);

}
