package com.osdb.ippay.sqlserver.reports.facade.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;


import static lombok.AccessLevel.PRIVATE;

@Getter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class TotalCardGtwDataDto {

	Long ipTransactionId;
	
    String merchantID;
    
    String transactionID;
    
    String merchantDBAName;

    String ccProcessor;

    String requestType;

    String actionCode;

 //   String transactionCount;

    String totalAmount;
    
    String authDateTime;


}