package com.osdb.ippay.sqlserver.reports.service.impl;

import com.osdb.ippay.sqlserver.reports.repository.MinimumDiscountDataRepository;
import com.osdb.ippay.sqlserver.reports.service.MinimumDiscountDataService;

import lombok.experimental.FieldDefaults;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.persistence.EntityManager;

import static lombok.AccessLevel.PRIVATE;

@Service
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class MinimumDiscountDataServiceImpl implements MinimumDiscountDataService {

	MinimumDiscountDataRepository minimumDiscountDataRepository;
	EntityManager entityManager;
	  
	  @Autowired
	    public MinimumDiscountDataServiceImpl(MinimumDiscountDataRepository minDiscountDataRepository,
	    		@Qualifier("sqlserverEmFactory") EntityManager entityManager) {

	       
			this.minimumDiscountDataRepository = minDiscountDataRepository;
			this.entityManager = entityManager;
	    }
	
	@Override
	public void insertMinimumDiscountData(String bankId, String groupId, String associationId, String merchantId,
			String merchantDBAName, String minDiscount, String minDiscountGLCode, String minDiscountUserDataCode,
			String startDate, String stopDate, String billingMethod) {

		minimumDiscountDataRepository.insertMinimumDiscountData(bankId, groupId, associationId, merchantId, merchantDBAName, minDiscount, minDiscountGLCode, minDiscountUserDataCode, startDate, stopDate, billingMethod);
		
	}
}
