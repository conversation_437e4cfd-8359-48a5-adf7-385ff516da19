package com.osdb.ippay.common.configuration;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.parameters.Parameter;
import io.swagger.v3.oas.models.servers.Server;
import org.springdoc.core.customizers.OperationCustomizer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Collections;

import static io.swagger.v3.oas.annotations.enums.ParameterIn.HEADER;


@Configuration
public class SpringDocConfig {

    @Value(value = "${springdoc.server.url}")
    String serverURL;

    @Bean
    public OpenAPI springShopOpenAPI() {

        return new OpenAPI()
                .info(new Info()
                        .title("IPPAY APIs")
                        .version("2.0.0"))
                .servers(Collections.singletonList(new Server().url(serverURL)));
    }

    @Bean
    public OperationCustomizer customize() {
        return (operation, handlerMethod) -> {
            boolean isNotGet = !handlerMethod.getMethod().getName().contains("get");
            boolean isNotExport = !handlerMethod.getMethod().getName().contains("export");
            boolean isNotExists = !handlerMethod.getMethod().getName().contains("exists");

            if(isNotGet && isNotExport && isNotExists) {
                return operation.addParametersItem(
                        new Parameter()
                                .in(HEADER.toString())
                                .required(false)
                                .name("X-XSRF-TOKEN"));
            }

            return null;
        };
    }
}
