package com.osdb.ippay.secondary.alternatepay.transaction.controller.facade.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionGetDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionGetResultDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionInitDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TrasactionInitResultDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.facade.*;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.mapper.TransactionGetControllerMapper;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.mapper.TransactionInitControllerMapper;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.Base64;
import java.util.Objects;

import static lombok.AccessLevel.PRIVATE;

@Component
@RequiredArgsConstructor
@FieldDefaults(makeFinal = true, level = PRIVATE)
public class TransactionCommitFacadeImpl implements TransactionCommitFacade {

    public static final String INVALID_JOB_STATE = "Job can not be deleted, job state not 'Queued'";

    TransactionGetControllerMapper mapper;

    @NonFinal
    @Value("${transaction-commit.url.get}")
    String transactionCommitUrlGet;

    @NonFinal
    boolean blnIsDecoded = true;
    
    
	@Override
	public TransactionGetResultDto create(String trnId) {
		RestTemplate restTemplate = new RestTemplate();

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        if(!blnIsDecoded)
        {
        	byte[] decodedBytes = Base64.getDecoder().decode(transactionCommitUrlGet);
            transactionCommitUrlGet = new String(decodedBytes);
        }
        
        
        TransactionGetResultDto trnGetResultDto = restTemplate.exchange(
        		transactionCommitUrlGet + trnId,
                HttpMethod.GET,
                null,
                new ParameterizedTypeReference<TransactionGetResultDto>() {
                },
                ""
        ).getBody();

        return Objects.nonNull(trnGetResultDto) ? mapper.toOutDto(trnGetResultDto) : null;
	}
}
