package com.osdb.ippay.sqlserveripcore.ipwhitelist.facade.mapper;

import com.osdb.ippay.sqlserveripcore.ipwhitelist.facade.dto.TerminalIpsAuditTrailRecordsDto;
import com.osdb.ippay.sqlserveripcore.ipwhitelist.repository.entity.TerminalIpsAuditTrailRecords;


import org.springframework.stereotype.Component;

@Component
public class TerminalIpsAuditTrailRecordsMapper {

    public TerminalIpsAuditTrailRecordsDto toDto(TerminalIpsAuditTrailRecords terminalIpsBlocking) {
        if (terminalIpsBlocking == null) {
            return null;
        }
        return TerminalIpsAuditTrailRecordsDto.builder()
        		.auditId(terminalIpsBlocking.getAuditId())
        		.recordId(terminalIpsBlocking.getRecordId())
                .addedIpAddress(terminalIpsBlocking.getAddedIpAddress())
                .deletedIpAddress(terminalIpsBlocking.getDeletedIpAddress())
                .addedTerminalId(terminalIpsBlocking.getAddedTerminalId())
                .deletedTerminalId(terminalIpsBlocking.getDeletedTerminalId())
                .addedStatus(terminalIpsBlocking.getAddedStatus())
                .deletedStatus(terminalIpsBlocking.getDeletedStatus())
                .audittrailDate(terminalIpsBlocking.getAudittrailDate())
                .oldUsername(terminalIpsBlocking.getOldUsername())
                .newUsername(terminalIpsBlocking.getNewUsername())
                .build();
    }

   
}