package com.osdb.ippay.sqlserver.sftp.service;

import java.io.InputStream;

import org.bouncycastle.openpgp.PGPSecretKeyRing;
import org.pgpainless.PGPainless;
import org.springframework.web.multipart.MultipartFile;

public interface PGPEncryptFileUpload {
	
	String EncryptUpload(String mid, InputStream inputStream, MultipartFile multipartPublicKeyFile, MultipartFile multipartPrivateKeyFile);
	String encrypt(String mid, InputStream inputStream, String filePath, PGPSecretKeyRing secretKeys, PGPSecretKeyRing pk);
	String UploadFileToSFTP();
}