package com.osdb.ippay.secondary.alternatepay.refund.controller;

import com.osdb.ippay.secondary.alternatepay.refund.controller.dto.MerchantPartialRefundDto;
import com.osdb.ippay.secondary.alternatepay.refund.controller.dto.MerchantRefundDto;
import com.osdb.ippay.secondary.alternatepay.refund.controller.dto.TransactionRefundResultDto;
import com.osdb.ippay.secondary.alternatepay.refund.controller.facade.MerchantRefundFacade;
import com.osdb.ippay.secondary.alternatepay.refund.controller.mapper.TransactionRefundControllerMapper;
import com.osdb.ippay.secondary.alternatepay.refund.controller.facade.RefundResponseFacade;

import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import static lombok.AccessLevel.PRIVATE;

@Tag(name = "Alternatepay/Refund")
@RestController
@RequestMapping(value = "/api/v2/payment/alternatepay")
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class TransactionRefundController {

	MerchantRefundFacade merchantRefundFacade; 
	RefundResponseFacade refundResponseFacade;

    /*@PostMapping("/refund")
    public ResponseEntity<TransactionRefundResultDto> CreateRefund(@Valid @RequestBody MerchantRefundDto merchantRefundDto) {
    	TransactionRefundResultDto response = merchantRefundFacade.create(merchantRefundDto);
        return ResponseEntity.ok(response);
    }*/
    
    @PostMapping("/refund")
    public ResponseEntity<TransactionRefundResultDto> CreatePartialRefund(@Valid @RequestBody MerchantPartialRefundDto merchantRefundDto) {
    	TransactionRefundResultDto response = merchantRefundFacade.createPartial(merchantRefundDto);
    	refundResponseFacade.create(response);
    	refundResponseFacade.update(response, merchantRefundDto.getTransactionNumber());
        return ResponseEntity.ok(response);
    }

  

}
