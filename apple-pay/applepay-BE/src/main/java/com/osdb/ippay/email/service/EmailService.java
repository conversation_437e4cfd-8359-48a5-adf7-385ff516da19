package com.osdb.ippay.email.service;

import java.io.IOException;

import javax.mail.MessagingException;

import org.springframework.core.io.ByteArrayResource;

import com.osdb.ippay.email.bean.Message;
import com.sendgrid.helpers.mail.objects.Attachments;

public interface EmailService {

	
    void sendEmail(Message message);

    void sendEmail(Message message, String attachment);
    void sendEmail(Message message, ByteArrayResource attachment);
    //Attachments getAttachments(byte[] content, String fileName);
    // void sendEmailAttachment() throws MessagingException, IOException;

}
