package com.osdb.ippay.sqlserveripcore.ipwhitelist.service.impl;

import com.osdb.ippay.secondary.alerts.repository.entity.SettledTransaction;

import com.osdb.ippay.sqlserveripcore.ipwhitelist.repository.TerminalIpsAuditTrailRecordsRepository;
import com.osdb.ippay.sqlserveripcore.ipwhitelist.service.TerminalIpsAuditTrailRecordsService;

import lombok.experimental.FieldDefaults;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static lombok.AccessLevel.PRIVATE;

@Service
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class TerminalIpsAuditTrailRecordsServiceImpl implements TerminalIpsAuditTrailRecordsService {

	TerminalIpsAuditTrailRecordsRepository terminalIpsAuditTrailRecordsRepository;
	
	Map<String, List<SettledTransaction>> mapSettledTrnList = new HashMap<>();

	    @Autowired
	    public TerminalIpsAuditTrailRecordsServiceImpl(TerminalIpsAuditTrailRecordsRepository termIpsAuditTrailRecordsRepository) {

	        this.terminalIpsAuditTrailRecordsRepository = termIpsAuditTrailRecordsRepository;

	    }
	    
	@Override
	public List generateReport() {
		List  terminalIpsAuditTrailRecords = terminalIpsAuditTrailRecordsRepository.getAuditTrailRecords();
		
		if (terminalIpsAuditTrailRecords.isEmpty()) {
			
				return new ArrayList<>();
	    }
		

	    if (terminalIpsAuditTrailRecords.get(0) instanceof String) {
	        return ((List<String>) terminalIpsAuditTrailRecords)
	          .stream()
	          .map(s -> new String[] { s })
	          .collect(Collectors.toList());
	    } else {
	        return (List<String[]>) terminalIpsAuditTrailRecords;
	}
}
	
}
