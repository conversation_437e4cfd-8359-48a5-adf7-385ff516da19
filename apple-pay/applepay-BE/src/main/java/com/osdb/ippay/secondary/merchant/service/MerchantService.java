package com.osdb.ippay.secondary.merchant.service;

import com.osdb.ippay.common.domain.FileDomain;
import com.osdb.ippay.primary.user.repository.entity.User;
import com.osdb.ippay.secondary.merchant.repository.entity.Merchant;
import com.osdb.ippay.secondary.merchant.service.filter.MerchantFilter;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;

import java.io.IOException;
import java.util.List;

public interface MerchantService {

    Page<Merchant> find(User authUser, MerchantFilter filter, Pageable pageable);

    List<Merchant> find(User authUser, MerchantFilter filter, Sort sort);

    List<Merchant> find(User authUser, MerchantFilter filter);

    List<Merchant> find(Long partnerId);

    List<Merchant> find(List<String> merchantIds);

    Merchant find(String merchantId);

    Merchant findByAchMerchantId(String merchantId);

    Merchant findByCCMerchantId(String merchantId);

    FileDomain export(User authUser, MerchantFilter filter) throws IOException;

    Integer countBy(Long partnerId);

}
