package com.osdb.ippay.sqlserver.reports.facade.mapper;

import com.osdb.ippay.sqlserver.reports.facade.dto.TotalACHDataGtwReportsDto;
import com.osdb.ippay.sqlserver.reports.facade.dto.TotalACHGtwDataDto;
import com.osdb.ippay.sqlserver.reports.repository.entity.TotalACHDataGtwReports;
import com.osdb.ippay.sqlserver.reports.repository.entity.TotalACHGtwData;

import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;

@Component
public class TotalACHGtwDataMapper {

    public Page<TotalACHGtwDataDto> toDto(Page<TotalACHGtwData> totalACHGtwData) {
        return totalACHGtwData.map(this::toDto);
    }

    public TotalACHGtwDataDto toDto(TotalACHGtwData totalACHDataGtwReports) {
       

        return TotalACHGtwDataDto.builder()
        		.ipTransactionId(totalACHDataGtwReports.getIpTransactionId())
        		.transactionID(totalACHDataGtwReports.getTransactionID())
        		.merchantDBAName(totalACHDataGtwReports.getMerchantDBAName())
                .merchantID(totalACHDataGtwReports.getMerchantID())
                .ccProcessor(totalACHDataGtwReports.getCcProcessor())
                .actionCode(totalACHDataGtwReports.getActionCode())
                .requestType(totalACHDataGtwReports.getRequestType())
             //   .transactionCount(totalACHDataGtwReports.getTransactionCount())
                .totalAmount(totalACHDataGtwReports.getTotalAmount())
                .build();
    }
}
