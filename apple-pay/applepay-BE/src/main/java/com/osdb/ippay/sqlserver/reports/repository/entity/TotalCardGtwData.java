package com.osdb.ippay.sqlserver.reports.repository.entity;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import javax.persistence.*;
import static javax.persistence.GenerationType.IDENTITY;
import static lombok.AccessLevel.PRIVATE;

@Entity
@Table(name = "jetPay_CreditRealTime")

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
public class TotalCardGtwData {

	 @Id
	 @Column(name = "ipTransactionID")
	 @GeneratedValue(strategy = IDENTITY)
	 Long ipTransactionId;
	 
	 
	 @Column(name = "Merchant_ID")
	 String merchantID;
	 
	 @Column(name = "Transaction_ID")
	 String transactionID;
	 
    @Column(name = "Merchant_Name")
    String merchantDBAName;

    @Column(name = "CCProcessor")
    String ccProcessor;

    @Column(name = "Request_Type")
    String requestType;

    @Column(name = "ActionCode")
    String actionCode;

    //@Column(name = "TRANSACTION_COUNT", insertable=false, updatable = false )
    //String transactionCount;

    @Column(name = "TotalAmount")
    String totalAmount;

    
    @Column(name = "AuthDateTime")
    String authDateTime;
    
    }

