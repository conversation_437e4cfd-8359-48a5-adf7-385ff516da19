package com.osdb.ippay.sqlserver.reports.service.impl;

import com.osdb.ippay.primary.user.repository.entity.User;
import com.osdb.ippay.secondary.chargeback.repository.entity.Chargeback;
import com.osdb.ippay.secondary.chargeback.service.ChargebackFilter;
import com.osdb.ippay.secondary.transaction.repository.ACHTransactionRepository;
import com.osdb.ippay.sqlserver.reports.repository.TotalACHDataGtwReportsRepository;
import com.osdb.ippay.sqlserver.reports.repository.TotalACHGtwDataRepository;
import com.osdb.ippay.sqlserver.reports.repository.entity.TotalACHDataGtwReports;
import com.osdb.ippay.sqlserver.reports.repository.entity.TotalACHGtwData;
import com.osdb.ippay.sqlserver.reports.service.TotalACHDataGtwReportsService;
import com.osdb.ippay.sqlserver.reports.service.filter.TotalACHDataGtwReportsFilter;
import com.osdb.ippay.sqlserver.reports.service.filter.TotalACHGtwDataFilter;

import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.persistence.EntityManager;
import javax.persistence.criteria.Predicate;

import static lombok.AccessLevel.PRIVATE;

@Service
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class TotalACHDataGtwReportsServiceImpl implements TotalACHDataGtwReportsService {

	TotalACHDataGtwReportsRepository totalACHDataGtwReportsRepository;
	TotalACHGtwDataRepository totalACHGtwDataRepository;

	  EntityManager entityManager;

	    @Autowired
	    public TotalACHDataGtwReportsServiceImpl(TotalACHDataGtwReportsRepository totACHDataGtwReportsRepository,
	    		TotalACHGtwDataRepository totalACHGtwDataRepository,
	                                     @Qualifier("sqlserverEmFactory") EntityManager entityManager) {

	        this.totalACHGtwDataRepository = totalACHGtwDataRepository;
			this.totalACHDataGtwReportsRepository = totACHDataGtwReportsRepository;
	        this.entityManager = entityManager;
	    }
	    
	    
	@Override
	public List<TotalACHDataGtwReports> find(Long ipTransactionId, String authStartDateTime, String authEndDateTime,
			TotalACHDataGtwReportsFilter filter) {
		
		List<TotalACHDataGtwReports>  totalACHDataGtwSummary= totalACHDataGtwReportsRepository.getTotalACHSummaryByModelEntiy(ipTransactionId, authStartDateTime, authEndDateTime);
		
		return totalACHDataGtwSummary;
	}


	@Override
	public Page<TotalACHDataGtwReports> find(Long ipTransactionId, String authStartDateTime, String authEndDateTime,
			TotalACHDataGtwReportsFilter filter, Pageable pageable) {
	    Specification<TotalACHDataGtwReports> specification = filterBy(filter);

	        PageRequest pageRequest = PageRequest.of(
	                pageable.getPageNumber(),
	                pageable.getPageSize(),
	                getSort(pageable)
	        );

	        return totalACHDataGtwReportsRepository.findAll(specification, pageRequest);
	}


	private Specification<TotalACHDataGtwReports> filterBy(TotalACHDataGtwReportsFilter filter) {
        return (r, rq, cb) -> {

            Predicate trnIdPredicate = (filter.getIpTransactionID() != null) ?
            		  cb.greaterThanOrEqualTo(
                              r.get("ipTransactionId"),
                              filter.getIpTransactionID()
                      ) : cb.conjunction();
            
            Predicate fromDatePredicate = StringUtils.isNotBlank(filter.getAuthStartDateTime()) ?
                    cb.greaterThanOrEqualTo(
                            r.get("authDateTime"),
                            filter.getAuthStartDateTime()
                    ) : cb.conjunction();

            Predicate toDatePredicate = StringUtils.isNotBlank(filter.getAuthEndDateTime()) ?
                    cb.lessThanOrEqualTo(
                            r.get("authDateTime"),
                            filter.getAuthEndDateTime()
                    ) : cb.conjunction();

          

            return cb.and(
            		trnIdPredicate,
                    fromDatePredicate,
                    toDatePredicate
            );
        };
    }
	
	private Specification<TotalACHGtwData> filterBy(TotalACHGtwDataFilter filter) {
        return (r, rq, cb) -> {

            Predicate ipTrnIdPredicate = (filter.getIpTransactionID() != null) ?
            		  cb.greaterThanOrEqualTo(
                              r.get("ipTransactionId"),
                              filter.getIpTransactionID()
                      ) : cb.conjunction();
            
            Predicate trnIdPredicate = (filter.getTransactionID() != null) ?
          		  cb.greaterThanOrEqualTo(
                            r.get("transactionId"),
                            filter.getTransactionID()
                    ) : cb.conjunction();
            
            Predicate fromDatePredicate = StringUtils.isNotBlank(filter.getAuthStartDateTime()) ?
                    cb.greaterThanOrEqualTo(
                            r.get("authDateTime"),
                            filter.getAuthStartDateTime()
                    ) : cb.conjunction();

            Predicate toDatePredicate = StringUtils.isNotBlank(filter.getAuthEndDateTime()) ?
                    cb.lessThanOrEqualTo(
                            r.get("authDateTime"),
                            filter.getAuthEndDateTime()
                    ) : cb.conjunction();

          

            return cb.and(
            		ipTrnIdPredicate,
            		trnIdPredicate,
                    fromDatePredicate,
                    toDatePredicate
            );
        };
    }


    private Sort getSort(Pageable pageable) {
        Sort.Order order = pageable.getSort().get()
                .findFirst()
                .orElse(Sort.Order.desc("authDateTime"));

        Sort.Direction direction = order.getDirection();
        String property = order.getProperty();

        return switch (property) {
    
            default -> Sort.by(order);
        };
    }


	
	@Override
	public List generateReport(Long ipTransactionId, String authStartDateTime,
			String authEndDateTime) {
		List results =  entityManager.createNamedStoredProcedureQuery("TotalACHDataGtwReports.getTotalACHSummaryByModelEntiy").setParameter("ipTransactionId",ipTransactionId)
				.setParameter("authStartDateTime",authStartDateTime)
				.setParameter("authEndDateTime",authEndDateTime).getResultList();
		
		if (results.isEmpty()) {
	        return new ArrayList<>();
	    }

	    if (results.get(0) instanceof String) {
	        return ((List<String>) results)
	          .stream()
	          .map(s -> new String[] { s })
	          .collect(Collectors.toList());
	    } else {
	        return (List<String[]>) results;
		
		//return totalACHDataGtwReportsRepository.getTotalACHSummaryByModelEntiy(ipTransactionId, authStartDateTime, authEndDateTime);
		//return null;
	}
}


	@Override
	public Page<TotalACHGtwData> findData(Long ipTransactionId, String authStartDateTime, String authEndDateTime,
			TotalACHGtwDataFilter filter, Pageable pageable) {

		  Specification<TotalACHGtwData> specification = filterBy(filter);

	        PageRequest pageRequest = PageRequest.of(
	                pageable.getPageNumber(),
	                pageable.getPageSize(),
	                getSort(pageable)
	        );

	        return totalACHGtwDataRepository.findAll(specification, pageRequest);
	}
	
}
