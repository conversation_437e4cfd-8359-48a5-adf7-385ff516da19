package com.osdb.ippay.sqlserver.reports.facade.mapper;

import com.osdb.ippay.sqlserver.reports.facade.dto.TotalACHDataGtwReportsDto;
import com.osdb.ippay.sqlserver.reports.repository.entity.TotalACHDataGtwReports;

import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;

@Component
public class TotalACHDataGtwReportsMapper {

    public Page<TotalACHDataGtwReportsDto> toDto(Page<TotalACHDataGtwReports> totalACHDataGtwReports) {
        return totalACHDataGtwReports.map(this::toDto);
    }

    public TotalACHDataGtwReportsDto toDto(TotalACHDataGtwReports totalACHDataGtwReports) {
       

        return TotalACHDataGtwReportsDto.builder()
        		.ipTransactionId(totalACHDataGtwReports.getIpTransactionId())
                .merchantID(totalACHDataGtwReports.getMerchantID())
                .ccProcessor(totalACHDataGtwReports.getCcProcessor())
                .actionCode(totalACHDataGtwReports.getActionCode())
                .requestType(totalACHDataGtwReports.getRequestType())
                .transactionCount(totalACHDataGtwReports.getTransactionCount())
                .totalAmount(totalACHDataGtwReports.getTotalAmount())
                .spsMid(totalACHDataGtwReports.getSpsMid())
                .trnId(totalACHDataGtwReports.getTrnId())
                .orgName(totalACHDataGtwReports.getOrgName())
                .build();
    }
}
