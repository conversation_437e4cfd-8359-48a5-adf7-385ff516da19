package com.osdb.ippay.hpp.alternatepay.transaction.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import javax.persistence.*;

import static lombok.AccessLevel.PRIVATE;


@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class HapiTransactionDto {

    @Id
    @GeneratedValue
    Long Id;

    String customerId;
    
    String mid;

    String terminalId;

    String referenceId;

    String token;

    String amount;

    int callbackUrl;

    String callbackUrlText;

    String cardToken;

    String customerEmail;
    
    String paymentType;
    
    int isRecurring;
    
    String enqReferenceId;
    
    String transactionType;
    
    String transactionID;
    
    String address;
    
    String city;
    
    String state;
    
    String zip;
    
    String country;
    
    String cardExpiryMonth;
    
    String cardExpiryYear;
    
    String cardHolderName;

    String tokenizeCardSelected;

    int saveForFuture;

    String valid;

    String authCode;

    String ippTransactionID;

    String returnToken;

    String responseText;

    String responseCode;

    int processedAmount;

    String saveCardForFuture;

    String cardNumberMasked;

    String cardType;
}
