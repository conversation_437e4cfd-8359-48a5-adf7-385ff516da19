package com.osdb.ippay.secondary.alternatepay.transaction.controller;

import com.osdb.ippay.secondary.alternatepay.payment.controller.facade.PaymentResponseFacade;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionGetResultDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionInitDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.facade.TransactionGetFacade;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.facade.TransactionRecurringGetFacade;
import com.osdb.ippay.secondary.alternatepay.payment.controller.facade.PaymentResponseFacade;

import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import static lombok.AccessLevel.PRIVATE;

import javax.validation.Valid;

@Tag(name = "Alternatepay/Transaction")
@RestController
@RequestMapping(value = "/api/v2/payment/alternatepay")
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class TransactionGetController {

	TransactionGetFacade transactionGetFacade; 
	TransactionRecurringGetFacade transactionRecurringGetFacade; 
	PaymentResponseFacade paymentResponseFacade;

    @GetMapping("/transaction/{transactionNumber}")
    public ResponseEntity<TransactionGetResultDto> GetTransactionById(@PathVariable("transactionNumber") String transactionNumber, String apiKey) {
    	TransactionGetResultDto response = transactionGetFacade.create(apiKey, transactionNumber);
    	//paymentResponseFacade.updateToken(response, transactionNumber);
    	//paymentResponseFacade.updateAddress(response, transactionNumber);
        return ResponseEntity.ok(response);
    }

   /* @GetMapping("/transaction/recurring/{subscriptionId}")
    public ResponseEntity<TransactionGetResultDto> GetSubscriptionById(@PathVariable("subscriptionId") String subscriptionId, String branchApiKey, String merchantApiKey) {
    	TransactionGetResultDto response = transactionRecurringGetFacade.getSubscription(subscriptionId, branchApiKey, merchantApiKey);
        return ResponseEntity.ok(response);
    }*/
}
