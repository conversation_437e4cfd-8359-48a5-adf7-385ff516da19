package com.osdb.ippay.secondary.transaction.facade.mapper;

import com.osdb.ippay.common.util.number.DoubleUtil;
import com.osdb.ippay.common.util.string.StringUtil;
import com.osdb.ippay.primary.user.repository.entity.User;
import com.osdb.ippay.secondary.alternatepay.payment.controller.dto.PaymentResultDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionGetResultDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionSaveDto;
import com.osdb.ippay.secondary.merchant.repository.entity.Merchant;
import com.osdb.ippay.secondary.transaction.facade.dto.CCTransactionDto;
import com.osdb.ippay.secondary.transaction.facade.dto.RefundVoidBtn;
import com.osdb.ippay.secondary.transaction.repository.entity.CPCCTransaction;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.osdb.ippay.common.util.date.DateUtil.isTheSameDayZoned;
import static com.osdb.ippay.secondary.transaction.facade.dto.RefundVoidBtn.*;

@Component
public class CPCCTransactionMapper {

    public List<CCTransactionDto> toDto(User user, List<CPCCTransaction> ccTransactions) {
        return ccTransactions.stream()
                .map(ccTransaction -> toDto(user, ccTransaction))
                .collect(Collectors.toList());
    }

    public CCTransactionDto toDto(User user, CPCCTransaction ccTransaction) {
        Merchant merchant = ccTransaction.getMerchant();

        return CCTransactionDto.builder()
                .id(ccTransaction.getId())
                .processorId(ccTransaction.getProcessorId())
                .terminalId(
                        user.isAdmin() ?
                                ccTransaction.getTerminalId() :
                                StringUtil.hashString(ccTransaction.getTerminalId())
                )
                .merchantId(ccTransaction.getMerchantId())
                .merchantName(Objects.nonNull(merchant) ? merchant.getMerchantName() : null)
                .approvalStatus(
                        StringUtils.isNotBlank(ccTransaction.getApprovalStatus()) ?
                                ccTransaction.getApprovalStatus() :
                                ccTransaction.getResponseText()
                )
                .cardType(ccTransaction.getCardType())
                .cardNumber(ccTransaction.getSafeCardNumber())
                .cardHolder(ccTransaction.getCardHolder())
                .address(ccTransaction.getAddress())
                .city(ccTransaction.getCity())
                .state(ccTransaction.getState())
                .zipCode(ccTransaction.getZipCode())
                .country(ccTransaction.getCountry())
                .phone(ccTransaction.getPhone())
                .email(ccTransaction.getEmail())
                .transactionId(ccTransaction.getTransactionId())
                .transactionType(ccTransaction.getTransactionType())
                .authCode(ccTransaction.getAuthCode())
                .authAmount(toPriceFormat(ccTransaction.getAuthAmount()))
                .authAmountCents(ccTransaction.getAuthAmount())
                .authDate(ccTransaction.getAuthDateInstant())
                .avs(ccTransaction.getAvs())
                .cvv(ccTransaction.getCvv())
                .ud1(ccTransaction.getUd1())
                .ud2(ccTransaction.getUd2())
                .ud3(ccTransaction.getUd3())
                .requestType(ccTransaction.getRequestType())
                .currencyCode(ccTransaction.getCurrencyCode())
                .orderNumber(ccTransaction.getOrderNumber())
                .settlementDate(ccTransaction.getSettlementDateInstant())
                .responseCode(ccTransaction.getResponseCode())
                .responseText(ccTransaction.getResponseText())
                .ipAddress(null)
                .token(ccTransaction.getToken())
                .refundVoidBtn(getRefundVoidBtn(ccTransaction))
                .ccProcessor(ccTransaction.getCcProcessor())
                .posTerminalId(ccTransaction.getPosTerminalId())
                .posAdditionalData(ccTransaction.getPosAdditionalData())
                .vNumber(ccTransaction.getVNumber())
                .retRefNumber(ccTransaction.getRetRefNumber())
                .safeCardNumber(ccTransaction.getSafeCardNumber())
                .expiry(ccTransaction.getExpiry())
                .bankTransID(ccTransaction.getBankTransID())
                .externalTransID(ccTransaction.getExternalTransID())
                .build();
    }
    
    public CCTransactionDto toDto(CPCCTransaction ccTransaction) {
        Merchant merchant = ccTransaction.getMerchant();

        return CCTransactionDto.builder()
                .id(ccTransaction.getId())
                .processorId(ccTransaction.getProcessorId())
                .terminalId(StringUtil.hashString(ccTransaction.getTerminalId()))
                .merchantId(ccTransaction.getMerchantId())
                .merchantName(Objects.nonNull(merchant) ? merchant.getMerchantName() : null)
                .approvalStatus(
                        StringUtils.isNotBlank(ccTransaction.getApprovalStatus()) ?
                                ccTransaction.getApprovalStatus() :
                                ccTransaction.getResponseText()
                )
                .cardType(ccTransaction.getCardType())
                .cardNumber(ccTransaction.getSafeCardNumber())
                .cardHolder(ccTransaction.getCardHolder())
                .address(ccTransaction.getAddress())
                .city(ccTransaction.getCity())
                .state(ccTransaction.getState())
                .zipCode(ccTransaction.getZipCode())
                .country(ccTransaction.getCountry())
                .phone(ccTransaction.getPhone())
                .email(ccTransaction.getEmail())
                .transactionId(ccTransaction.getTransactionId())
                .transactionType(ccTransaction.getTransactionType())
                .authCode(ccTransaction.getAuthCode())
                .authAmount(toPriceFormat(ccTransaction.getAuthAmount()))
                .authAmountCents(ccTransaction.getAuthAmount())
                .authDate(ccTransaction.getAuthDateInstant())
                .avs(ccTransaction.getAvs())
                .cvv(ccTransaction.getCvv())
                .ud1(ccTransaction.getUd1())
                .ud2(ccTransaction.getUd2())
                .ud3(ccTransaction.getUd3())
                .requestType(ccTransaction.getRequestType())
                .currencyCode(ccTransaction.getCurrencyCode())
                .orderNumber(ccTransaction.getOrderNumber())
                .settlementDate(ccTransaction.getSettlementDateInstant())
                .responseCode(ccTransaction.getResponseCode())
                .responseText(ccTransaction.getResponseText())
                .ipAddress(null)
                .token(ccTransaction.getToken())
                .refundVoidBtn(getRefundVoidBtn(ccTransaction))
                .ccProcessor(ccTransaction.getCcProcessor())
                .posTerminalId(ccTransaction.getPosTerminalId())
                .posAdditionalData(ccTransaction.getPosAdditionalData())
                .vNumber(ccTransaction.getVNumber())
                .retRefNumber(ccTransaction.getRetRefNumber())
                .safeCardNumber(ccTransaction.getSafeCardNumber())
                .expiry(ccTransaction.getExpiry())
                .bankTransID(ccTransaction.getBankTransID())
                .externalTransID(ccTransaction.getExternalTransID())
                .build();
    }

    public CPCCTransaction toEntity(TransactionSaveDto transactionSaveDto, 
			PaymentResultDto paymentResultDto,
			TransactionGetResultDto paymentGetResultDto,String mid,
			String terminalId){

    	java.util.Date utilDate = new java.util.Date();
    	java.sql.Timestamp  sqlDate = new java.sql.Timestamp (utilDate.getTime());

    	String strApprovalStatus = "00";
    	if(!paymentResultDto.getTransactionStatus().equals("TransactionApproved") )
    	{
    		strApprovalStatus = "01";
    	}
    	CPCCTransaction transactionResponse = new CPCCTransaction();
    	transactionResponse.setMerchantId(mid);
    	transactionResponse.setMerchantName("TESTMERCHANT");
    	transactionResponse.setProcessorId(0123456);
    	transactionResponse.setCcProcessor("5727");
    	transactionResponse.setApprovalStatus(strApprovalStatus);
    	transactionResponse.setAddress(paymentGetResultDto.getPosData().getValueJSON().getAddress());
    	//transactionResponse.setToken(paymentResultDto.getToken());
    	transactionResponse.setCardLastFour(paymentResultDto.getCardMask());
    	transactionResponse.setCardType(getCardType(paymentResultDto.getConfirmation().getCardType()));
    	transactionResponse.setAuthAmount(Integer.parseInt(paymentResultDto.getConfirmation().getAmount()));
    	transactionResponse.setTerminalId(terminalId);
    	transactionResponse.setCity(paymentGetResultDto.getPosData().getValueJSON().getCity());
    	transactionResponse.setState(paymentGetResultDto.getPosData().getValueJSON().getState());
    	transactionResponse.setCountry(paymentGetResultDto.getPosData().getValueJSON().getCountry());
    	transactionResponse.setZipCode(paymentGetResultDto.getPosData().getValueJSON().getZip());
    	try
    	{
    		transactionResponse.setAuthDate(sqlDate.toString());
    	}
    	finally
    	{
    		
    	}
    	
    	//transactionResponse.setBankTransID(paymentResultDto.getTransactionNumber());
    	transactionResponse.setCardLastFour(paymentResultDto.getConfirmation().getLastFourDigits());
    	transactionResponse.setEmail(paymentGetResultDto.getPosData().getValueJSON().getCustomerEmail());
    	transactionResponse.setCardHolder(paymentGetResultDto.getPosData().getValueJSON().getCustomerName());
    	transactionResponse.setSafeCardNumber(paymentResultDto.getCardMask());
    	transactionResponse.setTransactionId(paymentResultDto.getTransactionNumber().substring(0, 50));
    	transactionResponse.setTransactionType(paymentResultDto.getPaymentType());
    	
    	
    	return transactionResponse;
}
    
    public String getCardType(String cardType) {
        if(StringUtils.isBlank(cardType)) {
            return cardType;
        }

        return switch (cardType) {
            case "VS" -> "1";
            case "MC" -> "2";
            case "DS" -> "3";
            case "AX" -> "4";
            case "PP" -> "5";
            default -> cardType;
        };
    }
     
    private String toPriceFormat(Integer value) {
        return value != null ? DoubleUtil.toPriceFormat(value, 2, 100) : null;
    }

    private RefundVoidBtn getRefundVoidBtn(CPCCTransaction ccTransaction) {
        if(StringUtils.isBlank(ccTransaction.getToken())) return NONE_BTN;
        if(StringUtils.isNotBlank(ccTransaction.getSettlementDate())) return REFUND_BTN;

        ZoneId zoneId = ZoneId.of("US/Central");

        LocalDateTime authDateTime = LocalDateTime.ofInstant(ccTransaction.getAuthDateInstant(), zoneId);
        LocalDateTime currDateTime = LocalDateTime.now(zoneId);

        boolean isCurrDateEqAuthDate = isTheSameDayZoned(authDateTime, currDateTime);
        if(isCurrDateEqAuthDate) return VOID_BTN;

        return NONE_BTN;
    }
}
