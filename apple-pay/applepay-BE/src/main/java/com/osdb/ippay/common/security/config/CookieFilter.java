package com.osdb.ippay.common.security.config;

import org.springframework.stereotype.Component;

import javax.servlet.*;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Collection;

import static org.springframework.http.HttpHeaders.SET_COOKIE;

@Component
public class CookieFilter implements Filter {

    @Override
    public void doFilter(ServletRequest request, ServletResponse response,
                         FilterChain filterChain) throws IOException, ServletException {

        HttpServletResponse httpResponse = (HttpServletResponse) response;
        Collection<String> headers = httpResponse.getHeaders(SET_COOKIE);

        headers.forEach(header ->
                httpResponse.setHeader(
                        SET_COOKIE,
                        String.format("%s; %s", header, "SameSite=None; Secure")
                ));

        filterChain.doFilter(request, response);
    }
}
