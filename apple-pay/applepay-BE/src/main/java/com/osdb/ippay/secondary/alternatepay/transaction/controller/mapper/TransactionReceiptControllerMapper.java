package com.osdb.ippay.secondary.alternatepay.transaction.controller.mapper;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.*;

import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import static lombok.AccessLevel.PRIVATE;

@Component
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class TransactionReceiptControllerMapper {

    @NonFinal
    @Value("${tokenization.api-key}")
    String apiKey;
    
    @NonFinal
    @Value("${tokenization.branch-code}")
    String branchCode;

    public TransactionReceiptDto toDto(TransactionReceiptDto transactionReceiptDto) {
        return TransactionReceiptDto.builder()
               
                .apiKey(apiKey)
                .amount(transactionReceiptDto.getAmount())
                .callbackURL(transactionReceiptDto.getCallbackURL())
                .cancelURL(transactionReceiptDto.getCancelURL())
                .currency(transactionReceiptDto.getCurrency())
                .isCent(transactionReceiptDto.isCent())
                .returnURL(transactionReceiptDto.getReturnURL())
                .timeOut(transactionReceiptDto.getTimeOut())
                .receipt(transactionReceiptDto.getReceipt())
                .receiptNotifications(transactionReceiptDto.getReceiptNotifications())
                .build();
    }

  
    public TransactionReceiptResultDto toOutDto(TransactionReceiptResultDto trnGetResultDto) {
        return TransactionReceiptResultDto.builder()
                .amount(trnGetResultDto.getAmount())
                .currency(trnGetResultDto.getCurrency())
                .date(trnGetResultDto.getDate())
                .branchId(trnGetResultDto.getBranchId())
                .branchNumber(trnGetResultDto.getBranchNumber())
                .businessAddress(trnGetResultDto.getBusinessAddress())
                .businessCity(trnGetResultDto.getBusinessCity())
                .businessCountry(trnGetResultDto.getBusinessCountry())
                .businessId(trnGetResultDto.getBusinessId())
                .businessName(trnGetResultDto.getBusinessName())
                .businessOwnerPhone(trnGetResultDto.getBusinessOwnerPhone())
                .businessStreet(trnGetResultDto.getBusinessStreet())
                .pos(trnGetResultDto.getPos())
                .receiptPending(trnGetResultDto.getReceiptPending())
                .status(trnGetResultDto.getStatus())
                .statusName(trnGetResultDto.getStatusName())
                .timeoutDate(trnGetResultDto.getTimeoutDate())
                .TransactionNumber(trnGetResultDto.getTransactionNumber())
                .paymentType("Apple Pay")
                
                .build();
    }

}
