package com.osdb.ippay.secondary.reports.service.impl;

import com.osdb.ippay.secondary.reports.repository.MonthlyCreditCardReportRepository;
import com.osdb.ippay.secondary.reports.repository.entity.MonthlyCreditCardReport;
import com.osdb.ippay.secondary.reports.service.MonthlyCreditCardReportService;

import lombok.experimental.FieldDefaults;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import javax.persistence.EntityManager;


import static lombok.AccessLevel.PRIVATE;

@Service
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class MonthlyCreditCardReportServiceImpl implements MonthlyCreditCardReportService {

	MonthlyCreditCardReportRepository monthlyCreditCardReportRepository;	
	EntityManager entityManager;
	

	    @Autowired
	    public MonthlyCreditCardReportServiceImpl(MonthlyCreditCardReportRepository monthlyCreditCardReportRepositoryObj,
	    		@Qualifier("secondaryEmFactory") EntityManager entityManager) {

	        this.monthlyCreditCardReportRepository = monthlyCreditCardReportRepositoryObj;
	        this.entityManager = entityManager;
	    }
	    


    @Override
    public List generateReport(String startDateTime,
			String endDateTime) {
    	List  monthlyCreditCardTransactions = monthlyCreditCardReportRepository.getMonthlyCreditCardReport(startDateTime, endDateTime);
		
		if (monthlyCreditCardTransactions.isEmpty()) {
			
				return new ArrayList<>();
	    }
		

	    if (monthlyCreditCardTransactions.get(0) instanceof String) {
	        return ((List<String>) monthlyCreditCardTransactions)
	          .stream()
	          .map(s -> new String[] { s })
	          .collect(Collectors.toList());
	    } else {
	        return (List<String[]>) monthlyCreditCardTransactions;

	}
}

    private Pageable createPageRequestUsing(int page, int size) {
        return PageRequest.of(page, size);
    }
    
    @Override
    public Page<MonthlyCreditCardReport> find(String startDateTime,
			String endDateTime,
			Pageable pageable) {
	 
	    List<MonthlyCreditCardReport>  monthlyCreditCardReportTransactions = monthlyCreditCardReportRepository.getMonthlyCreditCardReport(startDateTime, endDateTime);
	    Pageable pageRequest = createPageRequestUsing(pageable.getPageNumber(), pageable.getPageSize());
	    int start = (int) pageRequest.getOffset();
	    int end = Math.min((start + pageRequest.getPageSize()), monthlyCreditCardReportTransactions.size());
	    List<MonthlyCreditCardReport> pageContent = monthlyCreditCardReportTransactions.subList(start, end);
	    
	    return new PageImpl<>(pageContent, pageRequest, monthlyCreditCardReportTransactions.size());
	}
}
