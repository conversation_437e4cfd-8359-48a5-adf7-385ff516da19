package com.osdb.ippay.secondary.alternatepay.transaction.controller.facade.impl;


import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionGetResultDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.facade.*;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.mapper.TransactionGetControllerMapper;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.Base64;
import java.util.Objects;

import static lombok.AccessLevel.PRIVATE;

@Component
@RequiredArgsConstructor
@FieldDefaults(makeFinal = true, level = PRIVATE)
public class TransactionRecurringGetFacadeImpl implements TransactionRecurringGetFacade {

    public static final String INVALID_JOB_STATE = "Job can not be deleted, job state not 'Queued'";

    TransactionGetControllerMapper mapper;

    @NonFinal
    @Value("${transaction-recurring.url.get}")
    String transactionUrlGet;

    @NonFinal
    boolean blnIsDecoded = true;
    
	@Override
	public TransactionGetResultDto getSubscription(String subscriptionId, String branchApiKey, String merchantApiKey) {

		RestTemplate restTemplate = new RestTemplate();

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("branchApiKey", branchApiKey);
        headers.add("merchantApiKey", merchantApiKey);
        
        HttpEntity<String> entity = new HttpEntity<String>(null, headers);
        
        if(!blnIsDecoded)
        {
        	byte[] decodedBytes = Base64.getDecoder().decode(transactionUrlGet);
            transactionUrlGet = new String(decodedBytes);
        }
        
        
        TransactionGetResultDto trnGetResultDto = restTemplate.exchange(
        		transactionUrlGet + subscriptionId,
                HttpMethod.GET,
                entity,
                new ParameterizedTypeReference<TransactionGetResultDto>() {
                },
                ""
        ).getBody();

        return Objects.nonNull(trnGetResultDto) ? mapper.toOutDto(trnGetResultDto) : null;
	}
}
