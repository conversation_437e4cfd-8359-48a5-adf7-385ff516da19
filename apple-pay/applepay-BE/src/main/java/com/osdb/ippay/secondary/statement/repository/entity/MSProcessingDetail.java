package com.osdb.ippay.secondary.statement.repository.entity;

import com.osdb.ippay.common.entity.BaseEntity;
import lombok.*;
import lombok.experimental.FieldDefaults;
import lombok.experimental.SuperBuilder;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import static lombok.AccessLevel.PRIVATE;

@Entity
@Table(name = "MERCHANT_STATEMENT_PROCESSING_DETAIL")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = PRIVATE)
@SuperBuilder(toBuilder = true)
public class MSProcessingDetail extends BaseEntity {

    @Column(name = "MERCHANT_ID")
    String merchantId;

    @Column(name = "DESCRIPTION")
    String description;

    @Column(name = "AMOUNT")
    String amount;

    @Column(name = "DISCOUNT_RATE")
    String discountRate;

    @Column(name = "NUM_X")
    String numX;

    @Column(name = "TRANSACTION_FEES")
    String transactionFee;

    @Column(name = "PROCESS_FEES")
    String processFee;

}
