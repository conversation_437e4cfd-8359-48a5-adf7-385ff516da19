package com.osdb.ippay.common.util.annotation;

import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;

import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static io.swagger.v3.oas.annotations.enums.ParameterIn.HEADER;
import static java.lang.annotation.ElementType.*;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

@Target({METHOD, ANNOTATION_TYPE, TYPE})
@Retention(RUNTIME)
@Parameters({
        @Parameter(name = "X-XSRF-TOKEN", in = HEADER)
})
public @interface XsrfTokenHeader {

}
