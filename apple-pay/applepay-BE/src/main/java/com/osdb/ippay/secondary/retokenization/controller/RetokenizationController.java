package com.osdb.ippay.secondary.retokenization.controller;

import com.osdb.ippay.common.logging.LogExecutionTime;
import com.osdb.ippay.common.util.annotation.ApiPageable;
import com.osdb.ippay.common.util.annotation.XsrfTokenHeader;
import com.osdb.ippay.common.util.response.PageResponse;
import com.osdb.ippay.secondary.retokenization.controller.dto.RetokenizationInDto;
import com.osdb.ippay.secondary.retokenization.controller.dto.RetokenizationOutDto;
import com.osdb.ippay.secondary.retokenization.controller.dto.RetokenizationPatchDto;
import com.osdb.ippay.secondary.retokenization.controller.facade.RetokenizationFacade;
import com.osdb.ippay.secondary.retokenization.controller.mapper.RetokenizationMapper;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import static lombok.AccessLevel.PRIVATE;

@Tag(name = "retokenization")
@RestController
@RequestMapping(value = "/api/v1/private/retokenization-jobs")
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class RetokenizationController {

    RetokenizationFacade retokenizationFacade;
    RetokenizationMapper retokenizationMapper;
/*
    @LogExecutionTime
    @ApiPageable
    @GetMapping
    public ResponseEntity<PageResponse<RetokenizationOutDto>> get(@Parameter(hidden = true) Pageable pageable) {
        PageResponse<RetokenizationOutDto> response = retokenizationFacade.find(pageable);
        return ResponseEntity.ok(response);
    }

    @LogExecutionTime
    @GetMapping("/{id}")
    public ResponseEntity<RetokenizationOutDto> getByid(@PathVariable(name = "id") Integer id) {
        RetokenizationOutDto response = retokenizationMapper.toOutDto(retokenizationFacade.findById(id));
        return ResponseEntity.ok(response);
    }

    @LogExecutionTime
    @XsrfTokenHeader
    @PostMapping
    public ResponseEntity<RetokenizationOutDto> create(@Valid @RequestBody RetokenizationInDto retokenizationInDto) {
        RetokenizationOutDto response = retokenizationFacade.create(retokenizationInDto);
        return ResponseEntity.ok(response);
    }

    @LogExecutionTime
    @XsrfTokenHeader
    @PatchMapping("/{id}")
    public ResponseEntity<RetokenizationOutDto> patch(@RequestBody RetokenizationPatchDto retokenizationPatchDto,
                                                      @PathVariable(name = "id") Integer id) {
        RetokenizationOutDto response = retokenizationFacade.patch(retokenizationPatchDto, id);
        return ResponseEntity.ok(response);
    }

    @LogExecutionTime
    @XsrfTokenHeader
    @PutMapping("/{id}")
    public ResponseEntity<RetokenizationOutDto> put(@RequestBody @Valid RetokenizationInDto retokenizationInDto,
                                                    @PathVariable(name = "id") Integer id) {
        RetokenizationOutDto response = retokenizationFacade.put(retokenizationInDto, id);
        return ResponseEntity.ok(response);
    }

    @XsrfTokenHeader
    @LogExecutionTime
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> delete(@PathVariable(name = "id") Integer id){
        retokenizationFacade.delete(id);
        return ResponseEntity.noContent().build();
    }
*/
}
