package com.osdb.ippay.sqlserver.reports.facade.mapper;

import com.osdb.ippay.sqlserver.reports.facade.dto.TotalACHDataGtwReportsDto;
import com.osdb.ippay.sqlserver.reports.facade.dto.TotalACHGtwDataDto;
import com.osdb.ippay.sqlserver.reports.facade.dto.TotalCardGtwDataDto;
import com.osdb.ippay.sqlserver.reports.repository.entity.TotalACHDataGtwReports;
import com.osdb.ippay.sqlserver.reports.repository.entity.TotalACHGtwData;
import com.osdb.ippay.sqlserver.reports.repository.entity.TotalCardGtwData;

import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;

@Component
public class TotalCardGtwDataMapper {

    public Page<TotalCardGtwDataDto> toDto(Page<TotalCardGtwData> totalCardGtwData) {
        return totalCardGtwData.map(this::toDto);
    }

    public TotalCardGtwDataDto toDto(TotalCardGtwData totalCardDataGtwReports) {
       

        return TotalCardGtwDataDto.builder()
        		.ipTransactionId(totalCardDataGtwReports.getIpTransactionId())
        		.transactionID(totalCardDataGtwReports.getTransactionID())
                .merchantID(totalCardDataGtwReports.getMerchantID())
                .ccProcessor(totalCardDataGtwReports.getCcProcessor())
                .actionCode(totalCardDataGtwReports.getActionCode())
                .requestType(totalCardDataGtwReports.getRequestType())
                .merchantDBAName(totalCardDataGtwReports.getMerchantDBAName())
                .totalAmount(totalCardDataGtwReports.getTotalAmount())
                .build();
    }
}
