package com.osdb.ippay.common.user.facade.dto;

import com.osdb.ippay.primary.user.repository.entity.UserRole;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import static lombok.AccessLevel.PRIVATE;

@Getter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class EditUserDto {

    @Parameter(required = true)
    @NotBlank(message = "First name is required")
    String firstName;

    @Parameter(required = true)
    @NotBlank(message = "Last name is required")
    String lastName;

    @Parameter(required = true)
    @NotNull(message = "Role is required")
    UserRole role;

    @Parameter(required = true)
    @NotNull(message = "Role is required")
    String email;

    Boolean receiveDailySettlement;

    Boolean receiveMonthlySettlement;

    Boolean receiveEcheckRejectNotices;

    Boolean receiveChargebacksNotices;

    Long partnerId;

}
