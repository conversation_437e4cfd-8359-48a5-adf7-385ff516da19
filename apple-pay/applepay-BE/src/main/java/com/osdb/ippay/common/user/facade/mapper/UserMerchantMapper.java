package com.osdb.ippay.common.user.facade.mapper;

import com.osdb.ippay.primary.user.facade.dto.UserMerchantDto;
import com.osdb.ippay.primary.user.repository.entity.UserMerchant;
import com.osdb.ippay.secondary.merchant.repository.entity.Merchant;
import com.osdb.ippay.secondary.merchant.service.MerchantService;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

import static lombok.AccessLevel.PRIVATE;

@Slf4j
@Component
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class UserMerchantMapper {

    MerchantService merchantService;

    public UserMerchantDto toDto(UserMerchant userMerchant) {
        Merchant merchant = null;

        try {
            merchant = merchantService.find(userMerchant.getMerchantId());

        } catch (Exception ex) {
            log.warn(ex.getMessage());
        }

        return UserMerchantDto.builder()
                .merchantId(userMerchant.getMerchantId())
                .merchantName(merchant != null ?  merchant.getMerchantName() : null)
                .jetpayMID(merchant != null ? merchant.getJetpayMID() : null)
                .build();
    }

    public List<UserMerchantDto> toDto(List<UserMerchant> merchants) {
        return merchants.stream()
                .map(this::toDto)
                .collect(Collectors.toList());
    }
}
