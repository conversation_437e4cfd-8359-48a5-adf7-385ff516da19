package com.osdb.ippay.primary.auth.facade.dto;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.*;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

import static lombok.AccessLevel.PRIVATE;

@Getter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
public class ChangePasswordDto {

    @Parameter(required = true)
    @NotBlank(message = "Current password is required")
    String currentPassword;

    @Parameter(required = true)
    @Pattern(
            regexp = "^(?=.*?\\p{Lu})(?=.*?\\p{Ll})(?=.*?\\d)" +
                    "(?=.*?[`~!@#$%^&*()\\-_=+\\\\|\\[{\\]};:'\",<.>/?])(?=.{8,}).*$",
            message = "Incorrect Password"
    )
    @NotBlank(message = "New password is required")
    String newPassword;

}
