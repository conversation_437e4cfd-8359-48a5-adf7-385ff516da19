package com.osdb.ippay.secondary.statement.repository.entity;

import com.osdb.ippay.common.entity.BaseEntity;
import lombok.*;
import lombok.experimental.FieldDefaults;
import lombok.experimental.SuperBuilder;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import static lombok.AccessLevel.PRIVATE;

@Entity
@Table(name = "MERCHANT_STATEMENT_OTHER_CARD_DEPOSIT")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = PRIVATE)
@SuperBuilder(toBuilder = true)
public class MSOtherCardDeposit extends BaseEntity {

    @Column(name = "MERCHANT_ID")
    String merchantId;

    @Column(name = "DATE")
    String date;

    @Column(name = "BATCH_AMOUNT")
    String batchAmount;

    @Column(name = "PAID_BY")
    String paidBy;

    @Column(name = "NET_AMOUNT")
    String netAmount;

}
