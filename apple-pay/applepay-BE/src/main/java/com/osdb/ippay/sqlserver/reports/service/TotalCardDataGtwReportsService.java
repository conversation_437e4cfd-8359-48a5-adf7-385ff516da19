package com.osdb.ippay.sqlserver.reports.service;

import com.osdb.ippay.sqlserver.reports.repository.entity.TotalACHDataGtwReports;
import com.osdb.ippay.sqlserver.reports.repository.entity.TotalACHGtwData;
import com.osdb.ippay.sqlserver.reports.repository.entity.TotalCardDataGtwReports;
import com.osdb.ippay.sqlserver.reports.repository.entity.TotalCardGtwData;
import com.osdb.ippay.sqlserver.reports.service.filter.TotalACHDataGtwReportsFilter;
import com.osdb.ippay.sqlserver.reports.service.filter.TotalACHGtwDataFilter;
import com.osdb.ippay.sqlserver.reports.service.filter.TotalCardDataGtwReportsFilter;
import com.osdb.ippay.sqlserver.reports.service.filter.TotalCardGtwDataFilter;

import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface TotalCardDataGtwReportsService {

	 Page<TotalCardDataGtwReports> find(Long ipTransactionId, 
				String authStartDateTime, 
				String authEndDateTime,
				TotalCardDataGtwReportsFilter filter, Pageable pageable);

	  
    List<TotalCardDataGtwReports> find( Long ipTransactionId, 
    									String authStartDateTime, 
    									String authEndDateTime,
    									TotalCardDataGtwReportsFilter filter);
    
    Page<TotalCardGtwData> findData(Long ipTransactionId, 
			String authStartDateTime, 
			String authEndDateTime,
			TotalCardGtwDataFilter filter, Pageable pageable);
    
    
    List generateReport( Long ipTransactionId, 
			String authStartDateTime, 
			String authEndDateTime);
}

