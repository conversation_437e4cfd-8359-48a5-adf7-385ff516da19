package com.osdb.ippay.secondary.statement.facade.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

import static lombok.AccessLevel.PRIVATE;

@Getter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class MSOtherDetailDto {

    Long id;

    String description;

    String amount;

    String discountRate;

    String numX;

    String transactionFee;

    String otherFee;

}
