package com.osdb.ippay.secondary.alternatepay.transaction.controller.mapper;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.*;

import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import static lombok.AccessLevel.PRIVATE;

@Component
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class TransactionInitRecurringControllerMapper {

    @NonFinal
    @Value("${tokenization.api-key}")
    String apiKey;
    
    @NonFinal
    @Value("${tokenization.branch-code}")
    String branchCode;

    public TransactionInitRecurringDto toDto(TransactionInitRecurringDto transactionInitDto) {
        return TransactionInitRecurringDto.builder()
                .branchId(branchCode)
                .apiKey(apiKey)
                .amount(transactionInitDto.getAmount())
                .currency(transactionInitDto.getCurrency())
                .callbackURL(transactionInitDto.getCallbackURL())
                .subscription(transactionInitDto.getSubscription())
                .isCent(transactionInitDto.isCent())              
                .timeOut(transactionInitDto.getTimeOut())
                .methodType(transactionInitDto.getMethodType())
                .build();
    }

  
    public TransactionInitRecurringResultDto toOutDto(TransactionInitRecurringResultDto trnInitResultDto) {
        return TransactionInitRecurringResultDto.builder()
                .amount(trnInitResultDto.getAmount())
                .currency(trnInitResultDto.getCurrency())
                .date(trnInitResultDto.getDate())
                .subscription(trnInitResultDto.getSubscription())
                .methodType(trnInitResultDto.getMethodType())                
                .status(trnInitResultDto.getStatus())
                .statusName(trnInitResultDto.getStatusName())
                .timeoutDate(trnInitResultDto.getTimeoutDate())
                .TransactionNumber(trnInitResultDto.getTransactionNumber())
                .paymentType("Apple Pay")
                
                .build();
    }
    
    public SubscriptionCancelDto toSubCancelDto(SubscriptionCancelDto subscriptionCancelDto) {
        return SubscriptionCancelDto.builder()
                .status("cancelled")
                .build();
    }

}
