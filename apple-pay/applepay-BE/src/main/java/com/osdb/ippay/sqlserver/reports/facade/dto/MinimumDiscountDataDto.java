package com.osdb.ippay.sqlserver.reports.facade.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

import static javax.persistence.GenerationType.IDENTITY;
import static lombok.AccessLevel.PRIVATE;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;

@Getter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class MinimumDiscountDataDto {


   Long Id;
	 
   String bankId;

   String groupId;

   String associationId;
	 
   String merchantId;

   String merchantDBAName;

   String minDiscount;

   String minDiscountGLCode;
   
   String minDiscountUserDataCode;

   String startDate;

   String stopDate;
   
   String billingMethod;


}