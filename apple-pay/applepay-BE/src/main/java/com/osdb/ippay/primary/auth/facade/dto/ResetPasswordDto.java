package com.osdb.ippay.primary.auth.facade.dto;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

import static lombok.AccessLevel.PRIVATE;

@Getter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
public class ResetPasswordDto {

    @Parameter(required = true)
    @NotBlank(message = "Token is required")
    String token;

    @Parameter(required = true)
    @Pattern(
            regexp = "^(?=.*?\\p{Lu})(?=.*?\\p{Ll})(?=.*?\\d)" +
                     "(?=.*?[`~!@#$%^&*()\\-_=+\\\\|\\[{\\]};:'\",<.>/?])(?=.{8,}).*$",
            message = "Incorrect Password"
    )
    @NotBlank(message = "New password is required")
    String newPassword;

}
