package com.osdb.ippay.sqlserveripcore.ipwhitelist.service.impl;

import com.osdb.ippay.sqlserveripcore.ipwhitelist.repository.GlobalWhitelistIpsRepository;
import com.osdb.ippay.sqlserveripcore.ipwhitelist.repository.TerminalIpsBlockingRepository;
import com.osdb.ippay.sqlserveripcore.ipwhitelist.repository.entity.TerminalIpsBlocking;
import com.osdb.ippay.sqlserveripcore.ipwhitelist.repository.entity.TerminalIpsGlobal;
import com.osdb.ippay.sqlserveripcore.ipwhitelist.service.TerminalIpsGlobalService;
import com.osdb.ippay.sqlserveripcore.ipwhitelist.service.filter.TerminalIpsBlockingFilter;
import com.osdb.ippay.sqlserveripcore.ipwhitelist.service.filter.TerminalIpsGlobalFilter;

import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import static lombok.AccessLevel.PRIVATE;

import javax.persistence.criteria.Predicate;

@Service
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class TerminalIpsGlobalServiceImpl implements TerminalIpsGlobalService {
    
    GlobalWhitelistIpsRepository globalWhitelistIpsRepository;
    TerminalIpsBlockingRepository terminalIpsBlockingRepository;
  
	@Override
	public Page<TerminalIpsGlobal> findData(TerminalIpsGlobalFilter filter,
			Pageable pageable) {

		 Specification<TerminalIpsGlobal> specification = filterBy(filter);

	        PageRequest pageRequest = PageRequest.of(
	                pageable.getPageNumber(),
	                pageable.getPageSize(),
	                getSort(pageable)
	        );

	        return globalWhitelistIpsRepository.findAll(specification, pageRequest);
	}
	
	private Specification<TerminalIpsGlobal> filterBy(TerminalIpsGlobalFilter filter) {
        return (r, rq, cb) -> {

        	 Predicate activeStatus = (filter.getActiveStatus() != null) ?
           		  cb.equal(
                             r.get("active"),
                             filter.getActiveStatus()
                     ) : cb.conjunction();
           
        	

            return cb.and(
            		activeStatus
            );
        };
    }

	private Specification<TerminalIpsBlocking> filterByTerminalIds(TerminalIpsBlockingFilter filter) {
        return (r, rq, cb) -> {

        	 Predicate activeStatus = (filter.getActiveStatus() != null) ?
              		  cb.equal(
                                r.get("active"),
                                filter.getActiveStatus()
                        ) : cb.conjunction();
           
        	   Predicate terminalIdPredicate = (filter.getTerminalID() != null) ?
             		  cb.equal(
                               r.get("terminalId"),
                               filter.getTerminalID()
                       ) : cb.conjunction();
             
             Predicate ipAddressPredicate = (filter.getIpAddress() != null) ?
           		  cb.equal(
                             r.get("ipAddress"),
                             filter.getIpAddress()
                     ) : cb.conjunction();
             
            return cb.and(
            		activeStatus,
            		terminalIdPredicate,
            		ipAddressPredicate
            );
        };
    }
	
	
    private Sort getSort(Pageable pageable) {
        Sort.Order order = pageable.getSort().get()
                .findFirst()
                .orElse(Sort.Order.desc("active"));

        Sort.Direction direction = order.getDirection();
        String property = order.getProperty();

        return switch (property) {
    
            default -> Sort.by(order);
        };
    }

	@Override
	public Page<TerminalIpsBlocking> findTerminalData(TerminalIpsBlockingFilter filter, Pageable pageable) {

		 Specification<TerminalIpsBlocking> specification = filterByTerminalIds(filter);

	        PageRequest pageRequest = PageRequest.of(
	                pageable.getPageNumber(),
	                pageable.getPageSize(),
	                getSort(pageable)
	        );

	        return terminalIpsBlockingRepository.findAll(specification, pageRequest);
	        
	}

	@Override
	public void delete(Long id) {
		terminalIpsBlockingRepository.deleteById(id);
	}

}
