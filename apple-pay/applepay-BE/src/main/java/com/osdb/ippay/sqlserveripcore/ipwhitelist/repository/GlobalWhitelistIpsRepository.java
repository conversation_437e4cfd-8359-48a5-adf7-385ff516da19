package com.osdb.ippay.sqlserveripcore.ipwhitelist.repository;

import com.osdb.ippay.sqlserveripcore.ipwhitelist.repository.entity.TerminalIpsGlobal;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface GlobalWhitelistIpsRepository extends
        JpaRepository<TerminalIpsGlobal, Long>,
        JpaSpecificationExecutor<TerminalIpsGlobal> {

	//	public List<TerminalIpsGlobal> getAll();

	//	public TerminalIpsGlobal getByPkey(final Integer id);

		//public Integer getRangeCount(final String search);

		//public List<TerminalIpsGlobal> queryRange();

		//public TerminalIpsGlobal update();

		//public TerminalIpsGlobal update();
	
}
