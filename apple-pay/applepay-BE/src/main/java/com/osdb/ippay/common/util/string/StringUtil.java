package com.osdb.ippay.common.util.string;

import org.apache.commons.lang3.StringUtils;

public class StringUtil {

    private StringUtil() {}

    public static String hashString(String input) {
        if(StringUtils.isBlank(input) || input.length() <= 5) {
            return input;
        }

        return String.valueOf(input.charAt(0)) +
                input.charAt(1) +
                "x".repeat(Math.max(0, input.length() - 5)) +
                input.charAt(input.length() - 3) +
                input.charAt(input.length() - 2) +
                input.charAt(input.length() - 1);
    }
}
