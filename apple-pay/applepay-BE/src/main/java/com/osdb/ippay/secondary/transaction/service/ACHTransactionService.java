package com.osdb.ippay.secondary.transaction.service;

import com.ippay.global.clients.ipp.IppayResponse;
import com.osdb.ippay.common.domain.FileDomain;
import com.osdb.ippay.primary.user.repository.entity.User;
import com.osdb.ippay.secondary.transaction.repository.entity.ACHSettlement;
import com.osdb.ippay.secondary.transaction.repository.entity.ACHTransaction;
import com.osdb.ippay.secondary.transaction.service.domain.RefundTransactionRequest;
import com.osdb.ippay.secondary.transaction.service.domain.VoidTransactionRequest;
import com.osdb.ippay.secondary.transaction.service.filter.AuthDateFilter;
import com.osdb.ippay.secondary.transaction.service.filter.SettlementFilter;
import com.osdb.ippay.secondary.transaction.service.filter.TransactionFilter;

import java.io.IOException;
import java.util.List;

public interface ACHTransactionService {

    List<ACHSettlement> findSettlements(SettlementFilter filter);

    List<ACHTransaction> find(User authUser, TransactionFilter filter);

    ACHTransaction find(Long id);

    FileDomain exportTransactions(User user, TransactionFilter filter) throws IOException;

    FileDomain exportSettlements(User user, TransactionFilter filter) throws IOException;

    List<String> findExistingACHAuthDates(AuthDateFilter filter);

    Double totalAchAmount(TransactionFilter filter);

    Double totalAchAmount(TransactionFilter filter, List<String> merchantIds);

    IppayResponse voidTransaction(Long id, VoidTransactionRequest voidTransactionRequest);

    IppayResponse refundTransaction(Long id, RefundTransactionRequest refundTransactionRequest);

}
