package com.osdb.ippay.secondary.transaction.repository;

import com.osdb.ippay.secondary.transaction.repository.entity.ACHSettlement;
import com.osdb.ippay.secondary.transaction.repository.entity.ACHTransaction;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Optional;

public interface ACHTransactionRepository extends
        JpaRepository<ACHTransaction, Long>,
        JpaSpecificationExecutor<ACHTransaction> {

    @Override
    @EntityGraph(attributePaths = { "merchant" })
    List<ACHTransaction> findAll(Specification<ACHTransaction> specification);

    @Override
    @EntityGraph(attributePaths = { "merchant" })
    Page<ACHTransaction> findAll(Specification<ACHTransaction> specification, Pageable pageable);

    @Override
    @EntityGraph(attributePaths = { "merchant" })
    Optional<ACHTransaction> findById(Long aLong);

    @Query(nativeQuery = true, value =
            "SELECT AuthTransDate " +
                    "FROM ACHTransactions " +
                    "WHERE SettleDate IS NULL AND MerchantID = ?1 LIMIT 15"
    )
    List<String> findAuthDatesForTransactions(String merchantId);

    @Query(nativeQuery = true, value =
            "SELECT SettleDate " +
                    "FROM ACHTransactions " +
                    "WHERE SettleDate IS NOT NULL AND MerchantID = ?1 LIMIT 15"
    )
    List<String> findAuthDatesForSettlements(String merchantId);

    @Query(
            value = "SELECT " +
                    "SettleDate as settleDate, " +
                    "(SUM(IF(RequestType = 'CHECK' AND ResponseText = 'CHECK ACCEPTED', TotalAmount, 0)) " +
                    "- SUM(IF(RequestType = 'VOIDACH' AND ResponseText = 'CHECK ACCEPTED' AND ReturnStatusFlag != 'B', TotalAmount, 0)) " +
                    "- sum(IF(RequestType = 'REVERSAL' AND ResponseText = 'CHECK ACCEPTED' AND ReturnStatusFlag != 'B', TotalAmount, 0))) as settleAmount, " +
                    "COUNT(ID) AS settleCount " +
                    "FROM ACHTransactions " +
                    "WHERE SettleDate >= ?2 AND SettleDate <= ?3 AND MerchantID = ?1 " +
                    "GROUP BY SettleDate " +
                    "ORDER BY settleDate DESC",
            nativeQuery = true
    )
    List<ACHSettlement> getSettlements(String merchantId, String from, String to);

}
