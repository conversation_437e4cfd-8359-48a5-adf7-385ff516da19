package com.osdb.ippay.sqlserver.sftp.repository.entity;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import javax.persistence.*;

import static lombok.AccessLevel.PRIVATE;


@Entity(name = "MerchantPANDetails")
@Table(name = "jetPay_CreditRealTime")


@NamedNativeQueries({
	@NamedNativeQuery(resultClass = MerchantPANDetails.class, name = "MerchantPANDetails.getPANDetails", query = "SELECT RC.Merchant_Name AS Merchant_Name, RC.Merchant_ID AS Merchant_ID, RC.Terminal_ID AS Terminal_ID, RC.Transaction_ID AS Transaction_ID, RC.Request_Type AS Request_Type, RC.Transaction_Type AS Transaction_Type, RC.TotalAmount AS TotalAmount, RC.FeeAmount AS FeeAmount, RC.Tax_Amount AS Tax_Amount, RC.Auth_Code AS Auth_Code, RC.AuthDateTime AS AuthDateTime, ISNULL(RC.CardNum,'') AS CardNum, ISNULL(RC.Token,'') AS Token, RC.CVV2 AS CVV2, RC.CardHolderName AS CardHolderName, RC.[Address] AS Address, RC.City AS City, RC.[State] AS State, RC.Zip AS Zip, RC.Country AS Country, RC.Phone AS Phone, RC.Email AS Email, TS.TokenSourceValue AS TokenSourceValue, TS.TokenTypeCode AS TokenTypeCode, TS.ExpMonth AS ExpMonth, TS.ExpYear AS ExpYear  FROM [ipReports].[dbo].[jetPay_CreditRealTime] AS RC WITH(NOLOCK) INNER JOIN [ipTkn].[dbo].[IPToken] AS TK WITH(NOLOCK) ON TK.TokenValue = RC.Token INNER JOIN [ipTkn].[dbo].[TokenSource] AS TS WITH(NOLOCK) ON TK.TokenSourceId = TS.TokenSourceId INNER JOIN [ipCore].[dbo].[Merchant] AS M WITH(NOLOCK) ON M.rowguid = TS.merchantID  where RC.Merchant_ID >= ?1  ORDER BY RC.ipTransactionID ASC, RC.Merchant_ID ASC, RC.Transaction_ID ASC, RC.AuthDateTime ASC" 
)}) 

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)

public class MerchantPANDetails{

	 @Id
	 @Column(name = "Merchant_ID")
	 String Merchant_ID;

	 @Column(name = "Merchant_Name", insertable=false, updatable = false)
	 String Merchant_Name;

	 @Column(name = "Terminal_ID", insertable=false, updatable = false)
	 String Terminal_ID;
	 
    @Column(name = "Transaction_ID", insertable=false, updatable = false)
    String Transaction_ID;

    @Column(name = "Request_Type", insertable=false, updatable = false)
    String Request_Type;

    @Column(name = "TotalAmount", insertable=false, updatable = false)
    String TotalAmount;

    @Column(name = "FeeAmount", insertable=false, updatable = false)
    String FeeAmount;
    
    @Column(name = "Tax_Amount", insertable=false, updatable = false)
    String Tax_Amount;

    @Column(name = "Auth_Code", insertable=false, updatable = false)
    String Auth_Code;

    @Column(name = "AuthDateTime", insertable=false, updatable = false)
    String AuthDateTime;

    @Column(name = "CardNum", insertable=false, updatable = false)
    String CardNum;
    
    @Column(name = "Token", insertable=false, updatable = false)
    String Token;
    
    @Column(name = "CVV2", insertable=false, updatable = false)
    String CVV2;
    
    @Column(name = "CardHolderName", insertable=false, updatable = false)
    String CardHolderName;
    
    @Column(name = "Address", insertable=false, updatable = false)
    String Address;
    
    @Column(name = "City", insertable=false, updatable = false)
    String City;
    
    @Column(name = "State", insertable=false, updatable = false)
    String State;
    
    @Column(name = "Zip", insertable=false, updatable = false)
    String Zip;
    
    @Column(name = "Country", insertable=false, updatable = false)
    String Country;
    
    @Column(name = "Phone", insertable=false, updatable = false)
    String Phone;
    
    @Column(name = "Email", insertable=false, updatable = false)
    String Email;
    
    @Column(name = "TokenSourceValue", insertable=false, updatable = false)
    String TokenSourceValue;
    
    @Column(name = "TokenTypeCode", insertable=false, updatable = false)
    String TokenTypeCode;
    
    @Column(name = "ExpMonth", insertable=false, updatable = false)
    String ExpMonth;
    
    @Column(name = "ExpYear", insertable=false, updatable = false)
    String ExpYear;
    
    }

