package com.osdb.ippay.primary.user.facade.mapper;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.osdb.ippay.common.exception.business.JsonParseException;
import com.osdb.ippay.primary.user.facade.dto.UserConfigDto;
import com.osdb.ippay.primary.user.repository.entity.UserConfig;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

import static com.osdb.ippay.common.exception.ErrorMessage.USER_CONFIG_PROPERTY_IS_INVALID;

@Component
public class UserConfigMapper {

    final ObjectMapper mapper = new ObjectMapper();

    public List<UserConfigDto> toDto(List<UserConfig> userConfigs) {
        return userConfigs.stream()
                .map(this::toDto)
                .collect(Collectors.toList());
    }

    public UserConfigDto toDto(UserConfig userConfig) {
        try {
            return UserConfigDto.builder()
                    .id(userConfig.getId())
                    .type(userConfig.getType())
                    .configs(mapper.readTree(userConfig.getConfigs()))
                    .build();

        } catch (IOException ex) {
            throw new JsonParseException(USER_CONFIG_PROPERTY_IS_INVALID);
        }
    }

    public UserConfig toEntity(Long userId, UserConfigDto userConfigDto) {
        return UserConfig.builder()
                .userId(userId)
                .type(userConfigDto.getType())
                .configs(userConfigDto.getConfigs().toPrettyString())
                .build();
    }
}
