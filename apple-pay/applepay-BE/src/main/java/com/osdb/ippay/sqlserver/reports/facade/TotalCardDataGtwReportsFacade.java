package com.osdb.ippay.sqlserver.reports.facade;

import com.osdb.ippay.common.domain.FileDomain;
import com.osdb.ippay.sqlserver.reports.facade.dto.TotalACHDataGtwReportsDto;
import com.osdb.ippay.sqlserver.reports.facade.dto.TotalACHGtwDataDto;
import com.osdb.ippay.sqlserver.reports.facade.dto.TotalCardDataGtwReportsDto;
import com.osdb.ippay.sqlserver.reports.facade.dto.TotalCardGtwDataDto;
import com.osdb.ippay.sqlserver.reports.service.filter.TotalACHDataGtwReportsFilter;
import com.osdb.ippay.sqlserver.reports.service.filter.TotalACHGtwDataFilter;
import com.osdb.ippay.sqlserver.reports.service.filter.TotalCardDataGtwReportsFilter;
import com.osdb.ippay.sqlserver.reports.service.filter.TotalCardGtwDataFilter;

import java.io.IOException;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;


public interface TotalCardDataGtwReportsFacade {
	
    FileDomain export(Long ipTransactionID, String authStartDateTime, String authEndDateTime,  TotalCardDataGtwReportsFilter filter) throws IOException;
    Page<TotalCardGtwDataDto> findData(Long ipTransactionID, String authStartDateTime, String authEndDateTime, TotalCardGtwDataFilter filter, Pageable pageable);
    Page<TotalCardDataGtwReportsDto> find(Long ipTransactionID, String authStartDateTime, String authEndDateTime,  TotalCardDataGtwReportsFilter filter, Pageable pageable);
}