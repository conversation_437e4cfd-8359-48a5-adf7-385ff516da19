package com.osdb.ippay.common.merchant.controller;

import com.osdb.ippay.common.logging.LogExecutionTime;
import com.osdb.ippay.common.merchant.facade.BankFacade;
import com.osdb.ippay.common.merchant.facade.dto.BankDto;
import com.osdb.ippay.common.security.service.AuthenticationService;
import com.osdb.ippay.common.util.annotation.ApiPageable;
import com.osdb.ippay.common.util.annotation.ApiSortable;
import com.osdb.ippay.common.util.response.PageResponse;
import com.osdb.ippay.secondary.merchant.service.filter.BankFilter;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static lombok.AccessLevel.PRIVATE;

@Tag(name = "bank")
@RestController
@RequestMapping(value = "/api/v1/private/banks")
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class BankController {
/*
    AuthenticationService authenticationService;
    BankFacade bankFacade;

    @LogExecutionTime
    @ApiSortable
    @GetMapping
    public ResponseEntity<List<BankDto>> get(@Parameter(hidden = true) Sort sort,
                                             @ParameterObject BankFilter filter,
                                             @Parameter(hidden = true) @AuthenticationPrincipal String email) {

        authenticationService.throwIfNotInternal(email);

        List<BankDto> response = bankFacade.find(filter, sort);
        return ResponseEntity.ok(response);
    }

    @LogExecutionTime
    @ApiPageable
    @GetMapping("/pageable")
    public ResponseEntity<PageResponse<BankDto>> get(@Parameter(hidden = true) Pageable pageable,
                                                     @ParameterObject BankFilter filter,
                                                     @Parameter(hidden = true) @AuthenticationPrincipal String email) {

        authenticationService.throwIfNotInternal(email);

        Page<BankDto> response = bankFacade.find(filter, pageable);
        return ResponseEntity.ok(new PageResponse<BankDto>(response.getContent(), response.getTotalElements()));
    }*/
}
