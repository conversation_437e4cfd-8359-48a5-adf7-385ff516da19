package com.osdb.ippay.secondary.transaction.facade.mapper;

import com.osdb.ippay.common.util.date.DateUtil;
import com.osdb.ippay.common.util.number.DoubleUtil;
import com.osdb.ippay.secondary.transaction.facade.dto.ACHSettlementDto;
import com.osdb.ippay.secondary.transaction.facade.dto.ACHSettlementResponse;
import com.osdb.ippay.secondary.transaction.repository.entity.ACHSettlement;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
public class ACHSettlementMapper {

    public ACHSettlementResponse toDto(List<ACHSettlement> achSettlements) {
        double totalAmount = achSettlements.stream()
                .map(ACHSettlement::getSettleAmount)
                .mapToDouble(Double::valueOf)
                .sum();

        long totalCount = achSettlements.stream()
                .map(ACHSettlement::getSettleCount)
                .mapToLong(Long::valueOf)
                .sum();

        List<ACHSettlementDto> settlements = achSettlements.stream()
                .map(this::toDto)
                .collect(Collectors.toList());

        return ACHSettlementResponse.builder()
                .settlements(settlements)
                .totalAmount(DoubleUtil.toPriceFormat(totalAmount, 2, 1))
                .totalCount(totalCount)
                .build();
    }

    public ACHSettlementDto toDto(ACHSettlement achSettlement) {
        String totalAmount = achSettlement.getSettleAmount() != null ?
                DoubleUtil.toPriceFormat(achSettlement.getSettleAmount(), 2, 1) :
                "0.00";

        return ACHSettlementDto.builder()
                .settleDate(
                        Objects.nonNull(achSettlement.getSettleDate()) ?
                                DateUtil.parseToInstant(achSettlement.getSettleDate()) :
                                null
                )
                .settleAmount(totalAmount)
                .settleCount(achSettlement.getSettleCount())
                .build();
    }
}
