package com.osdb.ippay.secondary.reports.repository.entity;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import javax.persistence.*;

import static lombok.AccessLevel.PRIVATE;



@Entity(name = "CreditCountReport")
@Table(name = "CCTransactions")

/*
@NamedNativeQueries({
	@NamedNativeQuery(resultClass = SettledTransaction.class, name = "SettledTransaction.getMonthlyOverlimitSettlements", query = "SELECT Distinct merchantId, email, '09:30' as triggerAlertTime, settlementDate, SUM(IF(RequestType = 'SALE' OR RequestType = 'CAPT', SettlementAmount, 0)) as actualAmount,IF((SUM(IF(RequestType = 'SALE' OR RequestType = 'CAPT', SettlementAmount, 0))/(select 2000 from CCTransactions limit 1))>1,100,(IF((SUM(IF(RequestType = 'SALE' OR RequestType = 'CAPT', SettlementAmount, 0))/(select 2000 from CCTransactions limit 1))>0.9,90,70))) as percentageHit, (select 2000 from CCTransactions limit 1) as amountHitOrExceeded, (SUM(IF(RequestType = 'SALE' OR RequestType = 'CAPT', SettlementAmount, 0)) - (select 2000 from CCTransactions limit 1)) as overlimitAmount FROM CCTransactions WHERE SettlementDate >= ?1 AND SettlementDate <= ?2 GROUP BY merchantId,settlementDate,email, triggerAlertTime, amountHitOrExceeded HAVING SUM(IF(RequestType = 'SALE' OR RequestType = 'CAPT', SettlementAmount, 0)) > 2000" 
),@NamedNativeQuery(resultClass = SettledTransaction.class, name = "SettledTransaction.getMonthlyOverlimitSettlementsCount", query = "SELECT Distinct merchantId, email, '09:30' as triggerAlertTime, settlementDate, SUM(IF(RequestType = 'SALE' OR RequestType = 'CAPT', SettlementAmount, 0)) as actualAmount,IF((COUNT(IF(RequestType = 'SALE' OR RequestType = 'CAPT', SettlementAmount, 0))/(select 10 from CCTransactions limit 1))>1,100,(IF((COUNT(IF(RequestType = 'SALE' OR RequestType = 'CAPT', SettlementAmount, 0))/(select 10 from CCTransactions limit 1))>0.9,90,70))) as percentageHit, (select 10 from CCTransactions limit 1) as amountHitOrExceeded, (COUNT(IF(RequestType = 'SALE' OR RequestType = 'CAPT', SettlementAmount, 0)) - (select 10 from CCTransactions limit 1)) as overlimitAmount FROM CCTransactions WHERE SettlementDate >= ?1 AND SettlementDate <= ?2  GROUP BY merchantId,settlementDate,email, triggerAlertTime, amountHitOrExceeded HAVING SUM(IF(RequestType = 'SALE' OR RequestType = 'CAPT', SettlementAmount, 0)) > 2000" 
		)})
*/


@NamedNativeQueries({
	@NamedNativeQuery(resultClass = CreditCountReport.class, name = "CreditCountReport.getCreditCountReport", query = "SELECT MerchantID as merchantId, jetpay_mid, SUM(TransAmount) as refundAmount, COUNT(MerchantID) as refundCount from CCTransactions CCTrn left outer join MERCHANTS Mer on CCTrn.MerchantID = Mer.mid where SettlementDate >= ?1 AND SettlementDate <= ?2 and RequestType='SALE' Group By MerchantID" 
)})



@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)

public class CreditCountReport{

	//@Id
	//@Column(name = "id")
   // Long ipTransId;
	 
	 @Id
	 @Column(name = "merchantId", insertable=false, updatable = false)
	 String merchantId;

	 @Column(name = "jetpay_mid", insertable=false, updatable = false)
	 String jetpay_mid;

	 @Column(name = "refundAmount", insertable=false, updatable = false)
	 String refundAmount;
	 
    @Column(name = "refundCount", insertable=false, updatable = false)
    String refundCount;

   
    }

