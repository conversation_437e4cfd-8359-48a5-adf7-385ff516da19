package com.osdb.ippay.secondary.alternatepay.transaction.controller.mapper;

import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.*;

import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import static lombok.AccessLevel.PRIVATE;

@Component
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class TransactionCommitGetControllerMapper {

    @NonFinal
    @Value("${tokenization.api-key}")
    String apiKey;
    
    @NonFinal
    @Value("${tokenization.branch-code}")
    String branchCode;

    public TransactionCommitDto toDto(TransactionCommitDto transactionGetDto) {
        return TransactionCommitDto.builder()
               
        		
                .apiKey(apiKey)
                .amount(transactionGetDto.getAmount())
                .currency(transactionGetDto.getCurrency())
                .callbackURL(transactionGetDto.getCallbackURL())
                .isCent(transactionGetDto.isCent())              
                .timeOut(transactionGetDto.getTimeOut())
                .methodType(transactionGetDto.getMethodType())
                .build();
    }

  
    public TransactionGetResultDto toOutDto(TransactionGetResultDto trnGetResultDto) {
        return TransactionGetResultDto.builder()
                .amount(trnGetResultDto.getAmount())
                .currency(trnGetResultDto.getCurrency())
                .date(trnGetResultDto.getDate())
                .branchId(trnGetResultDto.getBranchId())
                .branchNumber(trnGetResultDto.getBranchNumber())
                .businessAddress(trnGetResultDto.getBusinessAddress())
                .businessCity(trnGetResultDto.getBusinessCity())
                .businessCountry(trnGetResultDto.getBusinessCountry())
                .businessId(trnGetResultDto.getBusinessId())
                .businessName(trnGetResultDto.getBusinessName())
                .businessOwnerPhone(trnGetResultDto.getBusinessOwnerPhone())
                .businessStreet(trnGetResultDto.getBusinessStreet())
                .pos(trnGetResultDto.getPos())
                .receiptPending(trnGetResultDto.getReceiptPending())
                .status(6)
                .statusName("Payment Committed")
                .timeoutDate(trnGetResultDto.getTimeoutDate())
                .TransactionNumber(trnGetResultDto.getTransactionNumber())
                .message(trnGetResultDto.getMessage())
                .response(trnGetResultDto.getResponse())
                .paymentType("Apple Pay")
                
                .build();
    }

}
