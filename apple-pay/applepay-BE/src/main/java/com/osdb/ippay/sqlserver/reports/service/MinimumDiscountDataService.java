package com.osdb.ippay.sqlserver.reports.service;

import com.osdb.ippay.primary.user.repository.entity.User;
import com.osdb.ippay.secondary.chargeback.repository.entity.Chargeback;
import com.osdb.ippay.secondary.chargeback.service.ChargebackFilter;
import com.osdb.ippay.sqlserver.reports.repository.entity.TotalACHDataGtwReports;
import com.osdb.ippay.sqlserver.reports.repository.entity.TotalACHGtwData;
import com.osdb.ippay.sqlserver.reports.service.filter.TotalACHDataGtwReportsFilter;
import com.osdb.ippay.sqlserver.reports.service.filter.TotalACHGtwDataFilter;

import java.util.ArrayList;
import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface MinimumDiscountDataService {

	void insertMinimumDiscountData( String bankId, 
			String groupId, 
			String associationId, 
			String merchantId, 
			String merchantDBAName, 
			String minDiscount,
			String minDiscountGLCode,
			String minDiscountUserDataCode,
			String startDate,
			String stopDate,
			String billingMethod);
}
