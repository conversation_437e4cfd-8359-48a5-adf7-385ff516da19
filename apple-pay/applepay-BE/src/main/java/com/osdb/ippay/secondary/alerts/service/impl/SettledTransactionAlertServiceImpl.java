package com.osdb.ippay.secondary.alerts.service.impl;


import com.osdb.ippay.secondary.alerts.repository.MerchantSettlementRepository;
import com.osdb.ippay.secondary.alerts.repository.SettledTransactionRepository;
import com.osdb.ippay.secondary.alerts.repository.entity.Merchants;
import com.osdb.ippay.secondary.alerts.repository.entity.SettledTransaction;
import com.osdb.ippay.secondary.alerts.service.SettledTransactionAlertService;
import com.osdb.ippay.secondary.alerts.service.filter.SettledTransactionFilter;

import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.persistence.EntityManager;
import javax.persistence.criteria.Predicate;

import static lombok.AccessLevel.PRIVATE;

@Service
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class SettledTransactionAlertServiceImpl implements SettledTransactionAlertService {

	SettledTransactionRepository settledTransactionRepository;
	MerchantSettlementRepository merchantSettlementRepository;
	public static java.util.Date currDate = new java.util.Date();
	public static String eportType = "";
	
	EntityManager entityManager;
	Map<String, List<SettledTransaction>> mapSettledTrnList = new HashMap<>();

	    @Autowired
	    public SettledTransactionAlertServiceImpl(SettledTransactionRepository settldTransactionRepository,
	    		MerchantSettlementRepository merchantSetlmtRepository,
	    		SettledTransactionRepository settledTransactionAlert,
	                                     @Qualifier("secondaryEmFactory") EntityManager entityManager) {

	        this.settledTransactionRepository = settldTransactionRepository;
	        this.merchantSettlementRepository = merchantSetlmtRepository;
	        this.entityManager = entityManager;
	    }
	    

	private Specification<SettledTransaction> filterBy(SettledTransactionFilter filter) {
        return (r, rq, cb) -> {

           
            Predicate fromDatePredicate = StringUtils.isNotBlank(filter.getStartDateTime()) ?
                    cb.greaterThanOrEqualTo(
                            r.get("startDateTime"),
                            filter.getStartDateTime()
                    ) : cb.conjunction();

            Predicate toDatePredicate = StringUtils.isNotBlank(filter.getStartDateTime()) ?
                    cb.lessThanOrEqualTo(
                            r.get("endDateTime"),
                            filter.getStartDateTime()
                    ) : cb.conjunction();

          

            return cb.and(
            		
                    fromDatePredicate,
                    toDatePredicate
            );
        };
    }


    private Sort getSort(Pageable pageable) {
        Sort.Order order = pageable.getSort().get()
                .findFirst()
                .orElse(Sort.Order.desc("authDateTime"));

        Sort.Direction direction = order.getDirection();
        String property = order.getProperty();

        return switch (property) {
    
            default -> Sort.by(order);
        };
    }

    @Override
    public List generateReport(String startDateTime,
			String endDateTime) {
    	List  settledOverlimitTransactions = settledTransactionRepository.getMonthlyOverlimitSettlements(startDateTime, endDateTime);
		
		if (settledOverlimitTransactions.isEmpty()) {
			
				return new ArrayList<>();
	    }
		

	    if (settledOverlimitTransactions.get(0) instanceof String) {
	        return ((List<String>) settledOverlimitTransactions)
	          .stream()
	          .map(s -> new String[] { s })
	          .collect(Collectors.toList());
	    } else {
	        return (List<String[]>) settledOverlimitTransactions;
		
		//return totalACHDataGtwReportsRepository.getTotalACHSummaryByModelEntiy(ipTransactionId, authStartDateTime, authEndDateTime);
		//return null;
	}
}

	
	@Override
	public Map<String, List<SettledTransaction>> generateCSV(String startDateTime,
			String endDateTime) {
		
		/*List<Merchants>  merchants = merchantSettlementRepository.findAll();	
		
		mapSettledTrnList.clear();
		
		for(Merchants merchantsObj : merchants)
		{
			String Mid = merchantsObj.getMerchantId();
			List<SettledTransaction>  settledOverlimitTransactions = settledTransactionRepository.getMonthlyOverlimitSettlements(Mid, startDateTime, endDateTime);
			mapSettledTrnList.put(Mid, settledOverlimitTransactions);
		}
		*/
		
		return mapSettledTrnList;
		
		/*List results =  entityManager.createNamedStoredProcedureQuery("SettledTransaction.getOverlimitSettledTransactions")
				.setParameter("startDateTime",startDateTime)
				.setParameter("endDateTime",endDateTime).getResultList();
		
		if (results.isEmpty()) {
	        return new ArrayList<>();
	    }

	    if (results.get(0) instanceof String) {
	        return ((List<String>) results)
	          .stream()
	          .map(s -> new String[] { s })
	          .collect(Collectors.toList());
	    } else {
	        return (List<String[]>) results;*/		
	
	//}
}


	@Override
	public List<SettledTransaction> find(String startDateTime, String endDateTime, SettledTransactionFilter filter) {

		List<SettledTransaction>  settledOverlimitTransactions = settledTransactionRepository.getOverlimitSettledTransactions(startDateTime, endDateTime);
		
		return settledOverlimitTransactions;
	}


	@Override
	public List generateCountReport(String startDateTime, String endDateTime) {

List  settledOverlimitTransactions = settledTransactionRepository.getMonthlyOverlimitSettlementsCount(startDateTime, endDateTime);
		
		if (settledOverlimitTransactions.isEmpty()) {
			
				return new ArrayList<>();
	    }
		
	    if (settledOverlimitTransactions.get(0) instanceof String) {
	        return ((List<String>) settledOverlimitTransactions)
	          .stream()
	          .map(s -> new String[] { s })
	          .collect(Collectors.toList());
	    } else {
	        return (List<String[]>) settledOverlimitTransactions;
	}

	}
}
