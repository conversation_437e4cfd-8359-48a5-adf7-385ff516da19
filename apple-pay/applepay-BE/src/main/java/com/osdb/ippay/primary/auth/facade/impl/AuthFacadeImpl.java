package com.osdb.ippay.primary.auth.facade.impl;

import com.osdb.ippay.common.exception.business.IncorrectPasswordException;
import com.osdb.ippay.common.exception.business.NotFoundException;
import com.osdb.ippay.common.exception.business.PasswordExpiredException;
import com.osdb.ippay.common.exception.business.UnableToLoginException;
import com.osdb.ippay.common.security.filter.CookieAuthFilter;
import com.osdb.ippay.common.security.provider.JwtProvider;
import com.osdb.ippay.common.security.service.AuthenticationService;
import com.osdb.ippay.email.bean.Message;
import com.osdb.ippay.email.service.EmailService;
import com.osdb.ippay.primary.auth.facade.AuthFacade;
import com.osdb.ippay.primary.auth.facade.dto.*;
import com.osdb.ippay.common.user.facade.dto.UserDto;
import com.osdb.ippay.common.user.facade.mapper.UserMapper;
import com.osdb.ippay.primary.user.repository.entity.DefaultConfig;
import com.osdb.ippay.primary.user.repository.entity.User;
import com.osdb.ippay.primary.user.repository.entity.UserConfig;
import com.osdb.ippay.primary.user.service.DefaultConfigService;
import com.osdb.ippay.primary.user.service.UserConfigService;
import com.osdb.ippay.primary.user.service.UserPasswordService;
import com.osdb.ippay.primary.user.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.security.SecureRandom;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static com.osdb.ippay.common.exception.ErrorMessage.*;
import static com.osdb.ippay.common.exception.ErrorMessage.AUTH_DISABLED_ACCOUNT;
import static com.osdb.ippay.primary.user.repository.entity.UserStatus.ACTIVE;
import static com.osdb.ippay.primary.user.repository.entity.UserStatus.INVITED;
import static lombok.AccessLevel.PRIVATE;

@Slf4j
@Component
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class AuthFacadeImpl implements AuthFacade {

    // services
    UserService userService;
    EmailService emailService;
    AuthenticationService authenticationService;
    UserPasswordService userPasswordService;
    DefaultConfigService defaultConfigService;
    UserConfigService userConfigService;

    // providers / mappers
    JwtProvider jwtProvider;
    PasswordEncoder passwordEncoder;
    UserMapper userMapper;

    @NonFinal
    @Value(value = "${allowed.domain}")
    String domain;

    @NonFinal
    @Value(value = "${security.jwt.access-token.expire-length}")
    Integer cookieExpired;

    @NonFinal
    @Value(value = "${mail.specific.template.fp}")
    String forgotPasswordTemplate;

    @NonFinal
    @Value(value = "${mail.specific.template.fp.reset-url}")
    String forgotPasswordResetURL;

    @NonFinal
    @Value(value = "${security.password.expire-length}")
    Integer passwordExpired;

    @NonFinal
    @Value("${security.jwt.reset-token.expire-length}")
    Long resetPasswordExpiration;

    @Override
    public void signInStepOne(SignInStepOneDto stepOneDto) {
        User user = authenticationService.signInStepOne(stepOneDto);
        throwIfPasswordIsExpired(user);

        //String otp = String.format("%06d", new SecureRandom().nextInt(999999));
        String otp = "123456";
        user.setOtp(passwordEncoder.encode(otp));
        user.setSessionsHash(Optional.ofNullable(user.getSessionsHash()).orElse(UUID.randomUUID().toString()));
        userService.save(user);

        // send one time password to email
        sendOneTimePassword(user.getEmail(), otp);
    }

    @Override
    public UserDto signInStepTwo(SignInStepTwoDto stepTwoDto,
                                 HttpServletRequest request,
                                 HttpServletResponse response) {

        User user = authenticationService
                .signInStepTwo(
                        SignInStepTwoDto.builder()
                                .email(stepTwoDto.getEmail())
                                .otp(stepTwoDto.getOtp())
                                .build(),
                        request
                );

        SecurityContextHolder.getContext().setAuthentication(
                new UsernamePasswordAuthenticationToken(
                        stepTwoDto.getEmail(),
                        stepTwoDto.getOtp()
                )
        );

        String accessToken = jwtProvider.createAccessToken(user.getEmail(), user.getSessionsHash());
        addAuthCookie(accessToken, response);

        // save default configs if not exists
        saveDefaultConfigs(user.getId());

        return userMapper.toDto(user);
    }

    @Override
    public void signOut(HttpServletRequest request, HttpServletResponse response) {
        Cookie[] cookies = request.getCookies();

        if (cookies != null) {
            for (Cookie cookie : cookies) {
                cookie.setValue("");
                cookie.setPath("/");
                cookie.setDomain(domain);
                cookie.setMaxAge(0);

                response.addCookie(cookie);
            }

            SecurityContextHolder.clearContext();
        }
    }

    @Override
    public void signOut(HttpServletRequest request, HttpServletResponse response, String email) {
        signOut(request, response);

        // update current session hash
        User user = userService.findByEmail(email);
        user.setSessionsHash(UUID.randomUUID().toString());
        userService.save(user);
    }

    @Override
    public void forgotPassword(ForgotPasswordDto forgotPassword) {
        try {
            User user = userService.findByEmail(forgotPassword.getEmail());
            throwIfNonActive(user);

            String resetPwdToken = jwtProvider.createResetToken(user);

            Map<String, Object> params = new HashMap<>();
            params.put("username", user.getEmail());
            params.put("resetUrl", forgotPasswordResetURL);
            params.put("resetToken", resetPwdToken);
            params.put("fpExpired", resetPasswordExpiration / 60 / 1000 / 60);

            Message message = Message.builder()
                    .subject("Change Password")
                    .to(user.getEmail())
                    .template(forgotPasswordTemplate)
                    .params(params)
                    .build();

            emailService.sendEmail(message);

        } catch (NotFoundException ex) {
            log.warn(ex.getMessage());
        }
    }

    @Transactional
    @Override
    public void resetPassword(ResetPasswordDto resetPasswordDto) {
        String token = resetPasswordDto.getToken();
        String resetPasswordHash = jwtProvider.getResetTokenHash(token);

        User user = userService.findByResetPasswordHash(resetPasswordHash);
        user.setResetPasswordHash(null);
        user.setPassword(passwordEncoder.encode(resetPasswordDto.getNewPassword()));
        user.setStatus(Objects.equals(INVITED, user.getStatus()) ? ACTIVE : user.getStatus());
        user.setSessionsHash(UUID.randomUUID().toString());
        user.setPasswordLastChanged(Instant.now());

        userPasswordService.add(user.getId(), resetPasswordDto.getNewPassword());
        userService.save(user);
    }

    @Transactional
    @Override
    public void changePassword(String email, ChangePasswordDto changePasswordDto) {
        User user = userService.findActive(email);
        throwIfCurrPasswordIsInvalid(user, changePasswordDto.getCurrentPassword());

        user.setPassword(passwordEncoder.encode(changePasswordDto.getNewPassword()));
        user.setSessionsHash(UUID.randomUUID().toString());
        user.setPasswordLastChanged(Instant.now());

        userPasswordService.add(user.getId(), changePasswordDto.getNewPassword());
        userService.save(user);
    }

    @Override
    public UserDto getCurrentUser(String email) {
        User user = userService.findActive(email);
        return userMapper.toDto(user);
    }

    private void sendOneTimePassword(String email, String otp) {
        Map<String, Object> params = new HashMap<>();
        params.put("otp", otp);

        emailService.sendEmail(Message.builder()
                .subject(String.format("Your One time Passcode for reporting is: %s", otp))
                .to(email)
                .template("templates/auth_otp.vm")
                .params(params)
                .build());
    }

    private void addAuthCookie(String accessToken, HttpServletResponse response) {
        Cookie authCookie = new Cookie(CookieAuthFilter.AUTH_COOKIE, accessToken);

        authCookie.setHttpOnly(true);
        authCookie.setSecure(true);
        authCookie.setPath("/");
        authCookie.setDomain(domain);
        authCookie.setMaxAge(cookieExpired / 1000);

        response.addCookie(authCookie);
    }

    private void throwIfCurrPasswordIsInvalid(User authUser, String currPassword) {
        if (!passwordEncoder.matches(currPassword, authUser.getPassword())) {
            throw new IncorrectPasswordException(USER_INCORRECT_CURR_PASSWORD);
        }
    }

    private void throwIfPasswordIsExpired(User user) {
        Instant lastPasswordChanged = user.getPasswordLastChanged();
        Instant lastPasswordExpired = lastPasswordChanged.plus(passwordExpired, ChronoUnit.DAYS);

        if(lastPasswordExpired.isBefore(Instant.now())) {
            throw new PasswordExpiredException(AUTH_PASSWORD_EXPIRED);
        }
    }

    private void saveDefaultConfigs(Long userId) {
        List<UserConfig> userConfigs = userConfigService.find(userId);

        if(CollectionUtils.isEmpty(userConfigs)) {
            List<DefaultConfig> defaultConfigs = defaultConfigService.find();

            userConfigService.saveAll(
                    defaultConfigs.stream()
                            .map(defaultConfig ->
                                    UserConfig.builder()
                                            .userId(userId)
                                            .type(defaultConfig.getType())
                                            .configs(defaultConfig.getConfigs())
                                            .build()
                            ).collect(Collectors.toList())

            );
        }
    }

    private void throwIfNonActive(User user) {
        switch (user.getStatus()) {
            case LOCKED -> throw new UnableToLoginException(AUTH_LOCKED_ACCOUNT);
            case DISABLED -> throw new UnableToLoginException(AUTH_DISABLED_ACCOUNT);
        }
    }
}
