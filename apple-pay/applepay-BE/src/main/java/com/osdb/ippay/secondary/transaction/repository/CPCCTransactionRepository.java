package com.osdb.ippay.secondary.transaction.repository;

import com.osdb.ippay.secondary.transaction.repository.entity.CCSettlement;
import com.osdb.ippay.secondary.transaction.repository.entity.CPCCTransaction;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface CPCCTransactionRepository extends
        JpaRepository<CPCCTransaction, Long>,
        JpaSpecificationExecutor<CPCCTransaction> {

    @Query(
            value = "SELECT " +
                    "SettlementDate as date, " +
                    "SUM(IF(RequestType = 'SALE' OR RequestType = 'CAPT', TransAmount, 0)) as salesAmount, " +
                    "COUNT(IF(RequestType = 'SALE' OR RequestType = 'CAPT', 1, NULL)) as salesCount, " +
                    "SUM(IF(RequestType = 'CREDIT', TransAmount, 0)) as refundAmount, " +
                    "COUNT(IF(RequestType = 'CREDIT', 1, NULL)) as refundCount, " +
                    "SUM(IF((RequestType = 'SALE' OR RequestType = 'CAPT') AND CardType = 'A', TransAmount, 0)) as axAmount, " +
                    "SUM(IF((RequestType = 'CREDIT' OR RequestType = 'CAPT') AND CardType = 'A', TransAmount, 0)) as axRefund, " +
                    "COUNT(IF((RequestType = 'SALE' OR RequestType = 'CREDIT' OR RequestType = 'CAPT') AND CardType = 'A', 1, NULL)) as axCount " +
                    "FROM CardPresentCCTransaction " +
                    "WHERE SettlementDate >= ?2 AND SettlementDate <= ?3 AND MerchantID = ?1 " +
                    "GROUP BY date " +
                    "HAVING salesAmount != 0 " +
                    "ORDER BY date DESC",
            nativeQuery = true
    )
    List<CCSettlement> getSettlements(String merchantId, String from, String to);

}
