package com.osdb.ippay.sqlserveripcore.ipwhitelist.facade.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import static lombok.AccessLevel.PRIVATE;

import com.osdb.ippay.sqlserver.reports.facade.dto.TotalACHDataGtwReportsDto;


@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class TerminalIpsGlobalDto {


    public Long id;

   // public String terminalId;
    
    public String ipAddress;

    public Boolean active;

    public String username;

    public java.sql.Timestamp dateCreated;

    public java.sql.Timestamp dateModified;
    
    }

