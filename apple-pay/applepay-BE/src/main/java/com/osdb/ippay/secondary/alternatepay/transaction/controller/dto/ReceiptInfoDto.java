package com.osdb.ippay.secondary.alternatepay.transaction.controller.dto;

import lombok.*;
import lombok.experimental.FieldDefaults;

import static lombok.AccessLevel.PRIVATE;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class ReceiptInfoDto {

    String customerId;
    String firstName;
    String lastName;
    String address;
    String city;
    String zipCode;
    String socialID;
    String email;
    String languageCode;
    String currencyCode;
    String discount;
    String vat;
}

