package com.osdb.ippay.secondary.alternatepay.transaction.controller.facade.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionCancelResultDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionGetDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionGetResultDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionInitDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionUpdateDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TrasactionInitResultDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.facade.*;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.mapper.TransactionCancelControllerMapper;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.mapper.TransactionGetControllerMapper;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.mapper.TransactionInitControllerMapper;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.Base64;
import java.util.Objects;

import static lombok.AccessLevel.PRIVATE;

@Component
@RequiredArgsConstructor
@FieldDefaults(makeFinal = true, level = PRIVATE)
public class TransactionUpdateFacadeImpl implements TransactionUpdateFacade {

    public static final String INVALID_JOB_STATE = "Job can not be deleted, job state not 'Queued'";

    TransactionGetControllerMapper mapper;
    TransactionCancelControllerMapper cancelTrnMapper;

    @Autowired
    private ObjectMapper objectMapper;
    
    @NonFinal
    @Value("${transaction-fetch.url.get}")
    String transactionUrlGet;


    @NonFinal
    @Value("${transaction-cancel.url.post}")
    String transactionUrlPost;
    
    @NonFinal
    boolean blnIsDecoded = true;
    
	@Override
	public TransactionGetResultDto update(String trnId, TransactionUpdateDto trnUpdateDto) {

		RestTemplate restTemplate = new RestTemplate();

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        String objJackson = "";
        
        try {
			   objJackson = objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(trnUpdateDto);
			 //  System.out.println("Request body - 1 - " + objJackson);
			 
		} catch (JsonProcessingException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
               
       
        HttpEntity<String> entity = new HttpEntity<String>(objJackson, headers);
        HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
        requestFactory.setConnectTimeout(6000);
        requestFactory.setReadTimeout(6000);

        restTemplate.setRequestFactory(requestFactory);
        
        if(!blnIsDecoded)
        {
        	byte[] decodedBytes = Base64.getDecoder().decode(transactionUrlGet);
            transactionUrlGet = new String(decodedBytes);
        }
       
   
        TransactionGetResultDto trnGetResultDto = restTemplate.exchange(
        		transactionUrlGet + trnId,
                HttpMethod.PATCH,
                entity,
                new ParameterizedTypeReference<TransactionGetResultDto>() {
                },
                ""
        ).getBody();

        return Objects.nonNull(trnGetResultDto) ? mapper.toOutDto(trnGetResultDto) : null;
	}


	@Override
	public TransactionCancelResultDto cancel(String trnId, TransactionGetDto trnUpdateDto) {

		RestTemplate restTemplate = new RestTemplate();

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
    
        String objJackson = "";
        
        try {
			   objJackson = objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(trnUpdateDto);
			 //  System.out.println("Request body - 1 - " + objJackson);
			 
		} catch (JsonProcessingException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
               
       
        HttpEntity<String> entity = new HttpEntity<String>(objJackson, headers);
        
        if(!blnIsDecoded)
        {
        	byte[] decodedBytes = Base64.getDecoder().decode(transactionUrlPost);
            transactionUrlPost = new String(decodedBytes);
        }
        
        
        TransactionCancelResultDto trnCancelResultDto = restTemplate.exchange(
        		transactionUrlPost + trnId,
                HttpMethod.POST,
                entity,
                new ParameterizedTypeReference<TransactionCancelResultDto>() {
                },
                ""
        ).getBody();

        return Objects.nonNull(trnCancelResultDto) ? cancelTrnMapper.toOutDto(trnCancelResultDto) : null;
	}
}
