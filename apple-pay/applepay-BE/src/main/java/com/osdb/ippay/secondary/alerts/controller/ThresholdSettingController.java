package com.osdb.ippay.secondary.alerts.controller;

import com.osdb.ippay.common.logging.LogExecutionTime;
import com.osdb.ippay.secondary.alerts.facade.ThresholdSettingFacade;
import com.osdb.ippay.secondary.alerts.facade.dto.*;
import com.osdb.ippay.secondary.alerts.repository.entity.AlertType;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import static lombok.AccessLevel.PRIVATE;

@Tag(name = "Alerts")
@RestController
@RequestMapping(value = "/api/v1/private/threshold-setting")
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class ThresholdSettingController {
/*
    ThresholdSettingFacade thresholdSettingFacade;

    @LogExecutionTime
    @GetMapping("/{alertType}")
    public ResponseEntity<ThresholdSettingDto> getThresholdSetting(@PathVariable("alertType") AlertType alertType) {
        ThresholdSettingDto response = thresholdSettingFacade.find(alertType);
        return ResponseEntity.ok(response);
    }

    @LogExecutionTime
    @PutMapping("update/{alertType}/{mid}")
    public ResponseEntity<ThresholdSettingDto> updateThresholdSetting(@PathVariable("alertType") AlertType alertType,
    		@PathVariable("mid") String mid, @RequestBody ThresholdSettingDto thresholdSettingDto) {
        ThresholdSettingDto updatedDto = thresholdSettingFacade.update(alertType, thresholdSettingDto, mid);
        return ResponseEntity.ok(updatedDto);
    }
    
    @LogExecutionTime
    @PostMapping("create/{alertType}")
    public ResponseEntity<ThresholdSettingDto> createThresholdSetting(@PathVariable("alertType") AlertType alertType,
    		@RequestBody ThresholdSettingDto thresholdSettingDto) {
        ThresholdSettingDto createdDto = thresholdSettingFacade.create(alertType, thresholdSettingDto);
        return ResponseEntity.ok(createdDto);
    }*/
}