package com.osdb.ippay.secondary.retokenization.controller.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.Date;

import static lombok.AccessLevel.PRIVATE;

@Getter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class RetokenizationOutDto {

    @JsonProperty(required = true)
    Integer id;

    String state;

    @JsonProperty(required = true)
    String user;

    @JsonProperty(required = true)
    String jobId;

    @JsonProperty(required = true)
    String jobType;

    @JsonProperty(required = true)
    JobInfoDto jobInfo;

    String jobDescription;

    @JsonProperty(required = true)
    int jobMaxRuntime;

    String jobRanHost;

    JobResultDto jobResult;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    Date jobStartedAt;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    Date jobCompletedAt;

    String jobErrorMsg;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    Date dateCreated;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    Date dateModified;

}
