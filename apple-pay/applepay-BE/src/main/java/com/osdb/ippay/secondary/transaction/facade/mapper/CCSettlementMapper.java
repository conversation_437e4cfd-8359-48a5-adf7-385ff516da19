package com.osdb.ippay.secondary.transaction.facade.mapper;

import com.osdb.ippay.common.util.date.DateUtil;
import com.osdb.ippay.common.util.number.DoubleUtil;
import com.osdb.ippay.secondary.transaction.facade.dto.CCSettlementDto;
import com.osdb.ippay.secondary.transaction.facade.dto.CCSettlementResponse;
import com.osdb.ippay.secondary.transaction.repository.entity.CCSettlement;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
public class CCSettlementMapper {

    public CCSettlementResponse toDto(List<CCSettlement> settlements) {
        long totalSalesAmount = settlements.stream()
                .map(CCSettlement::getSalesAmount)
                .mapToLong(Long::longValue)
                .sum();

        long totalSalesCount = settlements.stream()
                .map(CCSettlement::getSalesCount)
                .mapToLong(Long::longValue)
                .sum();

        long totalRefundAmount = settlements.stream()
                .map(CCSettlement::getRefundAmount)
                .mapToLong(Long::longValue)
                .sum();

        long totalRefundCount = settlements.stream()
                .map(CCSettlement::getRefundCount)
                .mapToLong(Long::longValue)
                .sum();

        long totalSalesRefundAmount = totalSalesAmount - totalRefundAmount;
        long totalSalesRefundCount = totalSalesCount + totalRefundCount;

        long totalAxAmount = settlements.stream()
                .map(CCSettlement::getAxAmount)
                .mapToLong(Long::longValue)
                .sum();

        long totalAxRefund = settlements.stream()
                .map(CCSettlement::getAxRefund)
                .mapToLong(Long::longValue)
                .sum();

        long totalAxCount = settlements.stream()
                .map(CCSettlement::getAxCount)
                .mapToLong(Long::longValue)
                .sum();

        long bankCardSalesAmount = totalSalesRefundAmount - totalAxAmount;
        long bankCardCount = totalSalesRefundCount - totalAxCount;

        List<CCSettlementDto> settlementDtos = groupByDate(
                settlements.stream()
                        .map(this::toDto)
                        .sorted(Comparator.comparing(CCSettlementDto::getDate).reversed())
                        .collect(Collectors.toList())
        );

        return CCSettlementResponse.builder()
                .settlements(settlementDtos)
                .totalSalesAmount(DoubleUtil.toPriceFormat(totalSalesAmount, 2, 100))
                .totalSalesCount(totalSalesCount)
                .totalRefundAmount(DoubleUtil.toPriceFormat(totalRefundAmount, 2, 100))
                .totalRefundCount(totalRefundCount)
                .totalSalesRefundAmount(DoubleUtil.toPriceFormat(totalSalesRefundAmount, 2, 100))
                .totalSalesRefundCount(totalSalesRefundCount)
                .totalAxAmount(DoubleUtil.toPriceFormat(totalAxAmount, 2, 100))
                .totalAxRefund(DoubleUtil.toPriceFormat(totalAxRefund, 2, 100))
                .totalAxCount(totalAxCount)
                .totalBankCardSalesAmount(DoubleUtil.toPriceFormat(bankCardSalesAmount, 2, 100))
                .totalBankCardCount(bankCardCount)
                .build();
    }

    public CCSettlementDto toDto(CCSettlement ccSettlement) {
        String salesAmount = ccSettlement.getSalesAmount() != null ?
                DoubleUtil.toPriceFormat(ccSettlement.getSalesAmount(), 2, 100) :
                "0.00";

        String refundAmount = ccSettlement.getRefundAmount() != null ?
                DoubleUtil.toPriceFormat(ccSettlement.getRefundAmount(), 2, 100) :
                "0.00";

        String axAmount = ccSettlement.getAxAmount() != null ?
                DoubleUtil.toPriceFormat(ccSettlement.getAxAmount(), 2, 100) :
                "0.00";

        String axRefund = ccSettlement.getAxRefund() != null ?
                DoubleUtil.toPriceFormat(ccSettlement.getAxRefund(), 2, 100) :
                "0.00";

        long salesRefundAmount = ccSettlement.getSalesAmount() - ccSettlement.getRefundAmount();
        long bankCardSalesAmount = salesRefundAmount - ccSettlement.getAxAmount();

        long salesRefundCount = ccSettlement.getSalesCount() + ccSettlement.getRefundCount();
        long bankCardCount = salesRefundCount - ccSettlement.getAxCount();

        return CCSettlementDto.builder()
                .date(
                        Objects.nonNull(ccSettlement.getDate()) ?
                                DateUtil.parseToInstant(ccSettlement.getDate()) :
                                null
                )
                .salesAmount(salesAmount)
                .salesCount(ccSettlement.getSalesCount())
                .refundAmount(refundAmount)
                .refundCount(ccSettlement.getRefundCount())
                .salesRefundAmount(DoubleUtil.toPriceFormat(salesRefundAmount, 2, 100))
                .salesRefundCount(salesRefundCount)
                .axAmount(axAmount)
                .axRefund(axRefund)
                .axCount(ccSettlement.getAxCount())
                .bankCardSalesAmount(DoubleUtil.toPriceFormat(bankCardSalesAmount, 2, 100))
                .bankCardCount(bankCardCount)
                .build();
    }

    private List<CCSettlementDto> groupByDate(List<CCSettlementDto> settlementDtos) {
        List<CCSettlementDto> groupedResult = new ArrayList<>();

        for(int i = 0; i < settlementDtos.size(); i++) {
            CCSettlementDto currSettlement = settlementDtos.get(i);

            boolean isExist = groupedResult.stream().anyMatch(e -> e.getDate().equals(currSettlement.getDate()));
            if(isExist) break;

            for(int j = i + 1; j < settlementDtos.size(); j++) {
                CCSettlementDto dupSettlement = settlementDtos.get(j);

                if(currSettlement.getDate().equals(dupSettlement.getDate())) {
                    double saleAmount = Double.parseDouble(currSettlement.getSalesAmount()) + Double.parseDouble(dupSettlement.getSalesAmount());
                    currSettlement.setSalesAmount(Double.toString(saleAmount));
                    currSettlement.setSalesCount(currSettlement.getSalesCount() + dupSettlement.getSalesCount());

                    double refundAmount = Double.parseDouble(currSettlement.getRefundAmount()) + Double.parseDouble(dupSettlement.getRefundAmount());
                    currSettlement.setRefundAmount(Double.toString(refundAmount));
                    currSettlement.setRefundCount(currSettlement.getRefundCount() + dupSettlement.getRefundCount());

                    double salesRefundAmount = Double.parseDouble(currSettlement.getSalesRefundAmount()) + Double.parseDouble(dupSettlement.getSalesRefundAmount());
                    currSettlement.setSalesRefundAmount(Double.toString(salesRefundAmount));
                    currSettlement.setSalesRefundCount(currSettlement.getSalesRefundCount() + dupSettlement.getSalesRefundCount());

                    double axAmount = Double.parseDouble(currSettlement.getAxAmount()) + Double.parseDouble(dupSettlement.getAxAmount());
                    double axRefund = Double.parseDouble(currSettlement.getAxRefund()) + Double.parseDouble(dupSettlement.getAxRefund());
                    currSettlement.setAxAmount(Double.toString(axAmount));
                    currSettlement.setAxRefund(Double.toString(axRefund));
                    currSettlement.setAxCount(currSettlement.getAxCount() + dupSettlement.getAxCount());

                    double bankCardSalesAmount = Double.parseDouble(currSettlement.getBankCardSalesAmount()) + Double.parseDouble(dupSettlement.getBankCardSalesAmount());
                    currSettlement.setBankCardSalesAmount(Double.toString(bankCardSalesAmount));
                    currSettlement.setBankCardCount(currSettlement.getBankCardCount() + dupSettlement.getBankCardCount());
                }
            }

            groupedResult.add(currSettlement);
        }

        return groupedResult;
    }
}
