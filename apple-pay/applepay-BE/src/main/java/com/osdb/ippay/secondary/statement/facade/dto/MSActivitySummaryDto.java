package com.osdb.ippay.secondary.statement.facade.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

import static lombok.AccessLevel.PRIVATE;

@Getter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class MSActivitySummaryDto {

    Long id;

    String planType;

    String sales;

    String numSales;

    String credits;

    String numCredits;

    String netSales;

    String perItemFee;

    String rate;

    String discountDue;

    String numTotalTrans;

}
