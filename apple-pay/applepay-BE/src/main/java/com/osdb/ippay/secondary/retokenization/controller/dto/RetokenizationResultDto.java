package com.osdb.ippay.secondary.retokenization.controller.dto;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.Date;
import java.util.Objects;

import static lombok.AccessLevel.PRIVATE;

@Getter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class RetokenizationResultDto {

    Integer id;

    String state;

    String user;

    @JsonAlias("job_id")
    String jobId;

    @JsonAlias("job_type")
    String jobType;

    @JsonAlias("job_info")
    String jobInfo;

    @JsonAlias("job_description")
    String jobDescription;

    @JsonAlias("job_max_runtime")
    int jobMaxRuntime;

    @JsonAlias("job_ran_host")
    String jobRanHost;

    @JsonAlias("job_result")
    String jobResult;

    @JsonAlias("job_started_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    Date jobStartedAt;

    @JsonAlias("job_completed_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    Date jobCompletedAt;

    @JsonAlias("job_error_msg")
    String jobErrorMsg;

    @JsonAlias("date_created")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    Date dateCreated;

    @JsonAlias({"date_modified"})
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    Date dateModified;

    public String getJobResult() {
        return Objects.nonNull(jobResult) ? jobResult.replace("/", "") : "";
    }

    public String getJobInfo() {
        return Objects.nonNull(jobInfo) ? jobInfo.replace("/", "") : "";
    }
}
