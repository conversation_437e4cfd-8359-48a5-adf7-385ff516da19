package com.osdb.ippay.secondary.transaction.repository.entity;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum ACHTransactionType {

    CHECK("CHECK"), REVERSAL("REFUND"), VOIDACH("VOID"), TOKENIZE("TOKENIZE");

    String value;

    static final Map<String, ACHTransactionType> lookup = new HashMap<>();

    static {
        Arrays.stream(ACHTransactionType.values())
                .forEach(value -> lookup.put(value.getValue(), value));
    }

    public static ACHTransactionType getByValue(String value) {
        return lookup.get(value);
    }
}
