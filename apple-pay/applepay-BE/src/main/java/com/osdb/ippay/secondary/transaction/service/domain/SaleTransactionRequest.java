package com.osdb.ippay.secondary.transaction.service.domain;

import com.ippay.global.clients.ipp.IppayRequest;
import com.ippay.global.clients.ipp.data.AchAccountType;
import com.ippay.global.clients.ipp.data.ShippingMethodType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

import static lombok.AccessLevel.PRIVATE;

@Data
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = PRIVATE)
public class SaleTransactionRequest {

    String merchantId;

    String terminalId;

    PaymentMode paymentMode;

    PaymentType paymentType;

    IppayRequest.TransactionType transactionType;

    IppayRequest.Origin origin;

    String token;

    String accountNumber;

    String routingNumber;

    Integer checkNumber;

    AchAccountType accountType;

    String cardNumber;

    String cardExpireMonth;

    Integer cardExpireYear;

    String cardCVV;

    String cardHolderName;

    String customerEmail;

    String customerPONumber;

    String orderNumber;

    Long amount;

    String billingAddress;

    String billingCity;

    String billingState;

    String billingZipCode;

    String billingCountry;

    String billingPhone;

    String shippingName;

    ShippingMethodType shippingMethod;

    String shippingAddress;

    String shippingCity;

    String shippingState;

    String shippingZipCode;

    String shippingCountry;

    String shippingPhone;

    String ud1;

    String ud2;

    String ud3;

}
