package com.osdb.ippay.sqlserveriptrans.service;

import com.osdb.ippay.sqlserveriptrans.repository.entity.BlockedRequests;
import com.osdb.ippay.sqlserveriptrans.service.filter.BlockedRequestsFilter;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface BlockedRequestsService {

    
    Page<BlockedRequests> findData(String terminalID, String ipAddress, String transactionType, java.util.Date startDateTime, java.util.Date endDateTime,
    		BlockedRequestsFilter filter, Pageable pageable);

}
