/*package com.osdb.ippay.sqlserver.reports.service.filter;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.osdb.ippay.secondary.transaction.service.filter.StType;
import com.osdb.ippay.secondary.transaction.service.filter.TransactionType;

import static com.osdb.ippay.secondary.transaction.service.filter.TransactionType.SETTLEMENT;
import static lombok.AccessLevel.PRIVATE;

import java.time.LocalDate;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

@Data
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = PRIVATE)
public class FANFReportsFilter {

	 @NotNull(message = "Missing required parameter: 'fromDate'.")
	    @Parameter(example = "2017-01-25")
	    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
	    LocalDate fromDate;

	    @NotNull(message = "Missing required parameter: 'toDate'.")
	    @Parameter(example = "2020-07-16")
	    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
	    LocalDate toDate;

	    @NotBlank(message = "Missing required parameter: 'merchantId'.")
	    String merchantId;

	 
}*/