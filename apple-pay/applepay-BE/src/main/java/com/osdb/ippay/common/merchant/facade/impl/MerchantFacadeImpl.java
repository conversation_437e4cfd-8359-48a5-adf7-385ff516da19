package com.osdb.ippay.common.merchant.facade.impl;

import com.osdb.ippay.common.domain.FileDomain;
import com.osdb.ippay.common.exception.business.NoAccessException;
import com.osdb.ippay.common.merchant.facade.MerchantFacade;
import com.osdb.ippay.common.merchant.facade.dto.MerchantDto;
import com.osdb.ippay.common.merchant.facade.mapper.MerchantMapper;
import com.osdb.ippay.common.security.service.AuthenticationService;
import com.osdb.ippay.primary.user.repository.entity.User;
import com.osdb.ippay.primary.user.service.UserService;
import com.osdb.ippay.secondary.merchant.repository.entity.Merchant;
import com.osdb.ippay.secondary.merchant.service.MerchantService;
import com.osdb.ippay.secondary.merchant.service.filter.MerchantFilter;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.List;

import static com.osdb.ippay.common.exception.ErrorMessage.AUTH_NO_ACCESS;
import static lombok.AccessLevel.PRIVATE;

@Component
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class MerchantFacadeImpl implements MerchantFacade {

    // services
    UserService userService;
    MerchantService merchantService;
    AuthenticationService authenticationService;

    // mappers
    MerchantMapper merchantMapper;

    @Override
    public Page<MerchantDto> find(String email, MerchantFilter filter, Pageable pageable) {
        User authUser = userService.findByEmail(email);

        if(authUser.isNotAdmin()) {
            List<String> merchantIds = authenticationService.getMerchantIds(authUser);
            filter.setMerchantIds(merchantIds);
        }

        PageRequest pageRequest = PageRequest.of(
                pageable.getPageNumber(),
                pageable.getPageSize(),
                getSort(pageable)
        );

        Page<Merchant> merchants = merchantService.find(authUser, filter, pageRequest);
        return merchantMapper.toDto(merchants);
    }

    @Override
    public List<MerchantDto> find(String email, MerchantFilter filter, Sort sort) {
        User authUser = userService.findByEmail(email);

        if(authUser.isNotAdmin()) {
            List<String> merchantIds = authenticationService.getMerchantIds(authUser);
            filter.setMerchantIds(merchantIds);
        }

        List<Merchant> merchantTerminals = merchantService.find(authUser, filter, sort);
        return merchantMapper.toDto(merchantTerminals);
    }

    @Override
    public MerchantDto find(String email, String merchantId) {
        User authUser = userService.findByEmail(email);
        if(authUser.isNotAdmin()) throwIfUserDoesNotBelongToMerchant(merchantId, authUser);

        Merchant merchant = merchantService.find(merchantId);
        return merchantMapper.toDto(merchant);
    }

    @Override
    public FileDomain export(User user, MerchantFilter filter) throws IOException {
        if(user.isNotAdmin()) {
            List<String> merchantIds = authenticationService.getMerchantIds(user);
            filter.setMerchantIds(merchantIds);
        }

        return merchantService.export(user, filter);
    }

    private void throwIfUserDoesNotBelongToMerchant(String merchantId, User authUser) {
        List<String> merchantIds = authenticationService.getMerchantIds(authUser);

        if(!merchantIds.contains(merchantId)) {
            throw new NoAccessException(AUTH_NO_ACCESS);
        }
    }

    private Sort getSort(Pageable pageable) {
        Sort.Order order = pageable.getSort().get()
                .findFirst()
                .orElse(Sort.Order.asc("merchantId")).ignoreCase();

        Sort.Direction direction = order.getDirection();
        String property = order.getProperty();

        return switch (property) {
            case "bank" -> Sort.by(direction, "bank.bankName");
            case "partner" -> Sort.by(direction, "partner.partnerName");
            default -> Sort.by(order);
        };
    }
}
