package com.osdb.ippay.primary.user.facade;

import com.osdb.ippay.common.domain.FileDomain;
import com.osdb.ippay.common.user.facade.dto.UserDto;
import com.osdb.ippay.primary.user.facade.dto.UserStatusDto;
import com.osdb.ippay.primary.user.repository.entity.User;
import com.osdb.ippay.primary.user.service.filter.UserFilter;

import java.io.IOException;
import java.util.List;

public interface UserFacade {

    UserDto find(String email);

    UserDto updateStatus(Long id, UserStatusDto statusDto);

    void assignMerchants(Long id, List<String> merchantIds);

    FileDomain export(User user, UserFilter filter) throws IOException;

    Boolean exists(String email);

}
