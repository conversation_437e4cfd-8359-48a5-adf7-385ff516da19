package com.osdb.ippay.sqlserveriptrans.facade.mapper;

import com.osdb.ippay.sqlserveriptrans.facade.dto.BlockedRequestsDto;
import com.osdb.ippay.sqlserveriptrans.repository.entity.BlockedRequests;

import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;

@Component
public class BlockedRequestsMapper {

    public Page<BlockedRequestsDto> toDto(Page<BlockedRequests> blockedRequestsData) {
        return blockedRequestsData.map(this::toDto);
    }

    public BlockedRequestsDto toDto(BlockedRequests blockedRequests) {
       

        return BlockedRequestsDto.builder()
        		.id(blockedRequests.getId())
                .terminalId(blockedRequests.getTerminalId())
                .ipAddress(blockedRequests.getIpAddress())
                .transactionID(blockedRequests.getTransactionID())
                .cardName(blockedRequests.getCardName())
                .totalAmount(blockedRequests.getTotalAmount())
                .date(blockedRequests.getDate())
                .xml(blockedRequests.getXml())
                .build();
    }
}
