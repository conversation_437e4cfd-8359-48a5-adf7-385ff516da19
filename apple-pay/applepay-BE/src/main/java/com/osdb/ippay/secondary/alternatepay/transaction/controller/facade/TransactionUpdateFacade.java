package com.osdb.ippay.secondary.alternatepay.transaction.controller.facade;

import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionCancelResultDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionGetDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionGetResultDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionUpdateDto;



public interface TransactionUpdateFacade {

	TransactionGetResultDto update(String trnId, TransactionUpdateDto trnUpdateDto);
	TransactionCancelResultDto cancel(String trnId, TransactionGetDto trnUpdateDto);
}
