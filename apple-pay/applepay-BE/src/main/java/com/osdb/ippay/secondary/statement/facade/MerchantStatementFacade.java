package com.osdb.ippay.secondary.statement.facade;

import com.osdb.ippay.secondary.statement.facade.dto.*;
import com.osdb.ippay.secondary.statement.service.filter.MSFilter;

import java.util.List;
import java.util.concurrent.Future;

public interface MerchantStatementFacade {

    Future<List<MSActivitySummaryDto>> findActivitySummary(String merchantId, MSFilter msFilter);

    Future<List<MSDepositDetailDto>> findDepositDetails(String merchantId, MSFilter msFilter);

    Future<List<MSOtherCardDepositDto>> findOtherCardDeposits(String merchantId, MSFilter msFilter);

    Future<List<MSProcessingDetailDto>> findProcessingDetails(String merchantId, MSFilter msFilter);

    Future<List<MSAuthDetailDto>> findAuthDetails(String merchantId, MSFilter msFilter);

    Future<List<MSOtherDetailDto>> findOtherDetails(String merchantId, MSFilter msFilter);

    Future<List<MSTotalDetailDto>> findTotalDetail(String merchantId, MSFilter msFilter);

}
