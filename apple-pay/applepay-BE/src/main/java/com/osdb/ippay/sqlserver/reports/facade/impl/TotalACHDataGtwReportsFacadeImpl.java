package com.osdb.ippay.sqlserver.reports.facade.impl;

import com.osdb.ippay.common.domain.FileDomain;
import com.osdb.ippay.primary.user.repository.entity.User;
import com.osdb.ippay.secondary.chargeback.facade.dto.ChargebackDto;
import com.osdb.ippay.secondary.chargeback.repository.entity.Chargeback;
import com.osdb.ippay.secondary.chargeback.service.ChargebackFilter;
import com.osdb.ippay.sqlserver.reports.facade.TotalACHDataGtwReportsFacade;
import com.osdb.ippay.sqlserver.reports.facade.dto.TotalACHDataGtwReportsDto;
import com.osdb.ippay.sqlserver.reports.facade.dto.TotalACHGtwDataDto;
import com.osdb.ippay.sqlserver.reports.facade.mapper.TotalACHDataGtwReportsMapper;
import com.osdb.ippay.sqlserver.reports.facade.mapper.TotalACHGtwDataMapper;
import com.osdb.ippay.sqlserver.reports.repository.entity.TotalACHDataGtwReports;
import com.osdb.ippay.sqlserver.reports.repository.entity.TotalACHGtwData;
import com.osdb.ippay.sqlserver.reports.service.TotalACHDataGtwReportsService;
import com.osdb.ippay.sqlserver.reports.service.filter.TotalACHDataGtwReportsFilter;
import com.osdb.ippay.sqlserver.reports.service.filter.TotalACHGtwDataFilter;

import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.ListIterator;

import static lombok.AccessLevel.PRIVATE;

@Component
@RequiredArgsConstructor
@FieldDefaults(makeFinal = true, level = PRIVATE)
public class TotalACHDataGtwReportsFacadeImpl implements TotalACHDataGtwReportsFacade {

    // services
	TotalACHDataGtwReportsService totalACHDataGtwReportsService;

    // mappers
	TotalACHDataGtwReportsMapper totalACHDataGtwReportsMapper;
	TotalACHGtwDataMapper totalACHGtwDataMapper;

	
    // Method to export Total ACH Data into CSV format
	@Override
	public FileDomain export(Long ipTransactionID, String authStartDateTime, String authEndDateTime,
			TotalACHDataGtwReportsFilter filter) throws IOException {

		 String[] HEADERS = {
	                "Merchant_ID",
	                "JPS MID",
	                "Transaction_ID",
	                "DBA Name",
	                "CCProcessor",
	                "Request_Type",
	                "ActionCode",
	                "TRANSACTION_COUNT",
	                "TOTAL_AMOUNT"
	        };

	        final CSVFormat format = CSVFormat.Builder.create()
	                .setHeader(HEADERS)
	                .setIgnoreEmptyLines(true)
	                .build();
	        
	        List<TotalACHDataGtwReports> totalACHDataGtwReports  = totalACHDataGtwReportsService.generateReport(ipTransactionID, authStartDateTime, authEndDateTime);
	        for (int i = 0; i < totalACHDataGtwReports.size(); i++) {

	            // Print all elements of List
	            System.out.println(totalACHDataGtwReports.get(i).toString());
	        }
	        try (
	                ByteArrayOutputStream out = new ByteArrayOutputStream();
	                CSVPrinter csvPrinter = new CSVPrinter(new PrintWriter(out), format)
	        ) {
	        	//List<String> result = new ArrayList<>(totalACHDataGtwReports.size());
	        	//ListIterator<String> it = totalACHDataGtwReports.listIterator(totalACHDataGtwReports.size());
	        	
	       //  for(Object totalACHDataGtwReportsObj : totalACHDataGtwReports) {
	        	// List<String> data = Arrays.asList(
	        	 
	         //}

	            for (TotalACHDataGtwReports totalACHDataGtwReportsObj : totalACHDataGtwReports) {
	                List<String> data = Arrays.asList(
	                		totalACHDataGtwReportsObj.getMerchantID(),
	                		totalACHDataGtwReportsObj.getSpsMid(),
	                		totalACHDataGtwReportsObj.getTrnId(),
	                		totalACHDataGtwReportsObj.getOrgName(),
	                		totalACHDataGtwReportsObj.getCcProcessor(),
	                		totalACHDataGtwReportsObj.getRequestType(),
	                		totalACHDataGtwReportsObj.getActionCode(),
	                		totalACHDataGtwReportsObj.getTransactionCount(),
	                		totalACHDataGtwReportsObj.getTotalAmount()
	                );

	                csvPrinter.printRecord(data);
	            }
	            //csvPrinter.printRecord(data);
	            csvPrinter.flush();

	            return FileDomain.builder()
	                    .file(new ByteArrayResource(new ByteArrayInputStream(out.toByteArray()).readAllBytes()))
	                    .fileName(
	                            String.format(
	                                    "Totals-ACH-%s.csv",	             
	                                    filter.getAuthStartDateTime()
	                            )
	                    )
	                    .build();
	        }
	        
		
	}
	@Override
	public Page<TotalACHDataGtwReportsDto> find(Long ipTransactionID, String authStartDateTime,
			String authEndDateTime, TotalACHDataGtwReportsFilter filter, Pageable pageable) {
		
		 Page<TotalACHDataGtwReports> totalACHDataGtwReports = totalACHDataGtwReportsService.find(ipTransactionID, authStartDateTime, authEndDateTime, filter, pageable);
	        return totalACHDataGtwReportsMapper.toDto(totalACHDataGtwReports);
	}
	@Override
	public Page<TotalACHGtwDataDto> findData(Long ipTransactionID, String authStartDateTime, String authEndDateTime,
			TotalACHGtwDataFilter filter, Pageable pageable) {
		 Page<TotalACHGtwData> totalACHGtwData = totalACHDataGtwReportsService.findData(ipTransactionID, authStartDateTime, authEndDateTime, filter, pageable);
	       return totalACHGtwDataMapper.toDto(totalACHGtwData);
	}

}
