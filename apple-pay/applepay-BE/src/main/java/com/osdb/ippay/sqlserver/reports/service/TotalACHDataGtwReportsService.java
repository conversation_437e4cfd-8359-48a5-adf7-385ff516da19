package com.osdb.ippay.sqlserver.reports.service;

import com.osdb.ippay.primary.user.repository.entity.User;
import com.osdb.ippay.secondary.chargeback.repository.entity.Chargeback;
import com.osdb.ippay.secondary.chargeback.service.ChargebackFilter;
import com.osdb.ippay.sqlserver.reports.repository.entity.TotalACHDataGtwReports;
import com.osdb.ippay.sqlserver.reports.repository.entity.TotalACHGtwData;
import com.osdb.ippay.sqlserver.reports.service.filter.TotalACHDataGtwReportsFilter;
import com.osdb.ippay.sqlserver.reports.service.filter.TotalACHGtwDataFilter;

import java.util.ArrayList;
import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface TotalACHDataGtwReportsService {

    List<TotalACHDataGtwReports> find( Long ipTransactionId, 
    									String authStartDateTime, 
    									String authEndDateTime,
    									TotalACHDataGtwReportsFilter filter);
    Page<TotalACHDataGtwReports> find(Long ipTransactionId, 
			String authStartDateTime, 
			String authEndDateTime,
			TotalACHDataGtwReportsFilter filter, Pageable pageable);
    
    Page<TotalACHGtwData> findData(Long ipTransactionId, 
			String authStartDateTime, 
			String authEndDateTime,
			TotalACHGtwDataFilter filter, Pageable pageable);
    
    
    
    List generateReport( Long ipTransactionId, 
			String authStartDateTime, 
			String authEndDateTime);
}
