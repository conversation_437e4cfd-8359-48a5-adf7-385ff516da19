package com.osdb.ippay.secondary.chargeback.repository;

import com.osdb.ippay.secondary.chargeback.repository.entity.Chargeback;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface ChargebackRepository extends
        JpaRepository<Chargeback, Long>,
        JpaSpecificationExecutor<Chargeback> {

    @Query(nativeQuery = true, value =
            "SELECT DISTINCT STR_TO_DATE( im.DateResolved, '%m%d%Y' ) as dateResolved " +
                    "FROM ImportChargebacks im " +
                    "JOIN MERCHANTS m ON m.cb_mid = im.MerchantNumber " +
                    "WHERE im.DateResolved IS NOT NULL AND m.mid = ?1 " +
                    "ORDER BY dateResolved DESC " +
                    "LIMIT 15"
    )
    List<String> findInitialDates(String merchantId);

}
