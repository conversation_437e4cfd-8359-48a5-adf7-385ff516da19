package com.osdb.ippay.primary.user.service.impl;

import com.osdb.ippay.common.exception.business.NotFoundException;
import com.osdb.ippay.primary.user.repository.UserRepository;
import com.osdb.ippay.primary.user.repository.entity.User;
import com.osdb.ippay.primary.user.service.UserService;
import com.osdb.ippay.primary.user.service.filter.UserFilter;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.Predicate;
import java.util.List;

import static com.osdb.ippay.common.exception.ErrorMessage.*;
import static com.osdb.ippay.primary.user.repository.entity.UserStatus.ACTIVE;
import static lombok.AccessLevel.PRIVATE;

@Service
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class UserServiceImpl implements UserService {

    UserRepository userRepository;

    @Override
    public List<User> find(UserFilter userFilter) {
        Specification<User> specification = filterBy(userFilter);
        return userRepository.findAll(specification);
    }

    @Override
    public Page<User> find(UserFilter userFilter, Pageable pageable) {
        Specification<User> specification = filterBy(userFilter);

        PageRequest pageRequest = PageRequest.of(
                pageable.getPageNumber(),
                pageable.getPageSize(),
                getSort(pageable)
        );

        return userRepository.findAll(specification, pageRequest);
    }

    @Override
    public User find(Long id) {
        return userRepository
                .findById(id)
                .orElseThrow(() -> new NotFoundException(USER_ID_NOT_FOUND));
    }

    @Override
    public User findByEmail(String email) {
        return userRepository
                .findByEmail(email)
                .orElseThrow(() -> new NotFoundException(USER_EMAIL_NOT_FOUND));
    }

    @Override
    public User findActive(String email) {
        return userRepository
                .findByEmailAndStatus(email, ACTIVE)
                .orElseThrow(() -> new NotFoundException(USER_EMAIL_NOT_FOUND));
    }

    @Override
    public User findActive(String email, String sessionsHash) {
        return userRepository
                .findByEmailAndStatusAndSessionsHash(email, ACTIVE, sessionsHash)
                .orElseThrow(() -> new NotFoundException(USER_EMAIL_NOT_FOUND));
    }

    @Override
    public User findByResetPasswordHash(String resetPasswordHash) {
        return userRepository
                .findByResetPasswordHash(resetPasswordHash)
                .orElseThrow(() -> new NotFoundException(AUTH_INVALID_RESET_PASSWORD_TOKEN));
    }

    @Override
    public User save(User user) {
        return userRepository.save(user);
    }

    @Override
    public Boolean exists(String email) {
        return userRepository.existsByEmail(email);
    }

    private Specification<User> filterBy(UserFilter filter) {
        return (r, rq, cb) -> {
            Predicate searchPredicate = StringUtils.isNotBlank(filter.getSearch()) ? cb.or(
                    cb.like(cb.lower(r.get("firstName")), "%" + filter.getSearch().toLowerCase() + "%"),
                    cb.like(cb.lower(r.get("lastName")), "%" + filter.getSearch().toLowerCase() + "%"),
                    cb.like(cb.lower(r.get("email")), "%" + filter.getSearch().toLowerCase() + "%")) :
                    cb.conjunction();

            Predicate statusPredicate = filter.getStatus() != null ?
                    cb.equal(r.get("status"), filter.getStatus()) :
                    cb.conjunction();

            Predicate rolePredicate = filter.getRole() != null ?
                    cb.equal(r.get("role"), filter.getRole()) :
                    cb.conjunction();

            Predicate partnerIdPredicate = filter.getPartnerId() != null ?
                    cb.equal(r.get("partnerId"), filter.getPartnerId()) :
                    cb.conjunction();

            Predicate merchantIdPredicate = filter.getMerchantId() != null ?
                    cb.equal(r.join("merchants").get("merchantId"), filter.getMerchantId()) :
                    cb.conjunction();

            return cb.and(
                    searchPredicate,
                    statusPredicate,
                    rolePredicate,
                    partnerIdPredicate,
                    merchantIdPredicate
            );
        };
    }

    private Sort getSort(Pageable pageable) {
        return Sort.by(
                pageable.getSort().get()
                        .findFirst()
                        .orElse(Sort.Order.desc("id"))
        );
    }
}
