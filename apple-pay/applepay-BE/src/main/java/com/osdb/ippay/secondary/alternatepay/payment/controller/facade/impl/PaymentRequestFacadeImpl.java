package com.osdb.ippay.secondary.alternatepay.payment.controller.facade.impl;


import com.osdb.ippay.secondary.alternatepay.payment.controller.repository.PaymentRequestRepository;import com.osdb.ippay.secondary.alternatepay.payment.controller.repository.entity.PaymentRequest;
import com.osdb.ippay.secondary.alternatepay.payment.controller.dto.PaymentRequestDto;
import com.osdb.ippay.secondary.alternatepay.payment.controller.dto.PaymentResultDto;
import com.osdb.ippay.secondary.alternatepay.payment.controller.facade.PaymentRequestFacade;
import com.osdb.ippay.secondary.alternatepay.payment.controller.mapper.PaymentRequestMapper;


import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.stereotype.Component;

import static lombok.AccessLevel.PRIVATE;

@Component
@RequiredArgsConstructor
@FieldDefaults(makeFinal = true, level = PRIVATE)
public class PaymentRequestFacadeImpl implements PaymentRequestFacade {
   
    PaymentRequestMapper paymentRequestMapper;
    PaymentRequestRepository paymentRequestRepository;


	@Override
	public PaymentRequestDto create(PaymentResultDto paymentSaveDto, String JsonRequestBody) {

		PaymentRequest paymentData = paymentRequestMapper.toEntity(paymentSaveDto, JsonRequestBody);
		PaymentRequest updatedTrnData = paymentRequestRepository.save(paymentData);
		return paymentRequestMapper.toDto(updatedTrnData);
	}
}
