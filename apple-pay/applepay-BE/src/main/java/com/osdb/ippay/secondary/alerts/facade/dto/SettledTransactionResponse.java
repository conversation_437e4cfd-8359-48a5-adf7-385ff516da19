package com.osdb.ippay.secondary.alerts.facade.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;

import static lombok.AccessLevel.PRIVATE;

@Getter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class SettledTransactionResponse {

    List<SettledTransactionDto> settledTransactions;

    String totalSalesAmount;

    Long totalSalesCount;

    String totalRefundAmount;

    Long totalRefundCount;

    String totalSalesRefundAmount;

    Long totalSalesRefundCount;

    String totalAxAmount;

    String totalAxRefund;

    Long totalAxCount;

    String totalBankCardSalesAmount;

    Long totalBankCardCount;

}
