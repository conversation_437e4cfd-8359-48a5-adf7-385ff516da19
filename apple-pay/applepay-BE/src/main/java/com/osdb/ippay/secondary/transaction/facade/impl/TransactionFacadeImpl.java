package com.osdb.ippay.secondary.transaction.facade.impl;

import com.ippay.global.clients.ipp.IppayClient;
import com.ippay.global.clients.ipp.IppayHttpClientConfiguration;
import com.ippay.global.clients.ipp.IppayRequest;
import com.ippay.global.clients.ipp.IppayResponse;
import com.ippay.global.clients.ipp.data.*;
import com.osdb.ippay.common.domain.FileDomain;
import com.osdb.ippay.common.exception.business.NoAccessException;
import com.osdb.ippay.common.exception.business.NotFoundException;
import com.osdb.ippay.common.security.service.AuthenticationService;
import com.osdb.ippay.primary.user.repository.entity.User;
import com.osdb.ippay.secondary.alternatepay.payment.controller.dto.PaymentResultDto;
import com.osdb.ippay.hpp.alternatepay.transaction.repository.entity.HapiTransactionResponse;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionGetResultDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionSaveDto;
import com.osdb.ippay.secondary.merchant.repository.entity.Merchant;
import com.osdb.ippay.secondary.merchant.service.MerchantService;
import com.osdb.ippay.secondary.transaction.facade.mapper.TransactionFacade;
import com.osdb.ippay.secondary.transaction.facade.dto.*;
import com.osdb.ippay.secondary.transaction.facade.mapper.*;
import com.osdb.ippay.secondary.transaction.repository.CPCCTransactionRepository;
import com.osdb.ippay.secondary.transaction.repository.entity.*;
import com.osdb.ippay.secondary.transaction.service.ACHTransactionService;
import com.osdb.ippay.secondary.transaction.service.CCTransactionService;
import com.osdb.ippay.secondary.transaction.service.CPCCTransactionService;
import com.osdb.ippay.secondary.transaction.service.domain.RefundTransactionRequest;
import com.osdb.ippay.secondary.transaction.service.domain.SaleTransactionRequest;
import com.osdb.ippay.secondary.transaction.service.domain.VoidTransactionRequest;
import com.osdb.ippay.secondary.transaction.service.filter.AuthDateFilter;
import com.osdb.ippay.secondary.transaction.service.filter.SettlementFilter;
import com.osdb.ippay.secondary.transaction.service.filter.TransactionFilter;
import com.osdb.ippay.secondary.transaction.service.filter.TransactionType;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.data.domain.Sort;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.PrintWriter;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

import static com.osdb.ippay.common.exception.ErrorMessage.AUTH_NO_ACCESS;
import static com.osdb.ippay.common.util.date.DateUtil.isTheSameDayInstant;
import static com.osdb.ippay.secondary.transaction.service.filter.TransactionType.SETTLEMENT;
import static java.util.Comparator.*;
import static lombok.AccessLevel.PRIVATE;
import static org.apache.commons.lang.StringUtils.isNotBlank;

@Component
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class TransactionFacadeImpl implements TransactionFacade {

    static final String DATE_PATTERN = "MM/dd/yyyy HH:mm";

    @NonFinal
    @Value(value = "${ippay.trans.service.url}")
    String transURL;

    @NonFinal
    @Value(value = "${ippay.trans.service.connection.timeout}")
    Integer transConnTimeout;

    @NonFinal
    @Value(value = "${ippay.trans.service.response.timeout}")
    Integer transRespTimeout;

    // services
    CCTransactionService ccTransactionService;
    CPCCTransactionService cpccTransactionService;
    ACHTransactionService achTransactionService;
    AuthenticationService authenticationService;
    MerchantService merchantService;

    // mappers
    CCTransactionMapper ccTransactionMapper;
    CPCCTransactionMapper cpccTransactionMapper;
    ACHTransactionMapper achTransactionMapper;
    ACHSettlementMapper achSettlementMapper;
    CCSettlementMapper ccSettlementMapper;
    
    //repository
    CPCCTransactionRepository cpccTransactionRepo;

    @Override
    public CCSettlementResponse findCCSettlements(String email, SettlementFilter filter) {
        User authUser = authenticationService.getCurrentUser(email);
        throwIfNoAccessToCCMerchant(authUser, filter.getMerchantId());

        List<CCSettlement> ccSettlements = ccTransactionService.findSettlements(filter);
        List<CCSettlement> cpccSettlements = cpccTransactionService.findSettlements(filter);

        List<CCSettlement> result = new ArrayList<>(ccSettlements);
        result.addAll(cpccSettlements);

        return ccSettlementMapper.toDto(result);
    }

    @Override
    public ACHSettlementResponse findACHSettlements(String email, SettlementFilter filter) {
        User authUser = authenticationService.getCurrentUser(email);
        throwIfNoAccessToACHMerchant(authUser, filter.getMerchantId());

        List<ACHSettlement> settlements = achTransactionService.findSettlements(filter);
        return achSettlementMapper.toDto(settlements);
    }

    @Override
    public List<CCTransactionDto> findCCTransactions(User user, TransactionFilter filter, Sort sort) throws Exception {
        Merchant merchant = merchantService.findByCCMerchantId(filter.getMerchantId());
        if(merchant.getTransSource() == 0) {
            return Collections.emptyList();
        }

        if(user.isNotAdmin()) {
            List<String> merchantIds = authenticationService.getCCMerchantIds(user);
            filter.setMerchantIds(merchantIds);
        }

        Future<List<CCTransaction>> ccTransactions = merchant.getTransSource() == 1 || merchant.getTransSource() == 2 ?
                ccTransactionService.find(user, filter) :
                new AsyncResult<>(Collections.emptyList());

        Future<List<CPCCTransaction>> cpccTransactions = merchant.getTransSource() == 1 || merchant.getTransSource() == 3 ?
                cpccTransactionService.find(user, filter) :
                new AsyncResult<>(Collections.emptyList());

        List<CCTransactionDto> ccTransactionDtos = ccTransactionMapper.toDto(user, ccTransactions.get());

        List<CCTransactionDto> cpccTransactionDtos = cpccTransactionMapper
                .toDto(user, cpccTransactions.get()).stream()
                .filter(cpccTransaction ->
                        ccTransactionDtos.stream()
                                .noneMatch(ccTransactionDto ->
                                        cpccTransaction.getAuthCode().equals(ccTransactionDto.getAuthCode()) &&
                                        cpccTransaction.getAuthAmount().equals(ccTransactionDto.getAuthAmount()) &&
                                        isTheSameDayInstant(cpccTransaction.getAuthDate(), ccTransactionDto.getAuthDate())
                                )
                ).collect(Collectors.toList());

        List<CCTransactionDto> result = new ArrayList<>();
        result.addAll(ccTransactionDtos);
        result.addAll(cpccTransactionDtos);

        return sortCcTransactions(result, sort);
    }

    private List<CCTransactionDto> sortCcTransactions(List<CCTransactionDto> transactions, Sort sort) {
        Sort.Order order = sort.get()
                .findFirst()
                .orElse(Sort.Order.desc("id"));

        Sort.Direction direction = order.getDirection();
        String property = order.getProperty();

        return switch (property) {

            case "terminalId" ->
                    transactions.stream()
                            .sorted(
                                    direction.isDescending() ?
                                            comparing(CCTransactionDto::getTerminalId, nullsFirst(naturalOrder())).reversed() :
                                            comparing(CCTransactionDto::getTerminalId, nullsFirst(naturalOrder()))
                            ).collect(Collectors.toList());

            case "merchantId" ->
                    transactions.stream()
                            .sorted(
                                    direction.isDescending() ?
                                            comparing(CCTransactionDto::getMerchantId, nullsFirst(naturalOrder())).reversed() :
                                            comparing(CCTransactionDto::getMerchantId, nullsFirst(naturalOrder()))
                            ).collect(Collectors.toList());

            case "merchantName" ->
                    transactions.stream()
                            .sorted(
                                    direction.isDescending() ?
                                            comparing(CCTransactionDto::getMerchantName, nullsFirst(naturalOrder())).reversed() :
                                            comparing(CCTransactionDto::getMerchantName, nullsFirst(naturalOrder()))
                            ).collect(Collectors.toList());

            case "approvalStatus" ->
                    transactions.stream()
                            .sorted(
                                    direction.isDescending() ?
                                            comparing(CCTransactionDto::getApprovalStatus, nullsFirst(naturalOrder())).reversed() :
                                            comparing(CCTransactionDto::getApprovalStatus, nullsFirst(naturalOrder()))
                            ).collect(Collectors.toList());

            case "cardType" ->
                    transactions.stream()
                            .sorted(
                                    direction.isDescending() ?
                                            comparing(CCTransactionDto::getCardType, nullsFirst(naturalOrder())).reversed() :
                                            comparing(CCTransactionDto::getCardType, nullsFirst(naturalOrder()))
                            ).collect(Collectors.toList());

            case "cardNumber" ->
                    transactions.stream()
                            .sorted(
                                    direction.isDescending() ?
                                            comparing(CCTransactionDto::getSafeCardNumber, nullsFirst(naturalOrder())).reversed() :
                                            comparing(CCTransactionDto::getSafeCardNumber, nullsFirst(naturalOrder()))
                            ).collect(Collectors.toList());

            case "cardHolder" ->
                    transactions.stream()
                            .sorted(
                                    direction.isDescending() ?
                                            comparing(CCTransactionDto::getCardHolder, nullsFirst(naturalOrder())).reversed() :
                                            comparing(CCTransactionDto::getCardHolder, nullsFirst(naturalOrder()))
                            ).collect(Collectors.toList());

            case "address" ->
                    transactions.stream()
                            .sorted(
                                    direction.isDescending() ?
                                            comparing(CCTransactionDto::getAddress, nullsFirst(naturalOrder())).reversed() :
                                            comparing(CCTransactionDto::getAddress, nullsFirst(naturalOrder()))
                            ).collect(Collectors.toList());

            case "city" ->
                    transactions.stream()
                            .sorted(
                                    direction.isDescending() ?
                                            comparing(CCTransactionDto::getCity, nullsFirst(naturalOrder())).reversed() :
                                            comparing(CCTransactionDto::getCity, nullsFirst(naturalOrder()))
                            ).collect(Collectors.toList());

            case "state" ->
                    transactions.stream()
                            .sorted(
                                    direction.isDescending() ?
                                            comparing(CCTransactionDto::getState, nullsFirst(naturalOrder())).reversed() :
                                            comparing(CCTransactionDto::getState, nullsFirst(naturalOrder()))
                            ).collect(Collectors.toList());

            case "zipCode" ->
                    transactions.stream()
                            .sorted(
                                    direction.isDescending() ?
                                            comparing(CCTransactionDto::getZipCode, nullsFirst(naturalOrder())).reversed() :
                                            comparing(CCTransactionDto::getZipCode, nullsFirst(naturalOrder()))
                            ).collect(Collectors.toList());

            case "country" ->
                    transactions.stream()
                            .sorted(
                                    direction.isDescending() ?
                                            comparing(CCTransactionDto::getCountry, nullsFirst(naturalOrder())).reversed() :
                                            comparing(CCTransactionDto::getCountry, nullsFirst(naturalOrder()))
                            ).collect(Collectors.toList());

            case "transactionId" ->
                    transactions.stream()
                            .sorted(
                                    direction.isDescending() ?
                                            comparing(CCTransactionDto::getTransactionId, nullsFirst(naturalOrder())).reversed() :
                                            comparing(CCTransactionDto::getTransactionId, nullsFirst(naturalOrder()))
                            ).collect(Collectors.toList());

            case "transactionType" ->
                    transactions.stream()
                            .sorted(
                                    direction.isDescending() ?
                                            comparing(CCTransactionDto::getTransactionType, nullsFirst(naturalOrder())).reversed() :
                                            comparing(CCTransactionDto::getTransactionType, nullsFirst(naturalOrder()))
                            ).collect(Collectors.toList());

            case "authCode" ->
                    transactions.stream()
                            .sorted(
                                    direction.isDescending() ?
                                            comparing(CCTransactionDto::getAuthCode, nullsFirst(naturalOrder())).reversed() :
                                            comparing(CCTransactionDto::getAuthCode, nullsFirst(naturalOrder()))
                            ).collect(Collectors.toList());

            case "authAmount" ->
                    transactions.stream()
                            .sorted(
                                    direction.isDescending() ?
                                            comparing(CCTransactionDto::getAuthAmountCents, nullsFirst(naturalOrder())).reversed() :
                                            comparing(CCTransactionDto::getAuthAmountCents, nullsFirst(naturalOrder()))
                            ).collect(Collectors.toList());

            case "authDate" ->
                    transactions.stream()
                            .sorted(
                                    direction.isDescending() ?
                                            comparing(CCTransactionDto::getAuthDate, nullsFirst(naturalOrder())).reversed() :
                                            comparing(CCTransactionDto::getAuthDate, nullsFirst(naturalOrder()))
                            ).collect(Collectors.toList());

            case "avs" ->
                    transactions.stream()
                            .sorted(
                                    direction.isDescending() ?
                                            comparing(CCTransactionDto::getAvs, nullsFirst(naturalOrder())).reversed() :
                                            comparing(CCTransactionDto::getAvs, nullsFirst(naturalOrder()))
                            ).collect(Collectors.toList());

            case "cvv" ->
                    transactions.stream()
                            .sorted(
                                    direction.isDescending() ?
                                            comparing(CCTransactionDto::getCvv, nullsFirst(naturalOrder())).reversed() :
                                            comparing(CCTransactionDto::getCvv, nullsFirst(naturalOrder()))
                            ).collect(Collectors.toList());

            case "ud1" ->
                    transactions.stream()
                            .sorted(
                                    direction.isDescending() ?
                                            comparing(CCTransactionDto::getUd1, nullsFirst(naturalOrder())).reversed() :
                                            comparing(CCTransactionDto::getUd1, nullsFirst(naturalOrder()))
                            ).collect(Collectors.toList());

            case "ud2" ->
                    transactions.stream()
                            .sorted(
                                    direction.isDescending() ?
                                            comparing(CCTransactionDto::getUd2, nullsFirst(naturalOrder())).reversed() :
                                            comparing(CCTransactionDto::getUd2, nullsFirst(naturalOrder()))
                            ).collect(Collectors.toList());

            case "ud3" ->
                    transactions.stream()
                            .sorted(
                                    direction.isDescending() ?
                                            comparing(CCTransactionDto::getUd3, nullsFirst(naturalOrder())).reversed() :
                                            comparing(CCTransactionDto::getUd3, nullsFirst(naturalOrder()))
                            ).collect(Collectors.toList());

            case "requestType" ->
                    transactions.stream()
                            .sorted(
                                    direction.isDescending() ?
                                            comparing(CCTransactionDto::getRequestType, nullsFirst(naturalOrder())).reversed() :
                                            comparing(CCTransactionDto::getRequestType, nullsFirst(naturalOrder()))
                            ).collect(Collectors.toList());

            case "orderNumber" ->
                    transactions.stream()
                            .sorted(
                                    direction.isDescending() ?
                                            comparing(CCTransactionDto::getOrderNumber, nullsFirst(naturalOrder())).reversed() :
                                            comparing(CCTransactionDto::getOrderNumber, nullsFirst(naturalOrder()))
                            ).collect(Collectors.toList());

            case "settlementDate" ->
                    transactions.stream()
                            .sorted(
                                    direction.isDescending() ?
                                            comparing(CCTransactionDto::getSettlementDate, nullsFirst(naturalOrder())).reversed() :
                                            comparing(CCTransactionDto::getSettlementDate, nullsFirst(naturalOrder()))
                            ).collect(Collectors.toList());

            case "responseCode" ->
                    transactions.stream()
                            .sorted(
                                    direction.isDescending() ?
                                            comparing(CCTransactionDto::getResponseCode, nullsFirst(naturalOrder())).reversed() :
                                            comparing(CCTransactionDto::getResponseCode, nullsFirst(naturalOrder()))
                            ).collect(Collectors.toList());

            case "responseText" ->
                    transactions.stream()
                            .sorted(
                                    direction.isDescending() ?
                                            comparing(CCTransactionDto::getResponseText, nullsFirst(naturalOrder())).reversed() :
                                            comparing(CCTransactionDto::getResponseText, nullsFirst(naturalOrder()))
                            ).collect(Collectors.toList());

            default ->
                    transactions.stream()
                            .sorted(comparing(CCTransactionDto::getAuthDate, nullsFirst(naturalOrder())).reversed())
                            .collect(Collectors.toList());
        };
    }

    @Override
    public List<ACHTransactionDto> findACHTransactions(String email, TransactionFilter filter, Sort sort) {
        User authUser = authenticationService.getCurrentUser(email);

        if(authUser.isNotAdmin()) {
            List<String> merchantIds = authenticationService.getACHMerchantIds(authUser);
            filter.setMerchantIds(merchantIds);
        }

        List<ACHTransaction> transactions = achTransactionService.find(authUser, filter);
        return sortAchTransactions(achTransactionMapper.toDto(filter.getType(), authUser, transactions), sort);
    }

    private List<ACHTransactionDto> sortAchTransactions(List<ACHTransactionDto> transactions, Sort sort) {
        Sort.Order order = sort.get()
                .findFirst()
                .orElse(Sort.Order.desc("id"));

        Sort.Direction direction = order.getDirection();
        String property = order.getProperty();

        return switch (property) {

            case "terminalId" ->
                    transactions.stream()
                            .sorted(
                                    direction.isDescending() ?
                                            comparing(ACHTransactionDto::getTerminalId, nullsFirst(naturalOrder())).reversed() :
                                            comparing(ACHTransactionDto::getTerminalId, nullsFirst(naturalOrder()))
                            ).collect(Collectors.toList());

            case "merchantId" ->
                    transactions.stream()
                            .sorted(
                                    direction.isDescending() ?
                                            comparing(ACHTransactionDto::getMerchantId, nullsFirst(naturalOrder())).reversed() :
                                            comparing(ACHTransactionDto::getMerchantId, nullsFirst(naturalOrder()))
                            ).collect(Collectors.toList());

            case "merchantName" ->
                    transactions.stream()
                            .sorted(
                                    direction.isDescending() ?
                                            comparing(ACHTransactionDto::getMerchantName, nullsFirst(naturalOrder())).reversed() :
                                            comparing(ACHTransactionDto::getMerchantName, nullsFirst(naturalOrder()))
                            ).collect(Collectors.toList());

            case "transactionId" ->
                    transactions.stream()
                            .sorted(
                                    direction.isDescending() ?
                                            comparing(ACHTransactionDto::getTransactionId, nullsFirst(naturalOrder())).reversed() :
                                            comparing(ACHTransactionDto::getTransactionId, nullsFirst(naturalOrder()))
                            ).collect(Collectors.toList());

            case "accountNumber" ->
                    transactions.stream()
                            .sorted(
                                    direction.isDescending() ?
                                            comparing(ACHTransactionDto::getAccountNumber, nullsFirst(naturalOrder())).reversed() :
                                            comparing(ACHTransactionDto::getAccountNumber, nullsFirst(naturalOrder()))
                            ).collect(Collectors.toList());

            case "cardHolder" ->
                    transactions.stream()
                            .sorted(
                                    direction.isDescending() ?
                                            comparing(ACHTransactionDto::getCardHolder, nullsFirst(naturalOrder())).reversed() :
                                            comparing(ACHTransactionDto::getCardHolder, nullsFirst(naturalOrder()))
                            ).collect(Collectors.toList());

            case "address" ->
                    transactions.stream()
                            .sorted(
                                    direction.isDescending() ?
                                            comparing(ACHTransactionDto::getAddress, nullsFirst(naturalOrder())).reversed() :
                                            comparing(ACHTransactionDto::getAddress, nullsFirst(naturalOrder()))
                            ).collect(Collectors.toList());

            case "city" ->
                    transactions.stream()
                            .sorted(
                                    direction.isDescending() ?
                                            comparing(ACHTransactionDto::getCity, nullsFirst(naturalOrder())).reversed() :
                                            comparing(ACHTransactionDto::getCity, nullsFirst(naturalOrder()))
                            ).collect(Collectors.toList());

            case "state" ->
                    transactions.stream()
                            .sorted(
                                    direction.isDescending() ?
                                            comparing(ACHTransactionDto::getState, nullsFirst(naturalOrder())).reversed() :
                                            comparing(ACHTransactionDto::getState, nullsFirst(naturalOrder()))
                            ).collect(Collectors.toList());

            case "zipCode" ->
                    transactions.stream()
                            .sorted(
                                    direction.isDescending() ?
                                            comparing(ACHTransactionDto::getZipCode, nullsFirst(naturalOrder())).reversed() :
                                            comparing(ACHTransactionDto::getZipCode, nullsFirst(naturalOrder()))
                            ).collect(Collectors.toList());

            case "country" ->
                    transactions.stream()
                            .sorted(
                                    direction.isDescending() ?
                                            comparing(ACHTransactionDto::getCountry, nullsFirst(naturalOrder())).reversed() :
                                            comparing(ACHTransactionDto::getCountry, nullsFirst(naturalOrder()))
                            ).collect(Collectors.toList());

            case "phone" ->
                    transactions.stream()
                            .sorted(
                                    direction.isDescending() ?
                                            comparing(ACHTransactionDto::getPhone, nullsFirst(naturalOrder())).reversed() :
                                            comparing(ACHTransactionDto::getPhone, nullsFirst(naturalOrder()))
                            ).collect(Collectors.toList());

            case "email" ->
                    transactions.stream()
                            .sorted(
                                    direction.isDescending() ?
                                            comparing(ACHTransactionDto::getEmail, nullsFirst(naturalOrder())).reversed() :
                                            comparing(ACHTransactionDto::getEmail, nullsFirst(naturalOrder()))
                            ).collect(Collectors.toList());

            case "status" ->
                    transactions.stream()
                            .sorted(
                                    direction.isDescending() ?
                                            comparing(ACHTransactionDto::getStatus, nullsFirst(naturalOrder())).reversed() :
                                            comparing(ACHTransactionDto::getStatus, nullsFirst(naturalOrder()))
                            ).collect(Collectors.toList());

            case "amount" ->
                    transactions.stream()
                            .sorted(
                                    direction.isDescending() ?
                                            comparing(ACHTransactionDto::getAmountCents, nullsFirst(naturalOrder())).reversed() :
                                            comparing(ACHTransactionDto::getAmountCents, nullsFirst(naturalOrder()))
                            ).collect(Collectors.toList());

            case "ud1" ->
                    transactions.stream()
                            .sorted(
                                    direction.isDescending() ?
                                            comparing(ACHTransactionDto::getUd1, nullsFirst(naturalOrder())).reversed() :
                                            comparing(ACHTransactionDto::getUd1, nullsFirst(naturalOrder()))
                            ).collect(Collectors.toList());

            case "ud2" ->
                    transactions.stream()
                            .sorted(
                                    direction.isDescending() ?
                                            comparing(ACHTransactionDto::getUd2, nullsFirst(naturalOrder())).reversed() :
                                            comparing(ACHTransactionDto::getUd2, nullsFirst(naturalOrder()))
                            ).collect(Collectors.toList());

            case "ud3" ->
                    transactions.stream()
                            .sorted(
                                    direction.isDescending() ?
                                            comparing(ACHTransactionDto::getUd3, nullsFirst(naturalOrder())).reversed() :
                                            comparing(ACHTransactionDto::getUd3, nullsFirst(naturalOrder()))
                            ).collect(Collectors.toList());

            case "authCode" ->
                    transactions.stream()
                            .sorted(
                                    direction.isDescending() ?
                                            comparing(ACHTransactionDto::getAuthCode, nullsFirst(naturalOrder())).reversed() :
                                            comparing(ACHTransactionDto::getAuthCode, nullsFirst(naturalOrder()))
                            ).collect(Collectors.toList());

            case "authDate" ->
                    transactions.stream()
                            .sorted(
                                    direction.isDescending() ?
                                            comparing(ACHTransactionDto::getAuthDate, nullsFirst(naturalOrder())).reversed() :
                                            comparing(ACHTransactionDto::getAuthDate, nullsFirst(naturalOrder()))
                            ).collect(Collectors.toList());

            case "settlementDate" ->
                    transactions.stream()
                            .sorted(
                                    direction.isDescending() ?
                                            comparing(ACHTransactionDto::getSettlementDate, nullsFirst(naturalOrder())).reversed() :
                                            comparing(ACHTransactionDto::getSettlementDate, nullsFirst(naturalOrder()))
                            ).collect(Collectors.toList());

            case "achProcessor" ->
                    transactions.stream()
                            .sorted(
                                    direction.isDescending() ?
                                            comparing(ACHTransactionDto::getAchProcessor, nullsFirst(naturalOrder())).reversed() :
                                            comparing(ACHTransactionDto::getAchProcessor, nullsFirst(naturalOrder()))
                            ).collect(Collectors.toList());

            case "requestType" ->
                    transactions.stream()
                            .sorted(
                                    direction.isDescending() ?
                                            comparing(ACHTransactionDto::getRequestType, nullsFirst(naturalOrder())).reversed() :
                                            comparing(ACHTransactionDto::getRequestType, nullsFirst(naturalOrder()))
                            ).collect(Collectors.toList());

            case "orderNumber" ->
                    transactions.stream()
                            .sorted(
                                    direction.isDescending() ?
                                            comparing(ACHTransactionDto::getOrderNumber, nullsFirst(naturalOrder())).reversed() :
                                            comparing(ACHTransactionDto::getOrderNumber, nullsFirst(naturalOrder()))
                            ).collect(Collectors.toList());

            case "checkNumber" ->
                    transactions.stream()
                            .sorted(
                                    direction.isDescending() ?
                                            comparing(ACHTransactionDto::getCheckNumber, nullsFirst(naturalOrder())).reversed() :
                                            comparing(ACHTransactionDto::getCheckNumber, nullsFirst(naturalOrder()))
                            ).collect(Collectors.toList());

            case "responseCode" ->
                    transactions.stream()
                            .sorted(
                                    direction.isDescending() ?
                                            comparing(ACHTransactionDto::getResponseCode, nullsFirst(naturalOrder())).reversed() :
                                            comparing(ACHTransactionDto::getResponseCode, nullsFirst(naturalOrder()))
                            ).collect(Collectors.toList());

            case "responseText" ->
                    transactions.stream()
                            .sorted(
                                    direction.isDescending() ?
                                            comparing(ACHTransactionDto::getResponseText, nullsFirst(naturalOrder())).reversed() :
                                            comparing(ACHTransactionDto::getResponseText, nullsFirst(naturalOrder()))
                            ).collect(Collectors.toList());

            case "fundingDisposition" ->
                    transactions.stream()
                            .sorted(
                                    direction.isDescending() ?
                                            comparing(ACHTransactionDto::getFundingDisposition, nullsFirst(naturalOrder())).reversed() :
                                            comparing(ACHTransactionDto::getFundingDisposition, nullsFirst(naturalOrder()))
                            ).collect(Collectors.toList());

            case "type" ->
                    transactions.stream()
                            .sorted(
                                    direction.isDescending() ?
                                            comparing(ACHTransactionDto::getType, nullsFirst(naturalOrder())).reversed() :
                                            comparing(ACHTransactionDto::getType, nullsFirst(naturalOrder()))
                            ).collect(Collectors.toList());

            default ->
                    transactions.stream()
                            .sorted(comparing(ACHTransactionDto::getAuthDate, nullsFirst(naturalOrder())).reversed())
                            .collect(Collectors.toList());
        };
    }

    @Override
    public CCTransactionDto findCCTransaction(User user, Long id) {
        try {
            CCTransaction ccTransaction = ccTransactionService.find(id);
            throwIfNoAccessToMerchant(user, ccTransaction.getMerchantId());

            return ccTransactionMapper.toDto(user, ccTransaction);

        } catch (NotFoundException ex) {
            CPCCTransaction cpccTransaction = cpccTransactionService.find(id);
            throwIfNoAccessToMerchant(user, cpccTransaction.getMerchantId());

            return cpccTransactionMapper.toDto(user, cpccTransaction);
        }
    }

    @Override
    public ACHTransactionDto findACHTransaction(User user, Long id) {
        ACHTransaction achTransaction = achTransactionService.find(id);
        throwIfNoAccessToMerchant(user, achTransaction.getMerchantId());

        return achTransactionMapper.toDto(user, achTransaction);
    }

    @Override
    public FileDomain exportCCSettlementsSummary(String email, SettlementFilter filter) throws IOException {
        CCSettlementResponse settlementResponse = findCCSettlements(email, filter);

        String[] HEADERS = {
                "Settlement Date",
                "Sales Amount",
                "Sales Count",
                "Refund Amount",
                "Refund Count",
                "Refund Amount",
                "Refund Count",
                "AmericanExpress Amount",
                "AmericanExpress Refund",
                "AmericanExpress Count",
                "Bankcard Sales Amount",
                "Bankcard Sales Count"
        };

        final CSVFormat format = CSVFormat.Builder.create()
                .setHeader(HEADERS)
                .setIgnoreEmptyLines(true)
                .build();

        try (
                ByteArrayOutputStream out = new ByteArrayOutputStream();
                CSVPrinter csvPrinter = new CSVPrinter(new PrintWriter(out), format)
        ) {
            SimpleDateFormat dateFormat = new SimpleDateFormat(DATE_PATTERN);

            for (CCSettlementDto ccSettlementDto : settlementResponse.getSettlements()) {
                String settlementDate = ccSettlementDto.getDate() != null ?
                        dateFormat.format(Date.from(ccSettlementDto.getDate())) : "";

                List<String> data = Arrays.asList(
                        settlementDate,
                        ccSettlementDto.getSalesAmount(),
                        String.valueOf(ccSettlementDto.getSalesCount()),
                        ccSettlementDto.getRefundAmount(),
                        String.valueOf(ccSettlementDto.getRefundCount()),
                        ccSettlementDto.getSalesRefundAmount(),
                        String.valueOf(ccSettlementDto.getSalesRefundCount()),
                        ccSettlementDto.getAxAmount(),
                        ccSettlementDto.getAxRefund(),
                        String.valueOf(ccSettlementDto.getAxCount()),
                        ccSettlementDto.getBankCardSalesAmount(),
                        String.valueOf(ccSettlementDto.getBankCardCount())
                );

                csvPrinter.printRecord(data);
            }

            List<String> totalData = Arrays.asList(
                    "",
                    settlementResponse.getTotalSalesAmount(),
                    String.valueOf(settlementResponse.getTotalSalesCount()),
                    settlementResponse.getTotalRefundAmount(),
                    String.valueOf(settlementResponse.getTotalRefundCount()),
                    settlementResponse.getTotalSalesRefundAmount(),
                    String.valueOf(settlementResponse.getTotalSalesRefundCount()),
                    settlementResponse.getTotalAxAmount(),
                    settlementResponse.getTotalAxRefund(),
                    String.valueOf(settlementResponse.getTotalAxCount()),
                    settlementResponse.getTotalBankCardSalesAmount(),
                    String.valueOf(settlementResponse.getTotalBankCardCount())
            );

            csvPrinter.printRecord(totalData);
            csvPrinter.flush();

            return FileDomain.builder()
                    .file(new ByteArrayResource(new ByteArrayInputStream(out.toByteArray()).readAllBytes()))
                    .fileName(String.format("exported_cc_settlements_summary_%s.csv", LocalDate.now()))
                    .build();
        }
    }

    @Override
    public FileDomain exportACHSettlementsSummary(String email, SettlementFilter filter) throws IOException {
        ACHSettlementResponse settlementResponse = findACHSettlements(email, filter);

        String[] HEADERS = {
                "Settlement Date",
                "Merchant ID",
                "Merchant Name",
                "Settlement Amount",
                "Settlement Count"
        };

        Merchant merchant = merchantService.findByAchMerchantId(filter.getMerchantId());

        final CSVFormat format = CSVFormat.Builder.create()
                .setHeader(HEADERS)
                .setIgnoreEmptyLines(true)
                .build();

        try (
                ByteArrayOutputStream out = new ByteArrayOutputStream();
                CSVPrinter csvPrinter = new CSVPrinter(new PrintWriter(out), format)
        ) {

            SimpleDateFormat dateFormat = new SimpleDateFormat(DATE_PATTERN);

            for (ACHSettlementDto achSettlementDto : settlementResponse.getSettlements()) {
                String settlementDate = achSettlementDto.getSettleDate() != null ?
                        dateFormat.format(Date.from(achSettlementDto.getSettleDate())) : "";

                List<String> data = Arrays.asList(
                        settlementDate,
                        merchant.getMerchantId(),
                        merchant.getMerchantName(),
                        achSettlementDto.getSettleAmount(),
                        String.valueOf(achSettlementDto.getSettleCount())
                );

                csvPrinter.printRecord(data);
            }

            List<String> totalData = Arrays.asList(
                    "",
                    "",
                    "",
                    settlementResponse.getTotalAmount(),
                    String.valueOf(settlementResponse.getTotalCount())
            );

            csvPrinter.printRecord(totalData);
            csvPrinter.flush();

            return FileDomain.builder()
                    .file(new ByteArrayResource(new ByteArrayInputStream(out.toByteArray()).readAllBytes()))
                    .fileName(String.format("exported_ach_settlements_summary_%s.csv", LocalDate.now()))
                    .build();
        }
    }

    @Override
    public FileDomain exportCCTransactions(String email, TransactionFilter filter) throws Exception {
        User authUser = authenticationService.getCurrentUser(email);

        if(authUser.isNotAdmin()) {
            List<String> merchantIds = authenticationService.getCCMerchantIds(authUser);
            filter.setMerchantIds(merchantIds);
        }

        List<CCTransactionDto> transactions = findCCTransactions(authUser, filter, Sort.by(Sort.Order.desc("id")));
        Double totalAmount = totalCCAmount(transactions);

        return ccTransactionService.transactionsExport(authUser, transactions, totalAmount);
    }

    @Override
    public FileDomain exportCCSettlements(String email, TransactionFilter filter) throws Exception {
        User authUser = authenticationService.getCurrentUser(email);

        if(authUser.isNotAdmin()) {
            List<String> merchantIds = authenticationService.getCCMerchantIds(authUser);
            filter.setMerchantIds(merchantIds);
        }

        return ccTransactionService.settlementsExport(authUser, filter);
    }

    @Override
    public FileDomain exportACHTransactions(String email, TransactionFilter filter) throws IOException {
        User authUser = authenticationService.getCurrentUser(email);

        if(authUser.isNotAdmin()) {
            List<String> merchantIds = authenticationService.getACHMerchantIds(authUser);
            filter.setMerchantIds(merchantIds);
        }

        return achTransactionService.exportTransactions(authUser, filter);
    }

    @Override
    public FileDomain exportACHSettlements(String email, TransactionFilter filter) throws IOException {
        User authUser = authenticationService.getCurrentUser(email);

        if(authUser.isNotAdmin()) {
            List<String> merchantIds = authenticationService.getACHMerchantIds(authUser);
            filter.setMerchantIds(merchantIds);
        }

        return achTransactionService.exportSettlements(authUser, filter);
    }

    @Override
    public List<String> findExistingCCAuthDates(AuthDateFilter filter, String email) {
        User authUser = authenticationService.getCurrentUser(email);

        if(authUser.isNotAdmin()) {
            List<String> merchantIds = authenticationService.getMerchantIds(authUser);

            if(!merchantIds.contains(filter.getMerchantId())) {
                return Collections.emptyList();
            }
        }

        return ccTransactionService.findExistingCCAuthDates(filter);
    }

    @Override
    public List<String> findExistingACHAuthDates(AuthDateFilter filter, String email) {
        User authUser = authenticationService.getCurrentUser(email);

        if(authUser.isNotAdmin()) {
            List<String> merchantIds = authenticationService.getMerchantIds(authUser);

            if(!merchantIds.contains(filter.getMerchantId())) {
                return Collections.emptyList();
            }
        }

        return achTransactionService.findExistingACHAuthDates(filter);
    }

    @Override
    public Double totalAchAmount(TransactionType type, List<ACHTransactionDto> transactions) {
        double totalAmount = 0.0;

        boolean isSettlement = SETTLEMENT.equals(type);

        for(ACHTransactionDto transaction : transactions) {
            if(StringUtils.isNotBlank(transaction.getAmount())) {

                if(isSettlement && "REJECT".equalsIgnoreCase(transaction.getStatus())) {
                    boolean isFundingEmpty = StringUtils.isBlank(transaction.getFundingDisposition());
                    boolean isFundingA = "AFTER".equalsIgnoreCase(transaction.getFundingDisposition());

                    if (isFundingEmpty) totalAmount = totalAmount + Double.parseDouble(transaction.getAmount());
                    if (isFundingA) totalAmount = totalAmount - Double.parseDouble(transaction.getAmount());

                    continue;
                }

                boolean isCheckAccepted = "CHECK ACCEPTED".equalsIgnoreCase(transaction.getResponseText());

                boolean isCheck = "CHECK".equalsIgnoreCase(transaction.getRequestType());
                boolean isVoidACH = "VOIDACH".equalsIgnoreCase(transaction.getRequestType());
                boolean isReversal = "REVERSAL".equalsIgnoreCase(transaction.getRequestType());

                if (isCheckAccepted && isCheck) {
                    totalAmount = totalAmount + Double.parseDouble(transaction.getAmount());
                }

                if (isCheckAccepted && isVoidACH) {
                    totalAmount = totalAmount - Double.parseDouble(transaction.getAmount());
                }

                if (isCheckAccepted && isReversal) {
                    totalAmount = totalAmount - Double.parseDouble(transaction.getAmount());
                }
            }
        }

        return totalAmount;
    }

    @Override
    public Double totalCCAmount(List<CCTransactionDto> ccTransactionDtos) {
        double totalAmount = 0.0;

        for(CCTransactionDto transactionDto : ccTransactionDtos) {
            String requestType = transactionDto.getRequestType();
            String approvalStatus = transactionDto.getApprovalStatus();
            String responseText = transactionDto.getResponseText();

            boolean isAuthOnly = "AUTHONLY".equalsIgnoreCase(requestType);

            if(isAuthOnly && StringUtils.isBlank(transactionDto.getAuthAmount())) {
                break;
            }

            boolean isApproved = "APPROVED".equalsIgnoreCase(approvalStatus) || "APPROVED".equalsIgnoreCase(responseText);
            boolean isSaleOrCapt = "SALE".equalsIgnoreCase(requestType) || "CAPT".equalsIgnoreCase(requestType);

            if(isSaleOrCapt && isApproved) {
                totalAmount = totalAmount + Double.parseDouble(transactionDto.getAuthAmount());
            }

            boolean isCreditOrAccepted = "CREDIT".equalsIgnoreCase(requestType) && "RETURN ACCEPTED".equalsIgnoreCase(responseText);
            boolean isVoidOrProcessed = "VOID".equalsIgnoreCase(requestType) && "VOID PROCESSED".equalsIgnoreCase(responseText);

            if(isCreditOrAccepted || isVoidOrProcessed) {
                totalAmount = totalAmount - Double.parseDouble(transactionDto.getAuthAmount());
            }
        }

        return totalAmount;
    }

    @Override
    public IppayResponse saleTransaction(SaleTransactionRequest saleRequest) {
        final IppayHttpClientConfiguration configuration = new IppayHttpClientConfiguration
                .Builder(transURL, transConnTimeout, transRespTimeout)
                .build();

        IppayClient.init(configuration);

        IppayClient client  = IppayClient.getInstance();
        IppayRequest request = new IppayRequest();

        if(saleRequest.getAccountType() != null || saleRequest.getCheckNumber() != null) {
            AchType achType = buildAchType(saleRequest);
            request.setAch(achType);
        }

        setToken(request, saleRequest);
        setAccountNumber(request, saleRequest);
        setRoutingNumber(request, saleRequest);
        setAmount(request, saleRequest);
        setOrderNumber(request, saleRequest);
        setCustomerEmail(request, saleRequest);
        setTerminalId(request, saleRequest);
        setOrigin(request, saleRequest);
        setTransactionType(request, saleRequest);
        setCardDetails(request, saleRequest);
        setShippingInfo(request, saleRequest);
        setBillingDetails(request, saleRequest);
        setUserFields(request, saleRequest);

        return client.query(request);
    }

    @Override
    public IppayResponse voidACHTransaction(Long id, VoidTransactionRequest voidTransactionRequest) {
        return achTransactionService.voidTransaction(id, voidTransactionRequest);
    }

    @Override
    public IppayResponse voidCCTransaction(Long id, VoidTransactionRequest voidTransactionRequest) {
        return ccTransactionService.voidTransaction(id, voidTransactionRequest);
    }

    @Override
    public IppayResponse refundACHTransaction(Long id, RefundTransactionRequest refundTransactionRequest) {
        return achTransactionService.refundTransaction(id, refundTransactionRequest);
    }

    @Override
    public IppayResponse refundCCTransaction(Long id, RefundTransactionRequest refundTransactionRequest) {
        return ccTransactionService.refundTransaction(id, refundTransactionRequest);
    }

    private void throwIfNoAccessToMerchant(User authUser, String merchantId) {
        if(authUser.isNotAdmin()) {
            List<String> merchantIds = authenticationService.getMerchantIds(authUser);

            if(!merchantIds.contains(merchantId)) {
                throw new NoAccessException(AUTH_NO_ACCESS);
            }
        }
    }

    private void throwIfNoAccessToCCMerchant(User authUser, String merchantId) {
        if(authUser.isNotAdmin()) {
            List<String> merchantIds = authenticationService.getCCMerchantIds(authUser);

            if(!merchantIds.contains(merchantId)) {
                throw new NoAccessException(AUTH_NO_ACCESS);
            }
        }
    }

    private void throwIfNoAccessToACHMerchant(User authUser, String merchantId) {
        if(authUser.isNotAdmin()) {
            List<String> merchantIds = authenticationService.getACHMerchantIds(authUser);

            if(!merchantIds.contains(merchantId)) {
                throw new NoAccessException(AUTH_NO_ACCESS);
            }
        }
    }

    private AddressType buildAddressType(SaleTransactionRequest saleRequest) {
        AddressType addressType = new AddressType();

        addressType.setCity(saleRequest.getShippingCity());
        addressType.setStateProv(saleRequest.getShippingState());
        addressType.setPostalCode(saleRequest.getShippingZipCode());
        addressType.setCountry(saleRequest.getShippingCountry());
        addressType.setPhone(saleRequest.getShippingPhone());

        return addressType;
    }

    private ShippingInfoType buildShippingInfo(SaleTransactionRequest saleRequest, AddressType addressType) {
        ShippingInfoType shippingInfoType = new ShippingInfoType();

        shippingInfoType.setShippingName(saleRequest.getShippingName());
        shippingInfoType.setShippingMethod(saleRequest.getShippingMethod());
        shippingInfoType.setShippingAddr(addressType);
        shippingInfoType.setCustomerPO(saleRequest.getCustomerPONumber());

        return shippingInfoType;
    }

    private AchType buildAchType(SaleTransactionRequest saleRequest) {
        AchType achType = new AchType();

        if(saleRequest.getCheckNumber() != null) {
            CheckNumType checkNumber = new CheckNumType();
            checkNumber.setValue(saleRequest.getCheckNumber());

            achType.setCheckNumber(checkNumber);
        }

        if(saleRequest.getAccountType() != null) {
            achType.setType(saleRequest.getAccountType());
        }

        return achType;
    }

    private void setCardDetails(IppayRequest request, SaleTransactionRequest saleRequest) {
        if(isNotBlank(saleRequest.getCardNumber())) {
            CardNumType cardNumber = new CardNumType();
            cardNumber.setValue(saleRequest.getCardNumber().replaceAll(" ", ""));
            request.setCardNum(cardNumber);
        }

        if(isNotBlank(saleRequest.getCardCVV())) {
            Cvv2Type cvv2 = new Cvv2Type();
            cvv2.setValue(saleRequest.getCardCVV());
            request.setCvv2(cvv2);
        }

        if(saleRequest.getCardExpireMonth() != null) request.setCardExpMonth(saleRequest.getCardExpireMonth());
        if(saleRequest.getCardExpireYear() != null) request.setCardExpYear(saleRequest.getCardExpireYear());
        if(isNotBlank(saleRequest.getCardHolderName())) request.setCardName(saleRequest.getCardHolderName());
    }

    private void setUserFields(IppayRequest request, SaleTransactionRequest saleRequest) {
        if(isNotBlank(saleRequest.getUd1())) request.setUdField1(saleRequest.getUd1());
        if(isNotBlank(saleRequest.getUd2())) request.setUdField2(saleRequest.getUd2());
        if(isNotBlank(saleRequest.getUd3())) request.setUdField3(saleRequest.getUd3());
    }

    private void setBillingDetails(IppayRequest request, SaleTransactionRequest saleRequest){
        if(isNotBlank(saleRequest.getBillingAddress())) request.setBillingAddress(saleRequest.getBillingAddress());
        if(isNotBlank(saleRequest.getBillingCity())) request.setBillingCity(saleRequest.getBillingCity());
        if(isNotBlank(saleRequest.getBillingState())) request.setBillingStateProv(saleRequest.getBillingState());
        if(isNotBlank(saleRequest.getBillingZipCode())) request.setBillingPostalCode(saleRequest.getBillingZipCode());
        if(isNotBlank(saleRequest.getBillingCountry())) request.setBillingCountry(saleRequest.getBillingCountry());
        if(isNotBlank(saleRequest.getBillingPhone())) request.setBillingPhone(saleRequest.getBillingPhone());
    }

    private void setShippingInfo(IppayRequest request, SaleTransactionRequest saleRequest) {
        if(isNotBlank(saleRequest.getShippingCity()) || isNotBlank(saleRequest.getShippingAddress())) {
            AddressType addressType = buildAddressType(saleRequest);
            ShippingInfoType shippingInfoType = buildShippingInfo(saleRequest, addressType);
            request.setShippingInfo(shippingInfoType);
        }
    }

    private void setTransactionType(IppayRequest request, SaleTransactionRequest saleRequest) {
        if(saleRequest.getTransactionType() != null) {
            request.setTransactionType(saleRequest.getTransactionType());
        }
    }

    private void setCustomerEmail(IppayRequest request, SaleTransactionRequest saleRequest) {
        if(isNotBlank(saleRequest.getCustomerEmail())) {
            request.setEmail(saleRequest.getCustomerEmail());
        }
    }

    private void setTerminalId(IppayRequest request, SaleTransactionRequest saleRequest) {
        if(isNotBlank(saleRequest.getTerminalId())) {
            request.setTerminalID(saleRequest.getTerminalId());
        }
    }

    private void setOrigin(IppayRequest request, SaleTransactionRequest saleRequest) {
        if(saleRequest.getOrigin() != null) {
            request.setOrigin(saleRequest.getOrigin());
        }
    }

    private void setToken(IppayRequest request, SaleTransactionRequest saleRequest) {
        if(isNotBlank(saleRequest.getToken())) {
            request.setToken(saleRequest.getToken());
        }
    }

    private void setAccountNumber(IppayRequest request, SaleTransactionRequest saleRequest) {
        if(isNotBlank(saleRequest.getAccountNumber())) {
            request.setAccountNumber(saleRequest.getAccountNumber());
        }
    }

    private void setRoutingNumber(IppayRequest request, SaleTransactionRequest saleRequest) {
        if(isNotBlank(saleRequest.getRoutingNumber())) {
            request.setRoutingCode(saleRequest.getRoutingNumber());
        }
    }

    private void setAmount(IppayRequest request, SaleTransactionRequest saleRequest) {
        if(saleRequest.getAmount() != null) {
            request.setTotalAmount(saleRequest.getAmount());
        }
    }

    private void setOrderNumber(IppayRequest request, SaleTransactionRequest saleRequest) {
        if(isNotBlank(saleRequest.getOrderNumber())) {
            request.setOrderNumber(saleRequest.getOrderNumber());
        }
    }

	@Override
	public CCTransactionDto toEntity(TransactionSaveDto transactionSaveDto, PaymentResultDto paymentResultDto,
			TransactionGetResultDto paymentGetResultDto,String mid,
			String terminalId) {

		CPCCTransaction obj = cpccTransactionMapper.toEntity(transactionSaveDto, paymentResultDto, paymentGetResultDto, mid, terminalId);
		CPCCTransaction objGlobal = cpccTransactionRepo.save(obj);
				
        return cpccTransactionMapper.toDto(objGlobal);	
		//return null;
	}
}
