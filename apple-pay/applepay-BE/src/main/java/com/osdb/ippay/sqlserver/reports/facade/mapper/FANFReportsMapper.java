/*package com.osdb.ippay.sqlserver.reports.facade.mapper;

import com.osdb.ippay.common.util.number.DoubleUtil;
import com.osdb.ippay.common.util.string.StringUtil;
import com.osdb.ippay.primary.user.repository.entity.User;
import com.osdb.ippay.secondary.merchant.repository.entity.Merchant;
import com.osdb.ippay.secondary.transaction.facade.dto.CCTransactionDto;
import com.osdb.ippay.secondary.transaction.facade.dto.RefundVoidBtn;
import com.osdb.ippay.secondary.transaction.repository.entity.CCTransaction;
import com.osdb.ippay.sqlserver.reports.facade.dto.FANFReportsDto;
import com.osdb.ippay.sqlserver.reports.facade.dto.TotalACHDataGtwReportsDto;
import com.osdb.ippay.sqlserver.reports.facade.dto.TotalCardDataGtwReportsDto;
import com.osdb.ippay.sqlserver.reports.repository.entity.FANFReports;
import com.osdb.ippay.sqlserver.reports.repository.entity.TotalACHDataGtwReports;
import com.osdb.ippay.sqlserver.reports.repository.entity.TotalCardDataGtwReports;

import static com.osdb.ippay.common.util.date.DateUtil.isTheSameDayZoned;
import static com.osdb.ippay.secondary.transaction.facade.dto.RefundVoidBtn.NONE_BTN;
import static com.osdb.ippay.secondary.transaction.facade.dto.RefundVoidBtn.REFUND_BTN;
import static com.osdb.ippay.secondary.transaction.facade.dto.RefundVoidBtn.VOID_BTN;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;
//import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;

@Component
public class FANFReportsMapper {

   // public Page<TotalACHDataGtwReportsDto> toDto(Page<TotalCardDataGtwReports> totalCardDataGtwReports) {
      //  return TotalCardDataGtwReports.map(this::toDto);
   // }

	 public CCTransactionDto toDto(User user, CCTransaction ccTransaction) {
	        Merchant merchant = ccTransaction.getMerchant();

	        return CCTransactionDto.builder()
	                .id(ccTransaction.getId())
	                .terminalId(
	                        user.isAdmin() ?
	                                ccTransaction.getTerminalId() :
	                                StringUtil.hashString(ccTransaction.getTerminalId())
	                )
	                .merchantId(ccTransaction.getMerchantId())
	                .merchantName(Objects.nonNull(merchant) ? merchant.getMerchantName() : null)
	                .approvalStatus(
	                        StringUtils.isNotBlank(ccTransaction.getApprovalStatus()) ?
	                                ccTransaction.getApprovalStatus() :
	                                ccTransaction.getResponseText()
	                )
	                .cardType(ccTransaction.getCardType())
	                .cardNumber(ccTransaction.getToken())
	                .cardHolder(ccTransaction.getCardHolder())
	                .address(ccTransaction.getAddress())
	                .city(ccTransaction.getCity())
	                .state(ccTransaction.getState())
	                .zipCode(ccTransaction.getZipCode())
	                .country(ccTransaction.getCountry())
	                .phone(ccTransaction.getPhone())
	                .email(ccTransaction.getEmail())
	                .transactionId(ccTransaction.getTransactionId())
	                .transactionType(ccTransaction.getTransactionType())
	                .authCode(ccTransaction.getAuthCode())
	                .authAmount(toPriceFormat(ccTransaction.getAuthAmount()))
	                .authAmountCents(ccTransaction.getAuthAmount())
	                .authDate(ccTransaction.getAuthDateInstant())
	                .avs(ccTransaction.getAvs())
	                .cvv(ccTransaction.getCvv())
	                .ud1(ccTransaction.getUd1())
	                .ud2(ccTransaction.getUd2())
	                .ud3(ccTransaction.getUd3())
	                .requestType(ccTransaction.getRequestType())
	                .currencyCode(ccTransaction.getCurrencyCode())
	                .orderNumber(ccTransaction.getOrderNumber())
	                .settlementDate(ccTransaction.getSettlementDateInstant())
	                .responseCode(ccTransaction.getResponseCode())
	                .responseText(ccTransaction.getResponseText())
	                .ipAddress(null)
	                .token(ccTransaction.getToken())
	                .refundVoidBtn(getRefundVoidBtn(ccTransaction))
	                .ccProcessor(ccTransaction.getCcProcessor())
	                .posTerminalId(ccTransaction.getPosTerminalId())
	                .posAdditionalData(ccTransaction.getPosAdditionalData())
	                .vNumber(ccTransaction.getVNumber())
	                .retRefNumber(ccTransaction.getRetRefNumber())
	                .ipTransId(ccTransaction.getIpTransId())
	                .safeCardNumber(ccTransaction.getSafeCardNumber())
	                .expiry(ccTransaction.getExpiry())
	                .bankTransID(ccTransaction.getBankTransID())
	                .externalTransID(ccTransaction.getExternalTransID())
	                .build();
	    }

	 private String toPriceFormat(Integer value) {
	        return value != null ? DoubleUtil.toPriceFormat(value, 2, 100) : null;
	    }

	    private RefundVoidBtn getRefundVoidBtn(CCTransaction ccTransaction) {
	        if(StringUtils.isBlank(ccTransaction.getToken())) return NONE_BTN;
	        if(StringUtils.isNotBlank(ccTransaction.getSettlementDate())) return REFUND_BTN;

	        ZoneId zoneId = ZoneId.of("US/Central");

	        LocalDateTime authDateTime = LocalDateTime.ofInstant(ccTransaction.getAuthDateInstant(), zoneId);
	        LocalDateTime currDateTime = LocalDateTime.now(zoneId);

	        boolean isCurrDateEqAuthDate = isTheSameDayZoned(authDateTime, currDateTime);
	        if(isCurrDateEqAuthDate) return VOID_BTN;

	        return NONE_BTN;
	    }

}
*/