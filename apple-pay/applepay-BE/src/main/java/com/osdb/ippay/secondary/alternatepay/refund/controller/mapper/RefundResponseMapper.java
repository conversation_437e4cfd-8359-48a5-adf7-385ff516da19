package com.osdb.ippay.secondary.alternatepay.refund.controller.mapper;

import com.osdb.ippay.secondary.alternatepay.refund.controller.dto.RefundSaveDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionSaveDto;
import com.osdb.ippay.secondary.alternatepay.refund.controller.repository.entity.RefundResponse;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.Repository.Entity.TransactionResponse;
import com.osdb.ippay.secondary.alternatepay.refund.controller.dto.TransactionRefundResultDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionCancelResultDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionGetResultDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TrasactionInitResultDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.mapper.TransactionResponseMapper;

import org.springframework.stereotype.Component;

@Component
public class RefundResponseMapper {

	 TransactionResponseMapper transactionResponseMapper;
	 
    public RefundSaveDto toDto(RefundResponse refundResponse) {
        if (refundResponse == null) {
            return null;
        }
        return RefundSaveDto.builder()
                .amount(refundResponse.getAmount())
                .currency(refundResponse.getCurrency())
                .date(refundResponse.getDate())
                .status(refundResponse.getStatus())
                .approvedBy(refundResponse.getApprovedBy())
                .confirmationNumber(refundResponse.getConfirmationNumber())
                .dateApproval(refundResponse.getDateApproval())
                .providerId(refundResponse.getProviderId())
                .refundBy(refundResponse.getRefundBy())
                .refundConfirmation(refundResponse.getRefundConfirmation())
                .result(refundResponse.getResult())
                .status(refundResponse.getStatus())
                .transactionAmount(refundResponse.getTransactionAmount())
                .TransactionNumber(refundResponse.getTransactionNumber())
                .type(refundResponse.getType())
                .build();
    }

    public RefundResponse toEntity(TransactionRefundResultDto refundSaveDto) {
        if (refundSaveDto == null) {
            return null;
        }
        RefundResponse refundResponse = new RefundResponse();
        refundResponse.setAmount(refundSaveDto.getAmount());
        refundResponse.setApprovedBy(refundSaveDto.getApprovedBy());
        
        refundResponse.setConfirmationNumber(refundSaveDto.getRefundConfirmation());
        refundResponse.setDateApproval(refundSaveDto.getDateApproval());
        refundResponse.setProviderId(refundSaveDto.getProviderId());
        refundResponse.setRefundBy(refundSaveDto.getApprovedBy());
        refundResponse.setRefundConfirmation(refundSaveDto.getRefundConfirmation());
        refundResponse.setResult(refundSaveDto.getResult());
        refundResponse.setBusinessId(refundSaveDto.getTransaction().getBusinessId());
        refundResponse.setTransactionAmount(refundSaveDto.getTransaction().getAmount());
        refundResponse.setTransactionNumber(refundSaveDto.getTransaction().getTransactionNumber());
        refundResponse.setType(refundSaveDto.getTransaction().getType());
        refundResponse.setStatus(refundSaveDto.getTransaction().getStatus());
        refundResponse.setCurrency(refundSaveDto.getTransaction().getCurrency());
        refundResponse.setDate(refundSaveDto.getTransaction().getDate());
        
        return refundResponse;
    }
    
    public TransactionResponse putTransactionEntity(
    		TransactionRefundResultDto refundSaveDto,
    		TransactionResponse transactionResponse) {

         transactionResponse.setStatus(refundSaveDto.getTransaction().getStatus());
         transactionResponse.setStatusName(refundSaveDto.getResult());

         return transactionResponse;
    }

   

}