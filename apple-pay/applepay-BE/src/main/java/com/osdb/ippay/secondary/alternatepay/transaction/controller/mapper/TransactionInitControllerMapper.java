package com.osdb.ippay.secondary.alternatepay.transaction.controller.mapper;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionInitDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.*;

import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import static lombok.AccessLevel.PRIVATE;

@Component
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class TransactionInitControllerMapper {

    @NonFinal
    @Value("${tokenization.api-key}")
    String apiKey;
    
    @NonFinal
    @Value("${tokenization.branch-code}")
    String branchCode;

    public TransactionInitDto toDto(TransactionInitDto transactionInitDto) {
        return TransactionInitDto.builder()
                .branchId(branchCode)
                .apiKey(apiKey)
                .amount(transactionInitDto.getAmount())
                .currency(transactionInitDto.getCurrency())
                .callbackURL(transactionInitDto.getCallbackURL())
                .cancelURL(transactionInitDto.getCancelURL())
                .returnURL(transactionInitDto.getReturnURL())
                .clientEmail(transactionInitDto.getClientEmail())
                .clientName(transactionInitDto.getClientName())
                .clientPhoneNumber(transactionInitDto.getClientPhoneNumber())
                .isCent(transactionInitDto.isCent())
                .maxInstallments(transactionInitDto.getMaxInstallments())
                .partner(transactionInitDto.getPartner())
                .reason(transactionInitDto.getReason())
                .timeOut(transactionInitDto.getTimeOut())
                .readyForPayment(transactionInitDto.isReadyForPayment())
                .build();
    }

  
    public TrasactionInitResultDto toOutDto(TrasactionInitResultDto trnInitResultDto) {
        return TrasactionInitResultDto.builder()
                .amount(trnInitResultDto.getAmount())
                .currency(trnInitResultDto.getCurrency())
                .date(trnInitResultDto.getDate())
               // .maxInstallments(trnInitResultDto.getMaxInstallments())
                .methodType(trnInitResultDto.getMethodType())
                .reason(trnInitResultDto.getReason())
                .status(trnInitResultDto.getStatus())
                .statusName(trnInitResultDto.getStatusName())
                .timeoutDate(trnInitResultDto.getTimeoutDate())
                .TransactionNumber(trnInitResultDto.getTransactionNumber())
                .transactionPage(trnInitResultDto.getTransactionPage())
                .confirmation(trnInitResultDto.getConfirmation())
                .paymentType("Apple Pay")
                .build();
    }

}
