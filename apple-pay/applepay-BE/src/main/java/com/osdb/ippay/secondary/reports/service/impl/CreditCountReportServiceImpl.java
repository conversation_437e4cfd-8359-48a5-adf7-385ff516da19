package com.osdb.ippay.secondary.reports.service.impl;

import com.osdb.ippay.secondary.reports.repository.CreditCountReportRepository;
import com.osdb.ippay.secondary.reports.repository.entity.CreditCountReport;
import com.osdb.ippay.secondary.reports.service.CreditCountReportService;

import lombok.experimental.FieldDefaults;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import javax.persistence.EntityManager;


import static lombok.AccessLevel.PRIVATE;

@Service
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class CreditCountReportServiceImpl implements CreditCountReportService {

	CreditCountReportRepository creditCountReportRepository;	
	EntityManager entityManager;
	

	    @Autowired
	    public CreditCountReportServiceImpl(CreditCountReportRepository creditCountReportRepositoryObj,
	    		@Qualifier("secondaryEmFactory") EntityManager entityManager) {

	        this.creditCountReportRepository = creditCountReportRepositoryObj;
	        this.entityManager = entityManager;
	    }
	    


    @Override
    public List generateReport(String startDateTime,
			String endDateTime) {
    	List  creditCountTransactions = creditCountReportRepository.getCreditCountReport(startDateTime, endDateTime);
		
		if (creditCountTransactions.isEmpty()) {
			
				return new ArrayList<>();
	    }
		

	    if (creditCountTransactions.get(0) instanceof String) {
	        return ((List<String>) creditCountTransactions)
	          .stream()
	          .map(s -> new String[] { s })
	          .collect(Collectors.toList());
	    } else {
	        return (List<String[]>) creditCountTransactions;

	}
}

    private Pageable createPageRequestUsing(int page, int size) {
        return PageRequest.of(page, size);
    }
    
    @Override
    public Page<CreditCountReport> find(String startDateTime,
			String endDateTime,
			Pageable pageable) {
	 
	    List<CreditCountReport>  creditCountTransactions = creditCountReportRepository.getCreditCountReport(startDateTime, endDateTime);
	    Pageable pageRequest = createPageRequestUsing(pageable.getPageNumber(), pageable.getPageSize());
	    int start = (int) pageRequest.getOffset();
	    int end = Math.min((start + pageRequest.getPageSize()), creditCountTransactions.size());
	    List<CreditCountReport> pageContent = creditCountTransactions.subList(start, end);
	    
	    return new PageImpl<>(pageContent, pageRequest, creditCountTransactions.size());
	}
}
