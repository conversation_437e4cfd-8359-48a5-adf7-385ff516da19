package com.osdb.ippay.primary.user.facade.dto;

import com.osdb.ippay.common.entity.BaseEntity;
import lombok.*;
import lombok.experimental.FieldDefaults;
import lombok.experimental.SuperBuilder;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.Instant;
import java.util.List;

import static com.osdb.ippay.primary.user.repository.entity.UserRole.ADMIN_READ_ONLY_ROLE;
import static com.osdb.ippay.primary.user.repository.entity.UserRole.ADMIN_ROLE;
import static javax.persistence.EnumType.STRING;
import static lombok.AccessLevel.PRIVATE;

@Getter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class UserPartnerDetailsDto {


    String email;
    Long id;
  //  String firstName;

  //  String lastName;
    
}
