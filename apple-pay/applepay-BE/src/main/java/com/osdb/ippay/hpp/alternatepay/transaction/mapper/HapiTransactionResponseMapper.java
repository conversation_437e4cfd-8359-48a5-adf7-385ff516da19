package com.osdb.ippay.hpp.alternatepay.transaction.mapper;

import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionSaveDto;
import com.osdb.ippay.secondary.alternatepay.payment.controller.dto.PaymentResultDto;
import com.osdb.ippay.hpp.alternatepay.transaction.repository.entity.HapiTransactionResponse;
import com.osdb.ippay.hpp.alternatepay.transaction.dto.HapiTransactionDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionGetResultDto;

import java.math.BigInteger;
import java.security.MessageDigest;

import org.springframework.stereotype.Component;

@Component
public class HapiTransactionResponseMapper {

    public HapiTransactionDto toDto(HapiTransactionResponse transactionResponse) {
        if (transactionResponse == null) {
            return null;
        }
        return HapiTransactionDto.builder()
        		.Id(transactionResponse.getId())
                .address(transactionResponse.getAddress())
                .amount(transactionResponse.getAmount())
                .authCode(transactionResponse.getAuthCode())
                .callbackUrl(transactionResponse.getCallbackUrl())
                .callbackUrlText(transactionResponse.getCallbackUrlText())
                .cardExpiryMonth(transactionResponse.getCardExpiryMonth())
                .cardExpiryYear(transactionResponse.getCardExpiryYear())
                .cardHolderName(transactionResponse.getCardHolderName())
                .cardNumberMasked(transactionResponse.getCardNumberMasked())
                .cardToken(transactionResponse.getCardToken())
                .cardType(transactionResponse.getCardType())
                .city(transactionResponse.getCity())
                .country(transactionResponse.getCountry())
                .customerEmail(transactionResponse.getCustomerEmail())
                .customerId(transactionResponse.getCustomerId())
                .ippTransactionID(transactionResponse.getIppTransactionID())
                .isRecurring(transactionResponse.getIsRecurring())
                .mid(transactionResponse.getMid())
                .paymentType(transactionResponse.getPaymentType())
                .processedAmount(transactionResponse.getProcessedAmount())
                .referenceId(transactionResponse.getReferenceId())
                .responseCode(transactionResponse.getResponseCode())
                .responseText(transactionResponse.getResponseText())
                .returnToken(transactionResponse.getReturnToken())
                .saveCardForFuture(transactionResponse.getSaveCardForFuture())
                .saveForFuture(transactionResponse.getSaveForFuture())
                .state(transactionResponse.getState())
                .terminalId(transactionResponse.getTerminalId())
                .token(transactionResponse.getToken())
                .tokenizeCardSelected(transactionResponse.getTokenizeCardSelected())
                .transactionID(transactionResponse.getTransactionID())
                .transactionType(transactionResponse.getTransactionType())
                .valid(transactionResponse.getValid())
                .zip(transactionResponse.getZip())
                .enqReferenceId(transactionResponse.getEnqReferenceId())
        
                .build();
    }

    public String encryptSHA1(String input) {
    	
    	String hashtext = "";
        try {

            MessageDigest md = MessageDigest.getInstance("SHA-1");
            byte[] messageDigest = md.digest(input.getBytes());

            BigInteger no = new BigInteger(1, messageDigest);
            hashtext = no.toString(16);

            while (hashtext.length() < 40) {
                hashtext = "0" + hashtext;
            }

            return hashtext;
        }
        catch(Exception ex)
        {
        	return hashtext;
        }
        finally {
        	
        }
    }
        
    public HapiTransactionResponse toEntity(TransactionSaveDto transactionSaveDto) {
       

		HapiTransactionResponse transactionResponse = new HapiTransactionResponse();
		transactionResponse.setMid("84038965704");
        transactionResponse.setToken("5e7fc6777f18d9e2448b9adcfc7aca5ed94230b1");
        transactionResponse.setReferenceId("ERID6910946");
        transactionResponse.setCustomerId("890");
        transactionResponse.setCardHolderName("John Doe");
        
        transactionResponse.setAmount(transactionSaveDto.getAmount());
        transactionResponse.setAddress(transactionSaveDto.getAddress());
        transactionResponse.setCity(transactionSaveDto.getCity());
        transactionResponse.setCountry(transactionSaveDto.getCountry());
        transactionResponse.setPaymentType(transactionSaveDto.getPaymentType());
        transactionResponse.setState(transactionSaveDto.getState());
        transactionResponse.setTransactionID(transactionSaveDto.getTransactionId());
        transactionResponse.setTransactionType(transactionSaveDto.getPaymentType());
        transactionResponse.setZip(transactionSaveDto.getZip());
        
        /*transactionResponse.setAuthCode(transactionSaveDto.getAuthCode());
        transactionResponse.setCallbackUrl(transactionSaveDto.getCallbackUrl());
        transactionResponse.setCallbackUrlText(transactionSaveDto.getCallbackUrlText());
        transactionResponse.setCardExpiryMonth(transactionSaveDto.getCardExpiryMonth());
        transactionResponse.setCardExpiryYear(transactionSaveDto.getCardExpiryYear());
        transactionResponse.setCardNumberMasked(transactionSaveDto.getCardNumberMasked());
        transactionResponse.setCardToken(transactionSaveDto.getCardToken());
        transactionResponse.setCardType(transactionSaveDto.getCardType());
        transactionResponse.setCustomerEmail(transactionSaveDto.getCustomerEmail());
        transactionResponse.setTerminalId(transactionSaveDto.getTerminalId());
        transactionResponse.setIppTransactionID(transactionSaveDto.getIppTransactionID());
        transactionResponse.setIsRecurring(transactionSaveDto.getIsRecurring());
        transactionResponse.setProcessedAmount(transactionSaveDto.getProcessedAmount());
        transactionResponse.setResponseCode(transactionSaveDto.getResponseCode());
        transactionResponse.setResponseText(transactionSaveDto.getResponseText());
        transactionResponse.setReturnToken(transactionSaveDto.getReturnToken());
        transactionResponse.setSaveCardForFuture(transactionSaveDto.getSaveCardForFuture());
        transactionResponse.setSaveForFuture(transactionSaveDto.getSaveForFuture());
        transactionResponse.setTokenizeCardSelected(transactionSaveDto.getTokenizeCardSelected());
        transactionResponse.setValid(transactionSaveDto.getValid());*/
        
              
        return transactionResponse;
    }
    
    public String GetCardType(String strCardTypeAbbrv)
    {
    	  return switch (strCardTypeAbbrv) {
          case "VS" -> "Visa";
          case "MC" -> "Mastercard";
          case "DS" -> "Discovery";
          case "AX" -> "Amex";
        
          default -> strCardTypeAbbrv;
      };
    }

    public HapiTransactionResponse toPaymentEntity(TransactionSaveDto transactionSaveDto, 
    												PaymentResultDto paymentResultDto,
    												TransactionGetResultDto paymentGetResultDto, 
    												String mid,
    												String refId,
    												String customerId,
    												String terminalId){
        

		HapiTransactionResponse transactionResponse = new HapiTransactionResponse();
		/*transactionResponse.setMid("84038965704");
        transactionResponse.setToken(encryptSHA1("ERID6910946@Welcome"));
        transactionResponse.setReferenceId("ERID6910946");
        transactionResponse.setCustomerId("890");
        transactionResponse.setTerminalId("TESTTERMINAL");*/
        
        transactionResponse.setMid(mid);
        transactionResponse.setToken(encryptSHA1(refId+"@Welcome"));
        transactionResponse.setReferenceId(refId);
        transactionResponse.setCustomerId(customerId);
        transactionResponse.setTerminalId(terminalId);
        
        
        transactionResponse.setAmount(paymentResultDto.getConfirmation().getAmount());
        transactionResponse.setIsRecurring(0);
        transactionResponse.setEnqReferenceId(paymentResultDto.getConfirmation().getSensePassReferenceId());
        
        if(paymentGetResultDto != null && paymentGetResultDto.getPosData() != null)
        {
        	transactionResponse.setCustomerEmail(paymentGetResultDto.getPosData().getValueJSON().getCustomerEmail());
        	transactionResponse.setCardHolderName(paymentGetResultDto.getPosData().getValueJSON().getCustomerName());
        	transactionResponse.setAddress(paymentGetResultDto.getPosData().getValueJSON().getAddress());  
        	transactionResponse.setCity(paymentGetResultDto.getPosData().getValueJSON().getCity());  
        	transactionResponse.setCountry(paymentGetResultDto.getPosData().getValueJSON().getCountry());  
        	transactionResponse.setState(paymentGetResultDto.getPosData().getValueJSON().getState());  
        	transactionResponse.setZip(paymentGetResultDto.getPosData().getValueJSON().getZip());  
        }
        
        transactionResponse.setPaymentType(transactionSaveDto.getPaymentType());
        transactionResponse.setTransactionID(paymentGetResultDto.getTransactionNumber().substring(0, 55));
        transactionResponse.setTransactionType(transactionSaveDto.getPaymentType());
        
        transactionResponse.setAuthCode(paymentResultDto.getConfirmation().getConfirmationNumber());
        transactionResponse.setCardNumberMasked(paymentResultDto.getConfirmation().getCardMask());
        transactionResponse.setIppTransactionID(paymentGetResultDto.getTransactionNumber().substring(0, 55));
        transactionResponse.setReturnToken(encryptSHA1("ERID6910946@Welcome"));
        transactionResponse.setSaveCardForFuture("Yes");
        transactionResponse.setSaveForFuture(1);
        transactionResponse.setCardToken(paymentResultDto.getConfirmation().getCardMask());
        transactionResponse.setCardType(GetCardType(paymentResultDto.getConfirmation().getCardType()));
        
        /*
        transactionResponse.setCallbackUrl(transactionSaveDto.getCallbackUrl());
        transactionResponse.setCallbackUrlText(transactionSaveDto.getCallbackUrlText());
        transactionResponse.setCardExpiryMonth(transactionSaveDto.getCardExpiryMonth());
        transactionResponse.setCardExpiryYear(transactionSaveDto.getCardExpiryYear());
        
        
        transactionResponse.setCustomerEmail(transactionSaveDto.getCustomerEmail());
        transactionResponse.setTerminalId(transactionSaveDto.getTerminalId());
        
        transactionResponse.setIsRecurring(transactionSaveDto.getIsRecurring());
        transactionResponse.setProcessedAmount(transactionSaveDto.getProcessedAmount());
        transactionResponse.setResponseCode(transactionSaveDto.getResponseCode());
        transactionResponse.setResponseText(transactionSaveDto.getResponseText());
        
        
        transactionResponse.setTokenizeCardSelected(transactionSaveDto.getTokenizeCardSelected());
        transactionResponse.setValid(transactionSaveDto.getValid());*/
        
              
        return transactionResponse;
    }
    

}