package com.osdb.ippay.secondary.alternatepay.refund.controller.repository;

import com.osdb.ippay.secondary.alternatepay.refund.controller.repository.entity.RefundResponse;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;


@Repository
public interface RefundResponseRepository extends
        JpaRepository<RefundResponse, Long>,
        JpaSpecificationExecutor<RefundResponse> {
	
}
