package com.osdb.ippay.secondary.retokenization.presignedurl.controller;

import com.amazonaws.HttpMethod;
import com.osdb.ippay.common.logging.LogExecutionTime;
import com.osdb.ippay.common.util.annotation.XsrfTokenHeader;
import com.osdb.ippay.secondary.retokenization.presignedurl.controller.dto.PresignedURLOutDto;
import com.osdb.ippay.secondary.retokenization.presignedurl.controller.dto.UploadOutDto;
import com.osdb.ippay.secondary.retokenization.presignedurl.service.PresignedUrlFileService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.UUID;

import static lombok.AccessLevel.PRIVATE;
import static org.springframework.http.MediaType.MULTIPART_FORM_DATA_VALUE;

@RestController
@Tag(name = "presigned-url")
@RequestMapping(value = "/api/v1/private")
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class PresignedUrlFileController {

    PresignedUrlFileService presignedUrlFileService;

    @LogExecutionTime
    @XsrfTokenHeader
    @PostMapping(value = "/upload", consumes = MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<UploadOutDto> generateUrl(@RequestBody MultipartFile multipartFile) {
        String filename = UUID.randomUUID() + ".pgp";
        presignedUrlFileService.upload(filename, multipartFile);
        return ResponseEntity.ok(new UploadOutDto(filename, multipartFile.getOriginalFilename()));
    }

    @LogExecutionTime
    @GetMapping("/getUrl")
    public ResponseEntity<PresignedURLOutDto> getUrl(@RequestParam String filename) {
        String link = presignedUrlFileService.generatePreSignedUrl(filename, HttpMethod.GET);
        return ResponseEntity.ok(new PresignedURLOutDto(link, HttpMethod.GET.name()));
    }
    
}
