package com.osdb.ippay.primary.user.repository;

import com.osdb.ippay.primary.user.repository.entity.UserPartnerDetails;
import com.osdb.ippay.secondary.alerts.repository.entity.SettledTransaction;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface UserPartnerDetailsRepository extends
        JpaRepository<UserPartnerDetails, Long>,
        JpaSpecificationExecutor<UserPartnerDetails> {


	UserPartnerDetails getPartnerDetails(String merchantId);

	
}
