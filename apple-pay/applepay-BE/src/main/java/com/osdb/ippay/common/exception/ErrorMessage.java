package com.osdb.ippay.common.exception;

public class ErrorMessage {

    private ErrorMessage() {}

    // Auth
    public static final String AUTH_INVALID_TOKEN = "Invalid access token.";
    public static final String AUTH_INVALID_RESET_PASSWORD_TOKEN = "Access link has expired. Please contact Support";
    public static final String AUTH_INVALID_USER_CREDS = "User email and password not found.";
    public static final String AUTH_INVALID_OTP = "Incorrect OTP.";
    public static final String AUTH_LOCKED_ACCOUNT = "User account is locked. Please contact Support.";
    public static final String AUTH_DISABLED_ACCOUNT = "User account is disabled. Please contact Support.";
    public static final String AUTH_RECENT_PASSWORD_CANNOT_BE_REUSED = "Recent password cannot be reused.";
    public static final String AUTH_PASSWORD_EXPIRED = "Your password has expired, please change it.";
    public static final String AUTH_NO_ACCESS = "User does not have rights to perform this operation.";
    public static final String RESEND_INVITATION = "Resend invitation is available only for the users with INVITED status.";

    // User
    public static final String USER_EMAIL_NOT_FOUND = "User email not found.";
    public static final String USER_ID_NOT_FOUND = "User id not found.";
    public static final String USER_CONFIG_NOT_FOUND = "User config not found.";
    public static final String USER_CONFIG_ALREADY_EXISTS = "User config already exists.";
    public static final String USER_CONFIG_PROPERTY_IS_INVALID = "User configs property is not valid.";
    public static final String USER_INCORRECT_CURR_PASSWORD = "Incorrect current password.";
    public static final String EMAIL_ALREADY_EXISTS = "This Login ID is already being used.";

    // Transactions
    public static final String CC_TRANSACTION_NOT_FOUND = "Credit Card transaction not found.";
    public static final String ACH_TRANSACTION_NOT_FOUND = "E-Check transaction not found.";
    public static final String TRANSACTION_ID_NOT_FOUND = "Transaction id not found.";

    // Merchants
    public static final String MERCHANT_ID_NOT_FOUND = "Merchant id not found.";

    // Partners
    public static final String PARTNER_ID_NOT_FOUND = "Partner id not found.";

}
