package com.osdb.ippay.common.security.provider;

import com.osdb.ippay.common.exception.business.InvalidJwtException;
import com.osdb.ippay.common.exception.business.NotFoundException;
import com.osdb.ippay.primary.user.repository.entity.User;
import com.osdb.ippay.primary.user.service.UserService;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.UUID;

import static com.osdb.ippay.common.exception.ErrorMessage.AUTH_INVALID_RESET_PASSWORD_TOKEN;
import static com.osdb.ippay.common.exception.ErrorMessage.AUTH_INVALID_TOKEN;
import static lombok.AccessLevel.PRIVATE;

@Component
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class JwtProvider {

    @NonFinal
    @Value("${security.jwt.access-token.secret-key}")
    String ACCESS_TOKEN_SECRET_KEY;

    @NonFinal
    @Value("${security.jwt.access-token.expire-length}")
    Long ACCESS_TOKEN_EXPIRATION;

    @NonFinal
    @Value("${security.jwt.reset-token.secret-key}")
    String RESET_TOKEN_SECRET_KEY;

    @NonFinal
    @Value("${security.jwt.reset-token.expire-length}")
    Long RESET_TOKEN_EXPIRATION;

    UserService userService;

    public String createAccessToken(String email, String sessionsHash) {
        Claims claims = Jwts.claims().setSubject(String.format("%s::%s", email, sessionsHash));
        return getToken(claims, ACCESS_TOKEN_EXPIRATION, ACCESS_TOKEN_SECRET_KEY);
    }

    public String createResetToken(User user) {
        String hash = user.getResetPasswordHash();

        if(StringUtils.isBlank(hash)) {
            hash = UUID.randomUUID().toString();
            user.setResetPasswordHash(hash);
            userService.save(user);
        }

        Claims claims = Jwts.claims().setSubject(hash);
        return getToken(claims, RESET_TOKEN_EXPIRATION, RESET_TOKEN_SECRET_KEY);
    }

    private String getToken(Claims claims, Long expiration, String secretKey) {
        Date issuedAt = new Date();

        return Jwts.builder()
                .setClaims(claims)
                .setIssuedAt(issuedAt)
                .setExpiration(new Date(issuedAt.getTime() + expiration))
                .signWith(SignatureAlgorithm.HS256, secretKey)
                .compact();
    }

    public String getSubject(String token) {
        try {
            return Jwts.parser()
                    .setSigningKey(ACCESS_TOKEN_SECRET_KEY)
                    .parseClaimsJws(token)
                    .getBody()
                    .getSubject();

        } catch (Exception ex) {
            throw new InvalidJwtException(AUTH_INVALID_TOKEN);
        }
    }

    public String getResetTokenHash(String token) {
        try {
            return Jwts.parser()
                    .setSigningKey(RESET_TOKEN_SECRET_KEY)
                    .parseClaimsJws(token)
                    .getBody()
                    .getSubject();

        } catch (Exception ex) {
            throw new NotFoundException(AUTH_INVALID_RESET_PASSWORD_TOKEN);
        }
    }
}
