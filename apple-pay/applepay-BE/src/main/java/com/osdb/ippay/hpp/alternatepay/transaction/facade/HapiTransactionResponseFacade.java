package com.osdb.ippay.hpp.alternatepay.transaction.facade;

import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionSaveDto;
import com.osdb.ippay.secondary.alternatepay.payment.controller.dto.PaymentResultDto;
import com.osdb.ippay.hpp.alternatepay.transaction.dto.HapiTransactionDto;
import com.osdb.ippay.secondary.alternatepay.transaction.controller.dto.TransactionGetResultDto;

public interface HapiTransactionResponseFacade {
    
    HapiTransactionDto findByTrnid(String Trnid);
    
    HapiTransactionDto create(TransactionSaveDto transactionSaveDto);

    HapiTransactionDto toPaymentEntity(TransactionSaveDto transactionSaveDto, 
    									PaymentResultDto paymentResultDto,
    									TransactionGetResultDto paymentGetResultDto,
    									String mid,
										String refId,
										String customerId,
										String terminalId);
}