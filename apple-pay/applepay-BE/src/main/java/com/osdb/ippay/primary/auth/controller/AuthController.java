package com.osdb.ippay.primary.auth.controller;

import com.osdb.ippay.common.logging.LogExecutionTime;
import com.osdb.ippay.primary.auth.facade.AuthFacade;
import com.osdb.ippay.primary.auth.facade.dto.*;
import com.osdb.ippay.common.user.facade.dto.UserDto;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import static lombok.AccessLevel.PRIVATE;

@Tag(name = "auth")
@RestController
@RequestMapping(value = "/api/v1")
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class AuthController {
/*
    AuthFacade authFacade;

    @LogExecutionTime
    @PostMapping("/public/auth/sign-in/step-1")
    public ResponseEntity<Void> signInStepOne(@Valid @RequestBody SignInStepOneDto stepOneDto) {
        authFacade.signInStepOne(stepOneDto);
        return ResponseEntity.noContent().build();
    }

    @LogExecutionTime
    @PostMapping("/public/auth/sign-in/step-2")
    public ResponseEntity<UserDto> signInStepTwo(@Valid @RequestBody SignInStepTwoDto stepTwoDto,
                                                 @Parameter(hidden = true) HttpServletRequest httpRequest,
                                                 @Parameter(hidden = true) HttpServletResponse httpResponse) {

        UserDto response = authFacade.signInStepTwo(stepTwoDto, httpRequest, httpResponse);
        return ResponseEntity.ok(response);
    }

    @LogExecutionTime
    @PostMapping("/public/auth/forgot-password/step-1")
    public ResponseEntity<Void> forgotPassword(@Valid @RequestBody ForgotPasswordDto forgotPasswordDto) {
        authFacade.forgotPassword(forgotPasswordDto);
        return ResponseEntity.noContent().build();
    }

    @LogExecutionTime
    @PostMapping("/public/auth/forgot-password/step-2")
    public ResponseEntity<Void> newPassword(@Valid @RequestBody ResetPasswordDto resetPasswordDto) {
        authFacade.resetPassword(resetPasswordDto);
        return ResponseEntity.noContent().build();
    }

    @LogExecutionTime
    @GetMapping("/public/auth/csrf-token")
    public ResponseEntity<Void> getCsrfToken() {
        return ResponseEntity.noContent().build();
    }

    @LogExecutionTime
    @PostMapping("/private/auth/sign-out")
    public ResponseEntity<Void> signOut(@Parameter(hidden = true) HttpServletRequest request,
                                        @Parameter(hidden = true) HttpServletResponse response,
                                        @Parameter(hidden = true) @AuthenticationPrincipal String email) {

        authFacade.signOut(request, response, email);
        return ResponseEntity.noContent().build();
    }

    @LogExecutionTime
    @GetMapping("/private/auth/me")
    public ResponseEntity<UserDto> getAuthUser(@Parameter(hidden = true) @AuthenticationPrincipal String email) {
        UserDto response = authFacade.getCurrentUser(email);
        return ResponseEntity.ok(response);
    }

    @LogExecutionTime
    @PatchMapping("/private/auth/change-password")
    public ResponseEntity<Void> changePassword(@Valid @RequestBody ChangePasswordDto changePasswordDto,
                                               @Parameter(hidden = true) @AuthenticationPrincipal String email,
                                               @Parameter(hidden = true) HttpServletRequest request,
                                               @Parameter(hidden = true) HttpServletResponse response) {

        authFacade.changePassword(email, changePasswordDto);
        authFacade.signOut(request, response);

        return ResponseEntity.noContent().build();
    }*/
}
