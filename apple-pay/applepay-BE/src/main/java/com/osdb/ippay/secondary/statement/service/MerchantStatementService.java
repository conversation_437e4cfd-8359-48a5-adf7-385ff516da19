package com.osdb.ippay.secondary.statement.service;

import com.osdb.ippay.secondary.statement.repository.entity.*;
import com.osdb.ippay.secondary.statement.service.filter.MSFilter;

import java.util.List;

public interface MerchantStatementService {

    List<MSActivitySummary> findActivitySummary(String merchantId, MSFilter msFilter);

    List<MSDepositDetail> findDepositDetails(String merchantId, MSFilter msFilter);

    List<MSOtherCardDeposit> findOtherCardDeposits(String merchantId, MSFilter msFilter);

    List<MSProcessingDetail> findProcessingDetail(String merchantId, MSFilter msFilter);

    List<MSAuthDetail> findAuthDetails(String merchantId, MSFilter msFilter);

    List<MSOtherDetail> findOtherDetails(String merchantId, MSFilter msFilter);

    List<MSTotalDetail> findTotalDetail(String merchantId, MSFilter msFilter);

}
