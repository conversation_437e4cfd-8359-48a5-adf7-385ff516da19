package com.osdb.ippay.secondary.chargeback.repository.entity;

import com.osdb.ippay.secondary.merchant.repository.entity.Merchant;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Formula;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import javax.persistence.*;
import java.time.LocalDate;

import static javax.persistence.GenerationType.IDENTITY;
import static lombok.AccessLevel.PRIVATE;

@Entity
@Table(name = "ImportChargebacks")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
public class Chargeback {

    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = IDENTITY)
    Long id;

    @Column(name = "DBAName")
    String merchantName;

    @Column(name = "MerchantNumber")
    String merchantId;

    @NotFound(action = NotFoundAction.IGNORE)
    @ManyToOne
    @JoinColumn(name = "MerchantNumber", referencedColumnName = "mid", updatable = false, insertable = false)
    Merchant merchant;

    @Column(name = "CardBrand")
    String cardType;

    @Column(name = "CardholderAccountNumber")
    String creditCard;

    @Column(name = "CaseNumber")
    String caseNumber;

    @Column(name = "CaseAmount")
    String amount;

    @Column(name = "ReasonDesc")
    String reason;

    @Column(name = "ResolutionTo")
    String resolutionTo;

    @Column(name = "Debit_Credit")
    String debitCredit;

    @Column(name = "ItemType")
    String type;

    @Column(name = "OriginalReferenceNumber")
    String originRef;

    @Formula("STR_TO_DATE( DateTransaction, '%m%d%Y' )")
    @Column(name = "DateTransaction")
    LocalDate dateTransaction;

    @Formula("STR_TO_DATE( DateTransaction, '%m%d%Y' )")
    @Column(name = "DateResolved")
    LocalDate dateResolved;

    public String getResolutionTo() {
        if (StringUtils.isBlank(resolutionTo)) {
            return resolutionTo;
        }

        return switch (resolutionTo) {
            case "B" -> "Issuing Bank";
            case "M" -> "Merchant";
            case "G" -> "Acquirer's General Ledger";
            case "S" -> "Split";
            case "C" -> "Closed";
            default -> resolutionTo;
        };
    }

    public String getAmount() {
        if(StringUtils.isBlank(amount)) {
            return amount;
        }

        return amount.replaceFirst("^0+(?!$)", "");
    }

    public String getType() {
        if(StringUtils.isBlank(type)) {
            return type;
        }

        return switch (type) {
            case "00" -> "Retrieval";
            case "01" -> "First Chargeback";
            case "02" -> "Second Chargeback";
            case "03" -> "First Reversal";
            case "04" -> "Second Reversal";
            case "09" -> "Visa Pre Arbs";
            case "11" -> "Discover Retrieval";
            case "12" -> "Discover Chargeback";
            case "13" -> "Discover Reversal";
            case "14" -> "Discover PreArbitration";
            case "15" -> "Discover Incoming Dispute Arb";
            case "16" -> "Discover PreArb/Arb Debit";
            case "17" -> "American Express Retrieval";
            case "18" -> "American Express Chargeback";
            case "19" -> "American Express Reversal";
            case "20" -> "Allocation";
            case "21" -> "Collaboration";
            case "22" -> "Allocation Reversal";
            case "23" -> "Collaboration Reversal";
            case "24" -> "Collaboration PreArbitration";
            case "25" -> "MasterCard PreArbitration";
            case "26" -> "MasterCard Arbitration";
            default -> type;
        };
    }

    public String getDebitCredit() {
        if(StringUtils.isBlank(debitCredit)) {
            return debitCredit;
        }

        return switch (debitCredit) {
            case "D" -> "Debit";
            case "C" -> "Credit";
            case "N" -> "Non Financial";
            default -> debitCredit;
        };
    }

    public String getCardType() {
        if(StringUtils.isBlank(cardType)) {
            return cardType;
        }

        return switch (cardType) {
            case "1" -> "VS";
            case "2" -> "MC";
            case "3" -> "DS";
            case "4" -> "AX";
            case "5" -> "PP";
            default -> cardType;
        };
    }
}
