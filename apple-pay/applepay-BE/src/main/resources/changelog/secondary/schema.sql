
CREATE TABLE IF NOT EXISTS `ImportChargebacks` (
                                                   `ID` bigint(20) NOT NULL,
                                                   `ImportFileID` int(11) DEFAULT NULL,
                                                   `ImportDate` datetime(6) DEFAULT CURRENT_TIMESTAMP(6),
                                                   `CaseNumber` varchar(20) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `FamilyID` varchar(7) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `ItemType` varchar(2) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `MerchantNumber` varchar(16) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `CaseType` varchar(2) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `ResolutionTo` varchar(1) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `Debit_Credit` varchar(1) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `TranCode` varchar(2) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `ReasonCode` varchar(4) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `ReasonDesc` varchar(50) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `BIN_ICA` varchar(6) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `CaseAmount` varchar(12) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `RecordType` varchar(1) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `CardBrand` varchar(1) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `DateResolved` varchar(8) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `AcquirerReferenceNumber` varchar(23) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `OriginalReferenceNumber` varchar(30) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `Foreign_Domestic` varchar(1) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `MCC` varchar(4) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `AuthCode` varchar(6) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `DateTransaction` varchar(8) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `ImportDateTransaction` date DEFAULT NULL,
                                                   `ImportDateLoaded` date DEFAULT NULL,
                                                   `ImportDatePosted` date DEFAULT NULL,
                                                   `ImportDateSecondRequest` date DEFAULT NULL,
                                                   `ImportDateResolved` date DEFAULT NULL,
                                                   `DatePosted` varchar(8) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `DateLoaded` varchar(8) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `CardholderAccountNumber` varchar(19) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `DateSecondRequest` varchar(8) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `AccountNumberPrefix` varchar(6) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `AccountNumberSuffix` varchar(4) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `DBAName` varchar(45) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `Address1` varchar(40) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `Address2` varchar(40) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `City` varchar(25) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `State` varchar(2) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `ZipCode` varchar(10) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `GroupID` varchar(6) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `Association` varchar(16) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `InvoiceNumber` varchar(8) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `DateWarehouse` varchar(8) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `TransID` varchar(15) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `MerchAmount` varchar(12) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `MatchedTransactionID` int(11) DEFAULT NULL,
                                                   `MatchedDate` datetime DEFAULT NULL,
                                                   `CompleteLine` varchar(550) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `DateUpdated` datetime(6) DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)
) ENGINE=InnoDB AUTO_INCREMENT=139380 DEFAULT CHARSET=latin1;

ALTER TABLE `ImportChargebacks`
    ADD PRIMARY KEY (`ID`),
    ADD UNIQUE KEY `IDX_FILE` (`ID`),
    ADD KEY `AuthCode` (`AuthCode`),
    ADD KEY `TransID` (`TransID`),
    ADD KEY `ImportFileID` (`ImportFileID`),
    ADD KEY `BIN_ICA` (`BIN_ICA`),
    ADD KEY `AuthCode_2` (`AuthCode`),
    ADD KEY `AccountNumberSuffix` (`AccountNumberSuffix`),
    ADD KEY `DateUpdated` (`DateUpdated`),
    ADD KEY `MatchedDate` (`MatchedDate`);

ALTER TABLE `ImportChargebacks`
    MODIFY `ID` bigint(20) NOT NULL AUTO_INCREMENT,AUTO_INCREMENT=139380;


CREATE TABLE IF NOT EXISTS `CardPresentCCTransaction` (
                                                          `ID` bigint(20) NOT NULL,
                                                          `ProcessorID` bigint(20) NOT NULL,
                                                          `MerchantName` varchar(50) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                          `MerchantID` varchar(25) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                                          `TerminalID` varchar(25) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                          `CCProcessor` varchar(10) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                          `AuthCode` varchar(10) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                          `ActionCode` varchar(3) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                          `RequestType` varchar(10) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                          `TransactionType` varchar(15) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                          `TransAmount` int(11) DEFAULT NULL,
                                                          `AuthTransDate` datetime DEFAULT NULL,
                                                          `FeeAmount` int(11) DEFAULT NULL,
                                                          `TaxAmount` int(11) DEFAULT NULL,
                                                          `SettlementDate` datetime DEFAULT NULL,
                                                          `SettlementAmount` int(11) DEFAULT NULL,
                                                          `SettlementID` bigint(20) DEFAULT NULL,
                                                          `ApprovalStatus` varchar(25) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                          `Token` varchar(17) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                          `CardBin` varchar(6) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                          `CardLastFour` varchar(4) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                          `CardType` varchar(2) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                          `CurrencyCode` varchar(4) DEFAULT NULL,
                                                          `ExpDate` int(11) DEFAULT NULL,
                                                          `SafeCardNum` varchar(20) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                          `CardHolderName` varchar(60) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                          `Address` varchar(50) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                          `City` varchar(25) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                          `State` varchar(25) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                          `ZipCode` varchar(10) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                          `Country` varchar(3) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                          `Phone` varchar(50) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                          `Email` varchar(50) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                          `AVSResponse` varchar(2) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                          `CVVResponse` varchar(2) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                          `BankTransID` varchar(10) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                          `ExternalTransID` varchar(18) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                          `TransactionID` varchar(50) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                          `UserField1` varchar(60) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                          `UserField2` varchar(60) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                          `UserField3` varchar(60) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                          `POSTerminalID` varchar(4) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                          `POSAdditionalData` varchar(12) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                          `RetrievalReferenceNumber` bigint(12) DEFAULT NULL,
                                                          `VNumber` varchar(10) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                          `OrderNumber` varchar(25) DEFAULT NULL,
                                                          `ResponseText` varchar(25) DEFAULT NULL,
                                                          `ChargebackID` int(11) DEFAULT NULL,
                                                          `CardNumber` varchar(20) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                          `ChargebackLoadDate` date DEFAULT NULL,
                                                          `SecondRequestID` int(11) DEFAULT NULL,
                                                          `ChargebackSecondRequest` date DEFAULT NULL,
                                                          `DateAdded` datetime(6) DEFAULT CURRENT_TIMESTAMP(6),
                                                          `DateUpdated` datetime(6) DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)
) ENGINE=InnoDB AUTO_INCREMENT=1060827183 DEFAULT CHARSET=latin1;

