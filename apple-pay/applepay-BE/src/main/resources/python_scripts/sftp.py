import paramiko

from dotenv import load_dotenv
import os
     
load_dotenv()

# Accessing values for SFTP connection parameters

hostname = os.getenv('hostname')
port = os.getenv('port')
username = os.getenv('ftpusername')
password = os.getenv('ftppassword')


# Local and remote directory paths
local_directory_to_upload = os.getenv('localEncryptedFile')
remote_destination_directory = os.getenv('remoteEncryptedFile')

# Create an SSH client

def upload_directory_sftp(sftp_client, local_dir, remote_dir):
    """
    Uploads all files and subdirectories from a local directory to a remote SFTP directory.

    Args:
        sftp_client (paramiko.SFTPClient): An active SFTP client instance.
        local_dir (str): The path to the local directory to upload.
        remote_dir (str): The path to the remote directory on the SFTP server.
    """
    for item in os.listdir(local_dir):
        local_path = os.path.join(local_dir, item)
        remote_path = os.path.join(remote_dir, item)

        if os.path.isfile(local_path):
            # Upload individual files
            try:
                sftp_client.put(local_path, remote_path)
                print(f"Uploaded: {local_path} to {remote_path}")
            except Exception as e:
                print(f"Error uploading {local_path}: {e}")
        elif os.path.isdir(local_path):
            # Create remote directory and recursively upload its contents
            try:
                sftp_client.mkdir(remote_path)
                print(f"Created remote directory: {remote_path}")
            except IOError:
                # Directory might already exist, ignore error
                pass
            upload_directory_sftp(sftp_client, local_path, remote_path)



try:
    # Establish SSH connection
    ssh_client = paramiko.SSHClient()
    ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())  # Be cautious with AutoAddPolicy in production
    ssh_client.connect(hostname, port, username, password)

    # Open SFTP session
    sftp_client = ssh_client.open_sftp()

    # Create the base remote directory if it doesn't exist
    try:
        sftp_client.mkdir(remote_destination_directory)
        print(f"Created base remote directory: {remote_destination_directory}")
    except IOError:
        # Directory might already exist, ignore error
        pass

    # Upload the directory
    upload_directory_sftp(sftp_client, local_directory_to_upload, remote_destination_directory)


except paramiko.AuthenticationException:
    print("Authentication failed. Check your username and password.")
except paramiko.SSHException as e:
    print(f"SSH connection error: {e}")
except Exception as e:
    print(f"An unexpected error occurred: {e}")
finally:
    if 'sftp_client' in locals() and sftp_client:
        sftp_client.close()
    if 'ssh_client' in locals() and ssh_client:
        ssh_client.close()

        
if os.path.exists(local_directory_to_upload):
    for filename in os.listdir(local_directory_to_upload):
        file_path = os.path.join(local_directory_to_upload, filename)
        if os.path.isfile(file_path):
            try:
                os.remove(file_path)
                print(f"Removed: {file_path}")
            except OSError as e:
                print(f"Error removing {file_path}: {e}")