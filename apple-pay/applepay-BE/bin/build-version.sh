# will tag the repo and update the version in the config file

VERSION=$1

# update config files with version
sed -i -r "s/app.version=.*/app.version=$VERSION/" src/main/resources/application.properties
sed -i -r "s/app.version=.*/app.version=$VERSION/" src/main/resources/application-dev.properties
sed -i -r "s/app.version=.*/app.version=$VERSION/" src/main/resources/application-local.properties
sed -i -r "s/app.version=.*/app.version=$VERSION/" src/main/resources/application-prod.properties
sed -i -r "s/app.version=.*/app.version=$VERSION/" src/main/resources/application-stage.properties


# update branch with version
git add src/main/resources/application.properties
git add src/main/resources/application-dev.properties
git add src/main/resources/application-local.properties
git add src/main/resources/application-prod.properties
git add src/main/resources/application-stage.properties

git commit -m "Updating version # to $VERSION"

git tag $VERSION
