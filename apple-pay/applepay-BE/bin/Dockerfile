#
# IPPAY Merchant Portal backend Spring Boot API
#
# Build:
#   docker build -t ippay/merchant-portal-backend --build-arg GIT_COMMIT=$(git log -1 --format=%h) .
#
# Verify Git Commit:
#   docker inspect ippay/merchant-portal-backend | jq '.[].Config.Labels.GIT_COMMIT'
#
# Debug build:
#   docker build --target bulid -t ippay/merchant-portal-backend .
#   docker run --rm -it --entrypoint sh ippay/merchant-portal-backend
#
#   docker run --rm -it --entrypoint sh maven:3.8.1-openjdk-15
#

# STAGE 1: Build
FROM maven:3.8.1-openjdk-15 AS bulid
LABEL COMPANY="IPPAY"
LABEL MAINTAINER="<EMAIL>"
LABEL APPLICATION="IPPAY Merchant Portal backend"

# Tag Docker Images with Git Commit Information
ARG GIT_COMMIT
LABEL GIT_COMMIT=$GIT_COMMIT

WORKDIR /usr/src/app

# Copy the Project Object Model file
COPY ./pom.xml ./pom.xml

# Copy local client jar
COPY ./src/main/resources/libraries/ippay-client.jar /usr/src/app/src/main/resources/libraries/ippay-client.jar

# Install ippay-client.jar into local Maven repository
# https://stackoverflow.com/questions/4955635/how-to-add-local-jar-files-to-a-maven-project
RUN mvn install:install-file \
    -Dfile=/usr/src/app/src/main/resources/libraries/ippay-client.jar \
    -Dsources=/usr/src/app/src/main/resources/libraries/ippay-client.jar \
    -Djavadoc=/usr/src/app/src/main/resources/libraries/ippay-client.jar \
    -DgroupId=clients \
    -DartifactId=ippay-client \
    -Dversion=1.0 \
    -Dpackaging=jar

# Fetch all dependencies
RUN mvn dependency:go-offline -B

# Copy project files
COPY . /usr/src/app

# build for release
RUN mvn package

# STAGE 2: Deployment
FROM openjdk:15-jdk-slim

LABEL COMPANY="IPPAY"
LABEL MAINTAINER="<EMAIL>"
LABEL APPLICATION="IPPAY Merchant Portal backend"

# Tag Docker Images with Git Commit Information
ARG GIT_COMMIT
LABEL GIT_COMMIT=$GIT_COMMIT

WORKDIR /usr/local/app

# Run the application as a non-root user:
#RUN addgroup -S spring && adduser -S spring -G spring
RUN addgroup --system --gid 1001 spring && adduser --system --uid 1001 --group spring
USER spring:spring

COPY --from=bulid /usr/src/app/target/ippay-backend-1.0.0.jar /usr/local/app/ippay-app.jar

# ADD tzdata that allow to set timezone using environment variable TZ
#RUN apk add --no-cache --virtual tzdata

EXPOSE 8080 8443
ENTRYPOINT ["java", "-jar", "ippay-app.jar"]