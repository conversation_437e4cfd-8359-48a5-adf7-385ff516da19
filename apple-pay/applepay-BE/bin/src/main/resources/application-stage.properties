
frontend.specific.host=https://ippay-stage.osdb.io

# springdoc
springdoc.server.url=https://api-ippay-stage.osdb.io/

# primary datasource
db.primary.host=**************:3307
db.primary.database=ippay
db.primary.username=ippay
db.primary.password=uRzH6C5EBBFH15Ct

# secondary datasource
db.secondary.host=**************:3309
db.secondary.database=ippay
db.secondary.username=ippay
db.secondary.password=aOZjgd6r6MlRX8dO

# security (access-token = 45 min, reset-token = 90 min, password = 90 days)
security.jwt.access-token.secret-key=D6auJtKEPgA24V8Q9h45kTdZfV4cVY2c
security.jwt.access-token.expire-length=2700000
security.jwt.reset-token.secret-key=aAcWuLkM8DyQTfr1FJqH1nbFoMW7WnQj
security.jwt.reset-token.expire-length=5400000
security.password.expire-length=90

# cors / cookies configs
allowed.domain=osdb.io
allowed.origin=${frontend.specific.host}

# sendgrid
spring.sendgrid.api-key=*********************************************************************
mail.from.email=<EMAIL>

# transaction service
ippay.trans.service.url=https://testgtwy.ippay.com/ippay
ippay.trans.service.connection.timeout=120
ippay.trans.service.response.timeout=120


app.version=$VERSION
