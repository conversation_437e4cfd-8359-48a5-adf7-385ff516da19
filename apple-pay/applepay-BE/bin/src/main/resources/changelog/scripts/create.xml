<databaseChangeLog
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create-0001" author="OSDB">

        <!-- ======================= default_config ======================= -->
        <createTable tableName="default_config">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints primaryKey="true" unique="true" nullable="false"/>
            </column>
            <column name="type" type="ENUM(
                    'USERS_TABLE',
                    'CC_TRANSACTIONS_TABLE',
                    'ACH_TRANSACTIONS_TABLE',
                    'CC_SETTLEMENT_TABLE',
                    'ACH_SETTLEMENT_TABLE',
                    'MERCHANTS_TABLE',
                    'CHARGEBACKS_TABLE')">
                <constraints nullable="false"/>
            </column>
            <column name="configs" type="TEXT">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <!-- ======================= user ======================= -->
        <createTable tableName="user">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints primaryKey="true" unique="true" nullable="false"/>
            </column>
            <column name="email" type="VARCHAR(320)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="first_name" type="VARCHAR(256)">
                <constraints nullable="false"/>
            </column>
            <column name="last_name" type="VARCHAR(256)">
                <constraints nullable="false"/>
            </column>
            <column name="role" type="ENUM('ADMIN_ROLE','ADMIN_READ_ONLY_ROLE','MERCHANT_ADMIN_ROLE',
                        'MERCHANT_READ_ONLY_ROLE', 'MERCHANT_ADMIN_BLIND_REFUND_ROLE',
                        'MERCHANT_ADMIN_RECURRING_ROLE', 'ISO_READ_ONLY_ROLE', 'ISO_ADMIN')">
                <constraints nullable="false"/>
            </column>
            <column name="status" type="ENUM('ACTIVE', 'DISABLED', 'LOCKED', 'INVITED')">
                <constraints nullable="false"/>
            </column>
            <column name="password" type="text"/>
            <column name="password_last_changed" type="DATETIME" defaultValue="now()"/>
            <column name="otp" type="text"/>
            <column name="reset_password_hash" type="BINARY(36)"/>
            <column name="sessions_hash" type="BINARY(36)"/>
            <column name="partner_id" type="BIGINT"/>
            <column name="receive_daily_settlement" type="BOOLEAN" defaultValue="false"/>
            <column name="receive_monthly_settlement" type="BOOLEAN" defaultValue="false"/>
            <column name="receive_echeck_reject_notices" type="BOOLEAN" defaultValue="false"/>
            <column name="receive_chargebacks_notices" type="BOOLEAN" defaultValue="false"/>
            <column name="last_login" type="TIMESTAMP"/>
            <column name="total_failed_logins" type="INT" defaultValue="0"/>
            <column name="last_session_failed_logins" type="INT" defaultValue="0"/>
            <column name="last_session_ip_address" type="VARCHAR(64)"/>
        </createTable>

        <!-- ======================= user_password ======================= -->
        <createTable tableName="user_password">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints primaryKey="true" unique="true" nullable="false"/>
            </column>
            <column name="user_id" type="BIGINT">
                <constraints foreignKeyName="fk_user_password_user_id"
                             references="user(id)"
                             deleteCascade="true"
                             nullable="false"/>
            </column>
            <column name="password" type="text">
                <constraints nullable="false"/>
            </column>
            <column name="created_date" type="DATETIME" defaultValue="now()">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <!-- ======================= user_config ======================= -->
        <createTable tableName="user_config">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints primaryKey="true" unique="true" nullable="false"/>
            </column>
            <column name="type" type="ENUM('USERS_TABLE', 'CC_TRANSACTIONS_TABLE', 'ACH_TRANSACTIONS_TABLE', 'CC_SETTLEMENT_TABLE', 'ACH_SETTLEMENT_TABLE', 'MERCHANTS_TABLE', 'CHARGEBACKS_TABLE')">
                <constraints nullable="false"/>
            </column>
            <column name="user_id" type="BIGINT">
                <constraints foreignKeyName="fk_user_config_user_id"
                             references="user(id)"
                             deleteCascade="true"
                             nullable="false"/>
            </column>
            <column name="configs" type="TEXT">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <addUniqueConstraint
                columnNames="user_id, type"
                constraintName="unq_user_config_user_id_type"
                tableName="user_config" />

        <!-- ======================= user_merchant_ref ======================= -->
        <createTable tableName="user_merchant_ref">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints primaryKey="true" unique="true" nullable="false"/>
            </column>
            <column name="user_id" type="BIGINT">
                <constraints foreignKeyName="fk_user_merchant_ref_user_id"
                             references="user(id)"
                             deleteCascade="true"
                             nullable="false"/>
            </column>
            <column name="merchant_id" type="VARCHAR(64)">
                <constraints nullable="false" />
            </column>
        </createTable>

    </changeSet>

</databaseChangeLog>
