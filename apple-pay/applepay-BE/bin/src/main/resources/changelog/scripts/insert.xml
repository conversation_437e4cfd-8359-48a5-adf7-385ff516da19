<databaseChangeLog
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="insert-0001" author="OSDB">

        <!-- ======================= user ======================= -->
        <insert tableName="user">
            <column name="id">1</column>
            <column name="email"><EMAIL></column>
            <!-- password = O5bmgYy@ -->
            <column name="password">$2a$12$y.1Fjxomv8wa/sliR.ihPu0fN5cRdG1SDy1tq5rCA8w57ClaFOwBC</column>
            <column name="first_name">Davyd</column>
            <column name="last_name">Be<PERSON><PERSON>an</column>
            <column name="role">ADMIN_ROLE</column>
            <column name="status">ACTIVE</column>
        </insert>
        <insert tableName="user">
            <column name="id">2</column>
            <column name="email"><EMAIL></column>
            <!-- password = O5bmgYy@ -->
            <column name="password">$2a$12$y.1Fjxomv8wa/sliR.ihPu0fN5cRdG1SDy1tq5rCA8w57ClaFOwBC</column>
            <column name="first_name">Elena</column>
            <column name="last_name">Bondar</column>
            <column name="role">ADMIN_ROLE</column>
            <column name="status">ACTIVE</column>
        </insert>
        <insert tableName="user">
            <column name="id">3</column>
            <column name="email"><EMAIL></column>
            <!-- password = O5bmgYy@ -->
            <column name="password">$2a$12$y.1Fjxomv8wa/sliR.ihPu0fN5cRdG1SDy1tq5rCA8w57ClaFOwBC</column>
            <column name="first_name">Volodymyr</column>
            <column name="last_name">Molchanov</column>
            <column name="role">ADMIN_ROLE</column>
            <column name="status">ACTIVE</column>
        </insert>
        <insert tableName="user">
            <column name="id">4</column>
            <column name="email"><EMAIL></column>
            <!-- password = O5bmgYy@ -->
            <column name="password">$2a$12$y.1Fjxomv8wa/sliR.ihPu0fN5cRdG1SDy1tq5rCA8w57ClaFOwBC</column>
            <column name="first_name">Oleksii</column>
            <column name="last_name">Shnyra</column>
            <column name="role">ADMIN_ROLE</column>
            <column name="status">ACTIVE</column>
        </insert>
        <insert tableName="user">
            <column name="id">5</column>
            <column name="email"><EMAIL></column>
            <!-- password = O5bmgYy@ -->
            <column name="password">$2a$12$y.1Fjxomv8wa/sliR.ihPu0fN5cRdG1SDy1tq5rCA8w57ClaFOwBC</column>
            <column name="first_name">Steve</column>
            <column name="last_name">Sulatycki</column>
            <column name="role">ADMIN_ROLE</column>
            <column name="status">ACTIVE</column>
        </insert>
        <insert tableName="user">
            <column name="id">6</column>
            <column name="email"><EMAIL></column>
            <!-- password = Sunday!2 -->
            <column name="password">$2a$12$FXwK4sYszVIE5r74Jb9q3OtadpshqyLMpvqmNsCc4cp5vcllfz3W6</column>
            <column name="first_name">Elizabeth</column>
            <column name="last_name">Suwanski</column>
            <column name="role">ADMIN_ROLE</column>
            <column name="status">ACTIVE</column>
        </insert>
        <insert tableName="user">
            <column name="id">7</column>
            <column name="email"><EMAIL></column>
            <!-- password = Sunday!2 -->
            <column name="password">$2a$12$FXwK4sYszVIE5r74Jb9q3OtadpshqyLMpvqmNsCc4cp5vcllfz3W6</column>
            <column name="first_name">Elizabeth</column>
            <column name="last_name">Suwanski</column>
            <column name="role">ADMIN_ROLE</column>
            <column name="status">ACTIVE</column>
        </insert>
        <insert tableName="user">
            <column name="id">8</column>
            <column name="email"><EMAIL></column>
            <!-- password = Sunday!2 -->
            <column name="password">$2a$12$FXwK4sYszVIE5r74Jb9q3OtadpshqyLMpvqmNsCc4cp5vcllfz3W6</column>
            <column name="first_name">Elizabeth</column>
            <column name="last_name">Suwanski</column>
            <column name="role">MERCHANT_READ_ONLY_ROLE</column>
            <column name="status">ACTIVE</column>
            <column name="partner_id">1</column>
        </insert>
        <insert tableName="user">
            <column name="id">9</column>
            <column name="email"><EMAIL></column>
            <!-- password = O5bmgYy@ -->
            <column name="password">$2a$12$y.1Fjxomv8wa/sliR.ihPu0fN5cRdG1SDy1tq5rCA8w57ClaFOwBC</column>
            <column name="first_name">Thaminda</column>
            <column name="last_name">Thaminda</column>
            <column name="role">ADMIN_ROLE</column>
            <column name="status">ACTIVE</column>
        </insert>
        <insert tableName="user">
            <column name="id">10</column>
            <column name="email"><EMAIL></column>
            <!-- password = O5bmgYy@ -->
            <column name="password">$2a$12$y.1Fjxomv8wa/sliR.ihPu0fN5cRdG1SDy1tq5rCA8w57ClaFOwBC</column>
            <column name="first_name">Brendon</column>
            <column name="last_name">Brendon</column>
            <column name="role">ADMIN_ROLE</column>
            <column name="status">ACTIVE</column>
        </insert>
        <insert tableName="user">
            <column name="id">11</column>
            <column name="email" value="<EMAIL>"/>
            <!-- password = O5bmgYy@ -->
            <column name="password">$2a$12$y.1Fjxomv8wa/sliR.ihPu0fN5cRdG1SDy1tq5rCA8w57ClaFOwBC</column>
            <column name="first_name">Kateryna</column>
            <column name="last_name">Skibina</column>
            <column name="role">ADMIN_ROLE</column>
            <column name="status">ACTIVE</column>
        </insert>
        <insert tableName="user">
            <column name="id">12</column>
            <column name="email"><EMAIL></column>
            <!-- password = O5bmgYy@ -->
            <column name="password">$2a$12$y.1Fjxomv8wa/sliR.ihPu0fN5cRdG1SDy1tq5rCA8w57ClaFOwBC</column>
            <column name="first_name">Kianna</column>
            <column name="last_name">Williams</column>
            <column name="role">ISO_READ_ONLY_ROLE</column>
            <column name="status">ACTIVE</column>
            <column name="partner_id">4</column>
        </insert>
        <insert tableName="user">
            <column name="id">13</column>
            <column name="email"><EMAIL></column>
            <!-- password = O5bmgYy@ -->
            <column name="password">$2a$12$y.1Fjxomv8wa/sliR.ihPu0fN5cRdG1SDy1tq5rCA8w57ClaFOwBC</column>
            <column name="first_name">Camille</column>
            <column name="last_name">Harris</column>
            <column name="role">MERCHANT_ADMIN_RECURRING_ROLE</column>
            <column name="status">ACTIVE</column>
            <column name="partner_id">4</column>
        </insert>
        <insert tableName="user">
            <column name="id">14</column>
            <column name="email"><EMAIL></column>
            <!-- password = O5bmgYy@ -->
            <column name="password">$2a$12$y.1Fjxomv8wa/sliR.ihPu0fN5cRdG1SDy1tq5rCA8w57ClaFOwBC</column>
            <column name="first_name">Mark</column>
            <column name="last_name">Miller</column>
            <column name="role">MERCHANT_ADMIN_ROLE</column>
            <column name="status">ACTIVE</column>
            <column name="partner_id">5</column>
        </insert>
        <insert tableName="user">
            <column name="id">15</column>
            <column name="email"><EMAIL></column>
            <!-- password = O5bmgYy@ -->
            <column name="password">$2a$12$y.1Fjxomv8wa/sliR.ihPu0fN5cRdG1SDy1tq5rCA8w57ClaFOwBC</column>
            <column name="first_name">David</column>
            <column name="last_name">Martin</column>
            <column name="role">ISO_READ_ONLY_ROLE</column>
            <column name="status">ACTIVE</column>
            <column name="partner_id">5</column>
        </insert>
        <insert tableName="user">
            <column name="id">16</column>
            <column name="email"><EMAIL></column>
            <!-- password = O5bmgYy@ -->
            <column name="password">$2a$12$y.1Fjxomv8wa/sliR.ihPu0fN5cRdG1SDy1tq5rCA8w57ClaFOwBC</column>
            <column name="first_name">Alexandr</column>
            <column name="last_name">Voloday</column>
            <column name="role">ADMIN_ROLE</column>
            <column name="status">ACTIVE</column>
        </insert>
        <insert tableName="user">
            <column name="id">17</column>
            <column name="email"><EMAIL></column>
            <!-- password = O5bmgYy@ -->
            <column name="password">$2a$12$y.1Fjxomv8wa/sliR.ihPu0fN5cRdG1SDy1tq5rCA8w57ClaFOwBC</column>
            <column name="first_name">Vladislav</column>
            <column name="last_name">Musiichuk</column>
            <column name="role">ADMIN_ROLE</column>
            <column name="status">ACTIVE</column>
        </insert>

        <!-- ======================= user_password ======================= -->
        <insert tableName="user_password">
            <column name="id">1</column>
            <column name="user_id">1</column>
            <column name="password">$2a$12$y.1Fjxomv8wa/sliR.ihPu0fN5cRdG1SDy1tq5rCA8w57ClaFOwBC</column>
            <column name="created_date" valueDate="now()"/>
        </insert>
        <insert tableName="user_password">
            <column name="id">2</column>
            <column name="user_id">2</column>
            <column name="password">$2a$12$y.1Fjxomv8wa/sliR.ihPu0fN5cRdG1SDy1tq5rCA8w57ClaFOwBC</column>
            <column name="created_date" valueDate="now()"/>
        </insert>
        <insert tableName="user_password">
            <column name="id">3</column>
            <column name="user_id">3</column>
            <column name="password">$2a$12$y.1Fjxomv8wa/sliR.ihPu0fN5cRdG1SDy1tq5rCA8w57ClaFOwBC</column>
            <column name="created_date" valueDate="now()"/>
        </insert>
        <insert tableName="user_password">
            <column name="id">4</column>
            <column name="user_id">4</column>
            <column name="password">$2a$12$y.1Fjxomv8wa/sliR.ihPu0fN5cRdG1SDy1tq5rCA8w57ClaFOwBC</column>
            <column name="created_date" valueDate="now()"/>
        </insert>
        <insert tableName="user_password">
            <column name="id">5</column>
            <column name="user_id">5</column>
            <column name="password">$2a$12$y.1Fjxomv8wa/sliR.ihPu0fN5cRdG1SDy1tq5rCA8w57ClaFOwBC</column>
            <column name="created_date" valueDate="now()"/>
        </insert>
        <insert tableName="user_password">
            <column name="id">6</column>
            <column name="user_id">6</column>
            <column name="password">$2a$12$FXwK4sYszVIE5r74Jb9q3OtadpshqyLMpvqmNsCc4cp5vcllfz3W6</column>
            <column name="created_date" valueDate="now()"/>
        </insert>
        <insert tableName="user_password">
            <column name="id">7</column>
            <column name="user_id">7</column>
            <column name="password">$2a$12$FXwK4sYszVIE5r74Jb9q3OtadpshqyLMpvqmNsCc4cp5vcllfz3W6</column>
            <column name="created_date" valueDate="now()"/>
        </insert>
        <insert tableName="user_password">
            <column name="id">8</column>
            <column name="user_id">8</column>
            <column name="password">$2a$12$FXwK4sYszVIE5r74Jb9q3OtadpshqyLMpvqmNsCc4cp5vcllfz3W6</column>
            <column name="created_date" valueDate="now()"/>
        </insert>
        <insert tableName="user_password">
            <column name="id">9</column>
            <column name="user_id">9</column>
            <column name="password">$2a$12$y.1Fjxomv8wa/sliR.ihPu0fN5cRdG1SDy1tq5rCA8w57ClaFOwBC</column>
            <column name="created_date" valueDate="now()"/>
        </insert>
        <insert tableName="user_password">
            <column name="id">10</column>
            <column name="user_id">10</column>
            <column name="password">$2a$12$y.1Fjxomv8wa/sliR.ihPu0fN5cRdG1SDy1tq5rCA8w57ClaFOwBC</column>
            <column name="created_date" valueDate="now()"/>
        </insert>
        <insert tableName="user_password">
            <column name="id">11</column>
            <column name="user_id">11</column>
            <column name="password">$2a$12$y.1Fjxomv8wa/sliR.ihPu0fN5cRdG1SDy1tq5rCA8w57ClaFOwBC</column>
            <column name="created_date" valueDate="now()"/>
        </insert>
        <insert tableName="user_password">
            <column name="id">12</column>
            <column name="user_id">12</column>
            <column name="password">$2a$12$y.1Fjxomv8wa/sliR.ihPu0fN5cRdG1SDy1tq5rCA8w57ClaFOwBC</column>
            <column name="created_date" valueDate="now()"/>
        </insert>
        <insert tableName="user_password">
            <column name="id">13</column>
            <column name="user_id">13</column>
            <column name="password">$2a$12$y.1Fjxomv8wa/sliR.ihPu0fN5cRdG1SDy1tq5rCA8w57ClaFOwBC</column>
            <column name="created_date" valueDate="now()"/>
        </insert>
        <insert tableName="user_password">
            <column name="id">14</column>
            <column name="user_id">14</column>
            <column name="password">$2a$12$y.1Fjxomv8wa/sliR.ihPu0fN5cRdG1SDy1tq5rCA8w57ClaFOwBC</column>
            <column name="created_date" valueDate="now()"/>
        </insert>
        <insert tableName="user_password">
            <column name="id">15</column>
            <column name="user_id">15</column>
            <column name="password">$2a$12$y.1Fjxomv8wa/sliR.ihPu0fN5cRdG1SDy1tq5rCA8w57ClaFOwBC</column>
            <column name="created_date" valueDate="now()"/>
        </insert>
        <insert tableName="user_password">
            <column name="id">16</column>
            <column name="user_id">16</column>
            <column name="password">$2a$12$y.1Fjxomv8wa/sliR.ihPu0fN5cRdG1SDy1tq5rCA8w57ClaFOwBC</column>
            <column name="created_date" valueDate="now()"/>
        </insert>
        <insert tableName="user_password">
            <column name="id">17</column>
            <column name="user_id">17</column>
            <column name="password">$2a$12$y.1Fjxomv8wa/sliR.ihPu0fN5cRdG1SDy1tq5rCA8w57ClaFOwBC</column>
            <column name="created_date" valueDate="now()"/>
        </insert>

        <!-- ======================= default_config ======================= -->
        <insert tableName="default_config">
            <column name="id">1</column>
            <column name="type">USERS_TABLE</column>
            <column name="configs">{
                "columns" : [ {
                "field" : "lastName",
                "headerName" : "Name",
                "width" : 185,
                "hide" : false
                }, {
                "field" : "email",
                "headerName" : "Login ID",
                "width" : 225,
                "hide" : false
                }, {
                "field" : "role",
                "headerName" : "Roles",
                "width" : 200,
                "hide" : false
                }, {
                "field" : "partner",
                "headerName" : "Partner",
                "width" : 121,
                "hide" : false
                }, {
                "field" : "merchantsAssigned",
                "headerName" : "Merchants Assigned",
                "width" : 182,
                "hide" : false
                }, {
                "field" : "lastLogin",
                "headerName" : "Last Login",
                "width" : 156,
                "hide" : false
                }, {
                "field" : "lastSessionIpAddress",
                "headerName" : "IP Address",
                "width" : 124,
                "hide" : false
                }, {
                "field" : "totalFailedLogins",
                "headerName" : "Failed Logins",
                "width" : 138,
                "hide" : false
                }, {
                "field" : "status",
                "headerName" : "Current Status",
                "width" : 147,
                "hide" : false
                }, {
                "field" : "actions",
                "headerName" : "Actions",
                "width" : 79,
                "hide" : false
                } ],
                "density" : "standard"
                }</column>
        </insert>
        <insert tableName="default_config">
            <column name="id">2</column>
            <column name="type">CC_TRANSACTIONS_TABLE</column>
            <column name="configs">{
                "columns" : [ {
                "field" : "approvalStatus",
                "headerName" : "Approval Status",
                "width" : 154,
                "hide" : false
                }, {
                "field" : "merchantName",
                "headerName" : "Merchant Name",
                "width" : 156,
                "hide" : false
                }, {
                "field" : "terminalId",
                "headerName" : "Terminal ID",
                "width" : 126,
                "hide" : false
                }, {
                "field" : "requestType",
                "headerName" : "Request Type",
                "width" : 142,
                "hide" : false
                }, {
                "field" : "cardType",
                "headerName" : "Card",
                "width" : 85,
                "hide" : false
                }, {
                "field" : "transactionId",
                "headerName" : "Transaction ID",
                "width" : 177,
                "hide" : false
                }, {
                "field" : "cardNumber",
                "headerName" : "Card Number",
                "width" : 149,
                "hide" : false
                }, {
                "field" : "cardHolder",
                "headerName" : "Cardholder",
                "width" : 128,
                "hide" : false
                }, {
                "field" : "authAmount",
                "headerName" : "Amount",
                "width" : 104,
                "hide" : false
                }, {
                "field" : "authCode",
                "headerName" : "Auth Code",
                "width" : 123,
                "hide" : false
                }, {
                "field" : "ud1",
                "headerName" : "UD1",
                "width" : 90,
                "hide" : false
                }, {
                "field" : "ud2",
                "headerName" : "UD2",
                "width" : 85,
                "hide" : false
                }, {
                "field" : "ud3",
                "headerName" : "UD3",
                "width" : 87,
                "hide" : false
                }, {
                "field" : "avs",
                "headerName" : "AVS",
                "width" : 83,
                "hide" : false
                }, {
                "field" : "cvv",
                "headerName" : "CVV",
                "width" : 89,
                "hide" : false
                }, {
                "field" : "authDate",
                "headerName" : "Auth Date",
                "width" : 156,
                "hide" : false
                } ],
                "density" : "standard"
                }</column>
        </insert>
        <insert tableName="default_config">
            <column name="id">3</column>
            <column name="type">ACH_TRANSACTIONS_TABLE</column>
            <column name="configs">{
                "columns" : [ {
                "field" : "status",
                "headerName" : "Status",
                "width" : 155,
                "hide" : false
                }, {
                "field" : "merchantName",
                "headerName" : "Merchant Name",
                "width" : 155,
                "hide" : false
                }, {
                "field" : "terminalId",
                "headerName" : "Terminal ID",
                "width" : 127,
                "hide" : false
                }, {
                "field" : "requestType",
                "headerName" : "Request Type",
                "width" : 140,
                "hide" : false
                }, {
                "field" : "transactionId",
                "headerName" : "Transaction ID",
                "width" : 175,
                "hide" : false
                }, {
                "field" : "accountNumber",
                "headerName" : "Account Number",
                "width" : 161,
                "hide" : false
                }, {
                "field" : "cardHolder",
                "headerName" : "Account Holder",
                "width" : 153,
                "hide" : false
                }, {
                "field" : "amount",
                "headerName" : "Amount",
                "width" : 104,
                "hide" : false
                }, {
                "field" : "ud1",
                "headerName" : "UD1",
                "width" : 85,
                "hide" : false
                }, {
                "field" : "ud2",
                "headerName" : "UD2",
                "width" : 85,
                "hide" : false
                }, {
                "field" : "ud3",
                "headerName" : "UD3",
                "width" : 85,
                "hide" : false
                }, {
                "field" : "authDate",
                "headerName" : "Auth Date",
                "width" : 160,
                "hide" : false
                }, {
                "field" : "achProcessor",
                "headerName" : "Processor",
                "width" : 118,
                "hide" : false
                } ],
                "density" : "standard"
                }</column>
        </insert>
        <insert tableName="default_config">
            <column name="id">4</column>
            <column name="type">CC_SETTLEMENT_TABLE</column>
            <column name="configs">{
                "columns" : [ {
                "field" : "merchantName",
                "headerName" : "Merchant Name",
                "width" : 157,
                "hide" : false
                }, {
                "field" : "terminalId",
                "headerName" : "Terminal ID",
                "width" : 127,
                "hide" : false
                }, {
                "field" : "requestType",
                "headerName" : "Request Type",
                "width" : 141,
                "hide" : false
                }, {
                "field" : "cardType",
                "headerName" : "Card",
                "width" : 86,
                "hide" : false
                }, {
                "field" : "transactionId",
                "headerName" : "Transaction ID",
                "width" : 184,
                "hide" : false
                }, {
                "field" : "cardNumber",
                "headerName" : "Card Number",
                "width" : 156,
                "hide" : false
                }, {
                "field" : "cardHolder",
                "headerName" : "Cardholder",
                "width" : 147,
                "hide" : false
                }, {
                "field" : "authAmount",
                "headerName" : "Amount",
                "width" : 104,
                "hide" : false
                }, {
                "field" : "authCode",
                "headerName" : "Auth Code",
                "width" : 121,
                "hide" : false
                }, {
                "field" : "ud1",
                "headerName" : "UD1",
                "width" : 85,
                "hide" : false
                }, {
                "field" : "ud2",
                "headerName" : "UD2",
                "width" : 85,
                "hide" : false
                }, {
                "field" : "ud3",
                "headerName" : "UD3",
                "width" : 85,
                "hide" : false
                }, {
                "field" : "avs",
                "headerName" : "AVS",
                "width" : 85,
                "hide" : false
                }, {
                "field" : "cvv",
                "headerName" : "CVV",
                "width" : 91,
                "hide" : false
                }, {
                "field" : "authDate",
                "headerName" : "Auth Date",
                "width" : 160,
                "hide" : false
                }, {
                "field" : "settlementDate",
                "headerName" : "Settle Date",
                "width" : 130,
                "hide" : false
                } ],
                "density" : "standard"
                }</column>
        </insert>
        <insert tableName="default_config">
            <column name="id">5</column>
            <column name="type">ACH_SETTLEMENT_TABLE</column>
            <column name="configs">{
                "columns" : [ {
                "field" : "merchantName",
                "headerName" : "Merchant Name",
                "width" : 155,
                "hide" : false
                }, {
                "field" : "terminalId",
                "headerName" : "Terminal ID",
                "width" : 127,
                "hide" : false
                }, {
                "field" : "requestType",
                "headerName" : "Request Type",
                "width" : 141,
                "hide" : false
                }, {
                "field" : "transactionId",
                "headerName" : "Transaction ID",
                "width" : 175,
                "hide" : false
                }, {
                "field" : "accountNumber",
                "headerName" : "Account Number",
                "width" : 160,
                "hide" : false
                }, {
                "field" : "cardHolder",
                "headerName" : "Account Holder",
                "width" : 155,
                "hide" : false
                }, {
                "field" : "status",
                "headerName" : "Status",
                "width" : 155,
                "hide" : false
                }, {
                "field" : "type",
                "headerName" : "Type",
                "width" : 150,
                "hide" : false
                }, {
                "field" : "amount",
                "headerName" : "Amount",
                "width" : 105,
                "hide" : false
                }, {
                "field" : "ud1",
                "headerName" : "UD1",
                "width" : 85,
                "hide" : false
                }, {
                "field" : "ud2",
                "headerName" : "UD2",
                "width" : 85,
                "hide" : false
                }, {
                "field" : "ud3",
                "headerName" : "UD3",
                "width" : 85,
                "hide" : false
                }, {
                "field" : "authDate",
                "headerName" : "Auth Date",
                "width" : 160,
                "hide" : false
                }, {
                "field" : "settlementDate",
                "headerName" : "Settle Date",
                "width" : 130,
                "hide" : false
                }, {
                "field" : "fundingDisposition",
                "headerName" : "Funding",
                "width" : 120,
                "hide" : false
                }, {
                "field" : "achProcessor",
                "headerName" : "Processor",
                "width" : 120,
                "hide" : false
                } ],
                "density" : "standard"
                }</column>
        </insert>
        <insert tableName="default_config">
            <column name="id">6</column>
            <column name="type">MERCHANTS_TABLE</column>
            <column name="configs">{
                "columns" : [ {
                "field" : "merchantId",
                "headerName" : "AuthMID",
                "width" : 134,
                "hide" : false
                }, {
                "field" : "merchantName",
                "headerName" : "Merchant Name",
                "width" : 154,
                "hide" : false
                }, {
                "field" : "bank",
                "headerName" : "Bank",
                "width" : 140,
                "hide" : false
                }, {
                "field" : "jetpayMID",
                "headerName" : "MID",
                "width" : 149,
                "hide" : false
                }, {
                "field" : "achMID",
                "headerName" : "ACH MID",
                "width" : 146,
                "hide" : false
                }, {
                "field" : "tids",
                "headerName" : "TIDs",
                "width" : 220,
                "hide" : false
                }, {
                "field" : "partner",
                "headerName" : "Partner",
                "width" : 122,
                "hide" : false
                }, {
                "field" : "isActive",
                "headerName" : "Status",
                "width" : 96,
                "hide" : false
                }, {
                "field" : "usersNumber",
                "headerName" : "Users",
                "width" : 72,
                "hide" : false
                } ],
                "density" : "standard"
                }</column>
        </insert>
        <insert tableName="default_config">
            <column name="id">7</column>
            <column name="type">CHARGEBACKS_TABLE</column>
            <column name="configs">{
                "columns": [{
                "field": "merchantName",
                "headerName": "Merchant Name",
                "width": 156,
                "hide": false
                }, {
                "field": "merchantId",
                "headerName": "Merchant ID",
                "width": 156,
                "hide": false
                }, {
                "field": "cardType",
                "headerName": "Card",
                "width": 90,
                "hide": false
                }, {
                "field": "caseNumber",
                "headerName": "Case Number",
                "width": 150,
                "hide": false
                }, {
                "field": "creditCard",
                "headerName": "Credit Card",
                "width": 150,
                "hide": false
                }, {
                "field": "amount",
                "headerName": "Amount",
                "width": 130,
                "hide": false
                }, {
                "field": "reason",
                "headerName": "Reason",
                "width": 170,
                "hide": false
                }, {
                "field": "resolutionTo",
                "headerName": "ResolutionTo",
                "width": 140,
                "hide": false
                }, {
                "field": "debitCredit",
                "headerName": "Debit/Credit",
                "width": 140,
                "hide": false
                }, {
                "field": "type",
                "headerName": "Type",
                "width": 120,
                "hide": false
                },
                {
                "field": "originRef",
                "headerName": "originRef#",
                "width": 120,
                "hide": false
                },
                {
                "field": "dateTransaction",
                "headerName": "Initial",
                "width": 156,
                "hide": false
                },
                {
                "field": "dateResolved",
                "headerName": "Resolved",
                "width": 156,
                "hide": false
                }
                ],
                "density": "standard"
                }</column>
        </insert>

        <!-- ======================= user_merchant_ref ======================= -->
        <insert tableName="user_merchant_ref">
            <column name="id">1</column>
            <column name="user_id">8</column>
            <column name="merchant_id">MID00000000001</column>
        </insert>
        <insert tableName="user_merchant_ref">
            <column name="id">2</column>
            <column name="user_id">8</column>
            <column name="merchant_id">MID00000000002</column>
        </insert>
        <insert tableName="user_merchant_ref">
            <column name="id">3</column>
            <column name="user_id">8</column>
            <column name="merchant_id">MID00000000003</column>
        </insert>
        <insert tableName="user_merchant_ref">
            <column name="id">4</column>
            <column name="user_id">8</column>
            <column name="merchant_id">MID00000000004</column>
        </insert>
        <insert tableName="user_merchant_ref">
            <column name="id">5</column>
            <column name="user_id">8</column>
            <column name="merchant_id">MID00000000005</column>
        </insert>
        <insert tableName="user_merchant_ref">
            <column name="id">6</column>
            <column name="user_id">12</column>
            <column name="merchant_id">MID00000000001</column>
        </insert>
        <insert tableName="user_merchant_ref">
            <column name="id">7</column>
            <column name="user_id">12</column>
            <column name="merchant_id">MID00000000002</column>
        </insert>
        <insert tableName="user_merchant_ref">
            <column name="id">8</column>
            <column name="user_id">12</column>
            <column name="merchant_id">MID00000000005</column>
        </insert>
        <insert tableName="user_merchant_ref">
            <column name="id">9</column>
            <column name="user_id">12</column>
            <column name="merchant_id">MID00000000006</column>
        </insert>
        <insert tableName="user_merchant_ref">
            <column name="id">10</column>
            <column name="user_id">12</column>
            <column name="merchant_id">MID00000000007</column>
        </insert>
        <insert tableName="user_merchant_ref">
            <column name="id">11</column>
            <column name="user_id">13</column>
            <column name="merchant_id">MID00000000001</column>
        </insert>
        <insert tableName="user_merchant_ref">
            <column name="id">12</column>
            <column name="user_id">13</column>
            <column name="merchant_id">MID00000000002</column>
        </insert>
    </changeSet>

</databaseChangeLog>
