CREATE TABLE BANK_LOOKUP (
                             id int(5) UNSIGNED NOT NULL AUTO_INCREMENT,
                             bin char(4) COLLATE utf8_bin NOT NULL ,
                             bank_name VARCHAR(30) COLLATE utf8_bin NOT NULL ,
                             date_created datetime DEFAULT current_timestamp(),
                             date_modified datetime DEFAULT NULL ON UPDATE current_timestamp(),
                             PRIMARY KEY(id),
                             UNIQUE KEY (bin)
) ENGINE=InnoDB;

CREATE TABLE BANK_MID_TID_MAP (
                                  id int(5) UNSIGNED NOT NULL AUTO_INCREMENT,
                                  bank_id_fk int(5) UNSIGNED NOT NULL ,
                                  mcc char(4) NOT NULL ,
                                  mid VARCHAR(20) NOT NULL ,
                                  tid VARCHAR(20) NOT NULL ,
                                  date_created datetime DEFAULT current_timestamp(),
                                  date_modified datetime DEFAULT NULL ON UPDATE current_timestamp(),
                                  PRIMARY KEY(id),
                                  <PERSON>OR<PERSON><PERSON><PERSON>E<PERSON> (bank_id_fk) REFERENCES BANK_LOOKUP(id),
                                  UNIQUE KEY idx_map (bank_id_fk, mcc, mid, tid),
                                  KEY idx_tid (tid),
                                  KEY idx_mid (mid)
) ENGINE=InnoDB;

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
SET time_zone = "+00:00";

DROP TABLE IF EXISTS `ACHTransactions`;
CREATE TABLE IF NOT EXISTS `ACHTransactions` (
                                                 `ID` BIGINT(20) NOT NULL ,
                                                 `IPTransID` BIGINT(20) NOT NULL ,
                                                 `MerchantName` VARCHAR(50) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                 `MerchantID` VARCHAR(25) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL ,
                                                 `TerminalID` VARCHAR(25) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL ,
                                                 `ProcessorID` int(11) DEFAULT NULL,
                                                 `AuthCode` VARCHAR(10) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                 `ActionCode` VARCHAR(3) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                 `RequestType` VARCHAR(10) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                 `TransactionType` VARCHAR(3) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                 `TotalAmount` VARCHAR(11) DEFAULT NULL,
                                                 `TransDate` datetime DEFAULT NULL,
                                                 `AuthTransDate` datetime DEFAULT NULL,
                                                 `FeeAmount` int(11) DEFAULT NULL,
                                                 `ResponseText` VARCHAR(25) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                 `AccountNumber` VARCHAR(17) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                 `ABA` VARCHAR(17) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                 `CheckNumber` VARCHAR(10) DEFAULT NULL,
                                                 `SettleDate` datetime(6) DEFAULT NULL,
                                                 `CompletionDate` datetime(6) DEFAULT NULL,
                                                 `CompletionAmount` int(11) DEFAULT NULL,
                                                 `Status` VARCHAR(50) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                 `ReturnStatusFlag` VARCHAR(25) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                 `ImportItemID` VARCHAR(10) DEFAULT NULL,
                                                 `ImportAmount` VARCHAR(10) DEFAULT NULL,
                                                 `Token` VARCHAR(50) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                 `CardHolderName` VARCHAR(100) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                 `Address` VARCHAR(100) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                 `City` VARCHAR(25) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                 `State` VARCHAR(25) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                 `ZipCode` VARCHAR(10) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                 `Country` VARCHAR(3) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                 `Phone` VARCHAR(50) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                 `Email` VARCHAR(50) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                 `TransactionID` VARCHAR(50) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                 `OrderNumber` VARCHAR(25) DEFAULT NULL,
                                                 `CurrencyCode` VARCHAR(4) DEFAULT NULL,
                                                 `Invoice` VARCHAR(25) DEFAULT NULL,
                                                 `UserField1` VARCHAR(100) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                 `UserField2` VARCHAR(100) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                 `UserField3` VARCHAR(100) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                 `ACHProcessor` VARCHAR(25) DEFAULT NULL,
                                                 `CCProcessor` VARCHAR(25) DEFAULT NULL,
                                                 `DateAdded` datetime(6) DEFAULT CURRENT_TIMESTAMP(6),
                                                 `DateUpdated` datetime(6) DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)
) ENGINE=InnoDB AUTO_INCREMENT=10268614 DEFAULT CHARSET=latin1;

ALTER TABLE `ACHTransactions`
    ADD PRIMARY KEY(`ID`),
    ADD UNIQUE KEY `IPTransID` (`IPTransID`),
    ADD KEY `MerchantID_INX` (`MerchantID`) USING BTREE,
    ADD KEY `TransDate` (`TransDate`),
    ADD KEY `AuthTransDate` (`AuthTransDate`),
    ADD KEY `CardHolderName` (`CardHolderName`),
    ADD KEY `MerchantID` (`MerchantID`),
    ADD KEY `TransactionID` (`TransactionID`),
    ADD KEY `ACHProcessor` (`ACHProcessor`),
    ADD KEY `ImportItemID` (`ImportItemID`),
    ADD KEY `TerminalID` (`TerminalID`);

ALTER TABLE `ACHTransactions`
    MODIFY `ID` BIGINT(20) NOT NULL AUTO_INCREMENT,AUTO_INCREMENT=10268614;

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
SET time_zone = "+00:00";

DROP TABLE IF EXISTS `CCTransactions`;
CREATE TABLE IF NOT EXISTS `CCTransactions` (
                                                `ID` BIGINT(20) NOT NULL ,
                                                `IPTransID` BIGINT(20) NOT NULL ,
                                                `MerchantName` VARCHAR(50) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                `MerchantID` VARCHAR(25) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL ,
                                                `TerminalID` VARCHAR(25) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL ,
                                                `ProcessorID` int(11) DEFAULT NULL,
                                                `MerchantAltID` VARCHAR(11) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                `TerminalAltID` VARCHAR(12) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                `AuthCode` VARCHAR(10) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                `ActionCode` VARCHAR(3) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                `RequestType` VARCHAR(10) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                `TransactionType` VARCHAR(15) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                `TransAmount` int(11) DEFAULT NULL,
                                                `AuthTransDate` datetime DEFAULT NULL,
                                                `FeeAmount` int(11) DEFAULT NULL,
                                                `TaxAmount` int(11) DEFAULT NULL,
                                                `SettlementDate` datetime DEFAULT NULL,
                                                `SettlementAmount` int(11) DEFAULT NULL,
                                                `SettlementID` BIGINT(20) DEFAULT NULL,
                                                `ApprovalStatus` VARCHAR(25) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                `Token` VARCHAR(17) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                `CardBin` VARCHAR(6) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                `CardLastFour` VARCHAR(4) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                `CardType` VARCHAR(1) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                `CurrencyCode` VARCHAR(4) DEFAULT NULL,
                                                `ExpDate` int(11) DEFAULT NULL,
                                                `SafeCardNum` VARCHAR(20) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                `CardHolderName` VARCHAR(60) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                `Address` VARCHAR(50) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                `City` VARCHAR(25) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                `State` VARCHAR(25) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                `ZipCode` VARCHAR(10) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                `Country` VARCHAR(3) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                `Phone` VARCHAR(50) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                `Email` VARCHAR(50) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                `AVSResponse` VARCHAR(2) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                `CVVResponse` VARCHAR(2) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                `BankTransID` VARCHAR(10) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                `ExternalTransID` VARCHAR(18) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                `TransactionID` VARCHAR(50) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                `UserField1` VARCHAR(60) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                `UserField2` VARCHAR(60) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                `UserField3` VARCHAR(60) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                `POSTerminalID` VARCHAR(4) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                `POSAdditionalData` VARCHAR(12) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                `RetrievalReferenceNumber` BIGINT(12) DEFAULT NULL,
                                                `VNumber` VARCHAR(10) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                `OrderNumber` VARCHAR(25) DEFAULT NULL,
                                                `ResponseText` VARCHAR(25) DEFAULT NULL,
                                                `CCProcessor` VARCHAR(10) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                `ACHProcessor` VARCHAR(10) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                `ChargebackID` int(11) DEFAULT NULL,
                                                `CardNumber` VARCHAR(20) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                `ChargebackLoadDate` date DEFAULT NULL,
                                                `SecondRequestID` int(11) DEFAULT NULL,
                                                `ChargebackSecondRequest` date DEFAULT NULL,
                                                `DateAdded` datetime(6) DEFAULT CURRENT_TIMESTAMP(6),
                                                `DateUpdated` datetime(6) DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)
) ENGINE=InnoDB AUTO_INCREMENT=96233188 DEFAULT CHARSET=latin1;

ALTER TABLE `CCTransactions`
    ADD PRIMARY KEY(`ID`),
    ADD UNIQUE KEY `IPTransID` (`IPTransID`),
    ADD KEY `AuthCode` (`AuthCode`),
    ADD KEY `MerchantID_INX` (`MerchantID`) USING BTREE,
    ADD KEY `AuthTransDate` (`AuthTransDate`),
    ADD KEY `CardBin` (`CardBin`),
    ADD KEY `CardLastFour` (`CardLastFour`),
    ADD KEY `ID` (`ID`),
    ADD KEY `Token` (`Token`),
    ADD KEY `RequestType` (`RequestType`),
    ADD KEY `SafeCardNum` (`SafeCardNum`),
    ADD KEY `CardNumber` (`CardNumber`),
    ADD KEY `ChargebackLoadDate` (`ChargebackLoadDate`),
    ADD KEY `TerminalID` (`TerminalID`);

ALTER TABLE `CCTransactions`
    MODIFY `ID` BIGINT(20) NOT NULL AUTO_INCREMENT,AUTO_INCREMENT=96233188;

CREATE TABLE WHITELABELS (
                             id int(5) UNSIGNED NOT NULL AUTO_INCREMENT,
                             whitelabel_fqdn VARCHAR(128) COLLATE utf8_bin NOT NULL ,
                             whitelabel_logo VARCHAR(255) COLLATE utf8_bin NOT NULL ,
                             whitelabel_theme VARCHAR(255) COLLATE utf8_bin NOT NULL ,
                             created_by VARCHAR(64) COLLATE utf8_bin NULL,
                             updated_by VARCHAR(64) COLLATE utf8_bin NULL,
                             date_created datetime DEFAULT current_timestamp(),
                             date_modified datetime DEFAULT NULL ON UPDATE current_timestamp(),
                             PRIMARY KEY(id),
                             UNIQUE KEY (whitelabel_fqdn),
                             KEY created_by_idx (created_by),
                             KEY updated_by_idx (updated_by)
) ENGINE=InnoDB;

CREATE TABLE PARTNERS (
                          id int(5) UNSIGNED NOT NULL AUTO_INCREMENT,
                          partner_name VARCHAR(30) COLLATE utf8_bin NOT NULL ,
                          partner_desc VARCHAR(255) COLLATE utf8_bin NOT NULL ,
                          whitelabel_id_fk int(5) UNSIGNED NOT NULL ,
                          created_by VARCHAR(64) COLLATE utf8_bin NULL,
                          updated_by VARCHAR(64) COLLATE utf8_bin NULL,
                          date_created datetime DEFAULT current_timestamp(),
                          date_modified datetime DEFAULT NULL ON UPDATE current_timestamp(),
                          PRIMARY KEY(id),
                          FOREIGN KEY (whitelabel_id_fk) REFERENCES WHITELABELS(id),
                          UNIQUE KEY (partner_name),
                          KEY created_by_idx (created_by),
                          KEY updated_by_idx (updated_by)
) ENGINE=InnoDB;

CREATE TABLE BANKS (
                       id int(5) UNSIGNED NOT NULL AUTO_INCREMENT,
                       bin char(4) COLLATE utf8_bin NOT NULL ,
                       bank_name VARCHAR(30) COLLATE utf8_bin NOT NULL ,
                       bank_desc VARCHAR(255) COLLATE utf8_bin NOT NULL ,
                       created_by VARCHAR(64) COLLATE utf8_bin NULL,
                       updated_by VARCHAR(64) COLLATE utf8_bin NULL,
                       date_created datetime DEFAULT current_timestamp(),
                       date_modified datetime DEFAULT NULL ON UPDATE current_timestamp(),
                       PRIMARY KEY(id),
                       UNIQUE KEY idx_bank_name_bin (bin, bank_name),
                       KEY idx_bin (bin),
                       KEY idx_bank_name (bank_name),
                       KEY created_by_idx (created_by),
                       KEY updated_by_idx (updated_by)
) ENGINE=InnoDB;

CREATE TABLE MCC_LOOKUP (
                            id int(5) UNSIGNED NOT NULL AUTO_INCREMENT,
                            mcc_code char(4) NOT NULL ,
                            mcc_desc VARCHAR(255) COLLATE utf8_bin NOT NULL ,
                            created_by VARCHAR(64) COLLATE utf8_bin NULL,
                            updated_by VARCHAR(64) COLLATE utf8_bin NULL,
                            date_created datetime DEFAULT current_timestamp(),
                            date_modified datetime DEFAULT NULL ON UPDATE current_timestamp(),
                            PRIMARY KEY(id),
                            UNIQUE KEY idx_mcc_code (mcc_code),
                            KEY created_by_idx (created_by),
                            KEY updated_by_idx (updated_by)
) ENGINE=InnoDB;

CREATE TABLE MERCHANTS (
                           id int(5) UNSIGNED NOT NULL AUTO_INCREMENT,
                           active tinyint(1) NOT NULL,
                           bank_id_fk int(5) UNSIGNED NOT NULL,
                           mcc_id_fk int(5) UNSIGNED NOT NULL,
                           mid VARCHAR(20) COLLATE utf8_bin NOT NULL,
                           cb_mid VARCHAR(16) NOT NULL,
                           cc_setl_mid varchar(25) COLLATE utf8_bin NULL,
                           ach_setl_mid varchar(25) COLLATE utf8_bin NULL,
                           jetpay_mid VARCHAR(64) COLLATE utf8_bin NULL,
                           ach_mid VARCHAR(64) COLLATE utf8_bin NULL,
                           partner_id_fk int(5) UNSIGNED NULL,
                           merchant_desc VARCHAR(255) COLLATE utf8_bin NULL,
                           merchant_dba_name VARCHAR(255) COLLATE utf8_bin NOT NULL ,
                           created_by VARCHAR(64) COLLATE utf8_bin NULL,
                           updated_by VARCHAR(64) COLLATE utf8_bin NULL,
                           date_created datetime DEFAULT current_timestamp(),
                           date_modified datetime DEFAULT NULL ON UPDATE current_timestamp(),
                           trans_source int(1) unsigned NOT NULL DEFAULT '1' COMMENT '(0) No query, (1) All tables, (2) CCTransactions, (3) CardPresentCCTransactions',
                           PRIMARY KEY(id),
                           FOREIGN KEY (bank_id_fk) REFERENCES BANKS(id),
                           FOREIGN KEY (mcc_id_fk) REFERENCES MCC_LOOKUP(id),
                           FOREIGN KEY (partner_id_fk) REFERENCES PARTNERS(id),
                           UNIQUE KEY idx_mid (mid),
                           KEY idx_partner_id (partner_id_fk),
                           KEY created_by_idx (created_by),
                           KEY updated_by_idx (updated_by)
) ENGINE=InnoDB;

CREATE TABLE PROCESSSOR_TYPES (
                                  id int(5) UNSIGNED NOT NULL AUTO_INCREMENT,
                                  name VARCHAR(64) COLLATE utf8_bin NULL,
                                  description VARCHAR(64) COLLATE utf8_bin NULL,
                                  application_name VARCHAR(64) COLLATE utf8_bin NULL,
                                  application_version VARCHAR(64) COLLATE utf8_bin NULL,
                                  device_name VARCHAR(64) COLLATE utf8_bin NULL,
                                  device_version VARCHAR(64) COLLATE utf8_bin NULL,
                                  gateway_name VARCHAR(64) COLLATE utf8_bin NULL,
                                  gateway_version VARCHAR(64) COLLATE utf8_bin NULL,
                                  developer_id VARCHAR(64) COLLATE utf8_bin NULL,
                                  transaction_key VARCHAR(64) COLLATE utf8_bin NULL,
                                  created_by VARCHAR(64) COLLATE utf8_bin NULL,
                                  updated_by VARCHAR(64) COLLATE utf8_bin NULL,
                                  date_created datetime DEFAULT current_timestamp(),
                                  date_modified datetime DEFAULT NULL ON UPDATE current_timestamp(),
                                  PRIMARY KEY(id),
                                  KEY created_by_idx (created_by),
                                  KEY updated_by_idx (updated_by)
) ENGINE=InnoDB;

CREATE TABLE PROCESSORS (
                            id int(5) UNSIGNED NOT NULL AUTO_INCREMENT,
                            proc_party_id int(5) UNSIGNED NOT NULL ,
                            proc_test_url VARCHAR(255) COLLATE utf8_bin NOT NULL ,
                            proc_prod_url VARCHAR(255) COLLATE utf8_bin NOT NULL ,
                            row_guid VARCHAR(64) COLLATE utf8_bin NOT NULL ,
                            match_pattern VARCHAR(255) COLLATE utf8_bin NOT NULL ,
                            proc_id_alias int(5) UNSIGNED NULL,
                            proc_type_id_fk int(5) UNSIGNED NOT NULL ,
                            created_by VARCHAR(64) COLLATE utf8_bin NULL,
                            updated_by VARCHAR(64) COLLATE utf8_bin NULL,
                            date_created datetime DEFAULT current_timestamp(),
                            date_modified datetime DEFAULT NULL ON UPDATE current_timestamp(),
                            PRIMARY KEY(id),
                            FOREIGN KEY (proc_type_id_fk) REFERENCES PROCESSSOR_TYPES(id),
                            KEY created_by_idx (created_by),
                            KEY updated_by_idx (updated_by)
) ENGINE=InnoDB;

CREATE TABLE TERMINALS (
                           id int(5) UNSIGNED NOT NULL AUTO_INCREMENT,
                           tid VARCHAR(20) NOT NULL ,
                           cc_proc_id_fk int(5) UNSIGNED NOT NULL ,
                           ach_proc_id_fk int(5) UNSIGNED NOT NULL ,
                           terminal_desc VARCHAR(255) COLLATE utf8_bin NULL,
                           created_by VARCHAR(64) COLLATE utf8_bin NULL,
                           updated_by VARCHAR(64) COLLATE utf8_bin NULL,
                           date_created datetime DEFAULT current_timestamp(),
                           date_modified datetime DEFAULT NULL ON UPDATE current_timestamp(),
                           PRIMARY KEY(id),
                           FOREIGN KEY (cc_proc_id_fk) REFERENCES PROCESSORS(id),
                           FOREIGN KEY (ach_proc_id_fk) REFERENCES PROCESSORS(id),
                           UNIQUE KEY idx_tid (tid),
                           KEY created_by_idx (created_by),
                           KEY updated_by_idx (updated_by)
) ENGINE=InnoDB;

CREATE TABLE MID_TID_MAP (
                             id int(5) UNSIGNED NOT NULL AUTO_INCREMENT,
                             mid_id_fk int(5) UNSIGNED NOT NULL ,
                             tid_id_fk int(5) UNSIGNED NOT NULL ,
                             created_by VARCHAR(64) COLLATE utf8_bin NULL,
                             updated_by VARCHAR(64) COLLATE utf8_bin NULL,
                             date_created datetime DEFAULT current_timestamp(),
                             date_modified datetime DEFAULT NULL ON UPDATE current_timestamp(),
                             PRIMARY KEY(id),
                             FOREIGN KEY (mid_id_fk) REFERENCES MERCHANTS(id),
                             FOREIGN KEY (tid_id_fk) REFERENCES TERMINALS(id),
                             KEY idx_tid_id_fk (tid_id_fk),
                             KEY idx_mid_id_fk (mid_id_fk),
                             KEY created_by_idx (created_by),
                             KEY updated_by_idx (updated_by)
) ENGINE=InnoDB;

CREATE TABLE MERCHANT_STATEMENT
(
    ID BIGINT AUTO_INCREMENT PRIMARY KEY,
    FILENAME VARCHAR(150) NOT NULL,
    DATE_CREATED DATETIME NOT NULL,
    IMPORT_DATE DATETIME NULL
);

CREATE TABLE MERCHANT_STATEMENT_ACTIVITY_SUMMARY
(
    ID BIGINT AUTO_INCREMENT PRIMARY KEY,
    MERCHANT_STATEMENT_ID BIGINT NOT NULL,
    PLAN_TYPE VARCHAR(55) NULL,
    SALES VARCHAR(55) NULL,
    NUM_SALES VARCHAR(55) NULL,
    CREDITS VARCHAR(55) NOT NULL,
    NUM_CREDITS VARCHAR(55) NOT NULL,
    NET_SALES VARCHAR(55) NOT NULL,
    PER_ITEM_FEE VARCHAR(55) NOT NULL,
    RATE VARCHAR(55) NOT NULL,
    DISCOUNT_DUE VARCHAR(55) NOT NULL,
    NUM_TOTAL_TRANS VARCHAR(55) NOT NULL,
    DATE_CREATED DATETIME NOT NULL,
    MERCHANT_ID VARCHAR(55) NOT NULL
);

CREATE INDEX MERCHANT_ID_index
    on MERCHANT_STATEMENT_ACTIVITY_SUMMARY (MERCHANT_ID);

CREATE INDEX MERCHANT_STATEMENT_ID_index
    on MERCHANT_STATEMENT_ACTIVITY_SUMMARY (MERCHANT_STATEMENT_ID);

CREATE TABLE MERCHANT_STATEMENT_AUTH_DETAIL
(
    ID BIGINT AUTO_INCREMENT PRIMARY KEY,
    MERCHANT_STATEMENT_ID BIGINT NOT NULL ,
    DESCRIPTION VARCHAR(55) NOT NULL ,
    NUM_X VARCHAR(55) NULL,
    RATE VARCHAR(55) NULL,
    AUTH_FEE VARCHAR(55) NULL,
    DATE_CREATED DATETIME NOT NULL,
    MERCHANT_ID VARCHAR(55) NULL
);

CREATE INDEX MERCHANT_ID_index
    ON MERCHANT_STATEMENT_AUTH_DETAIL (MERCHANT_ID);

CREATE INDEX MERCHANT_STATEMENT_ID_index
    ON MERCHANT_STATEMENT_AUTH_DETAIL (MERCHANT_STATEMENT_ID);

CREATE TABLE MERCHANT_STATEMENT_DEPOSIT_DETAIL
(
    ID BIGINT AUTO_INCREMENT PRIMARY KEY,
    MERCHANT_STATEMENT_ID BIGINT NOT NULL,
    DEPOSIT_DATE VARCHAR(25) NULL,
    NUM_OF_TRANS INT NULL,
    BATCH_AMOUNT VARCHAR(55) NULL,
    OC_BATCH VARCHAR(55) NULL,
    ADJUST VARCHAR(55) NULL,
    CHARGEBACKS VARCHAR(55) NULL,
    FEE_PAID VARCHAR(55) NULL,
    NET_DEPOSIT VARCHAR(55) NULL,
    DATE_CREATED DATETIME NOT NULL,
    MERCHANT_ID VARCHAR(55) NULL
);

CREATE INDEX MERCHANT_ID_index
    ON MERCHANT_STATEMENT_DEPOSIT_DETAIL (MERCHANT_ID);

CREATE INDEX MERCHANT_STATEMENT_ID_index
    ON MERCHANT_STATEMENT_DEPOSIT_DETAIL (MERCHANT_STATEMENT_ID);

CREATE TABLE MERCHANT_STATEMENT_DETAIL
(
    ID BIGINT AUTO_INCREMENT PRIMARY KEY,
    MERCHANT_STATEMENT_ID BIGINT NOT NULL,
    MERCHANT_ID VARCHAR(55) NOT NULL,
    MERCHANT_NAME VARCHAR(55) NULL,
    ASSOCIATE VARCHAR(55) NULL,
    CHAIN VARCHAR(55) NULL,
    PROCESSING_DETAIL_TOTAL VARCHAR(55) NULL,
    AUTHORIZATION_DETAIL_TOTAL VARCHAR(55) NULL,
    OTHER_DETAIL_TOTAL VARCHAR(55) NULL,
    DISCOUNT_DUE_TOTAL VARCHAR(55) NULL,
    STATEMENT_MONTH CHAR(4) NULL
);

CREATE INDEX MERCHANT_ID_index
    ON MERCHANT_STATEMENT_DETAIL (MERCHANT_ID);

CREATE INDEX MERCHANT_STATEMENT_ID_index
    ON MERCHANT_STATEMENT_DETAIL (MERCHANT_STATEMENT_ID);

CREATE TABLE MERCHANT_STATEMENT_OTHER_CARD_DEPOSIT
(
    ID BIGINT AUTO_INCREMENT PRIMARY KEY,
    MERCHANT_STATEMENT_ID BIGINT NOT NULL,
    DATE VARCHAR(25) NOT NULL,
    BATCH_AMOUNT VARCHAR(55) NOT NULL,
    PAID_BY VARCHAR(55) NOT NULL,
    NET_AMOUNT VARCHAR(55) NOT NULL,
    DATE_CREATED datetime NOT NULL,
    MERCHANT_ID VARCHAR(55) NULL
);

CREATE INDEX MERCHANT_ID_index
    ON MERCHANT_STATEMENT_OTHER_CARD_DEPOSIT (MERCHANT_ID);

CREATE INDEX MERCHANT_STATEMENT_ID_index
    ON MERCHANT_STATEMENT_OTHER_CARD_DEPOSIT (MERCHANT_STATEMENT_ID);

CREATE TABLE MERCHANT_STATEMENT_OTHER_DETAIL
(
    ID BIGINT AUTO_INCREMENT PRIMARY KEY,
    MERCHANT_STATEMENT_ID BIGINT NOT NULL,
    DESCRIPTION VARCHAR(55) NOT NULL,
    AMOUNT VARCHAR(55) NOT NULL,
    DISCOUNT_RATE VARCHAR(55) NOT NULL,
    NUM_X VARCHAR(55) NOT NULL,
    TRANSACTION_FEE VARCHAR(55) NOT NULL,
    OTHER_FEE VARCHAR(55) NOT NULL,
    DATE_CREATED DATETIME NULL,
    MERCHANT_ID VARCHAR(55) NOT NULL
);

CREATE INDEX MERCHANT_ID_index
    ON MERCHANT_STATEMENT_OTHER_DETAIL (MERCHANT_ID);

CREATE INDEX MERCHANT_STATEMENT_ID_index
    ON MERCHANT_STATEMENT_OTHER_DETAIL (MERCHANT_STATEMENT_ID);

CREATE TABLE MERCHANT_STATEMENT_PROCESSING_DETAIL
(
    ID BIGINT AUTO_INCREMENT PRIMARY KEY,
    MERCHANT_STATEMENT_ID BIGINT NOT NULL,
    DESCRIPTION VARCHAR(255) NOT NULL,
    AMOUNT VARCHAR(55) NULL,
    DISCOUNT_RATE VARCHAR(55) NULL,
    NUM_X VARCHAR(55) NULL,
    TRANSACTION_FEES VARCHAR(55) NULL,
    PROCESS_FEES VARCHAR(55) NULL,
    DATE_CREATED DATETIME NULL,
    MERCHANT_ID VARCHAR(55) NOT NULL
);

CREATE INDEX MERCHANT_ID_index
    ON MERCHANT_STATEMENT_PROCESSING_DETAIL (MERCHANT_ID);

CREATE INDEX MERCHANT_STATEMENT_ID_index
    ON MERCHANT_STATEMENT_PROCESSING_DETAIL (MERCHANT_STATEMENT_ID);

CREATE TABLE IF NOT EXISTS `ImportChargebacks` (
                                                   `ID` bigint(20) NOT NULL,
                                                   `ImportFileID` int(11) DEFAULT NULL,
                                                   `ImportDate` datetime(6) DEFAULT CURRENT_TIMESTAMP(6),
                                                   `CaseNumber` varchar(20) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `FamilyID` varchar(7) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `ItemType` varchar(2) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `MerchantNumber` varchar(16) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `CaseType` varchar(2) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `ResolutionTo` varchar(1) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `Debit_Credit` varchar(1) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `TranCode` varchar(2) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `ReasonCode` varchar(4) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `ReasonDesc` varchar(50) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `BIN_ICA` varchar(6) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `CaseAmount` varchar(12) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `RecordType` varchar(1) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `CardBrand` varchar(1) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `DateResolved` varchar(8) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `AcquirerReferenceNumber` varchar(23) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `OriginalReferenceNumber` varchar(30) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `Foreign_Domestic` varchar(1) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `MCC` varchar(4) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `AuthCode` varchar(6) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `DateTransaction` varchar(8) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `ImportDateTransaction` date DEFAULT NULL,
                                                   `ImportDateLoaded` date DEFAULT NULL,
                                                   `ImportDatePosted` date DEFAULT NULL,
                                                   `ImportDateSecondRequest` date DEFAULT NULL,
                                                   `ImportDateResolved` date DEFAULT NULL,
                                                   `DatePosted` varchar(8) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `DateLoaded` varchar(8) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `CardholderAccountNumber` varchar(19) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `DateSecondRequest` varchar(8) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `AccountNumberPrefix` varchar(6) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `AccountNumberSuffix` varchar(4) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `DBAName` varchar(45) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `Address1` varchar(40) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `Address2` varchar(40) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `City` varchar(25) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `State` varchar(2) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `ZipCode` varchar(10) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `GroupID` varchar(6) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `Association` varchar(16) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `InvoiceNumber` varchar(8) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `DateWarehouse` varchar(8) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `TransID` varchar(15) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `MerchAmount` varchar(12) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `MatchedTransactionID` int(11) DEFAULT NULL,
                                                   `MatchedDate` datetime DEFAULT NULL,
                                                   `CompleteLine` varchar(550) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                   `DateUpdated` datetime(6) DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)
) ENGINE=InnoDB AUTO_INCREMENT=139380 DEFAULT CHARSET=latin1;

ALTER TABLE `ImportChargebacks`
    ADD PRIMARY KEY (`ID`),
    ADD UNIQUE KEY `IDX_FILE` (`ID`),
    ADD KEY `AuthCode` (`AuthCode`),
    ADD KEY `TransID` (`TransID`),
    ADD KEY `ImportFileID` (`ImportFileID`),
    ADD KEY `BIN_ICA` (`BIN_ICA`),
    ADD KEY `AuthCode_2` (`AuthCode`),
    ADD KEY `AccountNumberSuffix` (`AccountNumberSuffix`),
    ADD KEY `DateUpdated` (`DateUpdated`),
    ADD KEY `MatchedDate` (`MatchedDate`);

ALTER TABLE `ImportChargebacks`
    MODIFY `ID` bigint(20) NOT NULL AUTO_INCREMENT,AUTO_INCREMENT=139380;


CREATE TABLE IF NOT EXISTS `CardPresentCCTransaction` (
                                                          `ID` bigint(20) NOT NULL,
                                                          `ProcessorID` bigint(20) NOT NULL,
                                                          `MerchantName` varchar(50) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                          `MerchantID` varchar(25) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                                          `TerminalID` varchar(25) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                          `CCProcessor` varchar(10) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                          `AuthCode` varchar(10) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                          `ActionCode` varchar(3) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                          `RequestType` varchar(10) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                          `TransactionType` varchar(15) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                          `TransAmount` int(11) DEFAULT NULL,
                                                          `AuthTransDate` datetime DEFAULT NULL,
                                                          `FeeAmount` int(11) DEFAULT NULL,
                                                          `TaxAmount` int(11) DEFAULT NULL,
                                                          `SettlementDate` datetime DEFAULT NULL,
                                                          `SettlementAmount` int(11) DEFAULT NULL,
                                                          `SettlementID` bigint(20) DEFAULT NULL,
                                                          `ApprovalStatus` varchar(25) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                          `Token` varchar(17) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                          `CardBin` varchar(6) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                          `CardLastFour` varchar(4) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                          `CardType` varchar(2) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                          `CurrencyCode` varchar(4) DEFAULT NULL,
                                                          `ExpDate` int(11) DEFAULT NULL,
                                                          `SafeCardNum` varchar(20) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                          `CardHolderName` varchar(60) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                          `Address` varchar(50) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                          `City` varchar(25) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                          `State` varchar(25) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                          `ZipCode` varchar(10) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                          `Country` varchar(3) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                          `Phone` varchar(50) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                          `Email` varchar(50) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                          `AVSResponse` varchar(2) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                          `CVVResponse` varchar(2) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                          `BankTransID` varchar(10) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                          `ExternalTransID` varchar(18) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                          `TransactionID` varchar(50) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                          `UserField1` varchar(60) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                          `UserField2` varchar(60) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                          `UserField3` varchar(60) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                          `POSTerminalID` varchar(4) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                          `POSAdditionalData` varchar(12) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                          `RetrievalReferenceNumber` bigint(12) DEFAULT NULL,
                                                          `VNumber` varchar(10) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                          `OrderNumber` varchar(25) DEFAULT NULL,
                                                          `ResponseText` varchar(25) DEFAULT NULL,
                                                          `ChargebackID` int(11) DEFAULT NULL,
                                                          `CardNumber` varchar(20) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                                          `ChargebackLoadDate` date DEFAULT NULL,
                                                          `SecondRequestID` int(11) DEFAULT NULL,
                                                          `ChargebackSecondRequest` date DEFAULT NULL,
                                                          `DateAdded` datetime(6) DEFAULT CURRENT_TIMESTAMP(6),
                                                          `DateUpdated` datetime(6) DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)
) ENGINE=InnoDB AUTO_INCREMENT=1060827183 DEFAULT CHARSET=latin1;

ALTER TABLE `CardPresentCCTransaction`
    ADD PRIMARY KEY (`ID`),
    ADD UNIQUE KEY `ProcessorID` (`ProcessorID`),
    ADD KEY `AuthTransDate` (`AuthTransDate`),
    ADD KEY `AuthCode` (`AuthCode`),
    ADD KEY `MerchantID` (`MerchantID`),
    ADD KEY `RetrievalReferenceNumber` (`RetrievalReferenceNumber`),
    ADD KEY `SafeCardNum` (`SafeCardNum`),
    ADD KEY `ChargebackID` (`ChargebackID`);

ALTER TABLE `CardPresentCCTransaction`
    MODIFY `ID` bigint(20) NOT NULL AUTO_INCREMENT,AUTO_INCREMENT=1060827183;
