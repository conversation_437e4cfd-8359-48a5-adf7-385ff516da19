
-- ================== Bank Lookup Data ==================
INSERT INTO BANK_LOOKUP (bin, bank_name, date_created, date_modified) VALUES ('0012', 'Bank One', DEFAULT, null);

-- ================== BANK_MID_TID_MAP Data ==================
INSERT INTO BANK_MID_TID_MAP (bank_id_fk, mcc, mid, tid, date_created, date_modified) VALUES
(1, '1731', 'MID00000000001', 'TID100000001', DEFAULT, null),
(1, '8398', 'MID00000000002', 'TID100000002', DEFAULT, null),
(1, '7997', 'MID00000000003', 'TESTTERMINAL', DEFAULT, null),
(1, '4814', 'MID00000000004', 'TID100000001', DEFAULT, null),
(1, '4899', 'MID00000000005', 'TID100000002', DEFAULT, null);

-- ================== ACHTransactions Data ==================
INSERT INTO `ACHTransactions` (`ID`, `IPTransID`, `MerchantName`, `MerchantID`, `TerminalID`, `ProcessorID`, `AuthCode`, `ActionCode`, `RequestType`, `TransactionType`, `TotalAmount`, `TransDate`, `AuthTransDate`, `FeeAmount`, `ResponseText`, `AccountNumber`, `ABA`, `CheckNumber`, `SettleDate`, `CompletionDate`, `CompletionAmount`, `Status`, `ReturnStatusFlag`, `ImportItemID`, `ImportAmount`, `Token`, `CardHolderName`, `Address`, `City`, `State`, `ZipCode`, `Country`, `Phone`, `Email`, `TransactionID`, `OrderNumber`, `CurrencyCode`, `Invoice`, `UserField1`, `UserField2`, `UserField3`, `ACHProcessor`, `CCProcessor`, `DateAdded`, `DateUpdated`) VALUES
(1, ********, 'MID DBA 01', 'MID00000000001', 'TID100000001', NULL, NULL, '000', 'CHECK', 'ACH', '53.01', NULL, '2018-10-02 00:00:05', NULL, 'CHECK ACCEPTED', NULL, '', '6556', '2018-10-01 00:00:00.000000', NULL, NULL, NULL, NULL, NULL, NULL, 'D6L8K2J0E3M3F9397', 'SUSAN DAWSON', '', '', '', '', '', '', '', 'H20181001050005260', '', NULL, NULL, 'Sonar', '', '', '5706', NULL, '2018-10-30 12:37:38.513335', '2018-10-30 12:37:38.513335'),
(2, ********, 'MID DBA 01', 'MID00000000001', 'TID100000001', NULL, NULL, '000', 'CHECK', 'ACH', '77.13', NULL, '2018-10-02 00:00:05', NULL, 'CHECK ACCEPTED', NULL, '', '3100', '2018-10-01 00:00:00.000000', '2018-10-10 14:30:01.000000', 77, 'CMP', NULL, NULL, '77.00', 'C5K3J5F1D3G3J42719', 'Rex Scheetz', '', '', '', '', '', '', '', 'A20181001050005587', '', NULL, NULL, 'Sonar', '', '', '12', NULL, '2018-10-30 12:37:38.514525', '2018-11-16 15:06:35.389950'),
(3, 80626040, 'MID DBA 01', 'MID00000000001', 'TID100000001', NULL, NULL, '000', 'CHECK', 'ACH', '60.12', NULL, '2018-10-02 00:00:06', NULL, 'CHECK ACCEPTED', NULL, '', '10153', '2018-10-01 00:00:00.000000', NULL, NULL, NULL, NULL, NULL, NULL, 'F5H7G6M8B4C34860', 'Jennifer Staebler', '', '', '', '', '', '', '', 'E20181001050006593', '', NULL, NULL, 'Sonar', '', '', '5706', NULL, '2018-10-30 12:37:38.519003', '2018-10-30 12:37:38.519003'),
(4, 80626049, 'MID DBA 01', 'MID00000000001', 'TID100000002', NULL, NULL, '000', 'CHECK', 'ACH', '60.1400', NULL, '2018-10-02 00:00:07', NULL, 'CHECK ACCEPTED', NULL, '', '10154', '2018-10-01 00:00:00.000000', NULL, NULL, NULL, NULL, NULL, NULL, 'H5J7L8M8J8B1B5032', 'Daniel Johnson', '', '', '', '', '', '', '', 'B20181001050007347', '', NULL, NULL, 'Sonar', '', '', '5706', NULL, '2018-10-30 12:37:38.519742', '2018-10-30 12:37:38.519742'),
(5, 80626057, 'MID DBA 02', 'MID00000000002', 'TID100000002', NULL, NULL, '000', 'CHECK', 'ACH', '108.1400', NULL, '2018-10-01 00:00:08', NULL, 'CHECK ACCEPTED', NULL, '', '5909', '2018-10-01 00:00:00.000000', NULL, NULL, NULL, NULL, NULL, NULL, 'L1K4E6A2C6B08124', 'EV Harland', '', '', '', '', '', '', '', 'G20181001050008093', '', NULL, NULL, 'Sonar', '', '', '5706', NULL, '2018-10-30 12:37:38.520599', '2018-10-30 12:37:38.520599'),
(6, 80626072, 'MID DBA 02', 'MID00000000002', 'TID100000002', NULL, NULL, '000', 'CHECK', 'ACH', '100.1300', NULL, '2018-10-01 00:00:08', NULL, 'CHECK ACCEPTED', NULL, '', '1191', '2018-10-01 00:00:00.000000', NULL, NULL, NULL, NULL, NULL, NULL, 'H7H3K5F7J6M7B8M8936', 'Ashley Lawrence', '', '', '', '', '', '', '', 'G20181001050008760', '', NULL, NULL, 'Sonar', '', '', '5706', NULL, '2018-10-30 12:37:38.521236', '2018-10-30 12:37:38.521236'),
(7, 80626076, 'MID DBA 02', 'MID00000000002', 'TESTTERMINAL', NULL, NULL, '000', 'CHECK', 'ACH', '156.11', NULL, '2018-10-01 00:00:08', NULL, 'CHECK ACCEPTED', NULL, '', '1104', '2018-10-01 00:00:00.000000', '2018-10-05 14:30:02.000000', 156, 'CMP', NULL, NULL, '156.42', 'E6K4C7B2G0G83504', 'Jeff Long', '', '', '', '', '', '', '', 'E20181001050008990', '', NULL, NULL, 'Sonar', '', '', '12', NULL, '2018-10-30 12:37:38.521921', '2018-11-16 15:06:35.389950'),
(8, 80626087, 'MID DBA 03', 'MID00000000003', 'TESTTERMINAL', NULL, NULL, '000', 'CHECK', 'ACH', '152.02', NULL, '2018-10-03 00:00:09', NULL, 'CHECK ACCEPTED', NULL, '', '1061', '2018-10-02 00:00:00.000000', NULL, NULL, NULL, NULL, NULL, NULL, 'J2A9K8K3F6G19341', 'Sport America', '', '', '', '', '', '', '', 'G20181001050009850', '', NULL, NULL, 'Sonar', '', '', '5706', NULL, '2018-10-30 12:37:38.522536', '2018-10-30 12:37:38.522536'),
(9, 80626097, 'MID DBA 03', 'MID00000000003', 'TESTTERMINAL', NULL, NULL, '000', 'CHECK', 'ACH', '196.04', NULL, '2018-10-03 00:00:10', NULL, 'CHECK ACCEPTED', NULL, '', '10863', '2018-10-02 00:00:00.000000', NULL, NULL, NULL, NULL, NULL, NULL, 'A7D0K4M2C4B92705', 'Fannin County MultiPurpose Com', '', '', '', '', '', '', '', 'K20181001050010433', '', NULL, NULL, 'Sonar', '', '', '5706', NULL, '2018-10-30 12:37:38.523249', '2018-10-30 12:37:38.523249'),
(10, ********, 'MID DBA 03', 'MID00000000003', 'TID100000001', NULL, NULL, '000', 'CHECK', 'ACH', '211.08', NULL, '2018-10-03 00:00:10', NULL, 'CHECK ACCEPTED', NULL, '*********', '100', '2018-10-01 00:00:00.000000', '2018-10-05 14:30:02.000000', 211, 'CMP', NULL, NULL, '211.23', 'K5F7F6J2B9A4B2M5079', 'Accounts Payable', '3505 Houston Levee Road', 'Collierville', 'TN', '38139', 'USA', '901-850-9553', '<EMAIL>', 'ZWI181001050011044', '29536', NULL, NULL, '', '', 'Business::OnlinePayment::IPPay 0.1', '12', NULL, '2018-10-30 12:37:38.528650', '2018-11-16 15:06:35.389950'),
(11, ********, 'MID DBA 01', 'MID00000000001', 'TID100000002', NULL, NULL, '000', 'CHECK', 'ACH', '220.1100', NULL, '2018-10-03 00:00:10', NULL, 'CHECK ACCEPTED', NULL, '*********', '101', '2018-10-01 00:00:00.000000', '2018-10-05 14:30:02.000000', 256, 'CMP', NULL, NULL, '211.23', 'K5F7F6J2B9A4B2M5079', 'Accounts Payable', '3505 Houston Levee Road', '', '', '', '', '901-850-9553', '<EMAIL>', 'ZWI181001050011044', '29536', NULL, NULL, '', '', 'Business::OnlinePayment::IPPay 0.2', '12', NULL, '2018-10-30 12:37:38.528650', '2018-11-16 15:06:35.389950'),
(12, ********, 'MID DBA 02', 'MID00000000002', 'TID100000002', NULL, NULL, '000', 'CHECK', 'ACH', '240', NULL, '2018-10-03 00:00:09', NULL, 'CHECK ACCEPTED', NULL, '', '1052', '2018-10-02 00:00:00.000000', NULL, NULL, NULL, NULL, NULL, NULL, 'J2A9K8K3F6G19341', 'Sport America', '', '', '', '', '', '', '', 'G20181001050009851', '', NULL, NULL, 'Sonar', '', '', '5706', NULL, '2018-10-30 12:37:38.522536', '2018-10-30 12:37:38.522536'),
(13, ********, 'MID DBA 02', 'MID00000000002', 'TID100000002', NULL, NULL, '000', 'CHECK', 'ACH', '260', NULL, '2018-10-03 00:00:09', NULL, 'CHECK ACCEPTED', NULL, '', '1052', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'J2A9K8K3F6G19341', 'Sport America', '', '', '', '', '', '', '', 'G20181001050009852', '', NULL, NULL, 'Sonar', '', '', '5706', NULL, '2018-11-30 12:37:38.522536', '2018-11-30 12:37:38.522536'),
(14, 80626113, 'MID DBA 03', 'MID00000000003', 'TESTTERMINAL', NULL, NULL, '000', 'CHECK', 'ACH', '280', NULL, '2018-10-03 00:00:09', NULL, 'CHECK ACCEPTED', NULL, '', '1053', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'J2A9K8K3F6G19444', 'Sport America', '', '', '', '', '', '', '', 'G20181001050009853', '', NULL, NULL, 'Sonar', '', '', '5707', NULL, '2018-11-30 12:37:38.522536', '2018-11-30 12:37:38.522536'),
(15, 80626114, 'MID DBA 01', 'MID00000000001', 'TID100000001', NULL, NULL, '000', 'CHECK', 'ACH', '320', NULL, '2018-10-03 00:00:09', NULL, 'CHECK ACCEPTED', NULL, '', '1054', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'J2A9K8K3F6G19222', 'Sport America', '', '', '', '', '', '', '', 'G20181001050009854', '', NULL, NULL, 'Sonar', '', '', '5708', NULL, '2018-11-30 12:37:38.522536', '2018-11-30 12:37:38.522536'),
(16, 80626115, 'MID DBA 02', 'MID00000000002', 'TID100000002', NULL, NULL, '000', 'CHECK', 'ACH', '345', NULL, '2018-10-04 00:00:09', NULL, 'CHECK ACCEPTED', NULL, '', '1055', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'J2A9K8K3F6G19111', 'Sport America', '', '', '', '', '', '', '', 'G20181001050009855', '', NULL, NULL, 'Sonar', '', '', '5709', NULL, '2018-12-30 12:37:38.522536', '2018-12-30 12:37:38.522536'),
(17, 80626116, 'MID DBA 03', 'MID00000000003', 'TESTTERMINAL', NULL, NULL, '000', 'CHECK', 'ACH', '380', NULL, '2018-10-04 00:00:09', NULL, 'CHECK ACCEPTED', NULL, '', '1056', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'J2A9K8K3F6G19331', 'Sport America', '', '', '', '', '', '', '', 'G20181001050009856', '', NULL, NULL, 'Sonar', '', '', '5710', NULL, '2018-12-30 12:37:38.522536', '2018-12-30 12:37:38.522536'),
(18, 80626117, 'MID DBA 01', 'MID00000000001', 'TID100000001', NULL, NULL, '000', 'CHECK', 'ACH', '381', NULL, '2018-10-04 00:00:09', NULL, 'CHECK ACCEPTED', NULL, '', '1057', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'J2A9K8K3F6G19441', 'Sport America', '', '', '', '', '', '', '', 'G20181001050009857', '', NULL, NULL, 'Sonar', '', '', '5711', NULL, '2018-12-30 12:37:38.522536', '2018-12-30 12:37:38.522536'),
(19, 80626118, 'MID DBA 02', 'MID00000000002', 'TID100000002', NULL, NULL, '000', 'CHECK', 'ACH', '382', NULL, '2018-10-04 00:00:09', NULL, 'CHECK ACCEPTED', NULL, '', '1058', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'J2A9K8K3F6G19551', 'Sport America', '', '', '', '', '', '', '', 'G20181001050009858', '', NULL, NULL, 'Sonar', '', '', '5712', NULL, '2019-10-30 12:37:38.522536', '2019-10-30 12:37:38.522536'),
(20, 80626119, 'MID DBA 03', 'MID00000000003', 'TESTTERMINAL', NULL, NULL, '000', 'CHECK', 'ACH', '390', NULL, '2018-10-04 00:00:09', NULL, 'CHECK ACCEPTED', NULL, '', '1059', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'J2A9K8K3F6G19661', 'Sport America', '', '', '', '', '', '', '', 'G20181001050009859', '', NULL, NULL, 'Sonar', '', '', '5713', NULL, '2019-10-30 12:37:38.522536', '2019-10-30 12:37:38.522536'),
(21, 80626120, 'MID DBA 01', 'MID00000000001', 'TID100000001', NULL, NULL, '000', 'CHECK', 'ACH', '400', NULL, '2018-10-04 00:00:09', NULL, 'CHECK ACCEPTED', NULL, '', '1060', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'J2A9K8K3F6G19771', 'Sport America', '', '', '', '', '', '', '', 'G20181001050009860', '', NULL, NULL, 'Sonar', '', '', '5714', NULL, '2019-10-30 12:37:38.522536', '2019-10-30 12:37:38.522536'),
(22, 80626121, 'MID DBA 02', 'MID00000000002', 'TID100000002', NULL, NULL, '000', 'CHECK', 'ACH', '420', NULL, '2018-10-05 00:00:09', NULL, 'CHECK ACCEPTED', NULL, '', '1061', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'J2A9K8K3F6G19881', 'Sport America', '', '', '', '', '', '', '', 'G20181001050009861', '', NULL, NULL, 'Sonar', '', '', '5715', NULL, '2020-10-30 12:37:38.522536', '2020-10-30 12:37:38.522536'),
(23, 80626122, 'MID DBA 03', 'MID00000000003', 'TESTTERMINAL', NULL, NULL, '000', 'CHECK', 'ACH', '450', NULL, '2018-10-05 00:00:09', NULL, 'CHECK ACCEPTED', NULL, '', '1062', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'J2A9K8K3F6G19882', 'Sport America', '', '', '', '', '', '', '', 'G20181001050009862', '', NULL, NULL, 'Sonar', '', '', '5715', NULL, '2020-11-24 12:37:38.522536', '2020-11-24 12:37:38.522536'),
(24, 80626123, 'MID DBA 01', 'MID00000000001', 'TID100000001', NULL, NULL, '000', 'CHECK', 'ACH', '460', NULL, '2018-10-05 00:00:09', NULL, 'CHECK ACCEPTED', NULL, '', '1063', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'J2A9K8K3F6G19883', 'Sport America', '', '', '', '', '', '', '', 'G20181001050009863', '', NULL, NULL, 'Sonar', '', '', '5715', NULL, '2020-11-24 12:37:38.522536', '2020-11-24 12:37:38.522536'),
(25, 80626124, 'MID DBA 02', 'MID00000000002', 'TID100000002', NULL, NULL, '000', 'CHECK', 'ACH', '500', NULL, '2018-10-06 00:00:09', NULL, 'CHECK ACCEPTED', NULL, '', '1064', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'J2A9K8K3F6G19884', 'Sport America', '', '', '', '', '', '', '', 'G20181001050009864', '', NULL, NULL, 'Sonar', '', '', '5715', NULL, '2020-11-24 12:37:38.522536', '2020-11-24 12:37:38.522536'),
(26, 80626125, 'MID DBA 02', 'MID00000000002', 'TID100000002', NULL, NULL, '000', 'CHECK', 'ACH', '100', NULL, '2018-10-06 00:00:09', NULL, 'CHECK ACCEPTED', NULL, '', '1065', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'J2A9K8K3F6G19885', 'Sport America', '', '', '', '', '', '', '', 'G20181001050009865', '', NULL, NULL, 'Sonar', '', '', '5716', NULL, '2020-11-24 12:37:38.522536', '2020-11-24 12:37:38.522536'),
(27, 80626126, 'MID DBA 02', 'MID00000000002', 'TID100000002', NULL, NULL, '000', 'CHECK', 'ACH', '100', NULL, '2018-10-06 00:00:09', NULL, 'CHECK ACCEPTED', NULL, '', '1066', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'J2A9K8K3F6G19885', 'Ashley Lawrence', '', '', '', '', '', '', '', 'G20181001050009866', '', NULL, NULL, 'Sonar', '', '', '5716', NULL, '2020-11-24 12:37:38.522536', '2020-11-24 12:37:38.522536'),
(28, 80626127, 'MID DBA 02', 'MID00000000002', 'TID100000002', NULL, NULL, '000', 'CHECK', 'ACH', '200', NULL, '2018-10-06 00:00:09', NULL, 'CHECK ACCEPTED', NULL, '', '1067', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'J2A9K8K3F6G19886', 'Sport America', '', '', '', '', '', '', '', 'G20181001050009867', '', NULL, NULL, 'Sonar', '', '', '5717', NULL, '2020-11-24 12:37:38.522536', '2020-11-24 12:37:38.522536'),
(29, 80626128, 'MID DBA 02', 'MID00000000002', 'TID100000002', NULL, NULL, '000', 'CHECK', 'ACH', '300', NULL, '2018-10-06 00:00:09', NULL, 'CHECK ACCEPTED', NULL, '', '1068', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'J2A9K8K3F6G19887', 'Sport America', '', '', '', '', '', '', '', 'G20181001050009868', '', NULL, NULL, 'Sonar', '', '', '5718', NULL, '2020-11-24 12:37:38.522536', '2020-11-24 12:37:38.522536'),
(30, 80626129, 'MID DBA 02', 'MID00000000002', 'TID100000002', NULL, NULL, '000', 'CHECK', 'ACH', '101', NULL, '2018-10-06 00:00:09', NULL, 'CHECK ACCEPTED', NULL, '', '1069', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'J2A9K8K3F6G19888', 'Ashley Lawrence', '', '', '', '', '', '', '', 'G20181001050009869', '', NULL, NULL, 'Sonar', '', '', '5719', NULL, '2020-11-24 12:37:38.522536', '2020-11-24 12:37:38.522536'),
(31, 80626130, 'MID DBA 02', 'MID00000000002', 'TID100000002', NULL, NULL, '000', 'CHECK', 'ACH', '102', NULL, '2018-10-06 00:00:09', NULL, 'CHECK ACCEPTED', NULL, '', '1070', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'J2A9K8K3F6G19889', 'Sport America', '', '', '', '', '', '', '', 'G20181001050009810', '', NULL, NULL, 'Sonar', '', '', '5720', NULL, '2020-11-24 12:37:38.522536', '2020-11-24 12:37:38.522536'),
(32, 80626131, 'MID DBA 02', 'MID00000000002', 'TID100000002', NULL, NULL, '000', 'CHECK', 'ACH', '103', NULL, '2018-10-06 00:00:09', NULL, 'CHECK ACCEPTED', NULL, '', '1071', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'J2A9K8K3F6G19810', 'Sport America', '', '', '', '', '', '', '', 'G20181001050009811', '', NULL, NULL, 'Sonar', '', '', '5721', NULL, '2020-11-24 12:37:38.522536', '2020-11-24 12:37:38.522536'),
(33, 80626132, 'MID DBA 02', 'MID00000000002', 'TID100000002', NULL, NULL, '000', 'CHECK', 'ACH', '104', NULL, '2018-10-06 00:00:09', NULL, 'CHECK ACCEPTED', NULL, '', '1072', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'J2A9K8K3F6G19811', 'Sport America', '', '', '', '', '', '', '', 'G20181001050009812', '', NULL, NULL, 'Sonar', '', '', '5722', NULL, '2020-11-24 12:37:38.522536', '2020-11-24 12:37:38.522536'),
(34, 80626133, 'MID DBA 02', 'MID00000000002', 'TID100000002', NULL, NULL, '000', 'CHECK', 'ACH', '105', NULL, '2018-10-06 00:00:09', NULL, 'CHECK ACCEPTED', NULL, '', '1073', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'J2A9K8K3F6G19812', 'Ashley Lawrence', '', '', '', '', '', '', '', 'G20181001050009813', '', NULL, NULL, 'Sonar', '', '', '5723', NULL, '2020-11-24 12:37:38.522536', '2020-11-24 12:37:38.522536'),
(35, 80626134, 'MID DBA 02', 'MID00000000002', 'TID100000002', NULL, NULL, '000', 'CHECK', 'ACH', '106', NULL, '2018-10-06 00:00:09', NULL, 'CHECK ACCEPTED', NULL, '', '1074', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'J2A9K8K3F6G19813', 'Sport America', '', '', '', '', '', '', '', 'G20181001050009814', '', NULL, NULL, 'Sonar', '', '', '5724', NULL, '2020-11-24 12:37:38.522536', '2020-11-24 12:37:38.522536'),
(36, 80626135, 'MID DBA 02', 'MID00000000002', 'TID100000002', NULL, NULL, '000', 'CHECK', 'ACH', '110', NULL, '2018-10-06 00:00:09', NULL, 'CHECK ACCEPTED', NULL, '', '1075', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'J2A9K8K3F6G19814', 'Ashley Lawrence', '', '', '', '', '', '', '', 'G20181001050009815', '', NULL, NULL, 'Sonar', '', '', '5725', NULL, '2020-11-24 12:37:38.522536', '2020-11-24 12:37:38.522536'),
(37, 80626136, 'MID DBA 02', 'MID00000000002', 'TID100000002', NULL, NULL, '000', 'CHECK', 'ACH', '110', NULL, '2018-10-06 00:00:09', NULL, 'CHECK ACCEPTED', NULL, '', '1075', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'J2A9K8K3F6G19815', 'Ashley Lawrence', '', '', '', '', '', '', '', 'G20181001050009816', '', NULL, NULL, 'Sonar', '', '', '5726', NULL, '2020-11-24 12:37:38.522536', '2020-11-24 12:37:38.522536'),
(38, 80626137, 'MID DBA 02', 'MID00000000002', 'TID100000002', NULL, NULL, '000', 'CHECK', 'ACH', '111', NULL, '2018-10-06 00:00:09', NULL, 'CHECK ACCEPTED', NULL, '', '1076', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'J2A9K8K3F6G19816', 'Ashley Lawrence', '', '', '', '', '', '', '', 'G20181001050009817', '', NULL, NULL, 'Sonar', '', '', '5727', NULL, '2020-11-24 12:37:38.522536', '2020-11-24 12:37:38.522536'),
(39, 80626138, 'MID DBA 02', 'MID00000000002', 'TID100000002', NULL, NULL, '000', 'CHECK', 'ACH', '112', NULL, '2018-10-06 00:00:09', NULL, 'CHECK ACCEPTED', NULL, '', '1077', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'J2A9K8K3F6G19817', 'Ashley Lawrence', '', '', '', '', '', '', '', 'G20181001050009818', '', NULL, NULL, 'Sonar', '', '', '5728', NULL, '2020-11-24 12:37:38.522536', '2020-11-24 12:37:38.522536'),
(40, 80626139, 'MID DBA 02', 'MID00000000002', 'TID100000002', NULL, NULL, '000', 'CHECK', 'ACH', '113', NULL, '2018-10-06 00:00:09', NULL, 'CHECK ACCEPTED', NULL, '', '1078', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'J2A9K8K3F6G19818', 'Sport America', '', '', '', '', '', '', '', 'G20181001050009819', '', NULL, NULL, 'Sonar', '', '', '5729', NULL, '2020-11-24 12:37:38.522536', '2020-11-24 12:37:38.522536'),
(41, 80626140, 'MID DBA 02', 'MID00000000002', 'TID100000002', NULL, NULL, '000', 'CHECK', 'ACH', '114', NULL, '2018-10-06 00:00:09', NULL, 'CHECK ACCEPTED', NULL, '', '1079', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'J2A9K8K3F6G19819', 'Ashley Lawrence', '', '', '', '', '', '', '', 'G20181001050009820', '', NULL, NULL, 'Sonar', '', '', '5730', NULL, '2020-11-24 12:37:38.522536', '2020-11-24 12:37:38.522536'),
(42, 80626141, 'MID DBA 01', 'MID00000000001', 'TID100000001', NULL, NULL, '000', 'CHECK', 'ACH', '53', NULL, '2018-10-02 00:00:05', NULL, 'CHECK ACCEPTED', NULL, '', '1000', '2018-10-01 00:00:00.000000', NULL, NULL, NULL, NULL, NULL, NULL, 'D6L8K2J0E3M3F9100', 'SUSAN DAWSON', '', '', '', '', '', '', '', 'H20181001050005261', '', NULL, NULL, 'Sonar', '', '', '5100', NULL, '2018-10-30 12:37:38.513335', '2018-10-30 12:37:38.513335'),
(43, 80626142, 'MID DBA 01', 'MID00000000001', 'TID100000001', NULL, NULL, '000', 'CHECK', 'ACH', '53', NULL, '2018-10-02 00:00:05', NULL, 'CHECK ACCEPTED', NULL, '', '1001', '2018-10-01 00:00:00.000000', NULL, NULL, NULL, NULL, NULL, NULL, 'D6L8K2J0E3M3F9101', 'SUSAN DAWSON', '', '', '', '', '', '', '', 'H20181001050005262', '', NULL, NULL, 'Sonar', '', '', '5101', NULL, '2018-10-30 12:37:38.513335', '2018-10-30 12:37:38.513335'),
(44, 80626143, 'MID DBA 01', 'MID00000000001', 'TID100000001', NULL, NULL, '000', 'CHECK', 'ACH', '53', NULL, '2018-10-02 00:00:05', NULL, 'CHECK ACCEPTED', NULL, '', '1002', '2018-10-01 00:00:00.000000', NULL, NULL, NULL, NULL, NULL, NULL, 'D6L8K2J0E3M3F9102', 'SUSAN DAWSON', '', '', '', '', '', '', '', 'H20181001050005263', '', NULL, NULL, 'Sonar', '', '', '5102', NULL, '2018-10-30 12:37:38.513335', '2018-10-30 12:37:38.513335'),
(45, 80626144, 'MID DBA 01', 'MID00000000001', 'TID100000001', NULL, NULL, '000', 'CHECK', 'ACH', '53', NULL, '2018-10-02 00:00:05', NULL, 'CHECK ACCEPTED', NULL, '', '1003', '2018-10-01 00:00:00.000000', NULL, NULL, NULL, NULL, NULL, NULL, 'D6L8K2J0E3M3F9103', 'SUSAN DAWSON', '', '', '', '', '', '', '', 'H20181001050005264', '', NULL, NULL, 'Sonar', '', '', '5103', NULL, '2018-10-30 12:37:38.513335', '2018-10-30 12:37:38.513335'),
(46, 80626145, 'MID DBA 01', 'MID00000000001', 'TID100000001', NULL, NULL, '000', 'CHECK', 'ACH', '53', NULL, '2018-10-02 00:00:05', NULL, 'CHECK ACCEPTED', NULL, '', '1004', '2018-10-01 00:00:00.000000', NULL, NULL, NULL, NULL, NULL, NULL, 'D6L8K2J0E3M3F9104', 'SUSAN DAWSON', '', '', '', '', '', '', '', 'H20181001050005265', '', NULL, NULL, 'Sonar', '', '', '5104', NULL, '2018-10-30 12:37:38.513335', '2018-10-30 12:37:38.513335'),
(47, 80626146, 'MID DBA 01', 'MID00000000001', 'TID100000001', NULL, NULL, '000', 'CHECK', 'ACH', '53', NULL, '2018-10-02 00:00:05', NULL, 'CHECK ACCEPTED', NULL, '', '1005', '2018-10-01 00:00:00.000000', NULL, NULL, NULL, NULL, NULL, NULL, 'D6L8K2J0E3M3F9105', 'SUSAN DAWSON', '', '', '', '', '', '', '', 'H20181001050005266', '', NULL, NULL, 'Sonar', '', '', '5105', NULL, '2018-10-30 12:37:38.513335', '2018-10-30 12:37:38.513335'),
(48, 80626147, 'MID DBA 01', 'MID00000000001', 'TID100000001', NULL, NULL, '000', 'CHECK', 'ACH', '53', NULL, '2018-10-02 00:00:05', NULL, 'CHECK ACCEPTED', NULL, '', '1006', '2018-10-01 00:00:00.000000', NULL, NULL, NULL, NULL, NULL, NULL, 'D6L8K2J0E3M3F9106', 'SUSAN DAWSON', '', '', '', '', '', '', '', 'H20181001050005267', '', NULL, NULL, 'Sonar', '', '', '5106', NULL, '2018-10-30 12:37:38.513335', '2018-10-30 12:37:38.513335'),
(49, 80626148, 'MID DBA 01', 'MID00000000001', 'TID100000001', NULL, NULL, '000', 'CHECK', 'ACH', '53', NULL, '2018-10-02 00:00:05', NULL, 'CHECK ACCEPTED', NULL, '', '1007', '2018-10-01 00:00:00.000000', NULL, NULL, NULL, NULL, NULL, NULL, 'D6L8K2J0E3M3F9107', 'SUSAN DAWSON', '', '', '', '', '', '', '', 'H20181001050005268', '', NULL, NULL, 'Sonar', '', '', '5107', NULL, '2018-10-30 12:37:38.513335', '2018-10-30 12:37:38.513335'),
(51, 80626149, 'MID DBA 01', 'MID00000000001', 'TID100000001', NULL, NULL, '000', 'CHECK', 'ACH', '53', NULL, '2018-10-02 00:00:05', NULL, 'CHECK ACCEPTED', NULL, '', '1008', '2018-10-01 00:00:00.000000', NULL, NULL, NULL, NULL, NULL, NULL, 'D6L8K2J0E3M3F9108', 'SUSAN DAWSON', '', '', '', '', '', '', '', 'H20181001050005269', '', NULL, NULL, 'Sonar', '', '', '5108', NULL, '2018-10-30 12:37:38.513335', '2018-10-30 12:37:38.513335'),
(52, 80626150, 'MID DBA 01', 'MID00000000001', 'TID100000001', NULL, NULL, '000', 'CHECK', 'ACH', '53', NULL, '2018-10-02 00:00:05', NULL, 'CHECK ACCEPTED', NULL, '', '1009', '2018-10-01 00:00:00.000000', NULL, NULL, NULL, NULL, NULL, NULL, 'D6L8K2J0E3M3F9109', 'SUSAN DAWSON', '', '', '', '', '', '', '', 'H20181001050005100', '', NULL, NULL, 'Sonar', '', '', '5109', NULL, '2018-10-30 12:37:38.513335', '2018-10-30 12:37:38.513335'),
(53, 80626151, 'MID DBA 01', 'MID00000000001', 'TID100000001', NULL, NULL, '000', 'CHECK', 'ACH', '53', NULL, '2018-10-02 00:00:05', NULL, 'CHECK ACCEPTED', NULL, '', '1010', '2018-10-01 00:00:00.000000', NULL, NULL, NULL, NULL, NULL, NULL, 'D6L8K2J0E3M3F9110', 'SUSAN DAWSON', '', '', '', '', '', '', '', 'H20181001050005101', '', NULL, NULL, 'Sonar', '', '', '5110', NULL, '2018-10-30 12:37:38.513335', '2018-10-30 12:37:38.513335'),
(54, 80626152, 'MID DBA 01', 'MID00000000001', 'TID100000001', NULL, NULL, '000', 'CHECK', 'ACH', '53', NULL, '2018-10-02 00:00:05', NULL, 'CHECK ACCEPTED', NULL, '', '1011', '2018-10-01 00:00:00.000000', NULL, NULL, NULL, NULL, NULL, NULL, 'D6L8K2J0E3M3F9111', 'SUSAN DAWSON', '', '', '', '', '', '', '', 'H20181001050005101', '', NULL, NULL, 'Sonar', '', '', '5111', NULL, '2018-10-30 12:37:38.513335', '2018-10-30 12:37:38.513335'),
(55, 80626153, 'MID DBA 01', 'MID00000000001', 'TID100000001', NULL, NULL, '000', 'CHECK', 'ACH', '53', NULL, '2018-10-02 00:00:05', NULL, 'CHECK ACCEPTED', NULL, '', '1012', '2018-10-01 00:00:00.000000', NULL, NULL, NULL, NULL, NULL, NULL, 'D6L8K2J0E3M3F9112', 'SUSAN DAWSON', '', '', '', '', '', '', '', 'H20181001050005101', '', NULL, NULL, 'Sonar', '', '', '5112', NULL, '2018-10-30 12:37:38.513335', '2018-10-30 12:37:38.513335'),
(56, 80626154, 'MID DBA 01', 'MID00000000001', 'TID100000001', NULL, NULL, '000', 'CHECK', 'ACH', '53', NULL, '2018-10-02 00:00:05', NULL, 'CHECK ACCEPTED', NULL, '', '1013', '2018-10-01 00:00:00.000000', NULL, NULL, NULL, NULL, NULL, NULL, 'D6L8K2J0E3M3F9113', 'SUSAN DAWSON', '', '', '', '', '', '', '', 'H20181001050005101', '', NULL, NULL, 'Sonar', '', '', '5113', NULL, '2018-10-30 12:37:38.513335', '2018-10-30 12:37:38.513335'),
(57, 80626155, 'MID DBA 01', 'MID00000000001', 'TID100000001', NULL, NULL, '000', 'CHECK', 'ACH', '53', NULL, '2018-10-02 00:00:05', NULL, 'CHECK ACCEPTED', NULL, '', '1014', '2018-10-01 00:00:00.000000', NULL, NULL, NULL, NULL, NULL, NULL, 'D6L8K2J0E3M3F9114', 'SUSAN DAWSON', '', '', '', '', '', '', '', 'H20181001050005101', '', NULL, NULL, 'Sonar', '', '', '5114', NULL, '2018-10-30 12:37:38.513335', '2018-10-30 12:37:38.513335'),
(58, 80626156, 'MID DBA 01', 'MID00000000001', 'TID100000001', NULL, NULL, '000', 'CHECK', 'ACH', '53', NULL, '2018-10-02 00:00:05', NULL, 'CHECK ACCEPTED', NULL, '', '1015', '2018-10-01 00:00:00.000000', NULL, NULL, NULL, NULL, NULL, NULL, 'D6L8K2J0E3M3F9115', 'SUSAN DAWSON', '', '', '', '', '', '', '', 'H20181001050005101', '', NULL, NULL, 'Sonar', '', '', '5115', NULL, '2018-10-30 12:37:38.513335', '2018-10-30 12:37:38.513335'),
(59, 80626200, 'MID DBA 03', 'MID00000000003', 'TESTTERMINAL', NULL, NULL, '000', 'CHECK', 'ACH', '10', NULL, '2018-10-03 00:00:09', NULL, 'CHECK ACCEPTED', NULL, '', '1000', '2018-10-02 00:00:00.000000', NULL, NULL, NULL, NULL, NULL, NULL, 'J2A9K8K3F6G19100', 'Sport America', '', '', '', '', '', '', '', 'G20181001050007100', '', NULL, NULL, 'Sonar', '', '', '5800', NULL, '2018-10-30 12:37:38.522536', '2018-10-30 12:37:38.522536'),
(60, 80626201, 'MID DBA 03', 'MID00000000003', 'TESTTERMINAL', NULL, NULL, '000', 'CHECK', 'ACH', '11', NULL, '2018-10-03 00:00:09', NULL, 'CHECK ACCEPTED', NULL, '', '1001', '2018-10-02 00:00:00.000000', NULL, NULL, NULL, NULL, NULL, NULL, 'J2A9K8K3F6G19101', 'Sport America', '', '', '', '', '', '', '', 'G20181001050007101', '', NULL, NULL, 'Sonar', '', '', '5801', NULL, '2018-10-30 12:37:38.522536', '2018-10-30 12:37:38.522536'),
(61, 80626202, 'MID DBA 03', 'MID00000000003', 'TESTTERMINAL', NULL, NULL, '000', 'CHECK', 'ACH', '12', NULL, '2018-10-03 00:00:09', NULL, 'CHECK ACCEPTED', NULL, '', '1002', '2018-10-02 00:00:00.000000', NULL, NULL, NULL, NULL, NULL, NULL, 'J2A9K8K3F6G19102', 'Sport America', '', '', '', '', '', '', '', 'G20181001050007102', '', NULL, NULL, 'Sonar', '', '', '5802', NULL, '2018-10-30 12:37:38.522536', '2018-10-30 12:37:38.522536'),
(62, 80626203, 'MID DBA 03', 'MID00000000003', 'TESTTERMINAL', NULL, NULL, '000', 'CHECK', 'ACH', '13', NULL, '2018-10-03 00:00:09', NULL, 'CHECK ACCEPTED', NULL, '', '1003', '2018-10-02 00:00:00.000000', NULL, NULL, NULL, NULL, NULL, NULL, 'J2A9K8K3F6G19103', 'Sport America', '', '', '', '', '', '', '', 'G20181001050007103', '', NULL, NULL, 'Sonar', '', '', '5803', NULL, '2018-10-30 12:37:38.522536', '2018-10-30 12:37:38.522536'),
(63, 80626204, 'MID DBA 03', 'MID00000000003', 'TESTTERMINAL', NULL, NULL, '000', 'CHECK', 'ACH', '14', NULL, '2018-10-03 00:00:09', NULL, 'CHECK ACCEPTED', NULL, '', '1004', '2018-10-02 00:00:00.000000', NULL, NULL, NULL, NULL, NULL, NULL, 'J2A9K8K3F6G19104', 'Sport America', '', '', '', '', '', '', '', 'G20181001050007104', '', NULL, NULL, 'Sonar', '', '', '5804', NULL, '2018-10-30 12:37:38.522536', '2018-10-30 12:37:38.522536'),
(64, 80626205, 'MID DBA 03', 'MID00000000003', 'TESTTERMINAL', NULL, NULL, '000', 'CHECK', 'ACH', '15', NULL, '2018-10-03 00:00:09', NULL, 'CHECK ACCEPTED', NULL, '', '1005', '2018-10-02 00:00:00.000000', NULL, NULL, NULL, NULL, NULL, NULL, 'J2A9K8K3F6G19105', 'Sport America', '', '', '', '', '', '', '', 'G20181001050007105', '', NULL, NULL, 'Sonar', '', '', '5805', NULL, '2018-10-30 12:37:38.522536', '2018-10-30 12:37:38.522536'),
(65, 80626206, 'MID DBA 03', 'MID00000000003', 'TESTTERMINAL', NULL, NULL, '000', 'CHECK', 'ACH', '16', NULL, '2018-10-03 00:00:09', NULL, 'CHECK ACCEPTED', NULL, '', '1006', '2018-10-02 00:00:00.000000', NULL, NULL, NULL, NULL, NULL, NULL, 'J2A9K8K3F6G19106', 'Sport America', '', '', '', '', '', '', '', 'G20181001050007106', '', NULL, NULL, 'Sonar', '', '', '5806', NULL, '2018-10-30 12:37:38.522536', '2018-10-30 12:37:38.522536'),
(66, 80626207, 'MID DBA 03', 'MID00000000003', 'TESTTERMINAL', NULL, NULL, '000', 'CHECK', 'ACH', '17', NULL, '2018-10-03 00:00:09', NULL, 'CHECK ACCEPTED', NULL, '', '1007', '2018-10-02 00:00:00.000000', NULL, NULL, NULL, NULL, NULL, NULL, 'J2A9K8K3F6G19107', 'Sport America', '', '', '', '', '', '', '', 'G20181001050007107', '', NULL, NULL, 'Sonar', '', '', '5807', NULL, '2018-10-30 12:37:38.522536', '2018-10-30 12:37:38.522536'),
(67, 80626208, 'MID DBA 03', 'MID00000000003', 'TESTTERMINAL', NULL, NULL, '000', 'CHECK', 'ACH', '18', NULL, '2018-10-03 00:00:09', NULL, 'CHECK ACCEPTED', NULL, '', '1008', '2018-10-02 00:00:00.000000', NULL, NULL, NULL, NULL, NULL, NULL, 'J2A9K8K3F6G19108', 'Sport America', '', '', '', '', '', '', '', 'G20181001050007108', '', NULL, NULL, 'Sonar', '', '', '5808', NULL, '2018-10-30 12:37:38.522536', '2018-10-30 12:37:38.522536'),
(68, 80626209, 'MID DBA 03', 'MID00000000003', 'TESTTERMINAL', NULL, NULL, '000', 'CHECK', 'ACH', '19', NULL, '2018-10-03 00:00:09', NULL, 'CHECK ACCEPTED', NULL, '', '1009', '2018-10-02 00:00:00.000000', NULL, NULL, NULL, NULL, NULL, NULL, 'J2A9K8K3F6G19109', 'Sport America', '', '', '', '', '', '', '', 'G20181001050007109', '', NULL, NULL, 'Sonar', '', '', '5809', NULL, '2018-10-30 12:37:38.522536', '2018-10-30 12:37:38.522536'),
(69, 80626210, 'MID DBA 03', 'MID00000000003', 'TESTTERMINAL', NULL, NULL, '000', 'CHECK', 'ACH', '20', NULL, '2018-10-03 00:00:09', NULL, 'CHECK ACCEPTED', NULL, '', '1010', '2018-10-02 00:00:00.000000', NULL, NULL, NULL, NULL, NULL, NULL, 'J2A9K8K3F6G19110', 'Sport America', '', '', '', '', '', '', '', 'G20181001050007110', '', NULL, NULL, 'Sonar', '', '', '5810', NULL, '2018-10-30 12:37:38.522536', '2018-10-30 12:37:38.522536'),
(70, 80626211, 'MID DBA 03', 'MID00000000003', 'TESTTERMINAL', NULL, NULL, '000', 'CHECK', 'ACH', '21', NULL, '2018-10-03 00:00:09', NULL, 'CHECK ACCEPTED', NULL, '', '1011', '2018-10-02 00:00:00.000000', NULL, NULL, NULL, NULL, NULL, NULL, 'J2A9K8K3F6G19111', 'Sport America', '', '', '', '', '', '', '', 'G20181001050007111', '', NULL, NULL, 'Sonar', '', '', '5811', NULL, '2018-10-30 12:37:38.522536', '2018-10-30 12:37:38.522536'),
(71, 80626212, 'MID DBA 03', 'MID00000000003', 'TESTTERMINAL', NULL, NULL, '000', 'CHECK', 'ACH', '22', NULL, '2018-10-03 00:00:09', NULL, 'CHECK ACCEPTED', NULL, '', '1012', '2018-10-02 00:00:00.000000', NULL, NULL, NULL, NULL, NULL, NULL, 'J2A9K8K3F6G19112', 'Sport America', '', '', '', '', '', '', '', 'G20181001050007112', '', NULL, NULL, 'Sonar', '', '', '5812', NULL, '2018-10-30 12:37:38.522536', '2018-10-30 12:37:38.522536'),
(72, 80626213, 'MID DBA 03', 'MID00000000003', 'TESTTERMINAL', NULL, NULL, '000', 'CHECK', 'ACH', '23', NULL, '2018-10-03 00:00:09', NULL, 'CHECK ACCEPTED', NULL, '', '1013', '2018-10-02 00:00:00.000000', NULL, NULL, NULL, NULL, NULL, NULL, 'J2A9K8K3F6G19113', 'Sport America', '', '', '', '', '', '', '', 'G20181001050007113', '', NULL, NULL, 'Sonar', '', '', '5813', NULL, '2018-10-30 12:37:38.522536', '2018-10-30 12:37:38.522536'),
(73, 80626214, 'MID DBA 03', 'MID00000000003', 'TESTTERMINAL', NULL, NULL, '000', 'CHECK', 'ACH', '24', NULL, '2018-10-03 00:00:09', NULL, 'CHECK ACCEPTED', NULL, '', '1014', '2018-10-02 00:00:00.000000', NULL, NULL, NULL, NULL, NULL, NULL, 'J2A9K8K3F6G19114', 'Sport America', '', '', '', '', '', '', '', 'G20181001050007114', '', NULL, NULL, 'Sonar', '', '', '5814', NULL, '2018-10-30 12:37:38.522536', '2018-10-30 12:37:38.522536'),
(74, 80626215, 'MID DBA 03', 'MID00000000003', 'TESTTERMINAL', NULL, NULL, '000', 'CHECK', 'ACH', '25', NULL, '2018-10-03 00:00:09', NULL, 'CHECK ACCEPTED', NULL, '', '1015', '2018-10-02 00:00:00.000000', NULL, NULL, NULL, NULL, NULL, NULL, 'J2A9K8K3F6G19115', 'Sport America', '', '', '', '', '', '', '', 'G20181001050007115', '', NULL, NULL, 'Sonar', '', '', '5815', NULL, '2018-10-30 12:37:38.522536', '2018-10-30 12:37:38.522536'),
(75, 80626300, 'MID DBA 03', 'MID00000000003', 'TESTTERMINAL', NULL, NULL, '000', 'CHECK', 'ACH', '300', NULL, '2018-10-04 00:00:09', NULL, 'CHECK ACCEPTED', NULL, '', '300', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'J2A9K8K3F6G19200', 'Sport America', '', '', '', '', '', '', '', 'G20181001050009201', '', NULL, NULL, 'Sonar', '', '', '5200', NULL, '2019-10-30 12:37:38.522536', '2019-10-30 12:37:38.522536'),
(76, 80626301, 'MID DBA 03', 'MID00000000003', 'TESTTERMINAL', NULL, NULL, '000', 'CHECK', 'ACH', '301', NULL, '2018-10-04 00:00:09', NULL, 'CHECK ACCEPTED', NULL, '', '301', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'J2A9K8K3F6G19201', 'Sport America', '', '', '', '', '', '', '', 'G20181001050009202', '', NULL, NULL, 'Sonar', '', '', '5201', NULL, '2019-10-30 12:37:38.522536', '2019-10-30 12:37:38.522536'),
(77, 80626302, 'MID DBA 03', 'MID00000000003', 'TESTTERMINAL', NULL, NULL, '000', 'CHECK', 'ACH', '302', NULL, '2018-10-04 00:00:09', NULL, 'CHECK ACCEPTED', NULL, '', '302', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'J2A9K8K3F6G19202', 'Sport America', '', '', '', '', '', '', '', 'G20181001050009203', '', NULL, NULL, 'Sonar', '', '', '5202', NULL, '2019-10-30 12:37:38.522536', '2019-10-30 12:37:38.522536'),
(78, 80626303, 'MID DBA 03', 'MID00000000003', 'TESTTERMINAL', NULL, NULL, '000', 'CHECK', 'ACH', '303', NULL, '2018-10-04 00:00:09', NULL, 'CHECK ACCEPTED', NULL, '', '303', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'J2A9K8K3F6G19203', 'Sport America', '', '', '', '', '', '', '', 'G20181001050009204', '', NULL, NULL, 'Sonar', '', '', '5203', NULL, '2019-10-30 12:37:38.522536', '2019-10-30 12:37:38.522536'),
(79, 80626304, 'MID DBA 03', 'MID00000000003', 'TESTTERMINAL', NULL, NULL, '000', 'CHECK', 'ACH', '304', NULL, '2018-10-04 00:00:09', NULL, 'CHECK ACCEPTED', NULL, '', '304', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'J2A9K8K3F6G19204', 'Sport America', '', '', '', '', '', '', '', 'G20181001050009205', '', NULL, NULL, 'Sonar', '', '', '5204', NULL, '2019-10-30 12:37:38.522536', '2019-10-30 12:37:38.522536'),
(80, 80626305, 'MID DBA 03', 'MID00000000003', 'TESTTERMINAL', NULL, NULL, '000', 'CHECK', 'ACH', '305', NULL, '2018-10-04 00:00:09', NULL, 'CHECK ACCEPTED', NULL, '', '305', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'J2A9K8K3F6G19205', 'Sport America', '', '', '', '', '', '', '', 'G20181001050009206', '', NULL, NULL, 'Sonar', '', '', '5205', NULL, '2019-10-30 12:37:38.522536', '2019-10-30 12:37:38.522536'),
(81, 80626306, 'MID DBA 03', 'MID00000000003', 'TESTTERMINAL', NULL, NULL, '000', 'CHECK', 'ACH', '306', NULL, '2018-10-04 00:00:09', NULL, 'CHECK ACCEPTED', NULL, '', '306', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'J2A9K8K3F6G19206', 'Sport America', '', '', '', '', '', '', '', 'G20181001050009207', '', NULL, NULL, 'Sonar', '', '', '5206', NULL, '2019-10-30 12:37:38.522536', '2019-10-30 12:37:38.522536'),
(82, 80626307, 'MID DBA 03', 'MID00000000003', 'TESTTERMINAL', NULL, NULL, '000', 'CHECK', 'ACH', '307', NULL, '2018-10-04 00:00:09', NULL, 'CHECK ACCEPTED', NULL, '', '307', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'J2A9K8K3F6G19207', 'Sport America', '', '', '', '', '', '', '', 'G20181001050009208', '', NULL, NULL, 'Sonar', '', '', '5207', NULL, '2019-10-30 12:37:38.522536', '2019-10-30 12:37:38.522536'),
(83, 80626308, 'MID DBA 03', 'MID00000000003', 'TESTTERMINAL', NULL, NULL, '000', 'CHECK', 'ACH', '308', NULL, '2018-10-04 00:00:09', NULL, 'CHECK ACCEPTED', NULL, '', '308', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'J2A9K8K3F6G19208', 'Sport America', '', '', '', '', '', '', '', 'G20181001050009209', '', NULL, NULL, 'Sonar', '', '', '5208', NULL, '2019-10-30 12:37:38.522536', '2019-10-30 12:37:38.522536'),
(84, 80626309, 'MID DBA 03', 'MID00000000003', 'TESTTERMINAL', NULL, NULL, '000', 'CHECK', 'ACH', '309', NULL, '2018-10-04 00:00:09', NULL, 'CHECK ACCEPTED', NULL, '', '309', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'J2A9K8K3F6G19209', 'Sport America', '', '', '', '', '', '', '', 'G20181001050009210', '', NULL, NULL, 'Sonar', '', '', '5209', NULL, '2019-10-30 12:37:38.522536', '2019-10-30 12:37:38.522536'),
(85, 80626310, 'MID DBA 03', 'MID00000000003', 'TESTTERMINAL', NULL, NULL, '000', 'CHECK', 'ACH', '310', NULL, '2018-10-04 00:00:09', NULL, 'CHECK ACCEPTED', NULL, '', '310', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'J2A9K8K3F6G19210', 'Sport America', '', '', '', '', '', '', '', 'G20181001050009211', '', NULL, NULL, 'Sonar', '', '', '5210', NULL, '2019-10-30 12:37:38.522536', '2019-10-30 12:37:38.522536'),
(86, 80626311, 'MID DBA 03', 'MID00000000003', 'TESTTERMINAL', NULL, NULL, '000', 'CHECK', 'ACH', '311', NULL, '2018-10-04 00:00:09', NULL, 'CHECK ACCEPTED', NULL, '', '311', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'J2A9K8K3F6G19211', 'Sport America', '', '', '', '', '', '', '', 'G20181001050009212', '', NULL, NULL, 'Sonar', '', '', '5211', NULL, '2019-10-30 12:37:38.522536', '2019-10-30 12:37:38.522536'),
(87, 80626312, 'MID DBA 03', 'MID00000000003', 'TESTTERMINAL', NULL, NULL, '000', 'CHECK', 'ACH', '312', NULL, '2018-10-04 00:00:09', NULL, 'CHECK ACCEPTED', NULL, '', '312', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'J2A9K8K3F6G19212', 'Sport America', '', '', '', '', '', '', '', 'G20181001050009213', '', NULL, NULL, 'Sonar', '', '', '5212', NULL, '2019-10-30 12:37:38.522536', '2019-10-30 12:37:38.522536'),
(88, 80626313, 'MID DBA 03', 'MID00000000003', 'TESTTERMINAL', NULL, NULL, '000', 'CHECK', 'ACH', '313', NULL, '2018-10-04 00:00:09', NULL, 'CHECK ACCEPTED', NULL, '', '313', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'J2A9K8K3F6G19213', 'Sport America', '', '', '', '', '', '', '', 'G20181001050009214', '', NULL, NULL, 'Sonar', '', '', '5213', NULL, '2019-10-30 12:37:38.522536', '2019-10-30 12:37:38.522536');

-- ================== CCTransactions Data ==================
INSERT INTO `CCTransactions` (`ID`, `IPTransID`, `MerchantName`, `MerchantID`, `TerminalID`, `ProcessorID`, `MerchantAltID`, `TerminalAltID`, `AuthCode`, `ActionCode`, `RequestType`, `TransactionType`, `TransAmount`, `AuthTransDate`, `FeeAmount`, `TaxAmount`, `SettlementDate`, `SettlementAmount`, `SettlementID`, `ApprovalStatus`, `Token`, `CardBin`, `CardLastFour`, `CardType`, `CurrencyCode`, `ExpDate`, `SafeCardNum`, `CardHolderName`, `Address`, `City`, `State`, `ZipCode`, `Country`, `Phone`, `Email`, `AVSResponse`, `CVVResponse`, `BankTransID`, `ExternalTransID`, `TransactionID`, `UserField1`, `UserField2`, `UserField3`, `POSTerminalID`, `POSAdditionalData`, `RetrievalReferenceNumber`, `VNumber`, `OrderNumber`, `ResponseText`, `CCProcessor`, `ACHProcessor`, `ChargebackID`, `CardNumber`, `ChargebackLoadDate`, `SecondRequestID`, `ChargebackSecondRequest`, `DateAdded`, `DateUpdated`) VALUES
(26, ********, 'MID DBA 01', 'MID00000000001', 'TESTTERMINAL', 12, '', '', '145195', '000', 'SALE', 'POS', 5786, '2018-05-04 19:59:47', 0, 196, '2018-05-03 00:00:00', 298, 935295, 'APPROVED', '4F0L2J1B7M8A9131', NULL, NULL, 'V', NULL, NULL, '', 'GOMEZ/MAYLING', '', '', '', '', '', '', '', '', 'M', '', '', '000180503205946591', '', '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-03 21:31:50.083764', '2018-10-25 13:42:56.015400'),
(27, ********, 'MID DBA 01', 'MID00000000001', 'TESTTERMINAL', 5727, '**********', '**********00', '000001', '000', 'SALE', 'REC', 4590, '2018-05-04 19:59:52', 0, 0, '2018-05-03 00:00:00', 4590, 935796, 'APPROVED', '4A2C6G3K0G1J1484', '466186', '1484', 'V', NULL, NULL, '466186******1484', 'Luz Garcia', '328 Ave E', 'Moore Haven', 'FL', '33471', '', '8632271498', '<EMAIL>', 'Z', '', '1220609451', '812400202963', '180504005952171D2R', '2711', '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-03 21:31:50.084778', '2018-07-11 16:22:39.785970'),
(28, 74376890, 'MID DBA 01', 'MID00000000001', 'TESTTERMINAL', 12, '', '', '000015', '000', 'AUTHONLY', 'REC', 4575, '2018-05-04 20:00:04', 0, 0, '2018-05-03 19:59:52', 650, 935299, 'APPROVED', '51B3E5L5E8G83208', NULL, NULL, 'V', NULL, NULL, '', 'Mary Jones', '599 River Street', 'Troy', 'NY', '12180', '', '3722526583', '<EMAIL>', 'Y', '', '', '', 'IP03050604WNDDHZFK', 'massivemesh.net', 'Marijones1013.mj', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-03 21:31:50.085287', '2018-10-25 13:42:56.015400'),
(29, 74376891, 'MID DBA 01', 'MID00000000001', 'TID100000001', 5727, '0016508855', '001650885500', '402439', '000', 'SALE', 'REC', 10000, '2018-05-05 20:00:24', 0, 0, '2018-05-03 00:00:00', 10000, 952893, 'APPROVED', '54F7H0D1G1B38887', '546208', '8887', 'M', NULL, NULL, '546208******8887', 'Michael Hayes', 'PO Box 3532', 'Homer', 'AK', '99603', '', '', '', 'Y', '', '1220610809', '812401402439', '1805040100239531MN', '1226', 'ONLINE', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-03 21:31:50.085789', '2018-07-11 16:22:39.785970'),
(30, 74376892, 'MID DBA 02', 'MID00000000002', 'TID100000002', 5727, '0016481998', '001648199800', '603992', '000', 'SALE', 'REC', 6830, '2018-05-05 20:00:27', 0, 0, '2018-05-03 00:00:00', 6830, 940935, 'APPROVED', '4H2F6J1M3K7K6225', '462161', '6225', 'V', NULL, NULL, '462161******6225', 'Michael Armstrong ', '7141 Sabre Trl', 'Amarillo ', 'TX', '79124', '', '', '', 'Y', '', '1220610907', '812401603992', '180504010026999HCR', '68320', 'ONLINE', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-03 21:31:50.086260', '2018-07-11 16:22:39.785970'),
(31, 74376893, 'MID DBA 02', 'MID00000000002', 'TESTTERMINAL', 12, '', '', '446036', '000', 'SALE', 'I', 6995, '2018-05-03 20:00:33', 0, 0, '2018-05-02 00:00:00', 200, 935300, 'APPROVED', '4F1A2C6E2A9M9724', NULL, NULL, 'V', NULL, NULL, '', 'Doug Truax', '209 S 18th St', 'La Grande', 'OR', '97850', 'USA', '', '', 'Y', '', '', '', '108649061123210000', '', '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-03 21:31:50.086701', '2018-10-25 13:42:56.015400'),
(32, 74376896, 'MID DBA 02', 'MID00000000002', 'TID100000001', 12, '', '', '029907', '000', 'SALE', 'I', 3995, '2018-05-03 20:00:35', 0, 0, '2018-05-02 00:00:00', 320, 935301, 'APPROVED', '4B1C2A9D6G3K4495', NULL, NULL, 'V', NULL, NULL, '', 'Troy D Davis', '1410 Jackson Ave', 'La Grande', 'OR', '97850-3024', 'USA', '', '', 'Y', '', '', '', '109379900123210002', '', '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-03 21:31:50.087168', '2018-10-25 13:42:56.015400'),
(33, 74376900, 'MID DBA 03', 'MID00000000003', 'TID100000002', 12, '', '', '00398A', '000', 'SALE', 'I', 3995, '2018-05-03 20:00:35', 0, 0, '2018-05-02 00:00:00', 523, 935302, 'APPROVED', '4E6D4K1D7G7D1186', NULL, NULL, 'V', NULL, NULL, '', 'Susan Conley', 'PO Box 6/403 E 4th St.', 'Joseph', 'OR', '97846', 'USA', '', '', 'Y', '', '', '', '148472800123210003', '', '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-03 21:31:50.087577', '2018-10-25 13:42:56.015400'),
(34, 74376902, 'MID DBA 03', 'MID00000000003', 'TESTTERMINAL', 12, '', '', '537156', '000', 'SALE', 'I', 3995, '2018-05-03 20:00:36', 0, 0, '2018-05-02 00:00:00', 129, 935303, 'APPROVED', '4J6G7B8G0A1F0507', NULL, NULL, 'V', NULL, NULL, '', 'Amy Tomlinson', '1908 3rd St Apt 2', 'La Grande', 'OR', '97850', 'USA', '', '', 'Y', '', '', '', '287007057123210004', '', '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-03 21:31:50.088007', '2018-10-25 13:42:56.015400'),
(35, 74376903, 'MID DBA 03', 'MID00000000003', 'TESTTERMINAL', 5727, '', '', '023000', '000', 'SALE', 'I', 3995, '2018-05-03 20:00:37', 0, 0, '2018-05-02 00:00:00', 189, 935304, 'APPROVED', '4L7A8B8L5K0K4295', NULL, NULL, 'V', NULL, NULL, '', 'Richard Seymour', '67730 Promise Rd', 'Wallowa', 'OR', '97885', 'USA', '', '', 'Y', '', '', '', '294169820123210005', '', '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-03 21:31:50.088460', '2018-10-25 13:42:56.015400'),
(36, 74376904, 'MID DBA 01', 'MID00000000001', 'TID100000001', 12, '', '', '023001', '000', 'SALE', 'I', 3995, '2018-05-03 20:00:37', 0, 0, '2018-05-02 00:00:00', 237, 935305, 'APPROVED', '4L7A8B8L5K0K4295', NULL, NULL, 'V', NULL, NULL, '', 'Richard Seymour', '67730 Promise Rd', 'Wallowa', 'OR', '97885', 'USA', '', '', 'Y', '', '', '', '294169820123210005', '', '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-03 21:31:50.088460', '2018-10-25 13:42:56.015400'),
(37, 74376905, 'MID DBA 01', 'MID00000000001', 'TID100000001', 5727, '', '', '023001', '000', 'SALE', 'I', 3995, '2018-05-03 20:00:37', 0, 0, '2018-05-02 00:00:00', 333, 935306, 'APPROVED', '4L7A8B8L5K0K4296', NULL, NULL, 'V', NULL, NULL, '', 'Richard Seymour', '67730 Promise Rd', 'Wallowa', 'OR', '97885', 'USA', '', '', 'Y', '', '', '', '294169820123210005', '', '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-03 21:31:50.088460', '2018-10-25 13:42:56.015400'),
(38, 74376906, 'MID DBA 02', 'MID00000000002', 'TID100000002', 12, '', '', '023002', '000', 'SALE', 'I', 3995, '2018-05-03 20:00:37', 0, 0, NULL, NULL, NULL, 'APPROVED', '4L7A8B8L5K0K4297', NULL, NULL, 'V', NULL, NULL, '', 'Richard Seymour', '67730 Promise Rd', 'Wallowa', 'OR', '97885', 'USA', '', '', 'Y', '', '', '', '294169820123210005', '', '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-03 21:31:50.088460', '2018-10-25 13:42:56.015400'),
(39, 74376907, 'MID DBA 03', 'MID00000000003', 'TESTTERMINAL', 12, '', '', '023003', '000', 'SALE', 'I', 3995, '2018-05-03 20:00:37', 0, 0, NULL, NULL, NULL, 'APPROVED', '4L7A8B8L5K0K4298', NULL, NULL, 'V', NULL, NULL, '', 'Richard Seymour', '67730 Promise Rd', 'Wallowa', 'OR', '97885', 'USA', '', '', 'Y', '', '', '', '294169820123210005', '', '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-03 21:31:50.088460', '2018-10-25 13:42:56.015400'),
(40, 74376908, 'MID DBA 01', 'MID00000000001', 'TID100000001', 5727, '', '', '023004', '000', 'SALE', 'REC', 3995, '2018-05-03 20:00:37', 0, 0, NULL, NULL, NULL, 'APPROVED', '4L7A8B8L5K0K4299', NULL, NULL, 'V', NULL, NULL, '', 'Richard Seymour', '67730 Promise Rd', 'Wallowa', 'OR', '97885', 'USA', '', '', 'Y', '', '', '', '294169820123210005', '', '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-03 21:31:50.088460', '2018-10-25 13:42:56.015400'),
(41, 74376909, 'MID DBA 02', 'MID00000000002', 'TID100000002', 12, '', '', '023005', '000', 'SALE', 'I', 3995, '2018-05-03 20:00:37', 0, 0, NULL, NULL, NULL, 'APPROVED', '4L7A8B8L5K0K4100', NULL, NULL, 'V', NULL, NULL, '', 'Richard Seymour', '67730 Promise Rd', 'Wallowa', 'OR', '97885', 'USA', '', '', 'Y', '', '', '', '294169820123210005', '', '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-03 21:31:50.088460', '2018-10-25 13:42:56.015400'),
(42, 74376910, 'MID DBA 03', 'MID00000000003', 'TESTTERMINAL', 12, '', '', '023006', '000', 'SALE', 'POS', 3995, '2018-05-03 20:00:37', 0, 0, NULL, NULL, NULL, 'APPROVED', '4L7A8B8L5K0K4101', NULL, NULL, 'V', NULL, NULL, '', 'Richard Seymour', '67730 Promise Rd', 'Wallowa', 'OR', '97885', 'USA', '', '', 'Y', '', '', '', '294169820123210005', '', '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-03 21:31:50.088460', '2018-10-25 13:42:56.015400'),
(43, 74376911, 'MID DBA 01', 'MID00000000001', 'TID100000001', 5727, '', '', '023007', '000', 'SALE', 'I', 3995, '2018-05-03 20:00:37', 0, 0, NULL, NULL, NULL, 'APPROVED', '4L7A8B8L5K0K4102', NULL, NULL, 'V', NULL, NULL, '', 'Richard Seymour', '67730 Promise Rd', 'Wallowa', 'OR', '97885', 'USA', '', '', 'Y', '', '', '', '294169820123210005', '', '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-03 21:31:50.088460', '2018-10-25 13:42:56.015400'),
(44, 74376912, 'MID DBA 02', 'MID00000000002', 'TID100000002', 12, '', '', '023008', '000', 'SALE', 'REC', 3995, '2018-05-03 20:00:37', 0, 0, NULL, NULL, NULL, 'APPROVED', '4L7A8B8L5K0K4103', NULL, NULL, 'V', NULL, NULL, '', 'Richard Seymour', '67730 Promise Rd', 'Wallowa', 'OR', '97885', 'USA', '', '', 'Y', '', '', '', '294169820123210005', '', '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-03 21:31:50.088460', '2018-10-25 13:42:56.015400'),
(45, 74376913, 'MID DBA 03', 'MID00000000003', 'TESTTERMINAL', 12, '', '', '023009', '000', 'SALE', 'I', 4000, '2018-05-03 20:00:37', 0, 0, NULL, NULL, NULL, 'APPROVED', '4L7A8B8L5K0K4104', NULL, NULL, 'M', NULL, NULL, '', 'Richard Seymour', '67730 Promise Rd', 'Wallowa', 'OR', '97885', 'USA', '', '', 'Y', '', '', '', '294169820123210005', '', '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-03 21:31:50.088460', '2018-10-25 13:42:56.015400'),
(46, 74376914, 'MID DBA 01', 'MID00000000001', 'TID100000001', 12, '', '', '023010', '000', 'SALE', 'I', 4001, '2018-05-03 20:00:37', 0, 0, NULL, NULL, NULL, 'APPROVED', '4L7A8B8L5K0K4105', NULL, NULL, 'M', NULL, NULL, '', 'Richard Seymour', '67730 Promise Rd', 'Wallowa', 'OR', '97885', 'USA', '', '', 'Y', '', '', '', '294169820123210005', '', '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-03 21:31:50.088460', '2018-10-25 13:42:56.015400'),
(47, 74376915, 'MID DBA 02', 'MID00000000002', 'TID100000002', 5727, '', '', '023011', '000', 'SALE', 'I', 4200, '2018-05-03 20:00:37', 0, 0, NULL, NULL, NULL, 'APPROVED', '4L7A8B8L5K0K4106', NULL, NULL, 'V', NULL, NULL, '', 'Richard Seymour', '67730 Promise Rd', 'Wallowa', 'OR', '97885', 'USA', '', '', 'Y', '', '', '', '294169820123210005', '', '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-03 21:31:50.088460', '2018-10-25 13:42:56.015400'),
(48, 74376916, 'MID DBA 03', 'MID00000000003', 'TESTTERMINAL', 12, '', '', '023012', '000', 'SALE', 'I', 2850, '2018-05-03 20:00:37', 0, 0, NULL, NULL, NULL, 'APPROVED', '4L7A8B8L5K0K4107', NULL, NULL, 'V', NULL, NULL, '', 'Richard Seymour', '67730 Promise Rd', 'Wallowa', 'OR', '97885', 'USA', '', '', 'Y', '', '', '', '294169820123210005', '', '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-03 21:31:50.088460', '2018-10-25 13:42:56.015400'),
(49, 74376917, 'MID DBA 01', 'MID00000000001', 'TID100000001', 12, '', '', '023013', '000', 'SALE', 'I', 2995, '2018-05-03 20:00:37', 0, 0, NULL, NULL, NULL, 'APPROVED', '4L7A8B8L5K0K4108', NULL, NULL, 'V', NULL, NULL, '', 'Richard Seymour', '67730 Promise Rd', 'Wallowa', 'OR', '97885', 'USA', '', '', 'Y', '', '', '', '294169820123210005', '', '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-03 21:31:50.088460', '2018-10-25 13:42:56.015400'),
(50, 74376918, 'MID DBA 02', 'MID00000000002', 'TID100000002', 5727, '', '', '023014', '000', 'SALE', 'I', 1990, '2018-05-03 20:00:37', 0, 0, NULL, NULL, NULL, 'APPROVED', '4L7A8B8L5K0K4109', NULL, NULL, 'M', NULL, NULL, '', 'Richard Seymour', '67730 Promise Rd', 'Wallowa', 'OR', '97885', 'USA', '', '', 'Y', '', '', '', '294169820123210005', '', '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-03 21:31:50.088460', '2018-10-25 13:42:56.015400'),
(90, 74376100, 'MID DBA 02', 'MID00000000002', 'TID100000002', 5100, '', '', '023100', '000', 'SALE', 'I', 1000, '2018-05-03 20:00:37', 0, 0, NULL, NULL, NULL, 'APPROVED', '4L7A8B8L5K0K4109', NULL, NULL, 'M', NULL, NULL, '', 'Richard Seymour', '67730 Promise Rd', 'Wallowa', 'OR', '97100', 'USA', '', '', 'Y', '', '', '', '294169820123220000', '', '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-03 21:31:50.088460', '2018-10-25 13:42:56.015400'),
(91, 74376101, 'MID DBA 02', 'MID00000000002', 'TID100000002', 5101, '', '', '023101', '000', 'SALE', 'I', 1001, '2018-05-03 20:00:37', 0, 0, NULL, NULL, NULL, 'APPROVED', '4L7A8B8L5K0K4110', NULL, NULL, 'M', NULL, NULL, '', 'Richard Seymour', '67730 Promise Rd', 'Wallowa', 'OR', '97101', 'USA', '', '', 'Y', '', '', '', '294169820123220001', '', '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-03 21:31:50.088460', '2018-10-25 13:42:56.015400'),
(92, 74376102, 'MID DBA 02', 'MID00000000002', 'TID100000002', 5102, '', '', '023102', '000', 'SALE', 'I', 1002, '2018-05-03 20:00:37', 0, 0, NULL, NULL, NULL, 'APPROVED', '4L7A8B8L5K0K4111', NULL, NULL, 'M', NULL, NULL, '', 'Richard Seymour', '67730 Promise Rd', 'Wallowa', 'OR', '97102', 'USA', '', '', 'Y', '', '', '', '294169820123220002', '', '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-03 21:31:50.088460', '2018-10-25 13:42:56.015400'),
(93, 74376103, 'MID DBA 02', 'MID00000000002', 'TID100000002', 5103, '', '', '023103', '000', 'SALE', 'I', 1003, '2018-05-03 20:00:37', 0, 0, NULL, NULL, NULL, 'APPROVED', '4L7A8B8L5K0K4112', NULL, NULL, 'M', NULL, NULL, '', 'Richard Seymour', '67730 Promise Rd', 'Wallowa', 'OR', '97103', 'USA', '', '', 'Y', '', '', '', '294169820123220003', '', '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-03 21:31:50.088460', '2018-10-25 13:42:56.015400'),
(94, 74376104, 'MID DBA 02', 'MID00000000002', 'TID100000002', 5104, '', '', '023104', '000', 'SALE', 'I', 1004, '2018-05-03 20:00:37', 0, 0, NULL, NULL, NULL, 'APPROVED', '4L7A8B8L5K0K4113', NULL, NULL, 'M', NULL, NULL, '', 'Richard Seymour', '67730 Promise Rd', 'Wallowa', 'OR', '97104', 'USA', '', '', 'Y', '', '', '', '294169820123220004', '', '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-03 21:31:50.088460', '2018-10-25 13:42:56.015400'),
(95, 74376105, 'MID DBA 02', 'MID00000000002', 'TID100000002', 5105, '', '', '023105', '000', 'SALE', 'I', 1005, '2018-05-03 20:00:37', 0, 0, NULL, NULL, NULL, 'APPROVED', '4L7A8B8L5K0K4114', NULL, NULL, 'M', NULL, NULL, '', 'Richard Seymour', '67730 Promise Rd', 'Wallowa', 'OR', '97105', 'USA', '', '', 'Y', '', '', '', '294169820123220005', '', '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-03 21:31:50.088460', '2018-10-25 13:42:56.015400'),
(96, 74376106, 'MID DBA 02', 'MID00000000002', 'TID100000002', 5106, '', '', '023106', '000', 'SALE', 'I', 1006, '2018-05-03 20:00:37', 0, 0, NULL, NULL, NULL, 'APPROVED', '4L7A8B8L5K0K4115', NULL, NULL, 'M', NULL, NULL, '', 'Richard Seymour', '67730 Promise Rd', 'Wallowa', 'OR', '97106', 'USA', '', '', 'Y', '', '', '', '294169820123220006', '', '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-03 21:31:50.088460', '2018-10-25 13:42:56.015400'),
(97, 74376107, 'MID DBA 02', 'MID00000000002', 'TID100000002', 5107, '', '', '023107', '000', 'SALE', 'I', 1007, '2018-05-03 20:00:37', 0, 0, NULL, NULL, NULL, 'APPROVED', '4L7A8B8L5K0K4116', NULL, NULL, 'M', NULL, NULL, '', 'Richard Seymour', '67730 Promise Rd', 'Wallowa', 'OR', '97107', 'USA', '', '', 'Y', '', '', '', '294169820123220007', '', '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-03 21:31:50.088460', '2018-10-25 13:42:56.015400'),
(98, 74376108, 'MID DBA 02', 'MID00000000002', 'TID100000002', 5108, '', '', '023108', '000', 'SALE', 'I', 1008, '2018-05-03 20:00:37', 0, 0, NULL, NULL, NULL, 'APPROVED', '4L7A8B8L5K0K4117', NULL, NULL, 'M', NULL, NULL, '', 'Richard Seymour', '67730 Promise Rd', 'Wallowa', 'OR', '97108', 'USA', '', '', 'Y', '', '', '', '294169820123220008', '', '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-03 21:31:50.088460', '2018-10-25 13:42:56.015400'),
(99, 74376109, 'MID DBA 02', 'MID00000000002', 'TID100000002', 5109, '', '', '023109', '000', 'SALE', 'I', 1009, '2018-05-03 20:00:37', 0, 0, NULL, NULL, NULL, 'APPROVED', '4L7A8B8L5K0K4118', NULL, NULL, 'M', NULL, NULL, '', 'Richard Seymour', '67730 Promise Rd', 'Wallowa', 'OR', '97109', 'USA', '', '', 'Y', '', '', '', '294169820123220009', '', '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-03 21:31:50.088460', '2018-10-25 13:42:56.015400'),
(100, 74376110, 'MID DBA 02', 'MID00000000002', 'TID100000002', 5110, '', '', '023110', '000', 'SALE', 'I', 1010, '2018-05-03 20:00:37', 0, 0, NULL, NULL, NULL, 'APPROVED', '4L7A8B8L5K0K4119', NULL, NULL, 'M', NULL, NULL, '', 'Richard Seymour', '67730 Promise Rd', 'Wallowa', 'OR', '97110', 'USA', '', '', 'Y', '', '', '', '294169820123220010', '', '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-03 21:31:50.088460', '2018-10-25 13:42:56.015400'),
(101, 74376111, 'MID DBA 02', 'MID00000000002', 'TID100000002', 5111, '', '', '023111', '000', 'SALE', 'I', 1011, '2018-05-03 20:00:37', 0, 0, NULL, NULL, NULL, 'APPROVED', '4L7A8B8L5K0K4120', NULL, NULL, 'M', NULL, NULL, '', 'Richard Seymour', '67730 Promise Rd', 'Wallowa', 'OR', '97111', 'USA', '', '', 'Y', '', '', '', '294169820123220011', '', '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-03 21:31:50.088460', '2018-10-25 13:42:56.015400'),
(102, 74376112, 'MID DBA 02', 'MID00000000002', 'TID100000002', 5112, '', '', '023112', '000', 'SALE', 'I', 1012, '2018-05-03 20:00:37', 0, 0, NULL, NULL, NULL, 'APPROVED', '4L7A8B8L5K0K4121', NULL, NULL, 'M', NULL, NULL, '', 'Richard Seymour', '67730 Promise Rd', 'Wallowa', 'OR', '97112', 'USA', '', '', 'Y', '', '', '', '294169820123220012', '', '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-03 21:31:50.088460', '2018-10-25 13:42:56.015400'),
(103, 74376113, 'MID DBA 02', 'MID00000000002', 'TID100000002', 5113, '', '', '023113', '000', 'SALE', 'I', 1013, '2018-05-03 20:00:37', 0, 0, NULL, NULL, NULL, 'APPROVED', '4L7A8B8L5K0K4122', NULL, NULL, 'M', NULL, NULL, '', 'Richard Seymour', '67730 Promise Rd', 'Wallowa', 'OR', '97113', 'USA', '', '', 'Y', '', '', '', '294169820123220013', '', '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-03 21:31:50.088460', '2018-10-25 13:42:56.015400'),
(104, 74376114, 'MID DBA 02', 'MID00000000002', 'TID100000002', 5114, '', '', '023114', '000', 'SALE', 'I', 1014, '2018-05-03 20:00:37', 0, 0, NULL, NULL, NULL, 'APPROVED', '4L7A8B8L5K0K4123', NULL, NULL, 'M', NULL, NULL, '', 'Richard Seymour', '67730 Promise Rd', 'Wallowa', 'OR', '97114', 'USA', '', '', 'Y', '', '', '', '294169820123220014', '', '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-03 21:31:50.088460', '2018-10-25 13:42:56.015400'),
(105, 74376115, 'MID DBA 02', 'MID00000000002', 'TID100000002', 5115, '', '', '023115', '000', 'SALE', 'I', 1015, '2018-05-03 20:00:37', 0, 0, NULL, NULL, NULL, 'APPROVED', '4L7A8B8L5K0K4124', NULL, NULL, 'M', NULL, NULL, '', 'Richard Seymour', '67730 Promise Rd', 'Wallowa', 'OR', '97115', 'USA', '', '', 'Y', '', '', '', '294169820123220015', '', '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-03 21:31:50.088460', '2018-10-25 13:42:56.015400'),
(106, 74376200, 'MID DBA 01', 'MID00000000001', 'TID100000001', 5727, '0016510000', '001650885400', '402500', '000', 'SALE', 'REC', 20001, '2018-05-05 20:00:24', 0, 0, '2018-05-03 00:00:00', 20001, 952701, 'APPROVED', '54F7H0D1G1B38300', '546300', '8300', 'M', NULL, NULL, '546208******8301', 'Michael Hayes', 'PO Box 3532', 'Homer', 'AK', '99301', '', '', '', 'Y', '', '1220610301', '812401402301', '1805040100239532MN', '1231', 'ONLINE', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-03 21:31:50.085789', '2018-07-11 16:22:39.785970'),
(107, 74376201, 'MID DBA 01', 'MID00000000001', 'TID100000001', 5728, '0016510001', '001650885401', '402501', '000', 'SALE', 'REC', 20002, '2018-05-05 20:00:24', 0, 0, '2018-05-03 00:00:00', 20002, 952702, 'APPROVED', '54F7H0D1G1B38301', '546301', '8301', 'Y', NULL, NULL, '546208******8302', 'Michael Hayes', 'PO Box 3532', 'Homer', 'AK', '99302', '', '', '', 'Y', '', '1220610302', '812401402302', '1805040100239533MN', '1232', 'ONLINE', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-03 21:31:50.085789', '2018-07-11 16:22:39.785970'),
(108, 74376202, 'MID DBA 01', 'MID00000000001', 'TID100000001', 5729, '0016510002', '001650885402', '402502', '000', 'SALE', 'REC', 20003, '2018-05-05 20:00:24', 0, 0, '2018-05-03 00:00:00', 20003, 952703, 'APPROVED', '54F7H0D1G1B38302', '546302', '8302', 'M', NULL, NULL, '546208******8303', 'Michael Hayes', 'PO Box 3532', 'Homer', 'AK', '99303', '', '', '', 'Y', '', '1220610303', '812401402303', '1805040100239534MN', '1233', 'ONLINE', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-03 21:31:50.085789', '2018-07-11 16:22:39.785970'),
(109, 74376203, 'MID DBA 01', 'MID00000000001', 'TID100000001', 5720, '0016510003', '001650885403', '402503', '000', 'SALE', 'REC', 20004, '2018-05-05 20:00:24', 0, 0, '2018-05-03 00:00:00', 20004, 952704, 'APPROVED', '54F7H0D1G1B38303', '546303', '8303', 'M', NULL, NULL, '546208******8304', 'Michael Hayes', 'PO Box 3532', 'Homer', 'AK', '99304', '', '', '', 'Y', '', '1220610304', '812401402304', '1805040100239535MN', '1234', 'ONLINE', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-03 21:31:50.085789', '2018-07-11 16:22:39.785970'),
(110, 74376204, 'MID DBA 01', 'MID00000000001', 'TID100000001', 5711, '0016510004', '001650885404', '402504', '000', 'SALE', 'REC', 20005, '2018-05-05 20:00:24', 0, 0, '2018-05-03 00:00:00', 20005, 952705, 'APPROVED', '54F7H0D1G1B38304', '546304', '8304', 'Y', NULL, NULL, '546208******8305', 'Michael Hayes', 'PO Box 3532', 'Homer', 'AK', '99305', '', '', '', 'Y', '', '1220610305', '812401402305', '1805040100239536MN', '1235', 'ONLINE', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-03 21:31:50.085789', '2018-07-11 16:22:39.785970'),
(111, 74376205, 'MID DBA 01', 'MID00000000001', 'TID100000001', 5712, '0016510005', '001650885405', '402505', '000', 'SALE', 'REC', 20006, '2018-05-05 20:00:24', 0, 0, '2018-05-03 00:00:00', 20006, 952706, 'APPROVED', '54F7H0D1G1B38305', '546305', '8305', 'M', NULL, NULL, '546208******8306', 'Michael Hayes', 'PO Box 3532', 'Homer', 'AK', '99306', '', '', '', 'Y', '', '1220610306', '812401402306', '1805040100239537MN', '1236', 'ONLINE', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-03 21:31:50.085789', '2018-07-11 16:22:39.785970'),
(112, 74376206, 'MID DBA 01', 'MID00000000001', 'TID100000001', 5713, '0016510006', '001650885406', '402506', '000', 'SALE', 'REC', 20007, '2018-05-05 20:00:24', 0, 0, '2018-05-03 00:00:00', 20007, 952707, 'APPROVED', '54F7H0D1G1B38306', '546306', '8306', 'M', NULL, NULL, '546208******8307', 'Michael Hayes', 'PO Box 3532', 'Homer', 'AK', '99307', '', '', '', 'Y', '', '1220610307', '812401402307', '1805040100239538MN', '1237', 'ONLINE', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-03 21:31:50.085789', '2018-07-11 16:22:39.785970'),
(113, 74376207, 'MID DBA 01', 'MID00000000001', 'TID100000001', 5714, '0016510007', '001650885407', '402507', '000', 'SALE', 'REC', 20008, '2018-05-05 20:00:24', 0, 0, '2018-05-03 00:00:00', 20008, 952708, 'APPROVED', '54F7H0D1G1B38307', '546307', '8307', 'Y', NULL, NULL, '546208******8308', 'Michael Hayes', 'PO Box 3532', 'Homer', 'AK', '99308', '', '', '', 'Y', '', '1220610308', '812401402308', '1805040100239539MN', '1238', 'ONLINE', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-03 21:31:50.085789', '2018-07-11 16:22:39.785970'),
(114, 74376208, 'MID DBA 01', 'MID00000000001', 'TID100000001', 5715, '0016510008', '001650885408', '402508', '000', 'SALE', 'REC', 20009, '2018-05-05 20:00:24', 0, 0, '2018-05-03 00:00:00', 20009, 952709, 'APPROVED', '54F7H0D1G1B38308', '546308', '8308', 'M', NULL, NULL, '546208******8309', 'Michael Hayes', 'PO Box 3532', 'Homer', 'AK', '99309', '', '', '', 'Y', '', '1220610309', '812401402309', '1805040100239521MN', '1239', 'ONLINE', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-03 21:31:50.085789', '2018-07-11 16:22:39.785970'),
(115, 74376209, 'MID DBA 01', 'MID00000000001', 'TID100000001', 5716, '0016510009', '001650885409', '402509', '000', 'SALE', 'REC', 20010, '2018-05-05 20:00:24', 0, 0, '2018-05-03 00:00:00', 20010, 952710, 'APPROVED', '54F7H0D1G1B38309', '546309', '8309', 'Y', NULL, NULL, '546208******8310', 'Michael Hayes', 'PO Box 3532', 'Homer', 'AK', '99310', '', '', '', 'Y', '', '1220610310', '812401402310', '1805040100239522MN', '1240', 'ONLINE', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-03 21:31:50.085789', '2018-07-11 16:22:39.785970'),
(116, 74376210, 'MID DBA 01', 'MID00000000001', 'TID100000001', 5717, '0016510010', '001650885410', '402510', '000', 'SALE', 'REC', 20011, '2018-05-05 20:00:24', 0, 0, '2018-05-03 00:00:00', 20011, 952711, 'APPROVED', '54F7H0D1G1B38310', '546310', '8310', 'M', NULL, NULL, '546208******8311', 'Michael Hayes', 'PO Box 3532', 'Homer', 'AK', '99311', '', '', '', 'Y', '', '1220610311', '812401402311', '1805040100239523MN', '1241', 'ONLINE', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-03 21:31:50.085789', '2018-07-11 16:22:39.785970'),
(117, 74376211, 'MID DBA 01', 'MID00000000001', 'TID100000001', 5718, '0016510011', '001650885411', '402511', '000', 'SALE', 'REC', 20012, '2018-05-05 20:00:24', 0, 0, '2018-05-03 00:00:00', 20012, 952712, 'APPROVED', '54F7H0D1G1B38311', '546311', '8311', 'Y', NULL, NULL, '546208******8312', 'Michael Hayes', 'PO Box 3532', 'Homer', 'AK', '99312', '', '', '', 'Y', '', '1220610312', '812401402312', '1805040100239524MN', '1242', 'ONLINE', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-03 21:31:50.085789', '2018-07-11 16:22:39.785970'),
(118, 74376212, 'MID DBA 01', 'MID00000000001', 'TID100000001', 5719, '0016510012', '001650885412', '402512', '000', 'SALE', 'REC', 20013, '2018-05-05 20:00:24', 0, 0, '2018-05-03 00:00:00', 20013, 952713, 'APPROVED', '54F7H0D1G1B38312', '546312', '8312', 'M', NULL, NULL, '546208******8313', 'Michael Hayes', 'PO Box 3532', 'Homer', 'AK', '99313', '', '', '', 'Y', '', '1220610313', '812401402313', '1805040100239525MN', '1243', 'ONLINE', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-03 21:31:50.085789', '2018-07-11 16:22:39.785970'),
(119, 74376213, 'MID DBA 01', 'MID00000000001', 'TID100000001', 5720, '0016510013', '001650885413', '402513', '000', 'SALE', 'REC', 20014, '2018-05-05 20:00:24', 0, 0, '2018-05-03 00:00:00', 20014, 952714, 'APPROVED', '54F7H0D1G1B38313', '546313', '8313', 'Y', NULL, NULL, '546208******8314', 'Michael Hayes', 'PO Box 3532', 'Homer', 'AK', '99314', '', '', '', 'Y', '', '1220610314', '812401402314', '1805040100239526MN', '1244', 'ONLINE', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-03 21:31:50.085789', '2018-07-11 16:22:39.785970'),
(120, 74376214, 'MID DBA 01', 'MID00000000001', 'TID100000001', 5721, '0016510014', '001650885414', '402514', '000', 'SALE', 'REC', 20015, '2018-05-05 20:00:24', 0, 0, '2018-05-03 00:00:00', 20015, 952715, 'APPROVED', '54F7H0D1G1B38314', '546314', '8314', 'M', NULL, NULL, '546208******8315', 'Michael Hayes', 'PO Box 3532', 'Homer', 'AK', '99315', '', '', '', 'Y', '', '1220610315', '812401402315', '1805040100239527MN', '1245', 'ONLINE', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-03 21:31:50.085789', '2018-07-11 16:22:39.785970'),
(121, 74376215, 'MID DBA 01', 'MID00000000001', 'TID100000001', 5722, '0016510015', '001650885415', '402515', '000', 'SALE', 'REC', 20016, '2018-05-05 20:00:24', 0, 0, '2018-05-03 00:00:00', 20016, 952716, 'APPROVED', '54F7H0D1G1B38315', '546315', '8315', 'M', NULL, NULL, '546208******8316', 'Michael Hayes', 'PO Box 3532', 'Homer', 'AK', '99316', '', '', '', 'Y', '', '1220610316', '812401402316', '1805040100239528MN', '1256', 'ONLINE', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-03 21:31:50.085789', '2018-07-11 16:22:39.785970'),
(122, 74376216, 'MID DBA 01', 'MID00000000001', 'TID100000001', 5723, '0016510016', '001650885416', '402516', '000', 'SALE', 'REC', 20017, '2018-05-05 20:00:24', 0, 0, '2018-05-03 00:00:00', 20017, 952717, 'APPROVED', '54F7H0D1G1B38316', '546316', '8316', 'M', NULL, NULL, '546208******8317', 'Michael Hayes', 'PO Box 3532', 'Homer', 'AK', '99317', '', '', '', 'Y', '', '1220610317', '812401402317', '1805040100239529MN', '1257', 'ONLINE', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-03 21:31:50.085789', '2018-07-11 16:22:39.785970'),
(123, 74376217, 'MID DBA 01', 'MID00000000001', 'TID100000001', 5724, '0016510017', '001650885417', '402517', '000', 'SALE', 'REC', 20018, '2018-05-05 20:00:24', 0, 0, '2018-05-03 00:00:00', 20018, 952718, 'APPROVED', '54F7H0D1G1B38317', '546317', '8317', 'M', NULL, NULL, '546208******8318', 'Michael Hayes', 'PO Box 3532', 'Homer', 'AK', '99318', '', '', '', 'Y', '', '1220610318', '812401402318', '1805040100239530MN', '1258', 'ONLINE', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-03 21:31:50.085789', '2018-07-11 16:22:39.785970'),
(124, 74376400, 'MID DBA 03', 'MID00000000003', 'TESTTERMINAL', 10, '', '', '024000', '000', 'SALE', 'I', 4000, '2018-05-03 20:00:37', 0, 0, NULL, NULL, NULL, 'APPROVED', '4L7A8B8L5K0K4291', NULL, NULL, 'V', NULL, NULL, '', 'Richard Seymour', '67730 Promise Rd', 'Wallowa', 'OR', '97401', 'USA', '', '', 'Y', '', '', '', '294169820123210401', '', '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-03 21:31:50.088460', '2018-10-25 13:42:56.015400'),
(125, 74376401, 'MID DBA 03', 'MID00000000003', 'TESTTERMINAL', 11, '', '', '024001', '000', 'SALE', 'I', 4001, '2018-05-03 20:00:37', 0, 0, NULL, NULL, NULL, 'APPROVED', '4L7A8B8L5K0K4292', NULL, NULL, 'V', NULL, NULL, '', 'Richard Seymour', '67730 Promise Rd', 'Wallowa', 'OR', '97402', 'USA', '', '', 'Y', '', '', '', '294169820123210402', '', '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-03 21:31:50.088460', '2018-10-25 13:42:56.015400'),
(126, 74376402, 'MID DBA 03', 'MID00000000003', 'TESTTERMINAL', 12, '', '', '024002', '000', 'SALE', 'I', 4002, '2018-05-03 20:00:37', 0, 0, NULL, NULL, NULL, 'APPROVED', '4L7A8B8L5K0K4293', NULL, NULL, 'V', NULL, NULL, '', 'Richard Seymour', '67730 Promise Rd', 'Wallowa', 'OR', '97403', 'USA', '', '', 'Y', '', '', '', '294169820123210403', '', '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-03 21:31:50.088460', '2018-10-25 13:42:56.015400'),
(127, 74376403, 'MID DBA 03', 'MID00000000003', 'TESTTERMINAL', 13, '', '', '024003', '000', 'SALE', 'I', 4003, '2018-05-03 20:00:37', 0, 0, NULL, NULL, NULL, 'APPROVED', '4L7A8B8L5K0K4294', NULL, NULL, 'V', NULL, NULL, '', 'Richard Seymour', '67730 Promise Rd', 'Wallowa', 'OR', '97404', 'USA', '', '', 'Y', '', '', '', '294169820123210404', '', '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-03 21:31:50.088460', '2018-10-25 13:42:56.015400'),
(128, 74376404, 'MID DBA 03', 'MID00000000003', 'TESTTERMINAL', 14, '', '', '024004', '000', 'SALE', 'I', 4004, '2018-05-03 20:00:37', 0, 0, NULL, NULL, NULL, 'APPROVED', '4L7A8B8L5K0K4295', NULL, NULL, 'V', NULL, NULL, '', 'Richard Seymour', '67730 Promise Rd', 'Wallowa', 'OR', '97405', 'USA', '', '', 'Y', '', '', '', '294169820123210405', '', '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-03 21:31:50.088460', '2018-10-25 13:42:56.015400'),
(129, 74376405, 'MID DBA 03', 'MID00000000003', 'TESTTERMINAL', 15, '', '', '024005', '000', 'SALE', 'I', 4005, '2018-05-03 20:00:37', 0, 0, NULL, NULL, NULL, 'APPROVED', '4L7A8B8L5K0K4296', NULL, NULL, 'V', NULL, NULL, '', 'Richard Seymour', '67730 Promise Rd', 'Wallowa', 'OR', '97406', 'USA', '', '', 'Y', '', '', '', '294169820123210406', '', '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-03 21:31:50.088460', '2018-10-25 13:42:56.015400'),
(130, 74376406, 'MID DBA 03', 'MID00000000003', 'TESTTERMINAL', 16, '', '', '024006', '000', 'SALE', 'I', 4006, '2018-05-03 20:00:37', 0, 0, NULL, NULL, NULL, 'APPROVED', '4L7A8B8L5K0K4297', NULL, NULL, 'V', NULL, NULL, '', 'Richard Seymour', '67730 Promise Rd', 'Wallowa', 'OR', '97407', 'USA', '', '', 'Y', '', '', '', '294169820123210407', '', '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-03 21:31:50.088460', '2018-10-25 13:42:56.015400'),
(131, 74376407, 'MID DBA 03', 'MID00000000003', 'TESTTERMINAL', 17, '', '', '024007', '000', 'SALE', 'I', 4007, '2018-05-03 20:00:37', 0, 0, NULL, NULL, NULL, 'APPROVED', '4L7A8B8L5K0K4281', NULL, NULL, 'V', NULL, NULL, '', 'Richard Seymour', '67730 Promise Rd', 'Wallowa', 'OR', '97408', 'USA', '', '', 'Y', '', '', '', '294169820123210408', '', '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-03 21:31:50.088460', '2018-10-25 13:42:56.015400'),
(132, 74376408, 'MID DBA 03', 'MID00000000003', 'TESTTERMINAL', 18, '', '', '024008', '000', 'SALE', 'I', 4008, '2018-05-03 20:00:37', 0, 0, NULL, NULL, NULL, 'APPROVED', '4L7A8B8L5K0K4282', NULL, NULL, 'V', NULL, NULL, '', 'Richard Seymour', '67730 Promise Rd', 'Wallowa', 'OR', '97409', 'USA', '', '', 'Y', '', '', '', '294169820123210409', '', '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-03 21:31:50.088460', '2018-10-25 13:42:56.015400'),
(133, 74376409, 'MID DBA 03', 'MID00000000003', 'TESTTERMINAL', 19, '', '', '024009', '000', 'SALE', 'I', 4009, '2018-05-03 20:00:37', 0, 0, NULL, NULL, NULL, 'APPROVED', '4L7A8B8L5K0K4283', NULL, NULL, 'V', NULL, NULL, '', 'Richard Seymour', '67730 Promise Rd', 'Wallowa', 'OR', '97410', 'USA', '', '', 'Y', '', '', '', '294169820123210410', '', '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-03 21:31:50.088460', '2018-10-25 13:42:56.015400'),
(134, 74376410, 'MID DBA 03', 'MID00000000003', 'TESTTERMINAL', 20, '', '', '024010', '000', 'SALE', 'I', 4010, '2018-05-03 20:00:37', 0, 0, NULL, NULL, NULL, 'APPROVED', '4L7A8B8L5K0K4284', NULL, NULL, 'V', NULL, NULL, '', 'Richard Seymour', '67730 Promise Rd', 'Wallowa', 'OR', '97411', 'USA', '', '', 'Y', '', '', '', '294169820123210411', '', '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-03 21:31:50.088460', '2018-10-25 13:42:56.015400'),
(135, 74376411, 'MID DBA 03', 'MID00000000003', 'TESTTERMINAL', 21, '', '', '024011', '000', 'SALE', 'I', 4011, '2018-05-03 20:00:37', 0, 0, NULL, NULL, NULL, 'APPROVED', '4L7A8B8L5K0K4285', NULL, NULL, 'V', NULL, NULL, '', 'Richard Seymour', '67730 Promise Rd', 'Wallowa', 'OR', '97412', 'USA', '', '', 'Y', '', '', '', '294169820123210412', '', '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-03 21:31:50.088460', '2018-10-25 13:42:56.015400'),
(136, 74376412, 'MID DBA 03', 'MID00000000003', 'TESTTERMINAL', 22, '', '', '024012', '000', 'SALE', 'I', 4012, '2018-05-03 20:00:37', 0, 0, NULL, NULL, NULL, 'APPROVED', '4L7A8B8L5K0K4286', NULL, NULL, 'V', NULL, NULL, '', 'Richard Seymour', '67730 Promise Rd', 'Wallowa', 'OR', '97413', 'USA', '', '', 'Y', '', '', '', '294169820123210413', '', '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-03 21:31:50.088460', '2018-10-25 13:42:56.015400'),
(137, 74376413, 'MID DBA 03', 'MID00000000003', 'TESTTERMINAL', 23, '', '', '024013', '000', 'SALE', 'I', 4013, '2018-05-03 20:00:37', 0, 0, NULL, NULL, NULL, 'APPROVED', '4L7A8B8L5K0K4287', NULL, NULL, 'V', NULL, NULL, '', 'Richard Seymour', '67730 Promise Rd', 'Wallowa', 'OR', '97414', 'USA', '', '', 'Y', '', '', '', '294169820123210414', '', '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-03 21:31:50.088460', '2018-10-25 13:42:56.015400'),
(138, 74376414, 'MID DBA 03', 'MID00000000003', 'TESTTERMINAL', 24, '', '', '024014', '000', 'SALE', 'I', 4014, '2018-05-03 20:00:37', 0, 0, NULL, NULL, NULL, 'APPROVED', '4L7A8B8L5K0K4288', NULL, NULL, 'V', NULL, NULL, '', 'Richard Seymour', '67730 Promise Rd', 'Wallowa', 'OR', '97415', 'USA', '', '', 'Y', '', '', '', '294169820123210415', '', '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-03 21:31:50.088460', '2018-10-25 13:42:56.015400'),
(139, 74376415, 'MID DBA 03', 'MID00000000003', 'TESTTERMINAL', 25, '', '', '024015', '000', 'SALE', 'I', 4015, '2018-05-03 20:00:37', 0, 0, NULL, NULL, NULL, 'APPROVED', '4L7A8B8L5K0K4289', NULL, NULL, 'V', NULL, NULL, '', 'Richard Seymour', '67730 Promise Rd', 'Wallowa', 'OR', '97416', 'USA', '', '', 'Y', '', '', '', '294169820123210416', '', '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-03 21:31:50.088460', '2018-10-25 13:42:56.015400');


-- ================== WhiteLabels Data ==================
INSERT INTO WHITELABELS (whitelabel_fqdn, whitelabel_logo, whitelabel_theme, date_created, date_modified) VALUES
('Foo Whitelabel', 'Foo Whitelabel, for testing...', 'Foo Whitelabel theme, for testing...',DEFAULT, null);

-- ================== Partners Data ==================
INSERT INTO PARTNERS (partner_name, partner_desc, whitelabel_id_fk, date_created, date_modified) VALUES
('Partner 1', 'Partner 1, for testing...', 1, DEFAULT, null),
('Partner 2', 'Partner 2, for testing...', 1, DEFAULT, null),
('Partner 3', 'Partner 3, for testing...', 1, DEFAULT, null),
('Partner 4', 'Partner 4, for testing...', 1, DEFAULT, null),
('Partner 5', 'Partner 5, for testing...', 1, DEFAULT, null),
('Partner 6', 'Partner 6, for testing...', 1, DEFAULT, null),
('Partner 7', 'Partner 7, for testing...', 1, DEFAULT, null),
('Partner 8', 'Partner 8, for testing...', 1, DEFAULT, null),
('Partner 9', 'Partner 9, for testing...', 1, DEFAULT, null),
('Partner 10', 'Partner 10, for testing...', 1, DEFAULT, null),
('Partner 11', 'Partner 11, for testing...', 1, DEFAULT, null),
('Partner 12', 'Partner 12, for testing...', 1, DEFAULT, null),
('Partner 13', 'Partner 13, for testing...', 1, DEFAULT, null),
('Partner 14', 'Partner 14, for testing...', 1, DEFAULT, null),
('Partner 15', 'Partner 15, for testing...', 1, DEFAULT, null),
('Partner 16', 'Partner 16, for testing...', 1, DEFAULT, null),
('Partner 17', 'Partner 17, for testing...', 1, DEFAULT, null),
('Partner 18', 'Partner 18, for testing...', 1, DEFAULT, null),
('Partner 19', 'Partner 19, for testing...', 1, DEFAULT, null),
('Partner 20', 'Partner 20, for testing...', 1, DEFAULT, null),
('Partner 21', 'Partner 21, for testing...', 1, DEFAULT, null),
('Partner 22', 'Partner 22, for testing...', 1, DEFAULT, null),
('Partner 23', 'Partner 23, for testing...', 1, DEFAULT, null),
('Partner 24', 'Partner 24, for testing...', 1, DEFAULT, null),
('Partner 25', 'Partner 25, for testing...', 1, DEFAULT, null),
('Partner 26', 'Partner 26, for testing...', 1, DEFAULT, null),
('Partner 27', 'Partner 27, for testing...', 1, DEFAULT, null),
('Partner 28', 'Partner 28, for testing...', 1, DEFAULT, null),
('Partner 29', 'Partner 29, for testing...', 1, DEFAULT, null),
('Partner 30', 'Partner 30, for testing...', 1, DEFAULT, null),
('Partner 31', 'Partner 31, for testing...', 1, DEFAULT, null),
('Partner 32', 'Partner 32, for testing...', 1, DEFAULT, null),
('Partner 33', 'Partner 33, for testing...', 1, DEFAULT, null),
('Partner 34', 'Partner 34, for testing...', 1, DEFAULT, null),
('Partner 35', 'Partner 35, for testing...', 1, DEFAULT, null),
('Partner 36', 'Partner 36, for testing...', 1, DEFAULT, null),
('Partner 37', 'Partner 37, for testing...', 1, DEFAULT, null),
('Partner 38', 'Partner 38, for testing...', 1, DEFAULT, null),
('Partner 39', 'Partner 39, for testing...', 1, DEFAULT, null),
('Partner 40', 'Partner 40, for testing...', 1, DEFAULT, null),
('Partner 41', 'Partner 41, for testing...', 1, DEFAULT, null),
('Partner 42', 'Partner 42, for testing...', 1, DEFAULT, null),
('Partner 43', 'Partner 43, for testing...', 1, DEFAULT, null),
('Partner 44', 'Partner 44, for testing...', 1, DEFAULT, null),
('Partner 45', 'Partner 45, for testing...', 1, DEFAULT, null),
('Partner 46', 'Partner 46, for testing...', 1, DEFAULT, null),
('Partner 47', 'Partner 47, for testing...', 1, DEFAULT, null),
('Partner 48', 'Partner 48, for testing...', 1, DEFAULT, null),
('Partner 49', 'Partner 49, for testing...', 1, DEFAULT, null),
('Partner 50', 'Partner 50, for testing...', 1, DEFAULT, null),
('Partner 51', 'Partner 51, for testing...', 1, DEFAULT, null),
('Partner 52', 'Partner 52, for testing...', 1, DEFAULT, null),
('Partner 53', 'Partner 53, for testing...', 1, DEFAULT, null),
('Partner 54', 'Partner 54, for testing...', 1, DEFAULT, null),
('Partner 55', 'Partner 55, for testing...', 1, DEFAULT, null),
('Partner 56', 'Partner 56, for testing...', 1, DEFAULT, null),
('Partner 57', 'Partner 57, for testing...', 1, DEFAULT, null),
('Partner 58', 'Partner 58, for testing...', 1, DEFAULT, null),
('Partner 59', 'Partner 59, for testing...', 1, DEFAULT, null),
('Partner 60', 'Partner 60, for testing...', 1, DEFAULT, null);

-- ================== Banks Data ==================
INSERT INTO BANKS (bin, bank_name, bank_desc, date_created, date_modified) VALUES
('0012', 'Bank One', 'Test Bank One', DEFAULT, null),
('0013', 'Bank Two', 'Test Bank Two', DEFAULT, null);

-- ================== MCC Lookups Data ==================
INSERT INTO MCC_LOOKUP (mcc_code, mcc_desc) VALUES
('1520', 'General Contractors–Residential and Commercial'),
('1731', 'Electrical Contractors'),
('1750', 'Carpentry Contractors'),
('2741', 'Miscellaneous Publishing and Printing');

-- ================== Processors Data ==================
INSERT INTO PROCESSSOR_TYPES (name, description, application_name, application_version) VALUES
('Proc 1', 'Processor 001', 'Proc_Gtwt_01', '0.0.1'),
('Proc 2', 'Processor 002', 'Proc_Gtwt_02', '0.2.0'),
('Proc 3', 'Processor 003', 'Proc_01', '1.0.0');
INSERT INTO PROCESSORS (proc_party_id, proc_test_url, proc_prod_url, row_guid, match_pattern, proc_type_id_fk) VALUES
(10001, 'https://test-api.proc1.com/', 'https://api.proc1.com/', '5818e675-3ef2-45ce-b082-6624629136ee', '<proc1>', 1),
(10002, 'https://test-api.proc2.com/', 'https://api.proc2.com/', '5c824291-f622-4ce0-8e0a-274b30f34ced', '<proc2>', 2),
(10003, 'https://test-api.proc3.com/', 'https://api.proc3.com/', 'c15169b7-d0bd-4e04-b16d-24a009c8e0e4', '<proc3>', 3);

-- ================== Merchants Data ==================
INSERT INTO MERCHANTS (bank_id_fk, active, mcc_id_fk, mid, cc_setl_mid, ach_setl_mid, cb_mid, jetpay_mid, ach_mid, partner_id_fk, merchant_dba_name) VALUES
(1, 1, 1, 'MID00000000001', 'MID00000000001', 'MID00000000001', 'MID00000000101', '****************', '****************', 1, 'DBA 01'),
(2, 1, 1, 'MID00000000002', 'MID00000000002', 'MID00000000002', 'MID00000000102', '****************', '****************', 1, 'DBA 02'),
(2, 0, 1, 'MID00000000003', 'MID00000000003', 'MID00000000003', 'MID00000000103', '****************', '****************', 1, 'DBA 03'),
(2, 0, 1, 'MID00000000004', 'MID00000000004', 'MID00000000004', 'MID00000000104', '****************', '****************', 1, 'DBA 04'),
(2, 0, 1, 'MID00000000005', 'MID00000000005', 'MID00000000005', 'MID00000000105', '****************', '****************', 1, 'DBA 05'),
(2, 1, 1, 'MID00000000006', 'MID00000000006', 'MID00000000006', 'MID00000000106', '****************', '****************', 1, 'DBA 06'),
(1, 1, 1, 'MID00000000007', 'MID00000000007', 'MID00000000007', 'MID00000000107', '1234567890000007', '1234567890000107', 1, 'DBA 07'),
(1, 1, 1, 'MID00000000008', 'MID00000000008', 'MID00000000008', 'MID00000000108', '1234567890000008', '1234567890000108', 1, 'DBA 08'),
(1, 1, 1, 'MID00000000009', 'MID00000000009', 'MID00000000009', 'MID00000000109', '1234567890000009', '1234567890000109', 2, 'DBA 09'),
(1, 1, 1, 'MID00000000010', 'MID00000000010', 'MID00000000010', 'MID00000000110', '1234567890000010', '1234567890000110', 2, 'DBA 10'),
(1, 1, 1, 'MID00000000011', 'MID00000000011', 'MID00000000011', 'MID00000000111', '1234567890000011', '1234567890000111', 2, 'DBA 11'),
(1, 1, 1, 'MID00000000012', 'MID00000000012', 'MID00000000012', 'MID00000000112', '1234567890000012', '1234567890000112', 2, 'DBA 12'),
(1, 1, 1, 'MID00000000013', 'MID00000000013', 'MID00000000013', 'MID00000000113', '1234567890000013', '1234567890000113', 2, 'DBA 13'),
(1, 1, 1, 'MID00000000014', 'MID00000000014', 'MID00000000014', 'MID00000000114', '1234567890000014', '1234567890000114', 2, 'DBA 14'),
(1, 1, 1, 'MID00000000015', 'MID00000000015', 'MID00000000015', 'MID00000000115', '1234567890000015', '1234567890000115', 6, 'DBA 15'),
(1, 0, 1, 'MID00000000016', 'MID00000000016', 'MID00000000016', 'MID00000000116', '1234567890000016', '1234567890000116', 6, 'DBA 16'),
(1, 0, 1, 'MID00000000017', 'MID00000000017', 'MID00000000017', 'MID00000000117', '1234567890000017', '1234567890000117', 6, 'DBA 17'),
(1, 0, 1, 'MID00000000018', 'MID00000000018', 'MID00000000018', 'MID00000000118', '1234567890000018', '1234567890000118', 7, 'DBA 18'),
(1, 1, 1, 'MID00000000019', 'MID00000000019', 'MID00000000019', 'MID00000000119', '1234567890000019', '1234567890000119', 7, 'DBA 19'),
(1, 1, 1, 'MID00000000020', 'MID00000000020', 'MID00000000020', 'MID00000000120', '1234567890000020', '1234567890000120', 7, 'DBA 20'),
(1, 1, 1, 'MID00000000021', 'MID00000000021', 'MID00000000021', 'MID00000000121', '1234567890000021', '1234567890000121', 7, 'DBA 21'),
(1, 1, 1, 'MID00000000022', 'MID00000000022', 'MID00000000022', 'MID00000000122', '1234567890000022', '1234567890000122', 7, 'DBA 22'),
(1, 1, 1, 'MID00000000023', 'MID00000000023', 'MID00000000023', 'MID00000000123', '1234567890000023', '1234567890000123', 7, 'DBA 23'),
(1, 1, 1, 'MID00000000024', 'MID00000000024', 'MID00000000024', 'MID00000000124', '1234567890000024', '1234567890000124', 7, 'DBA 24'),
(1, 1, 1, 'MID00000000025', 'MID00000000025', 'MID00000000025', 'MID00000000125', '1234567890000025', '1234567890000125', 7, 'DBA 25'),
(1, 1, 1, 'MID00000000026', 'MID00000000026', 'MID00000000026', 'MID00000000126', '1234567890000026', '1234567890000126', 7, 'DBA 26'),
(1, 1, 1, 'MID00000000027', 'MID00000000027', 'MID00000000027', 'MID00000000127', '1234567890000027', '1234567890000127', 7, 'DBA 27'),
(1, 1, 1, 'MID00000000028', 'MID00000000028', 'MID00000000028', 'MID00000000128', '1234567890000028', '1234567890000128', 8, 'DBA 28'),
(1, 1, 1, 'MID00000000029', 'MID00000000029', 'MID00000000029', 'MID00000000129', '1234567890000029', '1234567890000129', 8, 'DBA 29'),
(1, 1, 1, 'MID00000000030', 'MID00000000030', 'MID00000000030', 'MID00000000130', '1234567890000030', '1234567890000130', 8, 'DBA 30'),
(1, 1, 1, 'MID00000000031', 'MID00000000031', 'MID00000000031', 'MID00000000131', '1234567890000031', '1234567890000131', 8, 'DBA 31'),
(1, 1, 1, 'MID00000000032', 'MID00000000032', 'MID00000000032', 'MID00000000132', '1234567890000032', '1234567890000132', 8, 'DBA 32'),
(1, 1, 1, 'MID00000000033', 'MID00000000033', 'MID00000000033', 'MID00000000133', '1234567890000033', '1234567890000133', 8, 'DBA 33'),
(1, 1, 1, 'MID00000000034', 'MID00000000034', 'MID00000000034', 'MID00000000134', '1234567890000034', '1234567890000134', 8, 'DBA 34'),
(1, 1, 1, 'MID00000000035', 'MID00000000035', 'MID00000000035', 'MID00000000135', '1234567890000035', '1234567890000135', 8, 'DBA 35'),
(1, 1, 1, 'MID00000000036', 'MID00000000036', 'MID00000000036', 'MID00000000136', '1234567890000036', '1234567890000136', 8, 'DBA 36'),
(1, 1, 1, 'MID00000000037', 'MID00000000037', 'MID00000000037', 'MID00000000137', '1234567890000037', '1234567890000137', 8, 'DBA 37'),
(1, 1, 1, 'MID00000000038', 'MID00000000038', 'MID00000000038', 'MID00000000138', '1234567890000038', '1234567890000138', 8, 'DBA 38'),
(1, 1, 1, 'MID00000000039', 'MID00000000039', 'MID00000000039', 'MID00000000139', '1234567890000039', '1234567890000139', 9, 'DBA 39'),
(1, 1, 1, 'MID00000000040', 'MID00000000040', 'MID00000000040', 'MID00*********', '1234567890000040', '1234567890000140', 9, 'DBA 40'),
(1, 1, 1, 'MID00000000041', 'MID00000000041', 'MID00000000041', 'MID00000000141', '1234567890000041', '1234567890000141', 9, 'DBA 41'),
(1, 1, 1, 'MID00000000042', 'MID00000000042', 'MID00000000042', 'MID00000000142', '1234567890000042', '1234567890000142', 9, 'DBA 42'),
(1, 1, 1, 'MID00000000043', 'MID00000000043', 'MID00000000043', 'MID00000000143', '1234567890000043', '1234567890000143', 9, 'DBA 43'),
(1, 1, 1, 'MID00000000044', 'MID00000000044', 'MID00000000044', 'MID00000000144', '1234567890000044', '1234567890000144', 9, 'DBA 44'),
(1, 1, 1, 'MID00000000045', 'MID00000000045', 'MID00000000045', 'MID00000000145', '1234567890000045', '1234567890000145', 9, 'DBA 45'),
(1, 1, 1, 'MID00000000046', 'MID00000000046', 'MID00000000046', 'MID00000000146', '1234567890000046', '1234567890000146', 9, 'DBA 46'),
(1, 1, 1, 'MID00000000047', 'MID00000000047', 'MID00000000047', 'MID00000000147', '1234567890000047', '1234567890000147', 9, 'DBA 47'),
(1, 1, 1, 'MID00000000048', 'MID00000000048', 'MID00000000048', 'MID00000000148', '1234567890000048', '1234567890000148', 9, 'DBA 48'),
(1, 1, 1, 'MID00000000049', 'MID00000000049', 'MID00000000049', 'MID00000000149', '1234567890000049', '1234567890000149', 10, 'DBA 49'),
(1, 1, 1, 'MID00000000050', 'MID00000000050', 'MID00000000050', 'MID00000000150', '1234567890000050', '1234567890000150', 10, 'DBA 50'),
(1, 1, 1, 'MID00000000051', 'MID00000000051', 'MID00000000051', 'MID00000000151', '1234567890000051', '1234567890000151', 10, 'DBA 51'),
(1, 1, 1, 'MID00000000052', 'MID00000000052', 'MID00000000052', 'MID00000000152', '1234567890000052', '1234567890000152', 10, 'DBA 52'),
(1, 1, 1, 'MID00000000053', 'MID00000000053', 'MID00000000053', 'MID00000000153', '1234567890000053', '1234567890000153', 10, 'DBA 53'),
(1, 1, 1, 'MID00000000054', 'MID00000000054', 'MID00000000054', 'MID00000000154', '1234567890000054', '1234567890000154', 10, 'DBA 54'),
(1, 1, 1, 'MID00000000055', 'MID00000000055', 'MID00000000055', 'MID00000000155', '1234567890000055', '1234567890000155', 11, 'DBA 55'),
(1, 1, 1, 'MID00000000056', 'MID00000000056', 'MID00000000056', 'MID00000000156', '1234567890000056', '1234567890000156', 11, 'DBA 56'),
(1, 1, 1, 'MID00000000057', 'MID00000000057', 'MID00000000057', 'MID00000000157', '1234567890000057', '1234567890000157', 11, 'DBA 57'),
(1, 1, 1, 'MID00000000058', 'MID00000000058', 'MID00000000058', 'MID00000000158', '1234567890000058', '1234567890000158', 11, 'DBA 58'),
(1, 1, 1, 'MID00000000059', 'MID00000000059', 'MID00000000059', 'MID00000000159', '1234567890000059', '1234567890000159', 11, 'DBA 59'),
(1, 1, 1, 'MID00000000060', 'MID00000000060', 'MID00000000060', 'MID00000000160', '1234567890000060', '1234567890000160', 11, 'DBA 60');

-- ================== Terminals Data ==================
INSERT INTO TERMINALS (tid, cc_proc_id_fk, ach_proc_id_fk) VALUES
('TESTTERMINAL', 1, 1),
('TID100000001', 1, 2),
('TID100000002', 2, 3);

-- ================== MID_TID_MAP Data ==================
INSERT INTO MID_TID_MAP (mid_id_fk, tid_id_fk) VALUES
(1, 1), (1, 2), (2, 3), (3, 1),
(3, 2), (3, 3), (5, 1), (5, 2),
(6, 1), (6, 3), (7, 1), (8, 2),
(8, 3), (10, 3), (10, 1), (13, 1),
(14, 1), (14, 2), (15, 1), (20, 3),
(24, 1), (24, 2), (25, 3), (31, 1),
(31, 2), (40, 3), (41, 1), (42, 2),
(42, 3), (53, 3), (54, 3), (55, 1),
(56, 3), (57, 3), (57, 3), (60, 3);

-- ================== Merchant Statements Data ==================
INSERT INTO MERCHANT_STATEMENT_ACTIVITY_SUMMARY (ID, MERCHANT_STATEMENT_ID, PLAN_TYPE, SALES, NUM_SALES, CREDITS, NUM_CREDITS, NET_SALES, PER_ITEM_FEE, RATE, DISCOUNT_DUE, NUM_TOTAL_TRANS, DATE_CREATED, MERCHANT_ID)
VALUES
    (1, 1, 'Visa', '292.39', '09', '0.00', '00', '292.39', '0.00000', '0.00000', '2.36', '09', '2021-09-26 23:45:11', 'MID00000000001'),
    (2, 1, 'Visa Debit', '3072.18', '94', '0.00', '00', '3072.18', '0.00000', '0.00000', '24.76', '94', '2021-09-26 23:45:11', 'MID00000000001'),
    (3, 1, 'Visa Business', '0.00', '00', '0.00', '00', '0.00', '0.00000', '0.00000', '0.00', '00', '2021-09-26 23:45:11', 'MID00000000001'),
    (4, 1, 'Visa Large Ticket', '0.00', '00', '0.00', '00', '0.00', '0.00000', '0.00000', '0.00', '00', '2021-09-26 23:45:11', 'MID00000000001'),
    (5, 1, 'MasterCard', '205.65', '07', '0.00', '00', '205.65', '0.00000', '0.00000', '1.73', '07', '2021-09-26 23:45:11', 'MID00000000001'),
    (6, 1, 'MasterCard Debit', '1162.51', '34', '0.00', '00', '1162.51', '0.00000', '0.00000', '9.21', '34', '2021-09-26 23:45:11', 'MID00000000001'),
    (7, 1, 'MasterCard Business', '0.00', '00', '0.00', '00', '0.00', '0.00000', '0.00000', '0.00', '00', '2021-09-26 23:45:11', 'MID00000000001'),
    (8, 1, 'MasterCard Large Ticket', '0.00', '00', '0.00', '00', '0.00', '0.00000', '0.00000', '0.00', '00', '2021-09-26 23:45:11', 'MID00000000001'),
    (9, 1, 'JCB', '0.00', '00', '0.00', '00', '0.00', '0.00000', '0.00000', '0.00', '00', '2021-09-26 23:45:11', 'MID00000000001'),
    (10, 1, 'American Express', '0.00', '00', '0.00', '00', '0.00', '0.00000', '0.00000', '0.00', '00', '2021-09-26 23:45:11', 'MID00000000001'),
    (11, 1, 'Discover', '0.00', '00', '0.00', '00', '0.00', '0.00000', '0.00000', '0.00', '00', '2021-09-26 23:45:11', 'MID00000000001'),
    (12, 1, 'Discover Debit', '0.00', '00', '0.00', '00', '0.00', '0.00000', '0.00000', '0.00', '00', '2021-09-26 23:45:11', 'MID00000000001'),
    (13, 1, 'Discover Business', '0.00', '00', '0.00', '00', '0.00', '0.00000', '0.00000', '0.00', '00', '2021-09-26 23:45:11', 'MID00000000001'),
    (14, 1, 'Discover JCB', '0.00', '00', '0.00', '00', '0.00', '0.00000', '0.00000', '0.00', '00', '2021-09-26 23:45:11', 'MID00000000001'),
    (15, 1, 'Debit', '0.00', '00', '0.00', '00', '0.00', '0.00000', '0.00000', '0.00', '00', '2021-09-26 23:45:11', 'MID00000000001'),
    (16, 1, 'UNKNOWN -PP', '0.00', '00', '0.00', '00', '0.00', '0.00000', '0.00000', '0.00', '00', '2021-09-26 23:45:11', 'MID00000000001'),
    (17, 1, 'Visa', '0.00', '00', '0.00', '00', '0.00', '0.00000', '0.00000', '0.00', '00', '2021-09-26 23:45:11', 'MID00000000002'),
    (18, 1, 'Visa Debit', '0.00', '00', '0.00', '00', '0.00', '0.00000', '0.00000', '0.00', '00', '2021-09-26 23:45:11', 'MID00000000002'),
    (19, 1, 'Visa Business', '0.00', '00', '0.00', '00', '0.00', '0.00000', '0.00000', '0.00', '00', '2021-09-26 23:45:11', 'MID00000000002'),
    (20, 1, 'Visa Large Ticket', '0.00', '00', '0.00', '00', '0.00', '0.00000', '0.00000', '0.00', '00', '2021-09-26 23:45:11', 'MID00000000002'),
    (21, 1, 'MasterCard', '0.00', '00', '0.00', '00', '0.00', '0.00000', '0.00000', '0.00', '00', '2021-09-26 23:45:11', 'MID00000000002'),
    (22, 1, 'MasterCard Debit', '0.00', '00', '0.00', '00', '0.00', '0.00000', '0.00000', '0.00', '00', '2021-09-26 23:45:11', 'MID00000000002'),
    (23, 1, 'MasterCard Business', '0.00', '00', '0.00', '00', '0.00', '0.00000', '0.00000', '0.00', '00', '2021-09-26 23:45:11', 'MID00000000002'),
    (24, 1, 'MasterCard Large Ticket', '0.00', '00', '0.00', '00', '0.00', '0.00000', '0.00000', '0.00', '00', '2021-09-26 23:45:11', 'MID00000000002'),
    (25, 1, 'JCB', '0.00', '00', '0.00', '00', '0.00', '0.00000', '0.00000', '0.00', '00', '2021-09-26 23:45:11', 'MID00000000002'),
    (26, 1, 'American Express', '0.00', '00', '0.00', '00', '0.00', '0.00000', '0.00000', '0.00', '00', '2021-09-26 23:45:11', 'MID00000000002'),
    (27, 1, 'Discover', '0.00', '00', '0.00', '00', '0.00', '0.00000', '0.00000', '0.00', '00', '2021-09-26 23:45:11', 'MID00000000002'),
    (28, 1, 'Discover Debit', '0.00', '00', '0.00', '00', '0.00', '0.00000', '0.00000', '0.00', '00', '2021-09-26 23:45:11', 'MID00000000002'),
    (29, 1, 'Discover Business', '0.00', '00', '0.00', '00', '0.00', '0.00000', '0.00000', '0.00', '00', '2021-09-26 23:45:11', 'MID00000000002'),
    (30, 1, 'Discover JCB', '0.00', '00', '0.00', '00', '0.00', '0.00000', '0.00000', '0.00', '00', '2021-09-26 23:45:11', 'MID00000000002'),
    (31, 1, 'Debit', '0.00', '00', '0.00', '00', '0.00', '0.00000', '0.00000', '0.00', '00', '2021-09-26 23:45:11', 'MID00000000002'),
    (32, 1, 'UNKNOWN -PP', '0.00', '00', '0.00', '00', '0.00', '0.00000', '0.00000', '0.00', '00', '2021-09-26 23:45:11', 'MID00000000002'),
    (33, 1, 'Visa', '0.00', '00', '0.00', '00', '0.00', '0.00000', '0.00000', '0.00', '00', '2021-09-26 23:45:11', '**************'),
    (34, 1, 'Visa Debit', '0.00', '00', '0.00', '00', '0.00', '0.00000', '0.00000', '0.00', '00', '2021-09-26 23:45:11', '**************'),
    (35, 1, 'Visa Business', '0.00', '00', '0.00', '00', '0.00', '0.00000', '0.00000', '0.00', '00', '2021-09-26 23:45:11', '**************'),
    (36, 1, 'Visa Large Ticket', '0.00', '00', '0.00', '00', '0.00', '0.00000', '0.00000', '0.00', '00', '2021-09-26 23:45:11', '**************'),
    (37, 1, 'MasterCard', '0.00', '00', '0.00', '00', '0.00', '0.00000', '0.00000', '0.00', '00', '2021-09-26 23:45:11', '**************'),
    (38, 1, 'MasterCard Debit', '0.00', '00', '0.00', '00', '0.00', '0.00000', '0.00000', '0.00', '00', '2021-09-26 23:45:11', '**************'),
    (39, 1, 'MasterCard Business', '0.00', '00', '0.00', '00', '0.00', '0.00000', '0.00000', '0.00', '00', '2021-09-26 23:45:11', '**************'),
    (40, 1, 'MasterCard Large Ticket', '0.00', '00', '0.00', '00', '0.00', '0.00000', '0.00000', '0.00', '00', '2021-09-26 23:45:11', '**************'),
    (41, 1, 'JCB', '0.00', '00', '0.00', '00', '0.00', '0.00000', '0.00000', '0.00', '00', '2021-09-26 23:45:11', '**************'),
    (42, 1, 'American Express', '0.00', '00', '0.00', '00', '0.00', '0.00000', '0.00000', '0.00', '00', '2021-09-26 23:45:11', '**************'),
    (43, 1, 'Discover', '0.00', '00', '0.00', '00', '0.00', '0.00000', '0.00000', '0.00', '00', '2021-09-26 23:45:11', '**************'),
    (44, 1, 'Discover Debit', '0.00', '00', '0.00', '00', '0.00', '0.00000', '0.00000', '0.00', '00', '2021-09-26 23:45:11', '**************'),
    (45, 1, 'Discover Business', '0.00', '00', '0.00', '00', '0.00', '0.00000', '0.00000', '0.00', '00', '2021-09-26 23:45:11', '**************'),
    (46, 1, 'Discover JCB', '0.00', '00', '0.00', '00', '0.00', '0.00000', '0.00000', '0.00', '00', '2021-09-26 23:45:11', '**************'),
    (47, 1, 'Debit', '0.00', '00', '0.00', '00', '0.00', '0.00000', '0.00000', '0.00', '00', '2021-09-26 23:45:11', '**************'),
    (48, 1, 'UNKNOWN -PP', '0.00', '00', '0.00', '00', '0.00', '0.00000', '0.00000', '0.00', '00', '2021-09-26 23:45:11', '**************'),
    (49, 1, 'Visa', '28537.47', '1,904', '0.00', '00', '28537.47', '0.00000', '0.00000', '932.38', '1,904', '2021-09-26 23:45:11', 'MID00000000003'),
    (50, 1, 'Visa Large Ticket', '0.00', '00', '0.00', '00', '0.00', '0.00000', '0.00000', '0.00', '00', '2021-09-26 23:45:11', 'MID00000000003'),
    (51, 1, 'MasterCard', '10359.12', '638', '0.00', '00', '10359.12', '0.00000', '0.00000', '333.13', '638', '2021-09-26 23:45:11', 'MID00000000003'),
    (52, 1, 'MasterCard Large Ticket', '0.00', '00', '0.00', '00', '0.00', '0.00000', '0.00000', '0.00', '00', '2021-09-26 23:45:11', 'MID00000000003'),
    (53, 1, 'American Express', '10419.08', '624', '0.00', '00', '10419.08', '0.00000', '0.00000', '0.00', '624', '2021-09-26 23:45:11', 'MID00000000003'),
    (54, 1, 'Discover', '0.00', '00', '0.00', '00', '0.00', '0.00000', '0.00000', '0.00', '00', '2021-09-26 23:45:11', 'MID00000000003');
INSERT INTO MERCHANT_STATEMENT_AUTH_DETAIL (ID, MERCHANT_STATEMENT_ID, DESCRIPTION, NUM_X, RATE, AUTH_FEE, DATE_CREATED, MERCHANT_ID)
VALUES
    (1, 1, 'Address Verification Service', '175', '0.01000', '1.75', '2021-09-26 23:45:11', 'MID00000000001'),
    (2, 1, 'Authorization Fee', '175', '0.10000', '17.50', '2021-09-26 23:45:11', 'MID00000000001'),
    (3, 1, 'Amex Authorizations', '626', '0.15000', '93.90', '2021-09-26 23:45:11', 'MID00000000003'),
    (4, 1, 'NETWORK TRANSACTION FEE', '3213', '0.05000', '160.65', '2021-09-26 23:45:11', 'MID00000000003'),
    (5, 1, 'V/M/D Authorizations', '2587', '0.10000', '258.70', '2021-09-26 23:45:11', 'MID00000000003');
INSERT INTO MERCHANT_STATEMENT_DEPOSIT_DETAIL (ID, MERCHANT_STATEMENT_ID, DEPOSIT_DATE, NUM_OF_TRANS, BATCH_AMOUNT, OC_BATCH, ADJUST, CHARGEBACKS, FEE_PAID, NET_DEPOSIT, DATE_CREATED, MERCHANT_ID)
VALUES
    (1, 1, '07-02-2021', 0, '85.80', '0.00', '0.00', '0.00', '0.00', '85.80', '2021-09-26 23:45:11', 'MID00000000001'),
    (2, 1, '07-07-2021', 0, '29.95', '0.00', '0.00', '0.00', '0.00', '29.95', '2021-09-26 23:45:11', 'MID00000000001'),
    (3, 1, '07-08-2021', 0, '64.53', '0.00', '0.00', '0.00', '0.00', '64.53', '2021-09-26 23:45:11', 'MID00000000001'),
    (4, 1, '07-15-2021', 0, '51.89', '0.00', '0.00', '0.00', '0.00', '51.89', '2021-09-26 23:45:11', 'MID00000000001'),
    (5, 1, '07-18-2021', 0, '89.84', '0.00', '0.00', '0.00', '0.00', '89.84', '2021-09-26 23:45:11', 'MID00000000001'),
    (6, 1, '07-18-2021', 0, '4241.84', '0.00', '0.00', '0.00', '0.00', '4241.84', '2021-09-26 23:45:11', 'MID00000000001'),
    (7, 1, '07-25-2021', 0, '168.88', '0.00', '0.00', '0.00', '0.00', '168.88', '2021-09-26 23:45:11', 'MID00000000001'),
    (8, 1, '07-01-2021', 0, '29.85', '0.00', '0.00', '0.00', '0.00', '29.85', '2021-09-26 23:45:11', 'MID00000000003'),
    (9, 1, '07-02-2021', 0, '89.55', '0.00', '0.00', '0.00', '0.00', '89.55', '2021-09-26 23:45:11', 'MID00000000003'),
    (10, 1, '07-04-2021', 0, '9.95', '0.00', '0.00', '0.00', '0.00', '9.95', '2021-09-26 23:45:11', 'MID00000000003'),
    (11, 1, '07-04-2021', 0, '10665.31', '2442.44', '0.00', '0.00', '0.00', '8222.87', '2021-09-26 23:45:11', 'MID00000000003'),
    (12, 1, '07-04-2021', 0, '30966.85', '6680.79', '0.00', '0.00', '0.00', '24286.06', '2021-09-26 23:45:11', 'MID00000000003'),
    (13, 1, '07-05-2021', 0, '4927.85', '1051.70', '0.00', '0.00', '0.00', '3876.15', '2021-09-26 23:45:11', 'MID00000000003'),
    (14, 1, '07-06-2021', 0, '1163.51', '199.30', '0.00', '0.00', '0.00', '964.21', '2021-09-26 23:45:11', 'MID00000000003'),
    (15, 1, '07-07-2021', 0, '293.75', '44.85', '0.00', '0.00', '0.00', '248.90', '2021-09-26 23:45:11', 'MID00000000003'),
    (16, 1, '07-08-2021', 0, '358.85', '0.00', '0.00', '0.00', '0.00', '358.85', '2021-09-26 23:45:11', 'MID00000000003'),
    (17, 1, '07-09-2021', 0, '214.40', '0.00', '0.00', '0.00', '0.00', '214.40', '2021-09-26 23:45:11', 'MID00000000003'),
    (18, 1, '07-11-2021', 0, '29.85', '0.00', '0.00', '0.00', '0.00', '29.85', '2021-09-26 23:45:11', 'MID00000000003'),
    (19, 1, '07-11-2021', 0, '34.90', '0.00', '0.00', '0.00', '0.00', '34.90', '2021-09-26 23:45:11', 'MID00000000003'),
    (20, 1, '07-12-2021', 0, '19.90', '0.00', '0.00', '0.00', '0.00', '19.90', '2021-09-26 23:45:11', 'MID00000000003'),
    (21, 1, '07-14-2021', 0, '76.70', '0.00', '0.00', '0.00', '0.00', '76.70', '2021-09-26 23:45:11', 'MID00000000003'),
    (22, 1, '07-15-2021', 0, '9.95', '0.00', '0.00', '0.00', '0.00', '9.95', '2021-09-26 23:45:11', 'MID00000000003'),
    (23, 1, '07-16-2021', 0, '105.80', '0.00', '0.00', '0.00', '0.00', '105.80', '2021-09-26 23:45:11', 'MID00000000003'),
    (24, 1, '07-18-2021', 0, '139.30', '0.00', '0.00', '0.00', '0.00', '139.30', '2021-09-26 23:45:11', 'MID00000000003'),
    (25, 1, '07-25-2021', 0, '39.80', '0.00', '0.00', '0.00', '0.00', '39.80', '2021-09-26 23:45:11', 'MID00000000003'),
    (26, 1, '07-26-2021', 0, '39.80', '0.00', '0.00', '0.00', '0.00', '39.80', '2021-09-26 23:45:11', 'MID00000000003'),
    (27, 1, '07-30-2021', 0, '99.80', '0.00', '0.00', '0.00', '0.00', '99.80', '2021-09-26 23:45:11', 'MID00000000003');
INSERT INTO MERCHANT_STATEMENT_DETAIL (ID, MERCHANT_STATEMENT_ID, MERCHANT_ID, MERCHANT_NAME, ASSOCIATE, CHAIN, PROCESSING_DETAIL_TOTAL, AUTHORIZATION_DETAIL_TOTAL, OTHER_DETAIL_TOTAL, DISCOUNT_DUE_TOTAL, STATEMENT_MONTH)
VALUES
    (1, 1, 'MID00000000001', 'GETFIT MONTREY', '028202', '028000', '87.56', '19.25', '71.07', '38.06', '0721'),
    (2, 1, 'MID00000000002', 'IPPAY INTERCHANGE', '001401', '001000', '0.00', '0.00', '8.50', '0.00', '0721'),
    (3, 1, '**************', 'IPPAY TIER', '001401', '001000', '0.00', '0.00', '8.50', '0.00', '0721'),
    (4, 1, 'MID00000000003', 'HURRICANE ELECTRIC LLC', '001201', '001000', '0.03', '513.25', '283.60', '1,265.51', '0721');
INSERT INTO MERCHANT_STATEMENT_OTHER_CARD_DEPOSIT (ID, MERCHANT_STATEMENT_ID, DATE, BATCH_AMOUNT, PAID_BY, NET_AMOUNT, DATE_CREATED, MERCHANT_ID)
VALUES
    (1, 1, '07-04-2021', '0.00', 'American Express', '0.00', '2021-09-26 23:45:11', 'MID00000000003'),
    (2, 1, '07-04-2021', '0.00', 'American Express', '0.00', '2021-09-26 23:45:11', 'MID00000000003'),
    (3, 1, '07-05-2021', '0.00', 'American Express', '0.00', '2021-09-26 23:45:11', 'MID00000000003'),
    (4, 1, '07-06-2021', '0.00', 'American Express', '0.00', '2021-09-26 23:45:11', 'MID00000000003'),
    (5, 1, '07-07-2021', '0.00', 'American Express', '0.00', '2021-09-26 23:45:11', 'MID00000000003');
INSERT INTO MERCHANT_STATEMENT_OTHER_DETAIL (ID, MERCHANT_STATEMENT_ID, DESCRIPTION, AMOUNT, DISCOUNT_RATE, NUM_X, TRANSACTION_FEE, OTHER_FEE, DATE_CREATED, MERCHANT_ID)
VALUES
    (1, 1, 'Card Brand Pass Through Fees', '0.00', '0.00', '0.00', '0.00', '0.00', '2021-09-26 23:45:11', 'MID00000000001'),
    (2, 1, 'MasterCard Acquirer License Fee', '0.00', '0.00', '0.00', '0.00', '0.00', '2021-09-26 23:45:11', 'MID00000000001'),
    (3, 1, 'Acquiring Support Fee', '0.00', '0.00', '0.00', '0.00', '0.00', '2021-09-26 23:45:11', 'MID00000000001'),
    (4, 1, 'Monthly Account Maintenance Fee', '0.00', '0.00', '0.00', '0.00', '0.00', '2021-09-26 23:45:11', 'MID00000000001'),
    (5, 1, 'PCI Compliance Fee', '0.00', '0.00', '0.00', '0.00', '0.00', '2021-09-26 23:45:11', 'MID00000000001'),
    (6, 1, 'Regulatory & Compliance Fee', '0.00', '0.00', '0.00', '0.00', '0.00', '2021-09-26 23:45:11', 'MID00000000001'),
    (7, 1, 'Batch Fee', '0.00', '0.00', '0.00', '0.00', '0.00', '2021-09-26 23:45:11', 'MID00000000001'),
    (8, 1, 'Transaction Fee', '0.00', '0.00', '0.00', '0.00', '0.00', '2021-09-26 23:45:11', 'MID00000000001'),
    (9, 1, 'Monthly Account Maintenance Fee', '0.00', '0.00', '0.00', '0.00', '0.00', '2021-09-26 23:45:11', 'MID00000000002'),
    (10, 1, 'Regulatory & Compliance Fee', '0.00', '0.00', '0.00', '0.00', '0.00', '2021-09-26 23:45:11', 'MID00000000002'),
    (11, 1, 'Monthly Account Maintenance Fee', '0.00', '0.00', '0.00', '0.00', '0.00', '2021-09-26 23:45:11', '**************'),
    (12, 1, 'Regulatory & Compliance Fee', '0.00', '0.00', '0.00', '0.00', '0.00', '2021-09-26 23:45:11', '**************'),
    (13, 1, 'MC-Assessments', '0.00', '0.00', '0.00', '0.00', '0.00', '2021-09-26 23:45:11', 'MID00000000003'),
    (14, 1, 'Card Brand Pass Through Fees', '0.00', '0.00', '0.00', '0.00', '0.00', '2021-09-26 23:45:11', 'MID00000000003'),
    (15, 1, 'Gateway Access Fee', '0.00', '0.00', '0.00', '0.00', '0.00', '2021-09-26 23:45:11', 'MID00000000003'),
    (16, 1, 'Regulatory & Compliance Fee', '0.00', '0.00', '0.00', '0.00', '0.00', '2021-09-26 23:45:11', 'MID00000000003'),
    (17, 1, 'Statement Fee', '0.00', '0.00', '0.00', '0.00', '0.00', '2021-09-26 23:45:11', 'MID00000000003'),
    (18, 1, 'Acquiring Support Fee', '0.00', '0.00', '0.00', '0.00', '0.00', '2021-09-26 23:45:11', 'MID00000000003'),
    (19, 1, 'PCI Compliance Fee', '0.00', '0.00', '0.00', '0.00', '0.00', '2021-09-26 23:45:11', 'MID00000000003');
INSERT INTO MERCHANT_STATEMENT_PROCESSING_DETAIL (ID, MERCHANT_STATEMENT_ID, DESCRIPTION, AMOUNT, DISCOUNT_RATE, NUM_X, TRANSACTION_FEES, PROCESS_FEES, DATE_CREATED, MERCHANT_ID)
VALUES
    (1, 1, 'VS CPS Retail Check Debit', '0.00', '0.00', '0.00', '0.00', '0.00', '2021-09-26 23:45:11', 'MID00000000001'),
    (2, 1, 'VS CPS CNP Debit', '0.00', '0.00', '0.00', '0.00', '0.00', '2021-09-26 23:45:11', 'MID00000000001'),
    (3, 1, 'VS VT Product 1', '0.00', '0.00', '0.00', '0.00', '0.00', '2021-09-26 23:45:11', 'MID00000000001'),
    (4, 1, 'VS VTR Product 1', '0.00', '0.00', '0.00', '0.00', '0.00', '2021-09-26 23:45:11', 'MID00000000001'),
    (5, 1, 'VS VIN Product 1', '0.00', '0.00', '0.00', '0.00', '0.00', '2021-09-26 23:45:11', 'MID00000000001'),
    (6, 1, 'VS VSP VIQ Product 1', '0.00', '0.00', '0.00', '0.00', '0.00', '2021-09-26 23:45:11', 'MID00000000001'),
    (7, 1, 'VS US Regulated Debit', '0.00', '0.00', '0.00', '0.00', '0.00', '2021-09-26 23:45:11', 'MID00000000001'),
    (8, 1, 'VS CPS CNP Pp', '0.00', '0.00', '0.00', '0.00', '0.00', '2021-09-26 23:45:11', 'MID00000000001'),
    (9, 1, 'MC Merit I Debit', '0.00', '0.00', '0.00', '0.00', '0.00', '2021-09-26 23:45:11', 'MID00000000001'),
    (10, 1, 'MC Merit III Debit', '0.00', '0.00', '0.00', '0.00', '0.00', '2021-09-26 23:45:11', 'MID00000000001'),
    (11, 1, 'MC Enhanced Merit I', '0.00', '0.00', '0.00', '0.00', '0.00', '2021-09-26 23:45:11', 'MID00000000001'),
    (12, 1, 'MC World Merit I', '0.00', '0.00', '0.00', '0.00', '0.00', '2021-09-26 23:45:11', 'MID00000000001'),
    (13, 1, 'MC World Elite Merit I', '0.00', '0.00', '0.00', '0.00', '0.00', '2021-09-26 23:45:11', 'MID00000000001'),
    (14, 1, 'MC High Value Merit I', '0.00', '0.00', '0.00', '0.00', '0.00', '2021-09-26 23:45:11', 'MID00000000001'),
    (15, 1, 'MC US Cons Regulated POS Debit w/Fraud Adj', '0.00', '0.00', '0.00', '0.00', '0.00', '2021-09-26 23:45:11', 'MID00000000001'),
    (16, 1, 'Interchange Differential', '0.00', '0.00', '0.00', '0.00', '0.00', '2021-09-26 23:45:11', 'MID00000000003');
INSERT INTO MERCHANT_STATEMENT (ID, FILENAME, DATE_CREATED, IMPORT_DATE)
VALUES
    (1, 'test.xml', '2021-09-26 23:45:11', '2021-09-26 23:45:11');
INSERT INTO ImportChargebacks (ID, ImportFileID, ImportDate, CaseNumber, FamilyID, ItemType, MerchantNumber, CaseType, ResolutionTo, Debit_Credit, TranCode, ReasonCode, ReasonDesc, BIN_ICA, CaseAmount, RecordType, CardBrand, DateResolved, AcquirerReferenceNumber, OriginalReferenceNumber, Foreign_Domestic, MCC, AuthCode, DateTransaction, ImportDateTransaction, ImportDateLoaded, ImportDatePosted, ImportDateSecondRequest, ImportDateResolved, DatePosted, DateLoaded, CardholderAccountNumber, DateSecondRequest, AccountNumberPrefix, AccountNumberSuffix, DBAName, Address1, Address2, City, State, ZipCode, GroupID, Association, InvoiceNumber, DateWarehouse, TransID, MerchAmount, MatchedTransactionID, MatchedDate, CompleteLine, DateUpdated)
VALUES
    (7, 10549, '2018-12-17 16:30:14.159203', '*************', '10.4', '04', 'MID00000000101', '22', 'M', 'D', '05', '10.4', 'Fraud Card Absent Environment', '414720', '*********.00', '', '1', '********', '24019118270900018960982', '*********', 'D', '4812', '02606C', '********', '2018-09-27', '2018-11-21', '2018-09-27', NULL, '2018-12-03', '********', '********', '414720XXXXXX1475', '', '414720', '1475', 'APPALACHIAN WIRELESS', '', '101 TECHNOLOGY TRL', 'IVEL', 'KY', '41642-9057', '034000', '**********', NULL, '********', '***************', '*********.00', ********, '2018-12-17 16:30:14', '***************.4   04009286002009449422MD0510.4Fraud Card Absent Environment                     ***************.00 1********24019118270900018960982*********                     D481202606C************************414720XXXXXX1475           4147201475APPALACHIAN WIRELESS                                                                 101 TECHNOLOGY TRL                      IVEL                     KY41642-9057034000**********      ********************************.0020387795', '2018-12-17 16:30:14.197388'),
    (10, 10549, '2018-12-17 16:30:14.160717', '2018332009513', '13.7', '03', 'MID00000000101', '21', 'B', 'D', '05', '13.7', 'Cancelled Merchandise/Services', '438857', '000000399.99', '', '1', '********', '24019118325900011430806', '4387289252', 'D', '7372', '06439C', '********', '2018-11-21', '2018-11-28', '2018-11-21', NULL, '2018-12-03', '********', '11282018', '438857XXXXXX0783', '', '438857', '0783', 'THINKGLOBAL', '', '5868 E 71ST ST STE E', 'INDIANAPOLIS', 'IN', '46220-4076', '001000', '9286055201', NULL, '********', '588324061347579', '000000000.00', NULL, NULL, '201833200951313.7   03009286001141381421BD0513.7Cancelled Merchandise/Services                    438857000000399.99 1********240191183259000114308064387289252                    D737206439C****************11282018438857XXXXXX0783           4388570783THINKGLOBAL                                                                          5868 E 71ST ST STE E                    INDIANAPOLIS             IN46220-40760010009286055201      ********588324061347579000000000.0020518496', '2018-12-17 16:30:14.160717'),
    (13, 10549, '2018-12-17 16:30:14.161782', '2018335000028', '4837', '03', 'MID00000000101', '03', 'B', 'D', '05', '4837', 'No Cardholder Authorization', '007177', '000000005.99', '', '2', '********', '82303568301900012639683', '1386193079', 'D', '6300', '80562P', '10272018', '2018-10-27', '2018-12-01', '2018-10-28', NULL, '2018-12-03', '10282018', '12012018', '546616XXXXXX2191', '', '546616', '2191', 'GRIP PHONE PROTECTION', '', '9970 W CHEYENNE AVE', 'LAS VEGAS', 'NV', '89129-7700', '017000', '9286017203', NULL, '********', 'MWEWEBJN71027', '000000000.00', NULL, NULL, '20183350000284837   03009286002009226303BD054837No Cardholder Authorization                       007177000000005.99 2********823035683019000126396831386193079                    D630080562P102720181028201812012018546616XXXXXX2191           5466162191GRIP PHONE PROTECTION                                                                9970 W CHEYENNE AVE                     LAS VEGAS                NV89129-77000170009286017203      ********MWEWEBJN71027  000000000.0020294623', '2018-12-17 16:30:14.161782'),
    (16, 10549, '2018-12-17 16:30:14.162785', '2018337013379', '13.2', '01', 'MID00000000101', '21', 'M', 'D', '05', '13.2', 'Cancelled Recurring', '414720', '000000009.99', '', '1', '********', '24019118331900018888370', '1439882073', 'D', '6300', '08320C', '11272018', '2018-11-27', '2018-12-03', '2018-11-27', NULL, '2018-12-03', '11272018', '********', '414720XXXXXX0716', '', '414720', '0716', 'GRIP PHONE PROTECTION', '', '9970 W CHEYENNE AVE', 'LAS VEGAS', 'NV', '89129-7700', '017000', '9286017203', NULL, '********', '588332087704872', '000000009.99', 83276554, '2018-12-17 16:30:14', '201833701337913.2   01009286002009226321MD0513.2Cancelled Recurring                               414720000000009.99 1********240191183319000188883701439882073                    D630008320C1127201811272018********414720XXXXXX0716           4147200716GRIP PHONE PROTECTION                                                                9970 W CHEYENNE AVE                     LAS VEGAS                NV89129-77000170009286017203      ********588332087704872000000009.9920559547', '2018-12-17 16:30:14.197388'),
    (19, 10549, '2018-12-17 16:30:14.163656', '2018337013379', '13.2', '04', 'MID00000000101', '21', 'M', 'D', '05', '13.2', 'Cancelled Recurring', '414720', '000000009.99', '', '1', '********', '24019118331900018888370', '1439882073', 'D', '6300', '08320C', '11272018', '2018-11-27', '2018-12-03', '2018-11-27', NULL, '2018-12-03', '11272018', '********', '414720XXXXXX0716', '', '414720', '0716', 'GRIP PHONE PROTECTION', '', '9970 W CHEYENNE AVE', 'LAS VEGAS', 'NV', '89129-7700', '017000', '9286017203', NULL, '********', '588332087704872', '000000009.99', 83276554, '2018-12-17 16:30:14', '201833701337913.2   04009286002009226321MD0513.2Cancelled Recurring                               414720000000009.99 1********240191183319000188883701439882073                    D630008320C1127201811272018********414720XXXXXX0716           4147200716GRIP PHONE PROTECTION                                                                9970 W CHEYENNE AVE                     LAS VEGAS                NV89129-77000170009286017203      ********588332087704872000000009.9920559547', '2018-12-17 16:30:14.197388'),
    (22, 10549, '2018-12-17 16:30:14.164560', '2018337013380', '13.7', '01', 'MID00000000101', '21', '', 'D', '05', '13.7', 'Cancelled Merchandise/Services', '414740', '000000004.99', '', '1', '', '24019118259900014251977', '1348299339', 'D', '6300', '03121D', '09152018', '2018-09-15', '2018-12-03', '2018-09-16', NULL, NULL, '09162018', '********', '414740XXXXXX7486', '', '414740', '7486', 'GRIP PHONE PROTECTION', '', '9970 W CHEYENNE AVE', 'LAS VEGAS', 'NV', '89129-7700', '017000', '9286017203', NULL, '********', '468258390632239', '000000000.00', NULL, NULL, '201833701338013.7   01009286002009226321 D0513.7Cancelled Merchandise/Services                    414740000000004.99 1        240191182599000142519771348299339                    D630003121D0915201809162018********414740XXXXXX7486           4147407486GRIP PHONE PROTECTION                                                                9970 W CHEYENNE AVE                     LAS VEGAS                NV89129-77000170009286017203      ********468258390632239000000000.0020559548', '2018-12-17 16:30:14.164560'),
    (25, 10549, '2018-12-17 16:30:14.165484', '2018337013381', '13.7', '01', 'MID00000000101', '21', '', 'D', '05', '13.7', 'Cancelled Merchandise/Services', '414740', '000000004.99', '', '1', '', '24019118288900010209842', '1364403229', 'D', '6300', '03225C', '10152018', '2018-10-15', '2018-12-03', '2018-10-15', NULL, NULL, '10152018', '********', '414740XXXXXX7486', '', '414740', '7486', 'GRIP PHONE PROTECTION', '', '9970 W CHEYENNE AVE', 'LAS VEGAS', 'NV', '89129-7700', '017000', '9286017203', NULL, '********', '588288390671600', '000000000.00', NULL, NULL, '201833701338113.7   01009286002009226321 D0513.7Cancelled Merchandise/Services                    414740000000004.99 1        240191182889000102098421364403229                    D630003225C1015201810152018********414740XXXXXX7486           4147407486GRIP PHONE PROTECTION                                                                9970 W CHEYENNE AVE                     LAS VEGAS                NV89129-77000170009286017203      ********588288390671600000000000.0020559549', '2018-12-17 16:30:14.165484'),
    (28, 10549, '2018-12-17 16:30:14.166533', '2018337013382', '13.7', '01', 'MID00000000101', '21', '', 'D', '05', '13.7', 'Cancelled Merchandise/Services', '414740', '000000004.99', '', '1', '', '24019118319900016311827', '1419547367', 'D', '6300', '03147C', '11152018', '2018-11-15', '2018-12-03', '2018-11-15', NULL, NULL, '11152018', '********', '414740XXXXXX7486', '', '414740', '7486', 'GRIP PHONE PROTECTION', '', '9970 W CHEYENNE AVE', 'LAS VEGAS', 'NV', '89129-7700', '017000', '9286017203', NULL, '********', '588319426641074', '000000000.00', NULL, NULL, '201833701338213.7   01009286002009226321 D0513.7Cancelled Merchandise/Services                    414740000000004.99 1        240191183199000163118271419547367                    D630003147C1115201811152018********414740XXXXXX7486           4147407486GRIP PHONE PROTECTION                                                                9970 W CHEYENNE AVE                     LAS VEGAS                NV89129-77000170009286017203      ********588319426641074000000000.0020559550', '2018-12-17 16:30:14.166533'),
    (31, 10549, '2018-12-17 16:30:14.167400', '2018337013383', '13.2', '01', 'MID00000000101', '21', 'M', 'D', '05', '13.2', 'Cancelled Recurring', '446542', '000000049.99', '', '1', '********', '24019118319900018397063', '1419995147', 'D', '4899', '015903', '11152018', '2018-11-15', '2018-12-03', '2018-11-15', NULL, '2018-12-03', '11152018', '********', '446542XXXXXX1729', '', '446542', '1729', 'PHOENIX INTERNET', '', '2922 W CLARENDON AVE', 'PHOENIX', 'AZ', '85017-4609', '049000', '9286049203', NULL, '********', '388319642564526', '000000049.99', 82790456, '2018-12-17 16:30:14', '201833701338313.2   01009286001650826621MD0513.2Cancelled Recurring                               ***************.99 1********240191183199000183970631419995147                    D48990159031115201811152018********446542XXXXXX1729           4465421729PHOENIX INTERNET                                                                     2922 W CLARENDON AVE                    PHOENIX                  AZ85017-46090490009286049203      ********388319642564526000000049.9920559551', '2018-12-17 16:30:14.197388'),
    (34, 10549, '2018-12-17 16:30:14.168368', '2018337013383', '13.2', '04', 'MID00000000101', '21', 'M', 'D', '05', '13.2', 'Cancelled Recurring', '446542', '000000049.99', '', '1', '********', '24019118319900018397063', '1419995147', 'D', '4899', '015903', '11152018', '2018-11-15', '2018-12-03', '2018-11-15', NULL, '2018-12-03', '11152018', '********', '446542XXXXXX1729', '', '446542', '1729', 'PHOENIX INTERNET', '', '2922 W CLARENDON AVE', 'PHOENIX', 'AZ', '85017-4609', '049000', '9286049203', NULL, '********', '388319642564526', '000000049.99', 82790456, '2018-12-17 16:30:14', '201833701338313.2   04009286001650826621MD0513.2Cancelled Recurring                               ***************.99 1********240191183199000183970631419995147                    D48990159031115201811152018********446542XXXXXX1729           4465421729PHOENIX INTERNET                                                                     2922 W CLARENDON AVE                    PHOENIX                  AZ85017-46090490009286049203      ********388319642564526000000049.9920559551', '2018-12-17 16:30:14.197388');
INSERT INTO CardPresentCCTransaction (`ID`, `ProcessorID`, `MerchantName`, `MerchantID`, `TerminalID`, `CCProcessor`, `AuthCode`, `ActionCode`, `RequestType`, `TransactionType`, `TransAmount`, `AuthTransDate`, `FeeAmount`, `TaxAmount`, `SettlementDate`, `SettlementAmount`, `SettlementID`, `ApprovalStatus`, `Token`, `CardBin`, `CardLastFour`, `CardType`, `CurrencyCode`, `ExpDate`, `SafeCardNum`, `CardHolderName`, `Address`, `City`, `State`, `ZipCode`, `Country`, `Phone`, `Email`, `AVSResponse`, `CVVResponse`, `BankTransID`, `ExternalTransID`, `TransactionID`, `UserField1`, `UserField2`, `UserField3`, `POSTerminalID`, `POSAdditionalData`, `RetrievalReferenceNumber`, `VNumber`, `OrderNumber`, `ResponseText`, `ChargebackID`, `CardNumber`, `ChargebackLoadDate`, `SecondRequestID`, `ChargebackSecondRequest`, `DateAdded`, `DateUpdated`) VALUES
    (3, 8258025, 'DBA 1', 'MID00000000001', '************', '12', '193627', NULL, 'CAPT', 'I', 14000, '2019-04-26 11:32:15', NULL, NULL, '2019-04-28 00:00:00', NULL, NULL, 'APPROVED', NULL, '527519', '0933', 'MC', '840', NULL, '527519XXXXXX0933', '', '', '', '', '', '', '', '', 'Z', ' ', NULL, NULL, '0426MDJ0P9N5J00000', '', '', NULL, NULL, NULL, NULL, NULL, '', NULL, NULL, NULL, NULL, NULL, NULL, '2019-04-28 16:55:36.481825', '2019-04-28 16:55:36.481825'),
    (6, 8258028, 'DBA 1', 'MID00000000001', '************', '12', '015249', NULL, 'CAPT', 'I', 2452, '2019-04-26 05:52:49', NULL, NULL, '2019-04-28 00:00:00', NULL, NULL, 'APPROVED', NULL, '479144', '1466', 'VS', '840', NULL, '479144XXXXXX1466', '', '', '', '', '', '', '', '', 'Y', ' ', NULL, NULL, '309116211695396000', '', '', NULL, NULL, NULL, NULL, NULL, '', NULL, NULL, NULL, NULL, NULL, NULL, '2019-04-28 16:55:36.481825', '2019-04-28 16:55:36.481825'),
    (9, 8258031, 'DBA 1', 'MID00000000001', '************', '12', '184760', NULL, 'CAPT', 'I', 12900, '2019-04-26 06:46:25', NULL, NULL, '2019-04-28 00:00:00', NULL, NULL, 'APPROVED', NULL, '474475', '8370', 'VS', '840', NULL, '474475XXXXXX8370', '', '', '', '', '', '', '', '', 'Z', ' ', NULL, NULL, '389116243842451000', '', '', NULL, NULL, NULL, NULL, NULL, '', NULL, NULL, NULL, NULL, NULL, NULL, '2019-04-28 16:55:36.481825', '2019-04-28 16:55:36.481825'),
    (12, 8258034, 'DBA 1', 'MID00000000001', '************', '12', '195261', NULL, 'CAPT', 'I', 1000, '2019-04-26 09:56:05', NULL, NULL, '2019-04-28 00:00:00', NULL, NULL, 'APPROVED', NULL, '527519', '2057', 'MC', '840', NULL, '527519XXXXXX2057', '', '', '', '', '', '', '', '', 'Z', ' ', NULL, NULL, '0426MDJ1B5LNW00000', '', '', NULL, NULL, NULL, NULL, NULL, '', NULL, NULL, NULL, NULL, NULL, NULL, '2019-04-28 16:55:36.481825', '2019-04-28 16:55:36.481825'),
    (15, 8258037, 'DBA 1', 'MID00000000001', '************', '12', '171049', NULL, 'CAPT', 'I', 6900, '2019-04-26 10:14:49', NULL, NULL, '2019-04-28 00:00:00', NULL, NULL, 'APPROVED', NULL, '474475', '7652', 'VS', '840', NULL, '474475XXXXXX7652', '', '', '', '', '', '', '', '', 'Z', ' ', NULL, NULL, '469116368889055000', '', '', NULL, NULL, NULL, NULL, NULL, '', NULL, NULL, NULL, NULL, NULL, NULL, '2019-04-28 16:55:36.481825', '2019-04-28 16:55:36.481825'),
    (18, 8258040, 'DBA 1', 'MID00000000001', '************', '12', '193960', NULL, 'CAPT', 'I', 100, '2019-04-26 11:36:05', NULL, NULL, '2019-04-28 00:00:00', NULL, NULL, 'APPROVED', NULL, '527519', '0933', 'MC', '840', NULL, '527519XXXXXX0933', '', '', '', '', '', '', '', '', 'Z', ' ', NULL, NULL, '0426MDJ0QZN5J00000', '', '', NULL, NULL, NULL, NULL, NULL, '', NULL, NULL, NULL, NULL, NULL, NULL, '2019-04-28 16:55:36.481825', '2019-04-28 16:55:36.481825'),
    (21, 8258043, 'DBA 1', 'MID00000000001', '************', '12', '002419', NULL, 'CAPT', 'I', 25200, '2019-04-26 12:33:17', NULL, NULL, '2019-04-28 00:00:00', NULL, NULL, 'APPROVED', NULL, '473702', '8792', 'VS', '840', NULL, '473702XXXXXX8792', '', '', '', '', '', '', '', '', 'Y', ' ', NULL, NULL, '******************', '', '', NULL, NULL, NULL, NULL, NULL, '', NULL, NULL, NULL, NULL, NULL, NULL, '2019-04-28 16:55:36.481825', '2019-04-28 16:55:36.481825'),
    (24, 8258046, 'DBA 1', 'MID00000000001', '676760225992', '12', '522029', NULL, 'CREDIT', 'I', 3700, '2019-04-26 01:00:00', NULL, NULL, '2019-04-28 00:00:00', NULL, NULL, 'APPROVED', NULL, '463158', '6840', 'VS', '840', NULL, '463158XXXXXX6840', '', '', '', '', '', '', '', '', 'Y', ' ', NULL, NULL, '589116577782072000', '', '', NULL, NULL, NULL, NULL, NULL, '', NULL, NULL, NULL, NULL, NULL, NULL, '2019-04-28 16:55:36.481825', '2019-04-28 16:55:36.481825'),
    (27, 8258049, 'DBA 1', 'MID00000000001', '676760225992', '12', '02629Q', NULL, 'CAPT', 'I', 695, '2019-04-27 01:39:51', NULL, NULL, '2019-04-28 00:00:00', NULL, NULL, 'APPROVED', NULL, '601149', '4638', 'DS', '840', NULL, '601149XXXXXX4638', '', '', '', '', '', '', '', '', 'Y', 'M', NULL, NULL, '079101003918547000', '', '', NULL, NULL, NULL, NULL, NULL, '', NULL, NULL, NULL, NULL, NULL, NULL, '2019-04-28 16:55:36.481825', '2019-04-28 16:55:36.481825'),
    (30, 8258052, 'DBA 1', 'MID00000000001', '676760225992', '12', '02882B', NULL, 'CAPT', 'I', 695, '2019-04-27 02:54:29', NULL, NULL, '2019-04-28 00:00:00', NULL, NULL, 'APPROVED', NULL, '517805', '1295', 'MC', '840', NULL, '517805XXXXXX1295', '', '', '', '', '', '', '', '', 'Z', 'M', NULL, NULL, '0426MPLZ60MGN00000', '', '', NULL, NULL, NULL, NULL, NULL, '', NULL, NULL, NULL, NULL, NULL, NULL, '2019-04-28 16:55:36.481825', '2019-04-28 16:55:36.481825'),
    (33, 8258064, 'DBA 2', 'MID00000000002', '676756042377', '12', '02688B', NULL, 'CAPT', 'R', 350, '2019-04-26 16:58:34', NULL, NULL, '2019-04-28 00:00:00', NULL, NULL, 'APPROVED', NULL, '524363', '6965', 'MC', '840', NULL, '524363XXXXXX6965', '', '', '', '', '', '', '', '', ' ', ' ', NULL, NULL, '0426MPLJWGPDY00000', '', '', NULL, NULL, NULL, NULL, NULL, '', NULL, NULL, NULL, NULL, NULL, NULL, '2019-04-28 16:55:36.481825', '2019-04-28 16:55:36.481825'),
    (36, 8258067, 'DBA 2', 'MID00000000002', '676756042377', '12', '152979', NULL, 'CAPT', 'R', 700, '2019-04-26 17:09:24', NULL, NULL, '2019-04-28 00:00:00', NULL, NULL, 'APPROVED', NULL, '526924', '5056', 'MC', '840', NULL, '526924XXXXXX5056', '', '', '', '', '', '', '', '', ' ', ' ', NULL, NULL, '0426MDJT4RCDY00000', '', '', NULL, NULL, NULL, NULL, NULL, '', NULL, NULL, NULL, NULL, NULL, NULL, '2019-04-28 16:55:36.481825', '2019-04-28 16:55:36.481825'),
    (39, 8258070, 'DBA 2', 'MID00000000002', '676756042377', '12', '05254P', NULL, 'CAPT', 'R', 2750, '2019-04-26 17:15:08', NULL, NULL, '2019-04-28 00:00:00', NULL, NULL, 'APPROVED', NULL, '514021', '9153', 'MC', '840', NULL, '514021XXXXXX9153', '', '', '', '', '', '', '', '', ' ', ' ', NULL, NULL, '0426MCWV2I4DK00000', '', '', NULL, NULL, NULL, NULL, NULL, '', NULL, NULL, NULL, NULL, NULL, NULL, '2019-04-28 16:55:36.481825', '2019-04-28 16:55:36.481825'),
    (42, 8258073, 'DBA 2', 'MID00000000002', '676756042377', '12', '153522', NULL, 'CAPT', 'R', 5297, '2019-04-26 17:17:06', NULL, NULL, '2019-04-28 00:00:00', NULL, NULL, 'APPROVED', NULL, '526924', '5656', 'MC', '840', NULL, '526924XXXXXX5656', '', '', '', '', '', '', '', '', ' ', ' ', NULL, NULL, '0426MDJUBH5DY00000', '', '', NULL, NULL, NULL, NULL, NULL, '', NULL, NULL, NULL, NULL, NULL, NULL, '2019-04-28 16:55:36.481825', '2019-04-28 16:55:36.481825'),
    (45, 8258076, 'DBA 2', 'MID00000000002', '676756042377', '12', '883665', NULL, 'CAPT', 'R', 1796, '2019-04-26 17:18:07', NULL, NULL, '2019-04-28 00:00:00', NULL, NULL, 'APPROVED', NULL, '426447', '0133', 'VS', '840', NULL, '426447XXXXXX0133', '', '', '', '', '', '', '', '', ' ', ' ', NULL, NULL, '389116622875616000', '', '', NULL, NULL, NULL, NULL, NULL, '', NULL, NULL, NULL, NULL, NULL, NULL, '2019-04-28 16:55:36.481825', '2019-04-28 16:55:36.481825');
