
# tomcat
server.port=8080

spring.jackson.serialization.indent_output=true

# active profile
spring.profiles.active=@activatedProperties@

# springdoc
springdoc.server.url=http://localhost:${server.port}/
springdoc.swagger-ui.operationsSorter=method
springdoc.swagger-ui.tagsSorter=alpha

# spring data
spring.data.web.pageable.default-page-size=2147483647
spring.data.web.pageable.max-page-size=2147483647

# primary datasource
spring.primary-datasource.url=jdbc:mariadb://${db.primary.host}/${db.primary.database}
spring.primary-datasource.username=${db.primary.username}
spring.primary-datasource.password=${db.primary.password}
spring.primary-datasource.driver-class-name=org.mariadb.jdbc.Driver

# secondary datasource
spring.secondary-datasource.url=jdbc:mysql://${db.secondary.host}/${db.secondary.database}?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=UTC&allowPublicKeyRetrieval=true
spring.secondary-datasource.username=${db.secondary.username}
spring.secondary-datasource.password=${db.secondary.password}
spring.secondary-datasource.driverClassName=com.zaxxer.hikari.HikariDataSource

# liquibase
spring.liquibase.change-log=classpath:/changelog/migrations.xml
spring.liquibase.enabled=true
spring.liquibase.url=jdbc:mariadb://${db.primary.host}/${db.primary.database}
spring.liquibase.user=${db.primary.username}
spring.liquibase.password=${db.primary.password}
spring.liquibase.driver-class-name=org.mariadb.jdbc.Driver

# templates
mail.specific.template.fp=templates/auth_fp.vm
mail.specific.template.fp.reset-url=${frontend.specific.host}/auth/reset-password
mail.specific.template.invitation=templates/user_invitation.vm
mail.specific.template.invitation.signup-url=${frontend.specific.host}/auth/invitation-signup

management.endpoint.health.probes.enabled=true
management.endpoint.health.show-details=always
management.endpoint.health.group.readiness.include=db,diskSpace

ippay.trans.service.client-ip-http-header=INCAP-CLIENT-IP

# jpa
spring.jpa.show-sql=false
#spring.jpa.properties.hibernate.format_sql=true
#logging.level.org.hibernate.SQL=DEBUG
#logging.level.org.hibernate.type=TRACE


app.version=1.3