
frontend.specific.host=https://ippay-prod.osdb.io

# springdoc
springdoc.server.url=https://api-ippay-prod.osdb.io/

# primary datasource
db.primary.host=**************:3388
db.primary.database=ippay
db.primary.username=ippay
db.primary.password=qjjXGGWVU7EzN47a

# secondary datasource
db.secondary.host=**************:3706
db.secondary.database=ippay
db.secondary.username=ippay
db.secondary.password=o4oQeypT0lC7jvG3

# security (access-token = 45 min, reset-token = 90 min, password = 90 days)
security.jwt.access-token.secret-key=D6auJtKEPgA24V8Q9h45kTdZfV4cVY2c
security.jwt.access-token.expire-length=2700000
security.jwt.reset-token.secret-key=IenlBusy9E01beNJ0G7lL0xtRhguwi7T
security.jwt.reset-token.expire-length=5400000
security.password.expire-length=90

# cors / cookies configs
allowed.domain=osdb.io
allowed.origin=${frontend.specific.host}

# sendgrid
spring.sendgrid.api-key=*********************************************************************
mail.from.email=<EMAIL>

# transaction service
ippay.trans.service.url=https://testgtwy.ippay.com/ippay
ippay.trans.service.connection.timeout=120
ippay.trans.service.response.timeout=120


app.version=$VERSION
