
frontend.specific.host=https://ippay-dev.osdb.io

# springdoc
springdoc.server.url=https://api-ippay-dev.osdb.io/

# primary datasource
db.primary.host=**************:3306
db.primary.database=ippay
db.primary.username=ippay
db.primary.password=g80Q4TgeYjA88lSE

# secondary datasource
db.secondary.host=**************:3308
db.secondary.database=ippay
db.secondary.username=ippay
db.secondary.password=X9oVYq600Uc5TJ1D

# security (access-token = 24 hours, reset-token = 24 hours, password = 90 days)
security.jwt.access-token.secret-key=D6auJtKEPgA24V8Q9h45kTdZfV4cVY2c
security.jwt.access-token.expire-length=86400000
security.jwt.reset-token.secret-key=ogX58wjXuGHXFw30ZuZ8NIShKTLSkr1Y
security.jwt.reset-token.expire-length=86400000
security.password.expire-length=90

# cors / cookies configs
allowed.domain=osdb.io
allowed.origin=${frontend.specific.host}

# sendgrid
spring.sendgrid.api-key=*********************************************************************
mail.from.email=<EMAIL>

# transaction service
ippay.trans.service.url=https://testgtwy.ippay.com/ippay
ippay.trans.service.connection.timeout=120
ippay.trans.service.response.timeout=120


app.version=$VERSION
