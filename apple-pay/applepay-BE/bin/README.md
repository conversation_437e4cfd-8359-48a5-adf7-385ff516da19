IPPAY REPORTS BACKEND
---------------------

### Technologies

- Java 14
- Spring Boot 2.4.6
- Swagger, Lombok
- MariaDB, Liquibase
- Maven, Docker

### Requirements
- JDK 14+
- Any IDE of your choice (Lombok plugin required). For IntelliJ IDEA: Build, Execution, Deployment -> Compiler -> Annotation processors -> Enable annotation processing
- MariaDB
- Docker 19+

### Build Application

- Navigate to project directory
- mvn clean install -Plocal | -Pdev | -Pstage | -Pprod

### Run Application

- Navigate to project directory
- java -jar target/ippay-backend-1.0.0.jar

### Swagger. REST APIs Documentation

- Swagger UI endpoint: http://localhost:8080/swagger-ui.html