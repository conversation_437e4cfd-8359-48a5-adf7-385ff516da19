package com.osdb.ippay.secondary.chargeback.facade.impl;

import com.osdb.ippay.common.domain.FileDomain;
import com.osdb.ippay.common.security.service.SecurityService;
import com.osdb.ippay.primary.user.repository.entity.User;
import com.osdb.ippay.secondary.chargeback.facade.ChargebackFacade;
import com.osdb.ippay.secondary.chargeback.facade.dto.ChargebackDto;
import com.osdb.ippay.secondary.chargeback.facade.mapper.ChargebackMapper;
import com.osdb.ippay.secondary.chargeback.repository.entity.Chargeback;
import com.osdb.ippay.secondary.chargeback.service.ChargebackFilter;
import com.osdb.ippay.secondary.chargeback.service.ChargebackService;
import com.osdb.ippay.secondary.merchant.repository.entity.Merchant;
import com.osdb.ippay.secondary.merchant.service.MerchantService;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.PrintWriter;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static lombok.AccessLevel.PRIVATE;
import static org.apache.commons.collections.CollectionUtils.isEmpty;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

@Component
@RequiredArgsConstructor
@FieldDefaults(makeFinal = true, level = PRIVATE)
public class ChargebackFacadeImpl implements ChargebackFacade {

    // services
    ChargebackService chargebackService;
    SecurityService securityService;
    MerchantService merchantService;

    // mappers
    ChargebackMapper chargebackMapper;

    @Override
    public Page<ChargebackDto> find(ChargebackFilter filter, Pageable pageable) {
        User authUser = securityService.getCurrentUser();

        if(authUser.isNotAdminAndLevel1Support()) {
            List<String> merchantIds = securityService.getMerchantIds(authUser);
            filter.setMerchantIds(merchantIds);
        }

        Page<Chargeback> chargebacks = chargebackService.find(authUser, filter, pageable);
        return chargebackMapper.toDto(chargebacks);
    }

    @Override
    public List<String> findDates(String merchantId) {
        User authUser = securityService.getCurrentUser();

        return hasAccess(authUser, merchantId) ?
                chargebackService.findDates(merchantId) :
                Collections.emptyList();
    }

    @Override
    public FileDomain export(User user, ChargebackFilter filter) throws IOException {
        String[] HEADERS = {
                "Merchant Name",
                "Merchant ID",
                "Card",
                "Case Number",
                "Credit Card",
                "Amount",
                "Reason",
                "ResolutionTo",
                "Debit/Credit",
                "Type",
                "OriginalRef#",
                "Trans Date",
                "Chargeback Date",
                "ARN",
                "Chargeback Reason Code",
                "RDR"
        };

        final CSVFormat format = CSVFormat.Builder.create()
                .setHeader(HEADERS)
                .setIgnoreEmptyLines(true)
                .build();

        if(user.isNotAdminAndLevel1Support()) {
            List<String> merchantIds = securityService.getMerchantIds(user);
            filter.setMerchantIds(merchantIds);
        }

        List<Chargeback> chargebacks = chargebackService.find(user, filter);
        if(isEmpty(chargebacks)) return null;

        try (
                ByteArrayOutputStream out = new ByteArrayOutputStream();
                CSVPrinter csvPrinter = new CSVPrinter(new PrintWriter(out), format)
        ) {

            String pattern = "MM/dd/yyyy";
            DateTimeFormatter dateFormat = DateTimeFormatter.ofPattern(pattern);

            for (Chargeback chargeback : chargebacks) {
                Merchant merchant = chargeback.getMerchant();

                String dateTransaction = chargeback.getDateTransaction() != null ?
                        chargeback.getFormattedDateTransaction().format(dateFormat) : "";

                String dateResolved = chargeback.getDateResolved() != null ?
                        chargeback.getFormattedDateResolved().format(dateFormat) : "";

                List<String> data = Arrays.asList(
                        merchant != null ? merchant.getMerchantName() : null,
                        merchant != null ? merchant.getMerchantId() : null,
                        chargeback.getCardType(),
                        chargeback.getCaseNumber(),
                        chargeback.getCreditCard(),
                        chargeback.getAmount(),
                        chargeback.getReason(),
                        chargeback.getResolutionTo(),
                        chargeback.getDebitCredit(),
                        chargeback.getExtendedType(),
                        chargeback.getOriginRef(),
                        dateTransaction,
                        dateResolved,
                        chargeback.getAcquirerReferenceNumber(),
                        chargeback.getReasonCode(),
                        chargeback.getRdr()
                );

                csvPrinter.printRecord(data);
            }

            csvPrinter.flush();

            return FileDomain.builder()
                    .file(new ByteArrayResource(new ByteArrayInputStream(out.toByteArray()).readAllBytes()))
                    .fileName(getExportFileName(filter))
                    .build();
        }
    }

    private String getExportFileName(ChargebackFilter filter) {
        if(isNotBlank(filter.getMerchantId())) {
            Merchant merchant = merchantService.find(filter.getMerchantId());

            return String.format(
                    "%s_%s_Chargebacks_%s_%s.csv",
                    merchant.getMerchantName(),
                    merchant.getMerchantId(),
                    filter.getFromDate(),
                    filter.getToDate()
            );
        }

        return String.format(
                "Chargebacks_%s_%s.csv",
                filter.getFromDate(),
                filter.getToDate()
        );
    }

    private boolean hasAccess(User authUser, String merchantId) {
        if(authUser.isNotAdminAndLevel1Support()) {
            List<String> merchantIds = securityService.getMerchantIds(authUser);
            return merchantIds.contains(merchantId);
        }

        return true;
    }
}
