package com.osdb.ippay.secondary.alerts.service.impl;

import static lombok.AccessLevel.PRIVATE;

import org.springframework.stereotype.Service;

import com.osdb.ippay.common.exception.business.NotFoundException;
import com.osdb.ippay.secondary.alerts.repository.ThresholdSettingRepository;
import com.osdb.ippay.secondary.alerts.repository.entity.AlertType;
import com.osdb.ippay.secondary.alerts.repository.entity.ThresholdSetting;
import com.osdb.ippay.secondary.alerts.service.ThresholdSettingService;

import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@Service
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class ThresholdSettingServiceImpl implements ThresholdSettingService {
    static final String THRESHOLD_SETTING_NOT_FOUND = "Threshold setting not found";
    ThresholdSettingRepository thresholdSettingRepository;

    @Override
    public ThresholdSetting find(AlertType alertType, String merchantId) {
        try {
            return thresholdSettingRepository
                    .findByAlertTypeAndMerchantId(alertType, merchantId)
                    .orElseThrow(() -> new NotFoundException(THRESHOLD_SETTING_NOT_FOUND));
        } catch (NotFoundException ex) {
            ThresholdSetting thresholdSetting = new ThresholdSetting();
            thresholdSetting.setAlertType(alertType);
            thresholdSetting.setMerchantId(merchantId);
            thresholdSetting.setPercentAmountLimit(0);
            thresholdSetting.setCountLimit(0);
            thresholdSetting.setTriggerAlertOne(0);
            thresholdSetting.setTriggerAlertTwo(0);
            thresholdSetting.setTriggerAlertThree(0);
            thresholdSetting.setAlertToSupport(false);
            thresholdSetting.setAlertToRisk(false);
            thresholdSetting.setAlertToMerchant(false);
            thresholdSetting.setAlertToPartner(false);
            thresholdSetting.setTriggerAlertTime("23:30:00");
            thresholdSettingRepository.save(thresholdSetting);

            return thresholdSetting;
        }

    }

    @Override
    public ThresholdSetting update(AlertType alertType, String merchantId, ThresholdSetting setting) {
        return thresholdSettingRepository.save(setting);
    }

}
