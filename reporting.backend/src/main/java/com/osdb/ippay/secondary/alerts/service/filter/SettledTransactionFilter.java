package com.osdb.ippay.secondary.alerts.service.filter;

import static lombok.AccessLevel.PRIVATE;

import javax.validation.constraints.NotNull;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

@Data
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = PRIVATE)
public class SettledTransactionFilter {

    @NotNull(message = "Missing required parameter: 'startDateTime'.")
    @Parameter(example = "2020-01-01 00:00:00")
    String startDateTime;

    @NotNull(message = "Missing required parameter: 'endDateTime'.")
    @Parameter(example = "2020-02-01 00:00:00")
    String endDateTime;

}