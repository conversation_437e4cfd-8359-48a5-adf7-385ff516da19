package com.osdb.ippay.secondary.alerts.controller;

import static lombok.AccessLevel.PRIVATE;

import java.io.IOException;

import javax.validation.Valid;

import org.springdoc.api.annotations.ParameterObject;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.osdb.ippay.common.domain.FileDomain;
import com.osdb.ippay.common.logging.LogExecutionTime;
import com.osdb.ippay.secondary.alerts.facade.SettledTransactionFacade;
import com.osdb.ippay.secondary.alerts.service.filter.SettledTransactionFilter;

import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@Tag(name = "Alerts")
@RestController
@RequestMapping(value = "/api/v1/private")
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class SettledTransactionAlertController {

    SettledTransactionFacade settledTransactionFacade;

    static final String TRANSACTION_FAILED_ERR_MSG = "Transaction declined, with error: %s";

    @LogExecutionTime
    @GetMapping("/generate-monthly-settlement-csv-data")
    public ResponseEntity<ByteArrayResource> generateCSVData(
            @Valid @ParameterObject SettledTransactionFilter filter,
            @RequestParam("startDateTime") String startDateTime, @RequestParam("endDateTime") String endDateTime)
            throws IOException {

        FileDomain response = settledTransactionFacade.export(startDateTime, endDateTime, filter);

        return ResponseEntity.ok()
                .contentType(new MediaType("application", "force-download"))
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=".concat(response.getFileName()))
                .body(response.getFile());
    }

}