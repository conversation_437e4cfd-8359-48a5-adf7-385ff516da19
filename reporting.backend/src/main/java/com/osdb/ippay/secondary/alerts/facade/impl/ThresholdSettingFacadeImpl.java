package com.osdb.ippay.secondary.alerts.facade.impl;

import com.osdb.ippay.secondary.alerts.facade.ThresholdSettingFacade;
import com.osdb.ippay.secondary.alerts.facade.dto.ThresholdSettingDto;
import com.osdb.ippay.secondary.alerts.facade.mapper.ThresholdSettingMapper;
import com.osdb.ippay.secondary.alerts.repository.entity.AlertType;
import com.osdb.ippay.secondary.alerts.repository.entity.ThresholdSetting;
import com.osdb.ippay.secondary.alerts.service.ThresholdSettingService;
import com.osdb.ippay.secondary.alerts.repository.ThresholdSettingRepository;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.stereotype.Component;

import static lombok.AccessLevel.PRIVATE;

@Component
@RequiredArgsConstructor
@FieldDefaults(makeFinal = true, level = PRIVATE)
public class ThresholdSettingFacadeImpl implements ThresholdSettingFacade {
    ThresholdSettingService thresholdSettingService;
    ThresholdSettingMapper thresholdSettingMapper;
    ThresholdSettingRepository thresholdSettingRepository;

    @Override
    public ThresholdSettingDto find(AlertType alertType, String merchantId) {
        ThresholdSetting thresholdSetting = thresholdSettingService.find(alertType, merchantId);
        return thresholdSettingMapper.toDto(thresholdSetting);
    }

    @Override
    public ThresholdSettingDto update(AlertType alertType, String merchantId, ThresholdSettingDto thresholdSettingDto) {
        ThresholdSetting setting = thresholdSettingRepository.findByAlertTypeAndMerchantId(alertType, merchantId)
                .orElseThrow();
        thresholdSettingMapper.putEntity(thresholdSettingDto, setting);
        ThresholdSetting updatedSetting = thresholdSettingRepository.save(setting);
        return thresholdSettingMapper.toDto(updatedSetting);
    }
}
