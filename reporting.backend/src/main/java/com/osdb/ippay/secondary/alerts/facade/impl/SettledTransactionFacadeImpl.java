package com.osdb.ippay.secondary.alerts.facade.impl;

import static lombok.AccessLevel.PRIVATE;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.stereotype.Component;

import com.osdb.ippay.common.domain.FileDomain;
import com.osdb.ippay.secondary.alerts.facade.SettledTransactionFacade;
import com.osdb.ippay.secondary.alerts.repository.entity.SettledTransaction;
import com.osdb.ippay.secondary.alerts.service.SettledTransactionAlertService;
import com.osdb.ippay.secondary.alerts.service.filter.SettledTransactionFilter;

import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@Component
@RequiredArgsConstructor
@FieldDefaults(makeFinal = true, level = PRIVATE)
public class SettledTransactionFacadeImpl implements SettledTransactionFacade {

    // services
    SettledTransactionAlertService settledTransactionAlertService;

    @Override
    public FileDomain export(String startDateTime, String endDateTime, SettledTransactionFilter filter)
            throws IOException {
        String[] HEADERS = {
                "MID",
                "Settlement date",
                "% (70, 90 or 100) limit hit",
                "$ Amount Hit",
                "Overlimit Amount",
                "Actual Amount"
        };

        final CSVFormat format = CSVFormat.Builder.create()
                .setHeader(HEADERS)
                .setIgnoreEmptyLines(true)
                .build();

        List<SettledTransaction> settledTransactionData = settledTransactionAlertService.generateReport(startDateTime,
                endDateTime);
        try (
                ByteArrayOutputStream out = new ByteArrayOutputStream();
                CSVPrinter csvPrinter = new CSVPrinter(new PrintWriter(out), format)) {

            for (SettledTransaction settledTransactionObj : settledTransactionData) {
                List<String> data = Arrays.asList(
                        settledTransactionObj.getMerchantId(),
                        settledTransactionObj.getSettlementDate(),
                        settledTransactionObj.getPercentageHit(),
                        settledTransactionObj.getAmountHitOrExceeded(),
                        settledTransactionObj.getOverlimitAmount(),
                        settledTransactionObj.getActualAmount());

                csvPrinter.printRecord(data);
            }
            // csvPrinter.printRecord(data);
            csvPrinter.flush();

            return FileDomain.builder()
                    .file(new ByteArrayResource(
                            new ByteArrayInputStream(out.toByteArray()).readAllBytes()))
                    .fileName(
                            String.format(
                                    "Percentage-Monthly-Settlement-%s.csv",
                                    filter.getStartDateTime()))
                    .build();
        }

    }

    @Override
    public Map<String, ByteArrayOutputStream> findMerchantsDataToExport(String startDateTime, String endDateTime,
            SettledTransactionFilter filter)
            throws IOException {
        String[] HEADERS = {
                "MID",
                "Settlement date",
                "% (70, 90 or 100) limit hit",
                "$ Amount Hit",
                "Overlimit Amount",
                "Actual Amount"
        };

        Map<String, ByteArrayOutputStream> retHashMap = new HashMap<>();

        final CSVFormat format = CSVFormat.Builder.create()
                .setHeader(HEADERS)
                .setIgnoreEmptyLines(true)
                .build();

        Map<String, List<SettledTransaction>> settledTransactionData = settledTransactionAlertService
                .generateCSV(startDateTime, endDateTime);
        for (Map.Entry<String, List<SettledTransaction>> settledTransactoionSet : settledTransactionData
                .entrySet()) {

            String Mid = settledTransactoionSet.getKey();
            List<SettledTransaction> settledTrnObjList = settledTransactoionSet.getValue();

            try (
                    ByteArrayOutputStream retByteArrayOutputStream = new ByteArrayOutputStream();
                    CSVPrinter csvPrinter = new CSVPrinter(
                            new PrintWriter(retByteArrayOutputStream), format)) {

                for (SettledTransaction settledTransactionObj : settledTrnObjList) {
                    List<String> data = Arrays.asList(
                            settledTransactionObj.getMerchantId(),
                            settledTransactionObj.getSettlementDate(),
                            settledTransactionObj.getPercentageHit(),
                            settledTransactionObj.getAmountHitOrExceeded(),
                            settledTransactionObj.getOverlimitAmount(),
                            settledTransactionObj.getActualAmount());

                    csvPrinter.printRecord(data);
                }
                csvPrinter.flush();
                retHashMap.put(Mid, retByteArrayOutputStream);

            }

        }

        return retHashMap;

    }

}
