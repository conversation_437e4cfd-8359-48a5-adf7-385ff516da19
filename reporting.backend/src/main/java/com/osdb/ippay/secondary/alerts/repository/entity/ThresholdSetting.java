package com.osdb.ippay.secondary.alerts.repository.entity;

import static javax.persistence.EnumType.STRING;
import static javax.persistence.GenerationType.IDENTITY;
import static lombok.AccessLevel.PRIVATE;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Entity
@Table(name = "alertThresholdSetting")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
public class ThresholdSetting {

    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = IDENTITY)
    Long id;

    @Enumerated(value = STRING)
    @Column(name = "alert_type")
    AlertType alertType;

    @Column(name = "merchant_id")
    String merchantId;

    @Column(name = "percent_amount_limit")
    Integer percentAmountLimit;

    @Column(name = "count_limit")
    Integer countLimit;

    @Column(name = "trigger_alert_one")
    Integer triggerAlertOne;

    @Column(name = "trigger_alert_two")
    Integer triggerAlertTwo;

    @Column(name = "trigger_alert_three")
    Integer triggerAlertThree;

    @Column(name = "alert_to_support")
    Boolean alertToSupport;

    @Column(name = "alert_to_risk")
    Boolean alertToRisk;

    @Column(name = "alert_to_merchant")
    Boolean alertToMerchant;

    @Column(name = "alert_to_partner")
    Boolean alertToPartner;

    @Column(name = "trigger_alert_time")
    String triggerAlertTime;

}
