package com.osdb.ippay.secondary.alerts.facade.dto;

import static lombok.AccessLevel.PRIVATE;

import javax.validation.constraints.NotBlank;

import com.osdb.ippay.secondary.alerts.repository.entity.AlertType;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class ThresholdSettingDto {

    @NotBlank(message = "Alert type is required")
    AlertType alertType;

    @NotBlank(message = "merchant ID is required")
    String merchantId;

    Integer percentAmountLimit;

    Integer countLimit;

    Integer triggerAlertOne;

    Integer triggerAlertTwo;

    Integer triggerAlertThree;

    Boolean alertToSupport;

    Boolean alertToRisk;

    Boolean alertToMerchant;

    Boolean alertToPartner;

    String triggerAlertTime;
}
