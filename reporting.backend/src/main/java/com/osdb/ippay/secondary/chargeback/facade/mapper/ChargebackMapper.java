package com.osdb.ippay.secondary.chargeback.facade.mapper;

import com.osdb.ippay.common.file.service.FileService;
import com.osdb.ippay.secondary.chargeback.facade.dto.ChargebackDto;
import com.osdb.ippay.secondary.chargeback.repository.entity.Chargeback;
import com.osdb.ippay.secondary.merchant.repository.entity.Merchant;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;

import static java.lang.Boolean.TRUE;
import static lombok.AccessLevel.PRIVATE;

@Component
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class ChargebackMapper {

    FileService fileService;

    public Page<ChargebackDto> toDto(Page<Chargeback> chargebacks) {
        return chargebacks.map(this::toDto);
    }

    public ChargebackDto toDto(Chargeback chargeback) {
        Merchant merchant = chargeback.getMerchant();

        String reportFileName = getReportFileName(chargeback);
        Boolean isReportExist = fileService.isCBFileExist(reportFileName);

        return ChargebackDto.builder()
                .id(chargeback.getId())
                .merchantName(merchant != null ? merchant.getMerchantName() : null)
                .merchantId(merchant != null ? merchant.getMerchantId() : null)
                .cardType(chargeback.getCardType())
                .creditCard(chargeback.getCreditCard())
                .caseNumber(chargeback.getCaseNumber())
                .amount(chargeback.getAmount())
                .reason(chargeback.getReason())
                .resolutionTo(chargeback.getResolutionTo())
                .debitCredit(chargeback.getDebitCredit())
                .type(chargeback.getExtendedType())
                .originRef(chargeback.getOriginRef())
                .dateTransaction(chargeback.getFormattedDateTransaction())
                .dateResolved(chargeback.getFormattedDateResolved())
                .merchantNumber(chargeback.getMerchantNumber())
                .reportFileName(TRUE.equals(isReportExist) ? reportFileName : null)
                .acquirerReferenceNumber(chargeback.getAcquirerReferenceNumber())
                .reasonCode(chargeback.getReasonCode())
                .rdr(chargeback.getRdr())
                .build();
    }

    private String getReportFileName(Chargeback chargeback) {
        return String.format(
                "%s_12_%s.pdf",
                chargeback.getCaseNumber(),
                chargeback.getMerchantNumber()
        );
    }
}
