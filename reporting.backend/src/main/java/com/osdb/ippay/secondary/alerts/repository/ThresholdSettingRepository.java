package com.osdb.ippay.secondary.alerts.repository;

import com.osdb.ippay.secondary.alerts.repository.entity.ThresholdSetting;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.Optional;

import com.osdb.ippay.secondary.alerts.repository.entity.AlertType;

@Repository
public interface ThresholdSettingRepository extends
        JpaRepository<ThresholdSetting, Long>,
        JpaSpecificationExecutor<ThresholdSetting> {

    Optional<ThresholdSetting> findByAlertTypeAndMerchantId(AlertType alertType, String merchantId);

}
