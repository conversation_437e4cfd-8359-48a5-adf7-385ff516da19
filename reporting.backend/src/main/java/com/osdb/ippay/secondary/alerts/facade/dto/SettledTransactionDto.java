package com.osdb.ippay.secondary.alerts.facade.dto;

import static lombok.AccessLevel.PRIVATE;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class SettledTransactionDto {

    Long ipTransId;

    String merchantId;

    String email;

    String settlementDate;

    String percentageHit;

    String actualAmount;

    String amountHitOrExceeded;

    String overlimitAmount;

    String triggerAlertTime;

}
