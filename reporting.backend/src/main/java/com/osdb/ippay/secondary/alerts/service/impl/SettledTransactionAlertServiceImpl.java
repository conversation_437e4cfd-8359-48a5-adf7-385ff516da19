package com.osdb.ippay.secondary.alerts.service.impl;

import static lombok.AccessLevel.PRIVATE;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.persistence.EntityManager;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.osdb.ippay.secondary.alerts.repository.MerchantSettlementRepository;
import com.osdb.ippay.secondary.alerts.repository.SettledTransactionRepository;
import com.osdb.ippay.secondary.alerts.repository.entity.SettledTransaction;
import com.osdb.ippay.secondary.alerts.service.SettledTransactionAlertService;
import com.osdb.ippay.secondary.alerts.service.filter.SettledTransactionFilter;

import lombok.experimental.FieldDefaults;

@Service
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class SettledTransactionAlertServiceImpl implements SettledTransactionAlertService {

    SettledTransactionRepository settledTransactionRepository;
    MerchantSettlementRepository merchantSettlementRepository;
    public static java.util.Date currDate = new java.util.Date();
    public static String eportType = "";

    EntityManager entityManager;
    Map<String, List<SettledTransaction>> mapSettledTrnList = new HashMap<>();

    @Autowired
    public SettledTransactionAlertServiceImpl(SettledTransactionRepository settldTransactionRepository,
            MerchantSettlementRepository merchantSetlmtRepository,
            SettledTransactionRepository settledTransactionAlert,
            @Qualifier("secondaryEmFactory") EntityManager entityManager) {

        this.settledTransactionRepository = settldTransactionRepository;
        this.merchantSettlementRepository = merchantSetlmtRepository;
        this.entityManager = entityManager;
    }

    @Override
    public List generateReport(String startDateTime,
            String endDateTime) {
        List settledOverlimitTransactions = settledTransactionRepository
                .getMonthlyOverlimitSettlements(startDateTime,
                        endDateTime);

        if (settledOverlimitTransactions.isEmpty()) {

            return new ArrayList<>();
        }

        if (settledOverlimitTransactions.get(0) instanceof String) {
            return ((List<String>) settledOverlimitTransactions)
                    .stream()
                    .map(s -> new String[] { s })
                    .collect(Collectors.toList());
        } else {
            return (List<String[]>) settledOverlimitTransactions;

        }
    }

    @Override
    public Map<String, List<SettledTransaction>> generateCSV(String startDateTime,
            String endDateTime) {

        return mapSettledTrnList;
    }

    @Override
    public List<SettledTransaction> find(String startDateTime, String endDateTime, SettledTransactionFilter filter) {

        List<SettledTransaction> settledOverlimitTransactions = settledTransactionRepository
                .getOverlimitSettledTransactions(startDateTime, endDateTime);

        return settledOverlimitTransactions;
    }

    @Override
    public List generateCountReport(String startDateTime, String endDateTime) {

        List settledOverlimitTransactions = settledTransactionRepository
                .getMonthlyOverlimitSettlementsCount(startDateTime, endDateTime);

        if (settledOverlimitTransactions.isEmpty()) {

            return new ArrayList<>();
        }

        if (settledOverlimitTransactions.get(0) instanceof String) {
            return ((List<String>) settledOverlimitTransactions)
                    .stream()
                    .map(s -> new String[] { s })
                    .collect(Collectors.toList());
        } else {
            return (List<String[]>) settledOverlimitTransactions;
        }

    }
}
