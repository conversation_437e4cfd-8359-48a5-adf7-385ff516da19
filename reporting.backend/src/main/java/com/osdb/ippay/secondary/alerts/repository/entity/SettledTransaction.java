package com.osdb.ippay.secondary.alerts.repository.entity;

import static lombok.AccessLevel.PRIVATE;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.NamedNativeQueries;
import javax.persistence.NamedNativeQuery;
import javax.persistence.Table;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Entity(name = "SettledTransaction")
@Table(name = "CCTransactions")

@NamedNativeQueries({
            @NamedNativeQuery(resultClass = SettledTransaction.class, name = "SettledTransaction.getMonthlyOverlimitSettlements", query = "SELECT Distinct merchantId, email, settlementDate, (select trigger_alert_time from alertThresholdSetting where alert_type='MONTHLY_SETTLEMENT' limit 1) as triggerAlertTime, SUM(IF(RequestType = 'SALE' OR RequestType = 'CAPT', SettlementAmount, 0)) as actualAmount,IF((SUM(IF(RequestType = 'SALE' OR RequestType = 'CAPT', SettlementAmount, 0))/(select percent_amount_limit from alertThresholdSetting where alert_type='MONTHLY_SETTLEMENT' limit 1))>(select trigger_alert_one from alertThresholdSetting where alert_type='MONTHLY_SETTLEMENT' limit 1)/100,(select trigger_alert_one from alertThresholdSetting where alert_type='MONTHLY_SETTLEMENT' limit 1),(IF((SUM(IF(RequestType = 'SALE' OR RequestType = 'CAPT', SettlementAmount, 0))/(select percent_amount_limit from alertThresholdSetting where alert_type='MONTHLY_SETTLEMENT' limit 1))>(select trigger_alert_two from alertThresholdSetting where alert_type='MONTHLY_SETTLEMENT' limit 1)/100,(select trigger_alert_two from alertThresholdSetting where alert_type='MONTHLY_SETTLEMENT' limit 1),(select trigger_alert_three from alertThresholdSetting where alert_type='MONTHLY_SETTLEMENT' limit 1)))) as percentageHit, (select percent_amount_limit from alertThresholdSetting where alert_type='MONTHLY_SETTLEMENT' limit 1) as amountHitOrExceeded,(select alert_to_support from alertThresholdSetting where alert_type='MONTHLY_SETTLEMENT' limit 1) as alert_to_support, (select alert_to_risk from alertThresholdSetting where alert_type='MONTHLY_SETTLEMENT' limit 1) as alert_to_risk, (SUM(IF(RequestType = 'SALE' OR RequestType = 'CAPT', SettlementAmount, 0)) - (select percent_amount_limit from alertThresholdSetting where alert_type='MONTHLY_SETTLEMENT' limit 1)) as overlimitAmount FROM CCTransactions WHERE SettlementDate >= ?1 AND SettlementDate <= ?2  GROUP BY merchantId,settlementDate,email, triggerAlertTime, amountHitOrExceeded, alert_to_support, alert_to_risk HAVING SUM(IF(RequestType = 'SALE' OR RequestType = 'CAPT', SettlementAmount, 0)) > (select percent_amount_limit from alertThresholdSetting where alert_type='MONTHLY_SETTLEMENT' limit 1)"),
            @NamedNativeQuery(resultClass = SettledTransaction.class, name = "SettledTransaction.getMonthlyOverlimitSettlementsCount", query = "SELECT Distinct merchantId, email, settlementDate, (select trigger_alert_time from alertThresholdSetting where alert_type='MONTHLY_SETTLEMENT' limit 1) as triggerAlertTime, SUM(IF(RequestType = 'SALE' OR RequestType = 'CAPT', SettlementAmount, 0)) as actualAmount,IF((COUNT(IF(RequestType = 'SALE' OR RequestType = 'CAPT', SettlementAmount, 0))/(select count_limit from alertThresholdSetting where alert_type='MONTHLY_SETTLEMENT' limit 1))>(select trigger_alert_one from alertThresholdSetting where alert_type='MONTHLY_SETTLEMENT' limit 1)/100,(select trigger_alert_one from alertThresholdSetting where alert_type='MONTHLY_SETTLEMENT' limit 1),(IF((COUNT(IF(RequestType = 'SALE' OR RequestType = 'CAPT', SettlementAmount, 0))/(select count_limit from alertThresholdSetting where alert_type='MONTHLY_SETTLEMENT' limit 1))>(select trigger_alert_two from alertThresholdSetting where alert_type='MONTHLY_SETTLEMENT' limit 1)/100,(select trigger_alert_two from alertThresholdSetting where alert_type='MONTHLY_SETTLEMENT' limit 1),(select trigger_alert_three from alertThresholdSetting where alert_type='MONTHLY_SETTLEMENT' limit 1)))) as percentageHit, (select count_limit from alertThresholdSetting where alert_type='MONTHLY_SETTLEMENT' limit 1) as amountHitOrExceeded,(select alert_to_support from alertThresholdSetting where alert_type='MONTHLY_SETTLEMENT' limit 1) as alert_to_support, (select alert_to_risk from alertThresholdSetting where alert_type='MONTHLY_SETTLEMENT' limit 1) as alert_to_risk, (SUM(IF(RequestType = 'SALE' OR RequestType = 'CAPT', SettlementAmount, 0)) - (select percent_amount_limit from alertThresholdSetting where alert_type='MONTHLY_SETTLEMENT' limit 1)) as overlimitAmount FROM CCTransactions WHERE SettlementDate >= ?1 AND SettlementDate <= ?2  GROUP BY merchantId,settlementDate,email, triggerAlertTime, amountHitOrExceeded, alert_to_support, alert_to_risk HAVING SUM(IF(RequestType = 'SALE' OR RequestType = 'CAPT', SettlementAmount, 0)) > (select percent_amount_limit from alertThresholdSetting where alert_type='MONTHLY_SETTLEMENT' limit 1)") })

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)

public class SettledTransaction {

      @Id
      @Column(name = "merchantId", insertable = false, updatable = false)
      String merchantId;

      @Column(name = "settlementDate", insertable = false, updatable = false)
      String settlementDate;

      @Column(name = "email", insertable = false, updatable = false)
      String email;

      @Column(name = "percentageHit", insertable = false, updatable = false)
      String percentageHit;

      @Column(name = "amountHitOrExceeded", insertable = false, updatable = false)
      String amountHitOrExceeded;

      @Column(name = "overlimitAmount", insertable = false, updatable = false)
      String overlimitAmount;

      @Column(name = "actualAmount", insertable = false, updatable = false)
      String actualAmount;

      @Column(name = "triggerAlertTime")
      String triggerAlertTime;

}
