package com.osdb.ippay.secondary.alerts.service;

import java.util.List;
import java.util.Map;

import com.osdb.ippay.secondary.alerts.repository.entity.SettledTransaction;
import com.osdb.ippay.secondary.alerts.service.filter.SettledTransactionFilter;

public interface SettledTransactionAlertService {

    List<SettledTransaction> find(String startDateTime,
            String endDateTime,
            SettledTransactionFilter filter);

    Map<String, List<SettledTransaction>> generateCSV(String startDateTime, String endDateTime);

    List generateReport(String startDateTime, String endDateTime);

    List generateCountReport(String startDateTime, String endDateTime);
}
