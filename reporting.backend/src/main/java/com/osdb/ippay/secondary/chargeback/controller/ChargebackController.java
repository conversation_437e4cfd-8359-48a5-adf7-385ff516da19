package com.osdb.ippay.secondary.chargeback.controller;

import com.osdb.ippay.common.domain.FileDomain;
import com.osdb.ippay.common.exception.handling.Error;
import com.osdb.ippay.common.exception.handling.ErrorResponse;
import com.osdb.ippay.common.logging.LogExecutionTime;
import com.osdb.ippay.common.security.service.SecurityService;
import com.osdb.ippay.common.util.annotation.ApiPageable;
import com.osdb.ippay.common.util.response.PageResponse;
import com.osdb.ippay.primary.user.repository.entity.User;
import com.osdb.ippay.secondary.chargeback.facade.ChargebackFacade;
import com.osdb.ippay.secondary.chargeback.facade.dto.ChargebackDto;
import com.osdb.ippay.secondary.chargeback.service.ChargebackFilter;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static java.util.Objects.isNull;
import static lombok.AccessLevel.PRIVATE;
import static org.springframework.http.HttpHeaders.CONTENT_DISPOSITION;
import static org.springframework.http.HttpHeaders.CONTENT_TYPE;
import static org.springframework.http.HttpStatus.BAD_REQUEST;

@Tag(name = "chargeback")
@RestController
@RequestMapping(value = "/api/v1/private/chargebacks")
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class ChargebackController {

    ChargebackFacade chargebackFacade;
    SecurityService securityService;

    @LogExecutionTime
    @ApiPageable
    @GetMapping
    public ResponseEntity<PageResponse<ChargebackDto>> get(@Valid @ParameterObject ChargebackFilter filter,
                                                           @Parameter(hidden = true) Pageable pageable) {

        Page<ChargebackDto> response = chargebackFacade.find(filter, pageable);
        return ResponseEntity.ok(new PageResponse<>(response.getContent(), response.getTotalElements()));
    }

    @LogExecutionTime
    @GetMapping("/{merchantId}/dates")
    public ResponseEntity<List<String>> getQuickDates(@PathVariable String merchantId) {
        List<String> response = chargebackFacade.findDates(merchantId);
        return ResponseEntity.ok(response);
    }

    @LogExecutionTime
    @GetMapping("/export")
    public ResponseEntity<?> export(@Valid @ParameterObject ChargebackFilter filter) throws IOException {
        User user = securityService.getCurrentUser();
        FileDomain response = chargebackFacade.export(user, filter);

        if(isNull(response)) {
            return ResponseEntity.badRequest().body(
                    new ErrorResponse(
                            System.currentTimeMillis(),
                            BAD_REQUEST.value(),
                            List.of(Error.builder().message("No chargeback data to export").build())
                    )
            );
        }

        return ResponseEntity.ok()
                .contentType(new MediaType("application", "force-download"))
                .header(CONTENT_DISPOSITION, "attachment; filename=".concat(response.getFileName()))
                .header(CONTENT_TYPE, "text/csv")
                .body(response.getFile());
    }
}
