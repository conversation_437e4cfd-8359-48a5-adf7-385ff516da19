package com.osdb.ippay.secondary.alerts.service.impl;

import static lombok.AccessLevel.PRIVATE;

import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.PrintWriter;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.mysql.cj.log.Log;
import com.osdb.ippay.primary.user.repository.entity.User;
import com.osdb.ippay.primary.user.service.UserService;
import com.osdb.ippay.primary.user.service.filter.UserFilter;
import com.osdb.ippay.secondary.alerts.facade.ThresholdSettingFacade;
import com.osdb.ippay.secondary.alerts.facade.dto.ThresholdSettingDto;
import com.osdb.ippay.secondary.alerts.repository.entity.AlertType;
import com.osdb.ippay.secondary.alerts.repository.entity.ThresholdSetting;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import com.osdb.ippay.email.bean.Message;
import com.osdb.ippay.email.service.EmailService;
import com.osdb.ippay.secondary.alerts.repository.entity.SettledTransaction;
import com.osdb.ippay.secondary.alerts.service.AlertScheduleService;
import com.osdb.ippay.secondary.alerts.service.SettledTransactionAlertService;
import com.sendgrid.helpers.mail.objects.Attachments;

import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class AlertScheduleServiceImpl implements AlertScheduleService {
    @Autowired
    private EmailService emailService;

    @Autowired
    private SettledTransactionAlertService settledTransactionAlertService;

    @Autowired
    private ThresholdSettingFacade thresholdSettingFacade;

    @Autowired
    private UserService userService;

    @NonFinal
    String scheduledTime = "";

    @NonFinal
    @Value(value = "${mail.specific.template.alert-monthly-settlements}")
    String alertMonthlySettlementsTemplate;

    @NonFinal
    @Value("${mail.to.email}")
    String emailTo;

    @NonFinal
    @Value("${mail.to.risk.email}")
    String emailToRisk;

    @NonFinal
    @Value("${mail.to.support.email}")
    String emailToSupport;

    public static java.util.Date currDate = new java.util.Date();
    ArrayList<String> arrMerchants = new ArrayList<String>();

    // Execute every 1 minutes
//    @Scheduled(fixedDelay = 60 * 1000)
    public void MonthlySettlementAlertSchedule() throws FileNotFoundException, IOException {
        Map<String, Object> params = new HashMap<>();
        params.put("username", "Merchant");

        String[] HEADERS = {
                "MID",
                "Settlement date",
                "% (70, 90 or 100) limit hit",
                "$ Amount Hit Or Item Count",
                "Overlimit Amount",
                "Actual Amount"
        };

        final CSVFormat format = CSVFormat.Builder.create()
                .setHeader(HEADERS)
                .setIgnoreEmptyLines(true)
                .build();

        // Get the current month
        YearMonth currentMonth = YearMonth.now();
        // Get the start and end dates of the last month
        LocalDate startOfLastMonth = currentMonth.atDay(1);
        LocalDate endOfLastMonth = currentMonth.atEndOfMonth();

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String startDate = startOfLastMonth.format(formatter);
        String endDate = endOfLastMonth.format(formatter);
        List<SettledTransaction> settledTransactionData = settledTransactionAlertService.generateReport(
                startDate, endDate);

        arrMerchants.clear();
        for (SettledTransaction settledTransactionObj : settledTransactionData) {

            scheduledTime = settledTransactionObj.getTriggerAlertTime();

            List<String> data = Arrays.asList(
                    settledTransactionObj.getMerchantId(),
                    settledTransactionObj.getSettlementDate(),
                    settledTransactionObj.getPercentageHit(),
                    settledTransactionObj.getAmountHitOrExceeded(),
                    settledTransactionObj.getOverlimitAmount(),
                    settledTransactionObj.getActualAmount());

            if (LocalTime.now().isAfter(LocalTime.parse(scheduledTime))) {

                java.util.Date triggerDate = getDateWithoutTimeUsingCalendar();
                if (!triggerDate.toString().trim().equals(SettledTransactionAlertServiceImpl.currDate.toString())) {

                    log.debug("triggerDate - " + triggerDate.toString());
                    log.debug("currDate - " +
                            SettledTransactionAlertServiceImpl.currDate.toString().trim());

                    if (!arrMerchants.contains(settledTransactionObj.getMerchantId())) {
                        ByteArrayOutputStream out = new ByteArrayOutputStream();
                        CSVPrinter csvPrinter = new CSVPrinter(new PrintWriter(out), format);
                        csvPrinter.printRecord(data);
                        csvPrinter.flush();
                        csvPrinter.close();
                        String reportType = "Monthly Settlement - Dollar amount Exceeded Alert";
                        byte[] content = out.toByteArray();
                        List<Attachments> attachments = new ArrayList<>();
                        attachments.add(emailService.getAttachments(content, "monthly_settlement_dollar_amount.csv"));

                        // Fetch the threshold setting for this merchant and alert type
                        ThresholdSettingDto thresholdSetting = thresholdSettingFacade
                                .find(AlertType.MONTHLY_SETTLEMENT, settledTransactionObj.getMerchantId());
                        List<String> recipients = new ArrayList<>();

                        if (Boolean.TRUE.equals(thresholdSetting.getAlertToSupport())) {
                            recipients.add(emailToSupport);
                        }
                        if (Boolean.TRUE.equals(thresholdSetting.getAlertToRisk())) {
                            recipients.add(emailToRisk);
                        }
                        if (Boolean.TRUE.equals(thresholdSetting.getAlertToMerchant())) {
                            UserFilter userFilter = new UserFilter();
                            userFilter.setMerchantId(settledTransactionObj.getMerchantId());
                            List<User> merchantUsers = userService.find(userFilter);
                            List<String> merchantEmails = merchantUsers.stream()
                                    .map(User::getEmail)
                                    .filter(StringUtils::isNotBlank)
                                    .collect(Collectors.toList());
                            recipients.addAll(merchantEmails);
                        }

                        if (recipients.isEmpty()) {
                            log.warn("No email recipients found for merchant: " + settledTransactionObj.getMerchantId());
                        } else {
                            log.debug("mail sent");
                            for (String aRecipient : recipients) {
                                Message message = Message.builder()
                                        .subject(reportType)
                                        .template(alertMonthlySettlementsTemplate)
                                        .params(params)
                                        .attachments(attachments)
                                        .to(aRecipient)
                                        .build();

                                emailService.sendEmail(message);
                            }
                        }
                        arrMerchants.add(settledTransactionObj.getMerchantId());
                        out.flush();
                        out.close();

                    }

                }
            }

        }

        // Count Report
        settledTransactionData = settledTransactionAlertService.generateCountReport(startDate, endDate);

        arrMerchants.clear();
        for (SettledTransaction settledTransactionObj : settledTransactionData) {

            scheduledTime = settledTransactionObj.getTriggerAlertTime();

            List<String> data = Arrays.asList(
                    settledTransactionObj.getMerchantId(),
                    settledTransactionObj.getSettlementDate(),
                    settledTransactionObj.getPercentageHit(),
                    settledTransactionObj.getAmountHitOrExceeded(),
                    settledTransactionObj.getOverlimitAmount(),
                    settledTransactionObj.getActualAmount());

            if (LocalTime.now().isAfter(LocalTime.parse(scheduledTime))) {

                java.util.Date triggerDate = getDateWithoutTimeUsingCalendar();
                if (!triggerDate.toString().trim().equals(SettledTransactionAlertServiceImpl.currDate.toString())) {

                    log.debug("triggerDate - " + triggerDate.toString());
                    log.debug("currDate - " +
                            SettledTransactionAlertServiceImpl.currDate.toString().trim());

                    if (!arrMerchants.contains(settledTransactionObj.getMerchantId())) {
                        ByteArrayOutputStream out = new ByteArrayOutputStream();
                        CSVPrinter csvPrinter = new CSVPrinter(new PrintWriter(out), format);
                        csvPrinter.printRecord(data);
                        csvPrinter.flush();
                        csvPrinter.close();
                        String reportType = "Monthly Settlement - Item count Exceeded Alert";
                        byte[] content = out.toByteArray();
                        List<Attachments> attachments = new ArrayList<>();
                        attachments.add(emailService.getAttachments(content, "monthly_settlement_item_count.csv"));

                        // Fetch the threshold setting for this merchant and alert type
                        ThresholdSettingDto thresholdSetting = thresholdSettingFacade
                                .find(AlertType.MONTHLY_SETTLEMENT, settledTransactionObj.getMerchantId());
                        List<String> recipients = new ArrayList<>();

                        if (Boolean.TRUE.equals(thresholdSetting.getAlertToSupport())) {
                            recipients.add(emailToSupport);
                        }
                        if (Boolean.TRUE.equals(thresholdSetting.getAlertToRisk())) {
                            recipients.add(emailToRisk);
                        }
                        if (Boolean.TRUE.equals(thresholdSetting.getAlertToMerchant())) {
                            UserFilter userFilter = new UserFilter();
                            userFilter.setMerchantId(settledTransactionObj.getMerchantId());
                            List<User> merchantUsers = userService.find(userFilter);
                            List<String> merchantEmails = merchantUsers.stream()
                                    .map(User::getEmail)
                                    .filter(StringUtils::isNotBlank)
                                    .collect(Collectors.toList());
                            recipients.addAll(merchantEmails);
                        }

                        if (recipients.isEmpty()) {
                            log.warn("No email recipients found for merchant: " + settledTransactionObj.getMerchantId());
                        } else {
                            log.debug("mail sent");
                            for (String aRecipient : recipients) {
                                Message message = Message.builder()
                                        .subject(reportType)
                                        .template(alertMonthlySettlementsTemplate)
                                        .params(params)
                                        .attachments(attachments)
                                        .to(aRecipient)
                                        .build();

                                emailService.sendEmail(message);
                            }
                        }
                        arrMerchants.add(settledTransactionObj.getMerchantId());
                        out.flush();
                        out.close();

                    }

                }
            }

        }

        SettledTransactionAlertServiceImpl.currDate = getDateWithoutTimeUsingCalendar();

    }

    public static java.util.Date getDateWithoutTimeUsingCalendar() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        return calendar.getTime();
    }
}
