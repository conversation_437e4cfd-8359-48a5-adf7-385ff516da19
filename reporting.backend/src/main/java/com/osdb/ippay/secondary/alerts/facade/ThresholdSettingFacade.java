package com.osdb.ippay.secondary.alerts.facade;

import com.osdb.ippay.secondary.alerts.facade.dto.ThresholdSettingDto;
import com.osdb.ippay.secondary.alerts.repository.entity.AlertType;

public interface ThresholdSettingFacade {

    ThresholdSettingDto find(AlertType alertType, String merchantId);

    ThresholdSettingDto update(AlertType alertType, String merchantId, ThresholdSettingDto thresholdSettingDto);
}