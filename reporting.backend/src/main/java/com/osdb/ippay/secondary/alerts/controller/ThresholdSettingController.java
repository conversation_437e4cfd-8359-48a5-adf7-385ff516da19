package com.osdb.ippay.secondary.alerts.controller;

import static lombok.AccessLevel.PRIVATE;

import javax.validation.Valid;

import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.osdb.ippay.common.logging.LogExecutionTime;
import com.osdb.ippay.secondary.alerts.facade.ThresholdSettingFacade;
import com.osdb.ippay.secondary.alerts.facade.dto.ThresholdSettingDto;
import com.osdb.ippay.secondary.alerts.repository.entity.AlertType;
import com.osdb.ippay.secondary.alerts.service.filter.ThresholdSettingFilter;

import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@Tag(name = "Alerts")
@RestController
@RequestMapping(value = "/api/v1/private/threshold-setting")
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class ThresholdSettingController {

    ThresholdSettingFacade thresholdSettingFacade;

    @LogExecutionTime
    @GetMapping("/")
    public ResponseEntity<ThresholdSettingDto> getThresholdSetting(
            @Valid @ParameterObject ThresholdSettingFilter filter) {
        ThresholdSettingDto response = thresholdSettingFacade.find(filter.getAlertType(), filter.getMerchantId());
        return ResponseEntity.ok(response);
    }

    @LogExecutionTime
    @PutMapping("/")
    public ResponseEntity<ThresholdSettingDto> updateThresholdSetting(
            @RequestBody ThresholdSettingDto thresholdSettingDto) {
        ThresholdSettingDto updatedDto = thresholdSettingFacade.update(thresholdSettingDto.getAlertType(),
                thresholdSettingDto.getMerchantId(),
                thresholdSettingDto);
        return ResponseEntity.ok(updatedDto);
    }
}