package com.osdb.ippay.secondary.alerts.service.impl;

import static lombok.AccessLevel.PRIVATE;

import java.util.List;

import org.springframework.stereotype.Service;

import com.osdb.ippay.secondary.alerts.repository.MerchantSettlementRepository;
import com.osdb.ippay.secondary.alerts.repository.entity.Merchants;
import com.osdb.ippay.secondary.alerts.service.MerchantSettlementService;

import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@Service
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class MerchantSettlementServiceImpl implements MerchantSettlementService {

	// repositories
	MerchantSettlementRepository merchantSettlementRepository;

	@Override
	public List<Merchants> find() {

		return merchantSettlementRepository.findAll();
	}
}
