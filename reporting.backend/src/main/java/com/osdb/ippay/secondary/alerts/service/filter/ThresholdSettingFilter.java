package com.osdb.ippay.secondary.alerts.service.filter;

import static lombok.AccessLevel.PRIVATE;

import com.osdb.ippay.secondary.alerts.repository.entity.AlertType;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

@Data
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class ThresholdSettingFilter {
    AlertType alertType;
    String merchantId;
}
