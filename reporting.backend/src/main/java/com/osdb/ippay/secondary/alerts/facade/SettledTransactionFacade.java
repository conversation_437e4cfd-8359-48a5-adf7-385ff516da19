package com.osdb.ippay.secondary.alerts.facade;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Map;

import com.osdb.ippay.common.domain.FileDomain;
import com.osdb.ippay.secondary.alerts.service.filter.SettledTransactionFilter;

public interface SettledTransactionFacade {

    Map<String, ByteArrayOutputStream> findMerchantsDataToExport(String startDateTime, String endDateTime,
            SettledTransactionFilter filter)
            throws IOException;

    FileDomain export(String startDateTime, String endDateTime, SettledTransactionFilter filter) throws IOException;
}