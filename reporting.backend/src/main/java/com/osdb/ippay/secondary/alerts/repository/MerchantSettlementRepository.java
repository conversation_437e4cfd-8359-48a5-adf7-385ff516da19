package com.osdb.ippay.secondary.alerts.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import com.osdb.ippay.secondary.alerts.repository.entity.Merchants;

public interface MerchantSettlementRepository extends
        JpaRepository<Merchants, Long>,
        JpaSpecificationExecutor<Merchants> {

    @Override
    List<Merchants> findAll();

}
