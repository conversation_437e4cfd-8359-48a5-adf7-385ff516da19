package com.osdb.ippay.primary.wlabel.service.impl;

import com.osdb.ippay.common.exception.business.BusinessException;
import com.osdb.ippay.common.exception.business.NoAccessException;
import com.osdb.ippay.common.exception.business.NotFoundException;
import com.osdb.ippay.primary.wlabel.repository.WLabelRepository;
import com.osdb.ippay.primary.wlabel.repository.entity.WLabel;
import com.osdb.ippay.primary.wlabel.service.WLabelService;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Optional;

import static com.osdb.ippay.common.exception.ErrorMessage.*;
import static com.osdb.ippay.common.util.request.RequestUtil.getReferer;
import static java.util.Objects.isNull;
import static lombok.AccessLevel.PRIVATE;
import static org.apache.commons.lang.StringUtils.EMPTY;
import static org.apache.commons.lang3.StringUtils.equalsIgnoreCase;

@Slf4j
@Service
@RequiredArgsConstructor
@FieldDefaults(makeFinal = true, level = PRIVATE)
public class WLabelServiceImpl implements WLabelService {

    WLabelRepository wLabelRepository;

    @Override
    public WLabel find(List<Long> partnerId) {
        return wLabelRepository.findFirstByPartnerIdIn(partnerId);
    }

    @Override
    public WLabel find(String subDomain) {
        return wLabelRepository
                .findBySubDomain(subDomain)
                .orElseThrow(() -> new NotFoundException(AUTH_WHITE_LABEL_NOT_FOUND));
    }

    @Override
    public WLabel find(HttpServletRequest request) {
        try {
            String subDomain = getSubdomain(getReferer(request));
            log.info("While label subdomain {}", subDomain);

            return find(subDomain);

        } catch (Exception exception) {
            throw new NotFoundException(AUTH_WHITE_LABEL_NOT_FOUND);
        }
    }

    @Override
    public void validateSubDomain(WLabel expectedWLabel, WLabel actualWLabel, HttpServletRequest request, boolean isSignIn) {
        if(isNull(expectedWLabel)) return;
        if(isNull(actualWLabel)) throwIfNoAccessByWLabel(isSignIn);

        String expectedSubDomain = expectedWLabel.getSubDomain();
        String actualSubdomain = actualWLabel.getSubDomain();
        if(!equalsIgnoreCase(expectedSubDomain, actualSubdomain)) throwIfNoAccessByWLabel(isSignIn);

        String actualWLabelTitle = Optional.ofNullable(actualWLabel.getTitle()).orElse(EMPTY).toLowerCase();
        if(!equalsIgnoreCase(actualWLabelTitle, "monere")) throw new NoAccessException(AUTH_NO_ACCESS);
    }

    private void throwIfNoAccessByWLabel(boolean isSignIn) {
        if(isSignIn) throw new BusinessException(AUTH_INVALID_USER_CREDS);
        throw new NoAccessException(AUTH_NO_ACCESS);
    }

    private String getSubdomain(String origin) {
        if(StringUtils.isBlank(origin)) {
            return EMPTY;
        }

        return origin
                .replaceAll("https://", "")
                .replaceAll("http://", "")
                .replaceAll("/", "")
                .split("\\.")[0];
    }
}
