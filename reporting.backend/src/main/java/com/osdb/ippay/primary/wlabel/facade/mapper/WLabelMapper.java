package com.osdb.ippay.primary.wlabel.facade.mapper;

import com.osdb.ippay.primary.wlabel.facade.dto.WLabelDto;
import com.osdb.ippay.primary.wlabel.repository.entity.WLabel;
import org.springframework.stereotype.Component;

@Component
public class WLabelMapper {

    public WLabelDto toDto(WLabel wLabel) {
        return WLabelDto.builder()
                .title(wLabel.getTitle())
                .subDomain(wLabel.getSubDomain())
                .bigLogoLink(wLabel.getBigLogoLink())
                .smallLogoLink(wLabel.getSmallLogoLink())
                .faviconLink(wLabel.getFaviconLink())
                .createdDate(wLabel.getCreatedDate())
                .build();
    }
}
