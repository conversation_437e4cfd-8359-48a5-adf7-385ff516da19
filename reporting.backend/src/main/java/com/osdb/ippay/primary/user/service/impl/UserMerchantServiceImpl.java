package com.osdb.ippay.primary.user.service.impl;

import com.osdb.ippay.primary.user.repository.UserMerchantRepository;
import com.osdb.ippay.primary.user.repository.entity.UserMerchant;
import com.osdb.ippay.primary.user.repository.entity.UserMerchantNumber;
import com.osdb.ippay.primary.user.service.UserMerchantService;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

import static lombok.AccessLevel.PRIVATE;

@Service
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class UserMerchantServiceImpl implements UserMerchantService {

    UserMerchantRepository userMerchantRepository;

    @Override
    public List<String> findMerchantIdsByUserId(Long userId) {
        return userMerchantRepository
                .findAllByUserId(userId).stream()
                .map(UserMerchant::getMerchantId)
                .collect(Collectors.toList());
    }

    @Override
    public void deleteBy(Long userId) {
        userMerchantRepository.deleteByUserId(userId);
    }

    @Override
    public void saveAll(Long userId, List<String> merchantIds) {
        List<UserMerchant> toSave = merchantIds.stream()
                .map(merchantId ->
                        UserMerchant.builder()
                                .userId(userId)
                                .merchantId(merchantId)
                                .build()
                ).collect(Collectors.toList());

        userMerchantRepository.saveAll(toSave);
    }

    @Override
    public Long countByMerchant(String merchantId) {
        return userMerchantRepository.countAllByMerchantId(merchantId);
    }

    @Override
    public List<UserMerchantNumber> countByMerchantIds(List<String> merchantIds) {
        return userMerchantRepository.countAssignedUsers(merchantIds);
    }
}
