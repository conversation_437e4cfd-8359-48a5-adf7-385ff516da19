package com.osdb.ippay.primary.user.service.impl;

import com.osdb.ippay.common.domain.FileDomain;
import com.osdb.ippay.primary.user.repository.entity.User;
import com.osdb.ippay.primary.user.repository.entity.UserMerchant;
import com.osdb.ippay.primary.user.service.UserExportService;
import com.osdb.ippay.primary.user.service.UserService;
import com.osdb.ippay.primary.user.service.filter.UserFilter;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.PrintWriter;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static java.util.Collections.emptyList;
import static java.util.Optional.ofNullable;
import static lombok.AccessLevel.PRIVATE;

@Service
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class UserExportServiceImpl implements UserExportService {

    UserService userService;

    @Override
    public FileDomain export(User authUser, UserFilter filter) throws IOException {
        List<User> users = userService.find(filter);

        ByteArrayInputStream byteArrayInputStream = export(users);

        return FileDomain.builder()
                .file(new ByteArrayResource(byteArrayInputStream.readAllBytes()))
                .fileName(String.format("exported_users_%s.csv", LocalDate.now()))
                .build();
    }

    private ByteArrayInputStream export(List<User> users) throws IOException {
        String[] headers = {
                "Name",
                "Login ID",
                "Role",
                "Status",
                "Assigned Merchants",
                "Receive Daily Settlement",
                "Receive Monthly Settlement",
                "Receive E-Check Reject Notices",
                "Receive Chargebacks Notices",
                "Last Login",
                "Failed Logins",
                "IP Address"
        };

        final CSVFormat format = CSVFormat.Builder.create()
                .setHeader(headers)
                .setIgnoreEmptyLines(true)
                .build();

        try (
                ByteArrayOutputStream out = new ByteArrayOutputStream();
                CSVPrinter csvPrinter = new CSVPrinter(new PrintWriter(out), format)
        ) {

            String pattern = "MM/dd/yyyy HH:mm";
            SimpleDateFormat dateFormat = new SimpleDateFormat(pattern);

            for (User user : users) {
                String lastLogin = user.getLastLogin() != null ?
                        dateFormat.format(Date.from(user.getLastLogin())) : "";

                String merchants = ofNullable(user.getMerchants())
                        .orElse(emptyList()).stream()
                        .map(UserMerchant::getMerchantId)
                        .collect(Collectors.joining("; "));

                List<String> data = Arrays.asList(
                        user.getName(),
                        user.getEmail(),
                        user.getRole().name(),
                        user.getStatus().name(),
                        merchants,
                        getOrElse(user.getReceiveDailySettlement()),
                        getOrElse(user.getReceiveMonthlySettlement()),
                        getOrElse(user.getReceiveEcheckRejectNotices()),
                        getOrElse(user.getReceiveChargebacksNotices()),
                        lastLogin,
                        getOrElse(user.getTotalFailedLogins()),
                        user.getLastSessionIpAddress()
                );

                csvPrinter.printRecord(data);
            }

            csvPrinter.flush();
            return new ByteArrayInputStream(out.toByteArray());
        }
    }

    public String getOrElse(Boolean value) {
        if(value != null) {
            return String.valueOf(value);
        }

        return StringUtils.EMPTY;
    }

    public String getOrElse(Integer value) {
        if(value != null) {
            return String.valueOf(value);
        }

        return StringUtils.EMPTY;
    }
}
