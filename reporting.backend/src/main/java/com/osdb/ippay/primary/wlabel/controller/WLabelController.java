package com.osdb.ippay.primary.wlabel.controller;

import com.osdb.ippay.common.logging.LogExecutionTime;
import com.osdb.ippay.primary.wlabel.facade.WLabelFacade;
import com.osdb.ippay.primary.wlabel.facade.dto.WLabelDto;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

import static lombok.AccessLevel.PRIVATE;

@Slf4j
@Tag(name = "white-label")
@RestController
@RequestMapping(value = "/api/v1/public/white-label")
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class WLabelController {

    WLabelFacade wLabelFacade;

    @LogExecutionTime
    @GetMapping
    public ResponseEntity<?> get(@Parameter(hidden = true) HttpServletRequest request) {
        try {
            WLabelDto response = wLabelFacade.find(request);
            return ResponseEntity.ok(response);

        } catch (Exception exception) {
            log.warn(exception.getMessage());
            return ResponseEntity.noContent().build();
        }
    }
}
