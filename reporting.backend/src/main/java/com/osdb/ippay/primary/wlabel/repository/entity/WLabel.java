package com.osdb.ippay.primary.wlabel.repository.entity;


import com.osdb.ippay.common.entity.BaseEntity;
import lombok.*;
import lombok.experimental.FieldDefaults;
import lombok.experimental.SuperBuilder;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import java.time.Instant;

import static lombok.AccessLevel.PRIVATE;

@Entity
@Table(name = "white_label")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = PRIVATE)
@SuperBuilder(toBuilder = true)
public class WLabel extends BaseEntity {

    @Column(name = "title")
    String title;

    @Column(name = "sub_domain")
    String subDomain;

    @Column(name = "small_logo_link")
    String smallLogoLink;

    @Column(name = "big_logo_link")
    String bigLogoLink;

    @Column(name = "favicon_link")
    String faviconLink;

    @Column(name = "partner_id")
    Long partnerId;

    @Column(name = "created_date")
    Instant createdDate;

}
