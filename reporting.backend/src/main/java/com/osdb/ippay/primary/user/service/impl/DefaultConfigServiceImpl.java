package com.osdb.ippay.primary.user.service.impl;

import com.osdb.ippay.primary.user.repository.DefaultConfigRepository;
import com.osdb.ippay.primary.user.repository.entity.DefaultConfig;
import com.osdb.ippay.primary.user.service.DefaultConfigService;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.stereotype.Service;

import java.util.List;

import static lombok.AccessLevel.PRIVATE;

@Service
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class DefaultConfigServiceImpl implements DefaultConfigService {

    DefaultConfigRepository configRepository;

    @Override
    public List<DefaultConfig> find() {
        return configRepository.findAll();
    }
}
