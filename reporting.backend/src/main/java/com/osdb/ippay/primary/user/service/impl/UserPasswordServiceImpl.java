package com.osdb.ippay.primary.user.service.impl;

import com.osdb.ippay.common.exception.business.AlreadyExistsException;
import com.osdb.ippay.primary.user.repository.UserPasswordRepository;
import com.osdb.ippay.primary.user.repository.entity.UserPassword;
import com.osdb.ippay.primary.user.service.UserPasswordService;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.List;

import static com.osdb.ippay.common.exception.ErrorMessage.AUTH_RECENT_PASSWORD_CANNOT_BE_REUSED;
import static lombok.AccessLevel.PRIVATE;

@Service
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class UserPasswordServiceImpl implements UserPasswordService {

    UserPasswordRepository passwordRepository;
    PasswordEncoder passwordEncoder;

    @Override
    public List<UserPassword> find(Long userId) {
        return passwordRepository.findByUserId(userId);
    }

    @Transactional
    @Override
    public void add(Long userId, String newPassword) {
        List<UserPassword> passwords = find(userId);
        throwIfNewPasswordIsPreviouslyUsed(passwords, newPassword);

        if(passwords.size() >= 5) {
            UserPassword userPassword = passwordRepository.findFirstByUserIdOrderByCreatedDateAsc(userId);
            passwordRepository.deleteByIdAndUserId(userPassword.getId(), userId);
        }

        passwordRepository.save(
                UserPassword.builder()
                        .userId(userId)
                        .password(passwordEncoder.encode(newPassword))
                        .createdDate(Instant.now())
                        .build()
        );
    }

    private void throwIfNewPasswordIsPreviouslyUsed(List<UserPassword> passwords, String newPassword) {
        passwords.stream()
                .filter(uPassword -> passwordEncoder.matches(newPassword, uPassword.getPassword()))
                .findFirst()
                .ifPresent(s -> { throw new AlreadyExistsException(AUTH_RECENT_PASSWORD_CANNOT_BE_REUSED); });
    }
}
