package com.osdb.ippay.primary.wlabel.service;

import com.osdb.ippay.primary.wlabel.repository.entity.WLabel;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

public interface WLabelService {

    WLabel find(List<Long> partnerId);

    WLabel find(String subDomain);

    WLabel find(HttpServletRequest request);

    void validateSubDomain(WLabel expectedWLabel, WLabel actualWLabel, HttpServletRequest request, boolean isSignIn);

}
