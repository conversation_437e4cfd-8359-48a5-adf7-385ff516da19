package com.osdb.ippay.primary.wlabel.repository;

import com.osdb.ippay.primary.wlabel.repository.entity.WLabel;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.Optional;

public interface WLabelRepository extends JpaRepository<WLabel, Long> {

    Optional<WLabel> findBySubDomain(String subDomain);

    WLabel findFirstByPartnerIdIn(List<Long> partnerId);

}
