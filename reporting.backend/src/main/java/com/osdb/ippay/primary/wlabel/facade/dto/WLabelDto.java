package com.osdb.ippay.primary.wlabel.facade.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.time.Instant;

import static lombok.AccessLevel.PRIVATE;

@Getter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class WLabelDto {

    String title;

    String subDomain;

    String smallLogoLink;

    String bigLogoLink;

    String faviconLink;

    Instant createdDate;

}
