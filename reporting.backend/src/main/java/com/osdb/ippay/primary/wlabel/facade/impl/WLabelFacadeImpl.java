package com.osdb.ippay.primary.wlabel.facade.impl;

import com.osdb.ippay.primary.wlabel.facade.WLabelFacade;
import com.osdb.ippay.primary.wlabel.facade.dto.WLabelDto;
import com.osdb.ippay.primary.wlabel.facade.mapper.WLabelMapper;
import com.osdb.ippay.primary.wlabel.repository.entity.WLabel;
import com.osdb.ippay.primary.wlabel.service.WLabelService;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;

import static lombok.AccessLevel.PRIVATE;

@Component
@RequiredArgsConstructor
@FieldDefaults(makeFinal = true, level = PRIVATE)
public class WLabelFacadeImpl implements WLabelFacade {

    WLabelService wLabelService;
    WLabelMapper wLabelMapper;

    @Override
    public WLabelDto find(HttpServletRequest request) {
        WLabel wLabel = wLabelService.find(request);
        return wLabelMapper.toDto(wLabel);
    }
}
